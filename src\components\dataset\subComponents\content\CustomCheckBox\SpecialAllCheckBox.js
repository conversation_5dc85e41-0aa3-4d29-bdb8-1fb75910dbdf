import React, { useContext, useState } from "react";

// ui
import { Checkbox } from "semantic-ui-react";
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";
import { PEICES_INFO } from "../../../../common/sheetCrud/sheetCrudHelper";

// store

const SpecialAllCheckBox = ({
    rowId,
    isChecked,
    onClick,
    // setActiveCheckedIds,
    // setHeaderChecked,
    // headerChecked,
    ...rest
}) => {
    const [state, dispatch] = useContext(StoreContext);
    const { content, sheet } = state.data;
    const { tabClass } = sheet.tabKey;
    const { rows } = content;
    // handle checkbox
    const [checked, setChecked] = useState(isChecked);

    // handle checkbox for delete
    const handleCheckbox = () => {
        // checkbox state
        setChecked(!checked);

        // record rowId if checked === true
        if (!checked) {
            // customCheckBox
            let data = Object.keys(rows).map(key => ({ rowId: key })) || [];
            // specialCheckBox
            if (tabClass === PEICES_INFO) {
                //  Object.values可以拿到陣列的key
                data = Object.values(rows).reduce((acc, cur, curIdx) => {
                    const cellIndex = Object.keys(cur?.transList).map(key => ({
                        rowId: `${curIdx}-${key}`
                    }));

                    acc.push(...cellIndex);

                    return acc;
                }, []);
            }
            // record checkbox for delete
            dispatch({
                type: Act.DATA_CONTENT_ROW_CHECKED_ALL,
                payload: data
            });

            // setActiveCheckedIds(data.map(item => item.rowId));
        } else {
            // cancel record checkbox for delete
            dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });
            // setActiveCheckedIds([]);
        }
    };

    return <Checkbox {...rest} checked={checked} onClick={handleCheckbox} />;
};

export default SpecialAllCheckBox;

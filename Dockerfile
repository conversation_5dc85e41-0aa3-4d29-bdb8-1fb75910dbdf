ARG APP_DIR=/app

# Use an official Node.js runtime as a parent image
FROM node:14-alpine AS build

# Consume the build argument in the build stage
ARG APP_DIR

# Set working directory
WORKDIR ${APP_DIR}

# Copy only package.json and package-lock.json first for better layer caching
COPY package*.json ./
COPY package-lock.json ./

# Install dependencies
RUN npm ci

# Copy the rest of the application code
COPY . .

# Build the project — this generates the dist folder
RUN npm run build

# Use nginx image
FROM nginx:alpine

# Consume the build argument in the build stage
ARG APP_DIR

COPY --from=build ${APP_DIR}/build /usr/share/nginx/html

COPY ./nginx/default.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Start Nginx
CMD ["nginx", "-g", "daemon off;"]
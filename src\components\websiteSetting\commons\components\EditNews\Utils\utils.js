import axios from "axios";
import { pubStatusSelect } from "./fixedSelect";
import textConfig from "./textConfig";
import NewsAct from "../EditNewsAction";
import {
    fileServerAPI,
    fileServerMethod,
    fsFrontEdit
} from "../../../../../../api/fileServer";
import uploadConfig from "../../../../../toolPages/components/upload/uploadConfig";
import initColumnDef from "./initColumnDef";

//
import saveNews from "./saveNews";
import { saveDataCallBack, getFileName } from "./saveDataUtils";
import saveURLEvent from "./saveURLEvent";

// for DraggableTable component use only
const uploadImg = subject => {
    const fsFolderName = "news";
    const inputEl = document.createElement("input");
    inputEl.setAttribute("type", "file");
    inputEl.setAttribute("accept", "image/*");
    inputEl.click();

    return new Promise((resolve, reject) => {
        inputEl.onchange = () => {
            if (inputEl.files) {
                const file = inputEl.files[0];
                const commonUrl = fileServerAPI.uploadFile.replace(
                    "[type]",
                    uploadConfig.image
                );
                const reqUrl = `${commonUrl}/${fsFrontEdit}/${subject}/${fsFolderName}`;

                const formData = new FormData();
                formData.append(uploadConfig.ImageFormName, file);

                // upload image
                axios({
                    method: fileServerMethod.uploadFile,
                    url: reqUrl,
                    headers: {
                        "Access-Control-Allow-Origin": "*"
                    },
                    data: formData
                })
                    .then(res => {
                        if (res.status === 200) {
                            const { imgUrl } = res.data.images[0];
                            resolve(imgUrl);
                        }
                    })
                    .catch(err => {
                        reject(err);
                    });
            }
        };
    });
};

const handleFileError = (reqUrl, formData) =>
    new Promise((resolve, reject) => {
        axios({
            method: "PUT",
            url: reqUrl,
            headers: {
                "Access-Control-Allow-Origin": "*"
            },
            data: formData,
            responseType: "json" // important
        })
            .then(res => {
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });

const uploadFile = (targetEl, subject, curAllUpload, fsFolderName) => {
    // const fsFolderName = "news";
    const allUpload = [];
    if (targetEl.files) {
        // eslint-disable-next-line no-restricted-syntax
        for (const file of targetEl.files) {
            const commonUrl = fileServerAPI.uploadFile.replace(
                "[type]",
                file.type.indexOf("image") > -1
                    ? uploadConfig.image
                    : uploadConfig.file
            );
            const reqUrl = `${commonUrl}/${subject}/${fsFolderName}`;

            const formData = new FormData();
            formData.append("image", file);

            const path = `${
                file.type.indexOf("image") > -1
                    ? fileServerAPI.readUploadImage
                    : fileServerAPI.readFile
            }/${subject}/${fsFolderName}/`;

            allUpload.push({
                promiseFun: handleFileError(reqUrl, formData),
                path
            });
        }
    }

    return new Promise((resolve, reject) => {
        Promise.all(allUpload.map(el => el.promiseFun))
            .then(allResult => {
                const allURL = allResult
                    .filter(obj => obj.data.message)
                    .map((tmpObj, idx) => {
                        const tmpFileName = getFileName(
                            tmpObj.data.images[0].imgUrl
                        );

                        return `${allUpload[idx].path}${tmpFileName}`;
                    });
                resolve([...curAllUpload, ...allURL]);
            })
            .catch(error => reject(error));
    });
};

// updated newsInfo
const setUpdateNewsInfo = (dispatch, tmpNewsEvents) => {
    dispatch({
        type: NewsAct.SET_UPDATENEWSINFO,
        payload: tmpNewsEvents
    });
};

// origin newsInfo
const setNewsInfo = (dispatch, tmpNewsEvents) => {
    dispatch({
        type: NewsAct.SET_NEWSFULLINFO,
        payload: tmpNewsEvents
    });
};

// 最新消息列表
const setTbBDData = (dispatch, tmpNewsEvents) => {
    dispatch({
        type: NewsAct.SET_TBBDDATA,
        payload: tmpNewsEvents
    });
};

// 檢查"newsType", "hasStartDate", "status"三個欄位有沒有填寫
// 檢查"titleZH"、"titleEN"欄位
const confirmColumn = tmpNewsEvents => {
    const checkCol = [
        { prop: initColumnDef.newsType, message: textConfig.Header_Class },
        { prop: initColumnDef.hasStartDate, message: textConfig.Header_Time },
        { prop: initColumnDef.status, message: textConfig.Header_Status },
        {
            prop: [initColumnDef.titleZH, initColumnDef.titleEN],
            message: `${textConfig.Header_Title}(擇一)`
        }
    ];

    let lackMsg = "";
    checkCol.forEach(el => {
        if (typeof el.prop === "string") {
            if (!tmpNewsEvents[el.prop]) {
                lackMsg += `${el.message}欄位還沒填 \n`;
            }
        } else if (Array.isArray(el.prop)) {
            // array裡面，至少擇一欄位填寫
            const fillAnyCol = el.prop.some(key => tmpNewsEvents[key]);
            if (!fillAnyCol) {
                lackMsg += `${el.message}欄位還沒填 \n`;
            }
        }
    });

    return lackMsg;
};

/** 參數說明:
 * dispatch: context state update callback function
 * newsFullInfo: 最新消息編輯前的資料
 * updateNewsInfo: 最新消息編輯後的資料
 * user: 登入者firebase資料
 * subject: 選擇更改網站名稱, e.g. nmtl、tltc、....
 * */
// update to fuseki
const saveData = async (
    dispatch,
    newsFullInfo,
    updateNewsInfo,
    user,
    subject
) => {
    // 先處理News instance
    const copySrc = JSON.parse(JSON.stringify(newsFullInfo));
    const copyDst = JSON.parse(JSON.stringify(updateNewsInfo));

    const collectRes = [];
    const newsRes = await saveNews(copySrc, copyDst, subject, user);
    collectRes.push(newsRes);

    // 再處理URLEvent instance
    const urlRes = await saveURLEvent(
        newsFullInfo,
        updateNewsInfo,
        subject,
        user
    );
    collectRes.push(urlRes);

    const res = collectRes.every(el => el === "OK") ? "OK" : "NOT OK";
    saveDataCallBack(res, dispatch);
};

// delete News，更改status狀態為"刪除"
const delNews = (dispatch, newsFullInfo, updateNewsInfo, user, subject) => {
    const tmpNewsEvents = JSON.parse(JSON.stringify(updateNewsInfo));
    tmpNewsEvents.status = pubStatusSelect.find(el => !el.enable).name;

    // update fuseki
    saveData(dispatch, newsFullInfo, tmpNewsEvents, user, subject);
};

export {
    uploadImg,
    uploadFile,
    setUpdateNewsInfo,
    setNewsInfo,
    delNews,
    confirmColumn,
    saveData,
    setTbBDData
};

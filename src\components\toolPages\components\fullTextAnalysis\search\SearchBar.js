import React, { useEffect, useState } from "react";
import { Input } from "semantic-ui-react";
import { useHistory } from "react-router-dom";
import queryString from "query-string";
import useDebounce from "../../../../common/hooks/useDebounce";
import { menuId } from "../../../config";

// 搜尋框
const SearchBar = () => {
    // route
    const history = useHistory();
    const {
        location: { search }
    } = history;
    const { keyword: urlKeyword } = queryString.parse(search);
    // store

    //
    const [loading, setLoading] = useState(false);
    const [searchValue, setSearchValue] = useState(null);
    const debSearchValue = useDebounce(searchValue, 800); // debounce value

    // searchbar change
    const handleSearchBarChange = value => {
        setLoading(true);
        setSearchValue(value);
    };

    // searchbar keyup
    const onSearchBarKeyUp = (e, data) => {
        // do nothing
    };

    useEffect(() => {
        if (debSearchValue !== null) {
            history.push({
                search: `?${queryString.stringify(
                    {
                        keyword: debSearchValue,
                        tab: menuId.fltAna
                    },
                    { arrayFormat: "comma" }
                )}`
            });
        }
        if (loading) setLoading(false);
    }, [debSearchValue]);

    return (
        <div>
            <Input
                fluid
                defaultValue={urlKeyword}
                type="text"
                placeholder="搜尋著作/文章的 id or 名稱"
                action
                onChange={(e, data) => handleSearchBarChange(data.value)}
                onKeyUp={(e, data) => {
                    onSearchBarKeyUp(e, data);
                }}
                icon="search"
                loading={loading}
            />
        </div>
    );
};

export default SearchBar;

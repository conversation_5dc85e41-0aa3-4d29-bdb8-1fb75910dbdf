import React, { useMemo } from "react";
import PropTypes from "prop-types";
import DropdownTreeSelect from "react-dropdown-tree-select";
import "react-dropdown-tree-select/dist/styles.css";
import { Grid } from "semantic-ui-react";
import { uuidv4 } from "../../../../commons/utility";
import uploadConfig from "../../../toolPages/components/upload/uploadConfig";

const convertToDropDownTree = files =>
    files.map(f => ({
        label: f.imgFileName,
        value: f.originalUrl,
        actiontype: f.type
    }));

// file:
// {
//     imgFileName: "2022-5-18-12-15-27-169079900.pdf"
//     imgInfo: ""
//     originalUrl: ""
//     type: "docs"
//     url: "2022-5-18-12-15-27-169079900.pdf"
// }
const FileList = ({ files, onChange, firstChild, ctlType, mode }) => {
    const data = convertToDropDownTree(files);

    // 把參數轉換成圖片所需要的，以正常顯示
    const handleClick = (selected, selectedAll) => {
        const addDataImgUrl = selectedAll.map(f => ({
            ...f,
            "data-img-url": f.value
        }));
        onChange({ checked: addDataImgUrl, original: [] });
    };

    // 「資料夾列表」與「上傳預覽」為不同的外觀與操作。
    const dropDownSelect =
        ctlType === uploadConfig.DropDownPreview ? (
            <DropdownTreeSelect
                key={uuidv4()}
                data={data}
                mode={mode || "simpleSelect"}
                showDropdown="always"
                rootMargin="500px"
                readOnly
            />
        ) : (
            <DropdownTreeSelect
                key={uuidv4()}
                data={data}
                mode={mode || "hierarchical"}
                showDropdown="always"
                rootMargin="500px"
                clearSearchOnChange
                onChange={handleClick}
            />
        );
    return useMemo(
        () => (
            <React.Fragment>
                <Grid style={{ width: "100%", height: "60vh" }}>
                    <Grid.Column width={3} style={{ width: "50px" }}>
                        {firstChild}
                    </Grid.Column>
                    <Grid.Column width={13}>
                        {data.length > 0 ? dropDownSelect : "沒有資料"}
                    </Grid.Column>
                </Grid>
            </React.Fragment>
        ),
        [files]
    );
};

FileList.defaultProps = {
    files: [],
    onChange: () => null,
    firstChild: null,
    ctlType: "preview"
};

FileList.propTypes = {
    files: PropTypes.instanceOf(Array).isRequired,
    onChange: PropTypes.func.isRequired,
    firstChild: PropTypes.instanceOf(Object),
    ctlType: PropTypes.string
};

export default FileList;

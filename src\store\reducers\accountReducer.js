import Act from "../actions";

const initState = { changedRole: {}, willRemoveUser: "", renderSignal: "" };

const accountReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.FIREBASE_USER_ROLE_CHANGED:
            return { ...state, changedRole: action.payload };
        case Act.FIREBASE_USER_ROLE_CHANGED_CLEAN:
            return { ...state, changedRole: {} };
        case Act.FIREBASE_USER_ROLE_UPDATE_RENDER_SIGNAL:
            return { ...state, renderSignal: action.payload };
        case Act.FIREBASE_USER_REMOVE:
            return { ...state, willRemoveUser: action.payload };
        default:
            return state;
    }
};

export default accountReducer;

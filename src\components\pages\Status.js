import React, { useContext } from "react";

// store
import { StoreContext } from '../../store/StoreProvider';

// ui
import {Container, Divider, Segment} from 'semantic-ui-react';

const Status = () => {

    console.log('Hi, I am Status');

    // eslint-disable-next-line no-unused-vars
    const [state, _] = useContext(StoreContext);

    return (
        <Container>

            <Segment basic compact>
                <h2>Reducer State</h2>
            </Segment>

            <Divider />

            <Segment>
                <pre>
                    {
                        JSON.stringify(
                            {
                                ...state,
                                data: {
                                    ...state.data,
                                    sheet: {
                                        ...state.data.sheet, headerFields: Object.keys(state.data.sheet.headerFields)
                                    }
                                }
                            }, null, 2)
                    }
                </pre>
            </Segment>

        </Container>
    )
};

export default Status;
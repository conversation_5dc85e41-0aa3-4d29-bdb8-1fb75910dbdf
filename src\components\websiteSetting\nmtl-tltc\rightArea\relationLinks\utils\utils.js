import Act from "../../../../../../store/actions";
import tltcAct from "../../../tltcAction";
import { isEmpty } from "../../../../../../commons";
import {
    createNmtlData,
    readNmtlData,
    updateNmtlData
} from "../../../../../../api/nmtl";
import { frontEditPrefix } from "../../../../../../api/nmtl/classPrefix";
import Api from "../../../../../../api/nmtl/Api";
import textMsg from "../../../../commons/textMsg";

export const handleEdit = (setIsEdited, dispatch, id = "") => {
    setIsEdited(true);
    dispatch({
        type: Act.FRONTEDIT_TLTC,
        localType: tltcAct.SET_RELLINKEDITID,
        payload: id
    });
};

export const createData = (key, value, text) => ({ key, value, text });

const splitLangTag = (tmpItem, value, key) => {
    const newTmpItem = Object.assign({}, tmpItem);
    const langTag = value.slice(-2).toUpperCase();
    const newPName = `${key}${langTag}`;
    newTmpItem[newPName] = value.slice(0, -3);
    return newTmpItem;
};

export const reduceSubData = (data, classType) =>
    data
        .filter(({ o }) => o !== classType)
        .reduce((cur, next) => {
            const tmpCur = Object.assign({}, cur);
            if (isEmpty(cur)) {
                tmpCur.id = next.srcId;
            }

            if (!Object.hasOwn(cur, next.p)) {
                tmpCur[next.p] = [next.o];
            } else {
                tmpCur[next.p].push(next.o);
            }
            return tmpCur;
        }, {});

/**
 * [{key1: value1}, {key2: value2}, {key2: value3}, ....]，資料結構轉格式
 * step1: 轉成 {id: "", key1: [value1], key2: [value2, value3], ....}
 * stpe2: 轉成 {id: "", key1: "value1", key2[langTag]: "value2", key2[langTag]: "value3", ....}
 * */
export const transInstanceData = (data, classType) => {
    // step 1
    const tmpItem = reduceSubData(data, classType);

    /**
     * {key: [value]}轉成{key: "value"}格式
     * 有multi value的欄位，個別拆，取新的property name
     * */
    let newTmpItem = {};
    Object.keys(tmpItem).forEach(key => {
        if (!Array.isArray(tmpItem[key])) {
            newTmpItem[key] = tmpItem[key];
        } else if (tmpItem[key].length === 1) {
            if (tmpItem[key][0].slice(-3, -2) === "@") {
                newTmpItem = splitLangTag(newTmpItem, tmpItem[key][0], key);
            } else {
                [newTmpItem[key]] = tmpItem[key];
            }
        } else {
            // 根據語系取新的property name
            tmpItem[key].forEach(value => {
                if (value.slice(-3, -2) === "@") {
                    newTmpItem = splitLangTag(newTmpItem, value, key);
                }
            });
        }
    });

    return newTmpItem;
};

// time: 12345678 -> yyyy-mm-dd
export const showDate = lastModified => {
    if (lastModified) {
        const date = new Date(parseInt(lastModified, 10));
        return date.toISOString().split("T")[0];
    }
    return "";
};

// 根據classPrefix取得event class type
export const getClassType = tmpID => {
    const findClassType = frontEditPrefix.find(
        el => tmpID.startsWith(el.prefix) && el.prefix
    );
    return findClassType.eventType || "";
};

// 建立instance與new event關係
export const connEvent = (graph, user, eventEntry) => {
    const id = "CARDtltc";
    return new Promise((resolve, reject) => {
        const apiStr = Api.getGeneric;
        const { sheetName } = textMsg;
        const tmpClassType = getClassType(id);
        const commonProp = {
            graph,
            classType: tmpClassType,
            srcId: id
        };

        const pageEntrySrc = {
            ...commonProp,
            value: {}
        };

        const pageEntryDst = {
            ...commonProp,
            value: {
                hasURL: eventEntry.srcId
            }
        };

        updateNmtlData(
            user,
            apiStr,
            graph,
            sheetName,
            pageEntrySrc,
            pageEntryDst
        )
            .then(res => {
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });
};

export const createInstIDonly = (graph, user, eventEntry) =>
    new Promise((resolve, reject) => {
        const apiStr = Api.getGeneric;
        const { sheetName } = textMsg;
        const tmpClassType = getClassType(eventEntry.srcId);
        const commonProp = {
            graph,
            classType: tmpClassType,
            srcId: eventEntry.srcId
        };

        const entry = {
            ...commonProp,
            value: {}
        };

        createNmtlData(user, apiStr, graph, sheetName, entry)
            .then(res => {
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });

// 更換分類，order給該分類的最大值 + 1
export const getNewOrder = (type, data) => {
    const tmpData = Object.assign({}, data);
    return new Promise((resolve, reject) => {
        const apiStr2 = Api.getTltcRellinksListOrder.replace("{type}", type);
        readNmtlData(apiStr2)
            .then(res => {
                if (res?.data) {
                    tmpData.order = `${parseInt(res.data[0].maxNum || 0, 10) +
                        1}`;
                    resolve(tmpData);
                }
            })
            .catch(err => reject(err));
    });
};

// load table data
export const loadTBData = (type, dispatch) => {
    if (!type) return;
    const apiStr = Api.getTltcRellinksList.replace("{type}", type);
    readNmtlData(apiStr).then(res => {
        if (res?.data) {
            dispatch({
                type: Act.FRONTEDIT_TLTC,
                localType: tltcAct.SET_RELLINKTABLE,
                payload: res.data
                    .map(el => ({ ...el, check: false }))
                    .sort(
                        (a, b) => parseInt(a.order, 10) - parseInt(b.order, 10)
                    )
            });
        }
    });
};

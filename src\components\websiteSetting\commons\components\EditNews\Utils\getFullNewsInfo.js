import Api from "../../../../../../api/nmtl/Api";
import { readNmtlData } from "../../../../../../api/nmtl";
import { isEmpty } from "../../../../../../commons";
import initColumnDef, { imageColDef } from "./initColumnDef";
import inputConfig from "../subComponents/NewsInfoArea/config";
import { setNewsInfo, setUpdateNewsInfo } from "./utils";

// image
import dragImage from "../../../../../../images/dragImage.svg";

const getFullNewsInfo = (tmpObj, dispatch) => {
    const { newsIdStr } = tmpObj;
    if (!newsIdStr) return;
    const apiStr = Api.getNewsInfo.replace("{newsId}", newsIdStr);
    readNmtlData(apiStr).then(res => {
        const data = res.data.length > 0 ? res.data[0] : {};

        // parse allurlStr string into array
        if (!isEmpty(data[initColumnDef.hasURL])) {
            const tmpArr = data[initColumnDef.hasURL]
                .split(inputConfig.SpSymb)
                .map(elStr => {
                    const imgObj = elStr.split(inputConfig.fileDataSpSymb);
                    return {
                        [imageColDef.urlId]: imgObj[0],
                        [imageColDef.imgUrl]: imgObj[1] || "",
                        [imageColDef.order]: imgObj[2],
                        [imageColDef.imgText]: imgObj[3] || dragImage
                    };
                });
            data[initColumnDef.hasURL] = tmpArr;
        } else {
            data[initColumnDef.hasURL] = [];
        }

        // parse allfileAvailableAt string into array
        if (!isEmpty(data[initColumnDef.fileAvailableAt])) {
            const tmpArr = data[initColumnDef.fileAvailableAt].split(
                inputConfig.SpSymb
            );
            data[initColumnDef.fileAvailableAt] = tmpArr;
        } else {
            data[initColumnDef.fileAvailableAt] = [];
        }

        // 同時設定一份在updateNewsInfo
        setUpdateNewsInfo(dispatch, data);
        setNewsInfo(dispatch, data);
    });
};

export default getFullNewsInfo;

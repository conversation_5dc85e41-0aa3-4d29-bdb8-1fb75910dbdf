import { Button, Divider, Icon, Segment, Header } from 'semantic-ui-react';
import React, { useContext } from 'react';
import { useDropzone } from 'react-dropzone';

// store
import { StoreContext } from '../../../../../store/StoreProvider';
import Act from '../../../../../store/actions';

import { uploadDB } from './DatabaseAction';

const handleDropAccepted = ({ acceptedFiles, dispatch }) => {
    // accept file msg
    const message = {
        type: 'success',
        title: `已接收 ${acceptedFiles.length} 個檔案 : ${acceptedFiles[0].name}`,
        text: '確認無誤後，按〈Save〉按鈕',
    };
    dispatch({
        type: Act.DATABASE_MSG,
        payload: message,
    });
    // store file in state
    dispatch({
        type: Act.UPLOAD_DB_FILE,
        payload: [acceptedFiles[0]],
    });
};

const handleDropReject = ({ files, dispatch, UPLOAD_DB_FILE_CONFIG }) => {
    const acceptExt = UPLOAD_DB_FILE_CONFIG.ACCEPTABLE_EXT.join(', ');
    const illegalFileName = files.map((f) => f.file.name).join(', ');
    // failed msg
    const message = {
        type: 'error',
        title: `檔案格式不符, 檔案為 ${illegalFileName}`,
        text: `可接受檔案格式： ${acceptExt}, 可接受數量: ${UPLOAD_DB_FILE_CONFIG.maxCount}`,
    };
    dispatch({
        type: Act.DATABASE_MSG,
        payload: message,
    });
};

const handleSaveBtnClick = (dbFiles, dispatch, displayName, columns) => {
    uploadDB(dbFiles[0], dispatch, displayName, columns);
};

const UploadDbFile = () => {
    const [state, dispatch] = useContext(StoreContext);
    const { database } = state;
    const { dbFiles, UPLOAD_DB_FILE_CONFIG } = database;
    const { headerActiveName } = state.common;
    const { displayName, systemDataActiveItem } = state.user;
    const columns = [headerActiveName, systemDataActiveItem, '資料庫回復'];

    const { getInputProps } = useDropzone({
        accept: UPLOAD_DB_FILE_CONFIG.ACCEPTABLE_MINE_TYPE.join(', '),
        maxFiles: UPLOAD_DB_FILE_CONFIG.maxCount,
        onDropAccepted: (acceptedFiles) =>
            handleDropAccepted({
                acceptedFiles,
                UPLOAD_DB_FILE_CONFIG,
                dispatch,
            }),
        onDropRejected: (files) => handleDropReject({ files, dispatch, UPLOAD_DB_FILE_CONFIG }),
    });

    return (
        <div style={{ width: '100%' }}>
            <Header as="h3">資料庫回復</Header>
            <Divider />
            <Segment
                className="dbFileDrop"
                style={{
                    border: '2px dashed #737373',
                    padding: '10px 15px',
                }}
            >
                <Icon name="upload" size="big" />
                <Button.Group>
                    <Button basic color="blue">
                        Turtle
                    </Button>
                    <input
                        {...getInputProps({
                            className: 'dropInput',
                        })}
                    />
                </Button.Group>
            </Segment>
            <Divider hidden />
            <Button
                basic
                color="green"
                onClick={() => handleSaveBtnClick(dbFiles, dispatch, displayName, columns)}
                disabled={dbFiles && dbFiles.length === 0}
            >
                Save
            </Button>
        </div>
    );
};

export default UploadDbFile;

/* eslint-disable */
import ExcelJs from 'exceljs';

const MAX_ROWS_PER_SHEET = 50000;

const CustomMultiSheetsExcelJS = async ({ allData, filename, batchSize = MAX_ROWS_PER_SHEET }) => {
  const workbook = new ExcelJs.Workbook();

  // rearrange allData
  if (allData !== null && typeof allData === 'object' && !Array.isArray(allData)) {
    Object.entries(allData)
      .sort(([, valueA], [, valueB]) => (valueA.order || 0) - (valueB.order || 0))
      .forEach(([key, value]) => {
        const { headers, data = [] } = value;

        // write workbook
        const columns = headers
          .sort((itemA, itemB) => itemA.seq - itemB.seq)
          .map(({ id, label }) => ({ header: label, key: id }));
        const totalChunks = Math.ceil((data?.length || 1) / batchSize);

        for (let i = 0; i < totalChunks; i++) {
          const chunk = data.slice(i * batchSize, (i + 1) * batchSize);
          const sheetName =
            totalChunks > 1
              ? `${key}【${i * batchSize + 1}-${
                  i + 1 >= totalChunks ? data.length : (i + 1) * batchSize
                }】`
              : key;
          const sheet = workbook.addWorksheet(sheetName);
          sheet.columns = columns;
          sheet.addRows(chunk || []);
        }
      });
  }

  workbook.xlsx
    .writeBuffer()
    .then((content) => {
      const link = document.createElement('a');
      const blob = new Blob([content], {
        type: 'application/vnd.ms-excel;charset=utf-8;',
      });
      const url = URL.createObjectURL(blob);

      link.href = url;
      link.download = `${filename}.xlsx`;
      document.body.appendChild(link); // Required for Firefox
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url); // Free up memory
    })
    .catch((error) => console.log(`Download ${filename}.xlsx Error: `, error));
};

export default CustomMultiSheetsExcelJS;

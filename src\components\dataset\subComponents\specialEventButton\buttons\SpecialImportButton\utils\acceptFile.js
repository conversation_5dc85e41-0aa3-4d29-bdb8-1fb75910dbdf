import axios from "axios";
import XLSX from "xlsx";
import {
    getSheetHeader,
    getSheetTabHeader
} from "../../../../../../../api/firebase/cloudFirestore";
import getListByApi from "../../../../../../../commons/getListByApi";
import Api from "../../../../../../../api/nmtl/Api";
import checkAccepted from "./checkAccepted";
import { checkRes } from "../config/config";
import {
    publicationHeader,
    personHeader,
    publicationMergeConfig,
    personMergeConfig
} from "../config/excelMergeConfig";

const acceptFile = async params => {
    const { acceptedFiles, newShtName, hasTab, graph, user, setMsg } = params;
    const { displayName, email } = user;
    let mergeColErr = "";

    if (!newShtName) return;
    // 按件層級header判斷，沒有就照原本架構取值
    const tabHeader = hasTab?.[1]?.tabClass;

    const fbHeader = !tabHeader
        ? await getSheetHeader(newShtName)
        : await getSheetTabHeader("PublicationInfo", tabHeader);

    // fixme: 改成根據不同sheet取得不同的checkList data
    const langList = await getListByApi(Api.getLanguage);
    const copyRightList = await getListByApi(Api.getCopyrightStatus);
    const literGenList = await getListByApi(Api.getLiteraryGenre);
    const perOrgListApi = Api.getDsPerOrgInfolist.replace("{ds}", graph);
    const perOrgList = await getListByApi(perOrgListApi);

    const checkData = {
        fbHeader,
        langList,
        copyRightList,
        literGenList,
        perOrgList
    };

    const checkTitleNumberMismatch = (zhArray, enArray) => {
        const zhNumTitleSet = new Set(zhArray.map(el => el.split("_")[0]));
        const enNumTitleSet = new Set(enArray.map(el => el.split("_")[0]));

        if (zhNumTitleSet.size !== enNumTitleSet.size) {
            return "數量不一致";
        }

        for (let title of zhNumTitleSet) {
            if (!enNumTitleSet.has(title)) {
                return "數字標題不一致";
            }
        }
        return "";
    };

    const mergeColumns = (row, zhHeader, enHeader, newHeader) => {
        let zhValue = "";
        let enValue = "";

        Object.keys(row).forEach(key => {
            if (key.includes(zhHeader)) {
                zhValue = row[key];
            }
            if (key.includes(enHeader)) {
                enValue = row[key];
            }
        });

        const zhArray = zhValue.split("\r\n");
        const enArray = enValue.split("\r\n");

        const getNumber = str => str.split("_")[0];

        const mergedResult = hasNumTitle =>
            zhArray
                .map(zhItem => {
                    const zhNumber = getNumber(zhItem);
                    const enItem = enArray.find(
                        enItem => getNumber(enItem) === zhNumber
                    );
                    return enItem && hasNumTitle
                        ? `${zhItem}_${enItem.split("_")[1]}`
                        : enItem && !hasNumTitle
                        ? `${zhItem.split("_")[1]}_${enItem.split("_")[1]}`
                        : zhItem;
                })
                .join("\n");

        let combinedValue = "";

        if (
            zhValue &&
            enValue &&
            ["references_Zh", "introduction_Zh", "label_Person_Zh"].includes(
                zhHeader
            )
        ) {
            combinedValue = `${zhValue}\n${enValue}`;
        } else if (
            zhValue &&
            enValue &&
            [
                "hasAuthor_Zh",
                "authorName_Zh",
                "hasTranslator_Zh",
                "translatorName_Zh"
            ].includes(zhHeader)
        ) {
            combinedValue = mergedResult(true);

            const mismatchMessage = checkTitleNumberMismatch(zhArray, enArray);

            mismatchMessage &&
                (mergeColErr += `${zhHeader} 與 ${enHeader} ${mismatchMessage} `);
        } else if (
            zhValue &&
            enValue &&
            ["hasEditor_Zh", "hasPublisher_Zh"].includes(zhHeader)
        ) {
            combinedValue = mergedResult(false);

            const mismatchMessage = checkTitleNumberMismatch(zhArray, enArray);

            mismatchMessage &&
                (mergeColErr += `${zhHeader} 與 ${enHeader} ${mismatchMessage} `);
        } else {
            combinedValue = `${zhValue || ""}${enValue || ""}`.trim();
        }

        row[newHeader] = combinedValue;

        Object.keys(row).forEach(key => {
            if (key.includes(enHeader) || key.includes(zhHeader)) {
                delete row[key];
            }
        });
    };

    const processExcelFile = async file => {
        const data = await file.arrayBuffer();
        const workbook = XLSX.read(new Uint8Array(data), { type: "array" });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        return XLSX.utils.sheet_to_json(worksheet, { defval: "" });
    };

    const formatHeaders = (data, headers, mergeConfig) => {
        // 合併指定的欄位
        data.forEach(row => {
            mergeConfig.forEach(config => {
                const { zhHeader, enHeader, newHeader } = config;
                mergeColumns(row, zhHeader, enHeader, newHeader);
            });
        });

        // 重新排序欄位
        return data.map(row => {
            const cleanedRow = {};

            // 清理換行符號
            Object.keys(row).forEach(key => {
                const cleanedKey = key.replace(/\r?\n/g, "");
                let cleanedValue = row[key];

                if (typeof cleanedValue === "string") {
                    cleanedValue = cleanedValue.replace(/\r?\n/g, "\n");
                }

                cleanedRow[cleanedKey] = cleanedValue;
            });

            const reorderedRow = {};

            // 根據指定的 headers 排序
            headers.forEach(key => {
                const cleanedExpectedKey = key.replace(/\r?\n/g, "");
                reorderedRow[key] = cleanedRow[cleanedExpectedKey] || "";
            });

            return reorderedRow;
        });
    };

    const generateExcelFile = (data, sheetName, fileName) => {
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.json_to_sheet(data);
        XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);

        const excelBuffer = XLSX.write(workbook, {
            bookType: "xlsx",
            type: "array"
        });

        const blob = new Blob([excelBuffer], {
            type:
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        });

        return [
            new File([blob], `${fileName}.xlsx`, {
                type: blob.type
            })
        ];
    };

    for (const file of acceptedFiles) {
        if (
            file.type ===
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        ) {
            let parsedData = await processExcelFile(file);
            const fileName = file.name.replace(".xlsx", "");

            let processedFile;

            switch (newShtName) {
                case "Publication": {
                    parsedData = formatHeaders(
                        parsedData,
                        publicationHeader,
                        publicationMergeConfig
                    );
                    processedFile = generateExcelFile(
                        parsedData,
                        "piecesInfo",
                        fileName
                    );
                    break;
                }
                case "Person": {
                    parsedData = formatHeaders(
                        parsedData,
                        personHeader,
                        personMergeConfig
                    );
                    processedFile = generateExcelFile(
                        parsedData,
                        "person",
                        fileName
                    );
                    break;
                }
                default:
                    processedFile = acceptedFiles;
                    break;
            }

            const res = await checkAccepted(processedFile, checkData);

            const pass = res.map(el => el.value.pass)[0];
            if (pass === checkRes.success && !mergeColErr) {
                let tmpMsg = res.map(el => el.value.res)[0];
                tmpMsg +=
                    "，格式正確，開始匯入。您可以關閉此視窗，資料匯入完成後將寄發通知信至您的信箱。";
                setMsg(tmpMsg);

                // api import
                const apiStr = Api.importData
                    .replace("{graph}", graph)
                    .replace("{sheetName}", newShtName);

                const formData = new FormData();

                // ※formData 要放的是 file 物件不是字串
                processedFile.forEach(file => {
                    formData.append("file", file);
                });
                formData.append("userName", displayName);
                formData.append("userEmail", email);

                // start send to api
                axios.post(apiStr, formData, {
                    headers: { "Content-Type": "multipart/form-data" }
                });
            } else {
                let tmpMsg = res.map(el => el.value.res)[0];
                tmpMsg += mergeColErr;
                tmpMsg +=
                    "格式錯誤，停止匯入。您可以關閉此視窗，資料匯入完成後將寄發通知信至您的信箱。";
                setMsg(tmpMsg);
            }
        }
    }
};

export default acceptFile;

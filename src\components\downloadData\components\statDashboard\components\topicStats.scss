.topicStats {
    padding: 32px 40px;
    border: 1px solid #eeeeee;
    display: flex;
    flex-direction: column;
    row-gap: 16px;
    position: relative;
    h2 {
        font-size: 20px;
        font-weight: 700;
        line-height: 28.96px;
        margin: 0;
    }
    .topicTitle {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .lastUpdatedTime {
            display: flex;
            align-items: center;
            column-gap: 4px;
            p {
                color: #2185d0;
                font-size: 12px;
                font-weight: 400;
                line-height: 17.38px;
                margin: 0;
            }
            button {
                border: none;
                height: 24px;
                width: 24px;
                cursor: pointer;
            }
        }
    }
    .statsSelect {
        width: 200px !important;
        height: 44px !important;
    }
    .topicStatsContainer,
    .topicStatsContainerWithCount {
        display: flex;
        flex-direction: column;
    }
    .topicStatsContainer {
        row-gap: 16px;
    }
    .topicStatsContainerWithCount {
        padding: 24px;
        border: 1px solid #eeeeee;
        row-gap: 24px;
    }
    .statCards,
    .statCardsWithCount {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
    }

    .statCards {
        gap: 16px;
    }

    .statCardsWithCount {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        gap: 33px;
    }

    .statCardsWithCount > div {
        position: relative;
    }

    .statCardsWithCount > div::after {
        content: "";
        position: absolute;
        left: -16px;
        top: 0;
        height: 100%;
        width: 1px;
        background-color: #eeeeee;
    }

    .statCardsWithCount > div:nth-child(4n + 1)::after {
        display: none;
    }
}

import { combineReducers } from "redux";
import fileReducer from "./file/fileReducer";
import fltAnaReducer from "./fltAnalysis/fltAnaReducer";
import accManageReducer from "./accManage/accManageReducer";
import RPReducer from "../components/reportIssue/reportIssueReducer";
import ataiManageReducer from "./ataiManage/ataiManageReducer";

// ex. import newReducer from './newReducer'

// 將要用的reducer都放入下方object
const rootReducer = combineReducers({
    files: fileReducer,
    fltAna: fltAnaReducer,
    accMng: accManageReducer,
    report: RPReducer,
    ataiMng: ataiManageReducer
});

export default rootReducer;

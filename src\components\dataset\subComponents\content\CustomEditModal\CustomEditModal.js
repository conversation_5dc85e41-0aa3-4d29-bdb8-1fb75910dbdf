import React, { useState, useContext, useEffect } from 'react'
import { Button, Modal, Input, Icon, Table, Message, Confirm, Label } from 'semantic-ui-react'
import { StoreContext } from '../../../../../store/StoreProvider'
import { isEmpty } from '../../../../../commons'

const CustomEditModal = ({
    rowId,
    cellId,
    idx,
    defaultValue,
    createState,
    setCallback,
    isDiffValue
}) => {
    const [state] = useContext(StoreContext)
    const [open, setOpen] = useState(false)
    const [editValues, setEditValues] = useState({})
    const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false)
    const [deleteTarget, setDeleteTarget] = useState(null)
    const [deleteWarning, setDeleteWarning] = useState('')

    // 將 defaultValue 轉換為多筆資料格式
    const parseMultipleValues = (value, fullCreateState) => {
        // 優先使用完整的 createState 資料
        if (fullCreateState && typeof fullCreateState === 'object' && fullCreateState !== null) {
            return fullCreateState
        }

        // 如果沒有完整資料，則使用 defaultValue
        if (!value) return {}
        if (typeof value === 'object') return value

        // 如果是字串，嘗試解析為多個值
        const values = {}
        if (typeof value === 'string') {
            values[0] = value
        }
        return values
    }

    useEffect(() => {
        const parsedValues = parseMultipleValues(defaultValue, createState)
        setEditValues(parsedValues)
    }, [defaultValue, createState])

    // 檢查資料是否有關聯
    const checkDataDependencies = async (key, value) => {
        // 這裡需要根據你的業務邏輯來檢查資料關聯
        // 模擬檢查邏輯
        if (value && value.includes('tlp1')) {
            return '此資料與其他表單有關聯，無法直接刪除'
        }
        return null
    }

    const handleValueChange = (key, newValue) => {
        setEditValues(prev => ({
            ...prev,
            [key]: newValue
        }))
    }

    const handleAddNew = () => {
        const newKey = Object.keys(editValues).length
        setEditValues(prev => ({
            ...prev,
            [newKey]: ''
        }))
    }

    const handleDeleteClick = async (key) => {
        const value = editValues[key]
        const warning = await checkDataDependencies(key, value)

        if (warning) {
            setDeleteWarning(warning)
            setDeleteTarget(null)
        } else {
            setDeleteTarget(key)
            setDeleteWarning('')
        }
        setDeleteConfirmOpen(true)
    }

    const handleDeleteConfirm = () => {
        if (deleteTarget !== null) {
            const newValues = { ...editValues }
            delete newValues[deleteTarget]
            setEditValues(newValues)
        }
        setDeleteConfirmOpen(false)
        setDeleteTarget(null)
        setDeleteWarning('')
    }

    const handleSave = () => {
        // 過濾掉空值
        const filteredValues = Object.fromEntries(
            Object.entries(editValues).filter(([key, value]) => value.trim() !== '')
        )
        setCallback(cellId, rowId, { [idx]: filteredValues })
        setOpen(false)
    }

    const handleCancel = () => {
        const parsedValues = parseMultipleValues(defaultValue, createState)
        setEditValues(parsedValues)
        setOpen(false)
    }

    const displayValue = !isEmpty(editValues)
        ? Object.values(editValues).filter(v => v).join(', ')
        : '編輯'

    return (
        <>
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px', flexWrap: 'wrap' }}>
                <Button
                    basic
                    color={isDiffValue ? 'orange' : 'blue'}
                    onClick={() => setOpen(true)}
                    size="small"
                    style={{ margin: '2px' }}
                >
                    <Icon name="edit" />
                    編輯
                </Button>
            </div>

            <Modal open={open} onClose={handleCancel} size="large">
                <Modal.Header>編輯 {cellId}</Modal.Header>
                <Modal.Content scrolling>
                    <Table celled>
                        <Table.Header>
                            <Table.Row>
                                <Table.HeaderCell width={12}>內容</Table.HeaderCell>
                                <Table.HeaderCell width={4}>操作</Table.HeaderCell>
                            </Table.Row>
                        </Table.Header>
                        <Table.Body>
                            {Object.entries(editValues).map(([key, value]) => (
                                <Table.Row key={key}>
                                    <Table.Cell>
                                        <Input
                                            fluid
                                            value={value}
                                            onChange={(e) => handleValueChange(key, e.target.value)}
                                            placeholder="請輸入內容"
                                        />
                                    </Table.Cell>
                                    <Table.Cell textAlign="center">
                                        <Button
                                            icon="trash"
                                            color="red"
                                            size="small"
                                            onClick={() => handleDeleteClick(key)}
                                        />
                                    </Table.Cell>
                                </Table.Row>
                            ))}
                        </Table.Body>
                    </Table>

                    <Button
                        icon="plus"
                        content="新增項目"
                        color="green"
                        onClick={handleAddNew}
                        style={{ marginTop: '10px' }}
                    />
                </Modal.Content>
                <Modal.Actions>
                    <Button onClick={handleCancel}>
                        取消
                    </Button>
                    <Button onClick={handleSave} color="green">
                        <Icon name="save" />
                        儲存
                    </Button>
                </Modal.Actions>
            </Modal>

            <Confirm
                open={deleteConfirmOpen}
                onCancel={() => setDeleteConfirmOpen(false)}
                onConfirm={handleDeleteConfirm}
                header="確認刪除"
                content={
                    deleteWarning ? (
                        <Message negative>
                            <Message.Header>無法刪除</Message.Header>
                            <p>{deleteWarning}</p>
                        </Message>
                    ) : (
                        "確定要刪除這筆資料嗎？"
                    )
                }
                confirmButton={deleteWarning ? null : "刪除"}
                cancelButton="取消"
            />
        </>
    )
}

export default CustomEditModal


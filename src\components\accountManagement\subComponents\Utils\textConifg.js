const textConfig = {
    USERINFO_TITLE: "使用者資訊",
    GROUPINFO_TITLE: "群組資訊",
    MEMBERINFO_TITLE: "成員資訊",
    DATASETAREA_TITLE: "已選資料集",
    DATASETPOOL_TITLE: "未選資料集",
    DATASETPOOL_EMPTY: "無可選資料集",
    // CustomButton
    GROUPINFO_BTN_GROUPADD: "新增群組",
    GROUP<PERSON>FO_BTN_GROUPDEL: "刪除群組",
    GROUPINFO_BTN_MEMADD: "新增成員",
    GROUPINFO_BTN_MEMDEL: "移除成員",
    GROUPINFO_MENUBAR_PAGE: "後台頁面控制",
    GROUPINFO_MENUBAR_DATASET: "資料集控制",
    GROUPINFO_MENUBAR_MEMBERINFO: "成員資訊",
    GROUPINFO_MENUBAR_SHEETINFO: "表單控制",
    GROUPINFO_BTN_CANCEL: "取消編輯",
    GROUPINFO_BTN_SAVE: "儲存資料",
    GROUPINFO_BTN_REMOVESETS: "取消所有已選資料集",
    GROUPINFO_BTN_ADDSETS: "選取所有未選資料集",
    // CustomInput
    GROUPINFO_Input_Title: "群組名稱",
    GROUPINFO_Input_PLH: "請輸入群組名稱",
    // Default Info
    NO_GROUPDATA: "目前無群組建立",
    NO_SUBMENU: "目前無子選單",
    NO_MEMBER: "還沒有成員加入",
    NO_MOREMEMBER: "沒有成員可以再加入",
    NO_SHEETS: "目前無表單",
    // modal message
    SuccessMessage: {
        callerName: "SuccessMessage",
        SuccessTitle: "資料更新完成",
        SuccessBody: "資料更改成功"
    },
    ErrorMessage: {
        callerName: "ErrorMessage",
        ErrorTitle: "發生錯誤",
        ErrorInternal: "呼叫參數少帶",
        ErrorBody: "資料儲存發生異常"
    },
    ColumnCheck: {
        callerName: "ColumnCheck",
        EmptyGroupName: "請輸入群組名稱"
    }
};

export default textConfig;

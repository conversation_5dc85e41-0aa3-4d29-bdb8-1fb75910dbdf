import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Modal, Checkbox, Segment } from "semantic-ui-react";
import { useSelector, useDispatch } from "react-redux";
import { findColLabel, specialColumns } from "../config";
import FltAnaAct from "../../../../../reduxStore/fltAnalysis/FltAnaAct";

const TOGGLE_ALL_KEY = "all";

// 控制顯示欄位
const ColModal = () => {
    // store
    const dispatch = useDispatch();
    const { table } = useSelector(state => state.fltAna);
    // state
    const [modalOpen, setModalOpen] = useState(false);
    // 初始設定
    const [initColumnVisibility, setInitColumnVisibility] = useState({});
    // 即時狀態
    const [columnVisibility, setColumnVisibility] = React.useState({});

    const closeModal = React.useCallback(() => {
        setModalOpen(false);
    }, []);

    // 僅用來控制 read table
    const thisTable = table?.read;

    //
    const handleSave = () => {
        try {
            if (thisTable) {
                // 確認後,使用 react-table 建立的 table 實體來開啟或關閉 column 的顯示
                thisTable.allColumns.forEach(column => {
                    if (column?.id in columnVisibility) {
                        column.getToggleHiddenProps().onChange({
                            target: {
                                checked: columnVisibility[column.id]
                            }
                        });
                    }
                });
                // 傳遞訊息使 <Content /> 的react-table 重新渲染
                dispatch({
                    type: FltAnaAct.setRefreshTableSignal
                });
            }
        } catch (e) {
            //
            console.log("err", e);
        }
        closeModal();
    };

    const handleCancel = () => {
        // 回復原本設定
        setColumnVisibility(initColumnVisibility);
        closeModal();
    };

    const tableReady = !!table?.read;

    React.useEffect(() => {
        try {
            // 使用 react-table 建立的 table 實體,來取得 columnVisibility
            if (thisTable) {
                const visibility = thisTable.allColumns.reduce((acc, col) => {
                    // 非資料表欄位的 column 不允許開啟或關閉
                    const specialCol = specialColumns.find(
                        sc =>
                            sc.accessor.toLowerCase() ===
                            (col?.id || "").toLowerCase()
                    );
                    if (specialCol) return acc;
                    acc[col.id] = col.isVisible;
                    return acc;
                }, {});
                visibility[
                    TOGGLE_ALL_KEY
                ] = thisTable.getToggleHideAllColumnsProps().checked;
                setInitColumnVisibility(visibility);
                setColumnVisibility(visibility);
            }
        } catch (e) {
            console.log("err", e);
        }
    }, [table]);

    const onCheckboxChange = colId => (e, { checked }) => {
        if (colId === TOGGLE_ALL_KEY) {
            const tmpVisibility = Object.keys(columnVisibility).reduce(
                (obj, key) => {
                    // eslint-disable-next-line no-param-reassign
                    obj[key] = checked;
                    return obj;
                },
                {}
            );
            setColumnVisibility(tmpVisibility);
        } else {
            const tmpVisibility = { ...columnVisibility };
            if (!checked) {
                tmpVisibility[TOGGLE_ALL_KEY] = false;
            }
            tmpVisibility[colId] = checked;
            setColumnVisibility(tmpVisibility);
        }
    };

    if (!thisTable) return null;

    return (
        <Modal
            onClose={() => {
                // closeModal();
            }}
            onOpen={() => setModalOpen(true)}
            open={modalOpen}
            size="large"
            trigger={
                <Button icon basic color="blue" size="medium">
                    欄位設定
                </Button>
            }
        >
            <Header>調整欄位顯示</Header>
            <Modal.Content>
                {(tableReady && (
                    <Segment>
                        <div>
                            <Checkbox
                                label="全部欄位"
                                checked={columnVisibility[TOGGLE_ALL_KEY]}
                                onChange={onCheckboxChange(TOGGLE_ALL_KEY)}
                                style={{
                                    marginRight: "10px",
                                    marginBottom: "20px"
                                }}
                            />
                        </div>
                        {Object.keys(columnVisibility).map((key, idx) => {
                            if (key === TOGGLE_ALL_KEY) {
                                // eslint-disable-next-line react/no-array-index-key
                                return <React.Fragment key={idx.toString()} />;
                            }
                            return (
                                <Checkbox
                                    key={idx.toString()}
                                    label={findColLabel(key)}
                                    checked={columnVisibility[key]}
                                    onChange={onCheckboxChange(key)}
                                    style={{
                                        marginRight: "20px",
                                        marginBottom: "12px"
                                    }}
                                />
                            );
                        })}
                    </Segment>
                )) || <div />}
            </Modal.Content>
            <Modal.Actions>
                <div>
                    <Button
                        onClick={() => {
                            handleSave();
                        }}
                        color="blue"
                        // disabled={!allowSave}
                    >
                        確認
                    </Button>
                    <Button onClick={() => handleCancel()} color="yellow">
                        取消
                    </Button>
                </div>
            </Modal.Actions>
        </Modal>
    );
};

export default ColModal;

const storageDb = {};

/**
 * 取得 collection 中的所有 docs
 *
 * @param firestoreDb => firebase.firestore()
 * @param collectionName => firestore collectionName, e.g. "api"
 * @param docName => firestore docName, e.g. "read"
 * @param callback => func.
 */
storageDb.apiColListener = async (
    firestoreDb,
    collectionName,
    docName,
    callback
) => {
    if (!(firestoreDb && collectionName && docName)) {
        const errMsg = {
            error: "parameters required: firestoreDb, collectionName, docName"
        };
        if (callback) return callback(null, errMsg);
        return Promise.reject(errMsg);
    }
    try {
        const colRef = firestoreDb.collection(collectionName);
        const result = await colRef.get();

        if (!result.exists) {
            const errMsg = { error: "No such collection!" };
            if (callback) return callback(null, errMsg);
            return Promise.reject(errMsg);
        }
        const apiObj = {};
        result.docs.forEach(x => {
            apiObj[x.id] = x.data();
        });

        if (callback) return callback(apiObj, null);
        return Promise.resolve(apiObj);
    } catch (err) {
        if (callback) return callback(null, err);
        return Promise.reject(err);
    }
};

/**
 * 取得 collection 中的單一文件
 *
 * @param firestoreDb => firebase.firestore()
 * @param collectionName => firestore collectionName, e.g. "api"
 * @param docName => firestore docName, e.g. "read"
 * @param callback (optional)=> func.
 */
storageDb.apiCollDocListener = async (
    firestoreDb,
    collectionName,
    docName,
    callback
) => {
    if (!(firestoreDb && collectionName && docName)) {
        const errMsg = {
            error:
                "Parameters required in apiCollDocListener(): firestoreDb, collectionName, docName"
        };
        if (callback) return callback(null, errMsg);
        return Promise.reject(errMsg);
    }
    try {
        const docRef = firestoreDb.collection(collectionName).doc(docName);
        const doc = await docRef.get();
        if (!doc.exists) {
            const errMsg = { error: "No such document!" };
            if (callback) return callback(null, errMsg);
            return Promise.reject(errMsg);
        }
        if (callback) return callback(doc, null);
        return Promise.resolve(doc.data());
    } catch (err) {
        if (callback) return callback(null, err);
        return Promise.reject(err);
    }
};

/**
 * 寫入 collection 中的單一文件
 *
 * @param firestoreDb => firebase.firestore()
 * @param collectionName => firestore collectionName, e.g. "api"
 * @param docName => firestore docName, e.g. "read"
 * @param callback (optional)=> func.
 */
storageDb.apiCollDocWriteSync = async (
    firestoreDb,
    collectionName,
    docName,
    dataObj,
    callback
) => {
    if (!(firestoreDb && collectionName && docName && dataObj)) {
        const errMsg = {
            error:
                "Parameters required in apiCollDocListener(): firestoreDb, collectionName, docName"
        };
        if (callback) {
            callback(null, errMsg);
        }
        return;
    }
    const res = firestoreDb
        .collection(collectionName)
        .doc(docName)
        .set(dataObj);
    if (callback) {
        callback(null, res);
    }
};

/**
 * monitor collection 中的單一文件
 *
 * @param firestoreDb => firebase.firestore()
 * @param collectionName => firestore collectionName, e.g. "api"
 * @param callback (optional)=> func.
 */
storageDb.apiColMonitor = async (firestoreDb, collectionName, callback) => {
    const colRef = firestoreDb.collection(collectionName);

    colRef.onSnapshot(
        docSnapshot => {
            // console.log(`Received doc snapshot: ${docSnapshot}`);
            colRef
                .get()
                .then(result => {
                    const apiObj = {};
                    result.docs.forEach(x => {
                        apiObj[x.id] = x.data();
                    });
                    callback(apiObj);
                })
                .catch(error => {
                    // console.log(error);
                    callback(null);
                });
        },
        err => {
            // console.log(err);
            callback(null);
        }
    );
};

export default storageDb;

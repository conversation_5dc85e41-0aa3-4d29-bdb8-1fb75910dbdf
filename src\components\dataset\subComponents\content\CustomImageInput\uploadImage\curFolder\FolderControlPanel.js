import { Button } from "semantic-ui-react";
import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import FileAct from "../../../../../../../reduxStore/file/fileAction";

const FolderControlPanel = () => {
    const dispatchRedux = useDispatch();
    const {
        files: { curFolderFiles, defaultValue, uploadImagesLatest }
    } = useSelector(state => state);

    const [diffValue, setDiffValue] = useState(false);
    const [originalValue] = useState(defaultValue);

    const setDefault = () => {
        // reset it to default(false)
        setDiffValue(false);

        // clear currentValue
        dispatchRedux({
            type: FileAct.SELECT_FILE,
            payload: ""
        });
        dispatchRedux({
            type: FileAct.SET_DEFAULT_VALUE,
            payload: originalValue
        });
    };

    // 刪除連結
    const handleClearLink = () => {
        dispatchRedux({
            type: FileAct.SET_DEFAULT_VALUE,
            payload: ""
        });
    };

    const handlePick = () => {
        if (curFolderFiles.checked.length === 0) {
            return dispatchRedux({
                type: FileAct.PICKER_CUR_FOLDER_MESSAGE,
                payload: {
                    type: "warning",
                    title: "尚未選取檔案",
                    text: ""
                }
            });
        }
        const imgUrlChecked = curFolderFiles.checked.map(dff => dff.value);

        if (imgUrlChecked[0] !== originalValue) {
            // update diff state
            setDiffValue(true);

            // set selected image to currentValue
            return dispatchRedux({
                type: FileAct.SELECT_FILE,
                payload: imgUrlChecked[0]
            });
        }
        return setDefault();
    };

    const handleCropImg = () => {
        const imgUrlChecked = curFolderFiles.checked.map(dff => dff.value);
        dispatchRedux({
            type: FileAct.SELECTED_CROP_IMG,
            payload: imgUrlChecked[0]
        });

        dispatchRedux({
            type: FileAct.OPEN_CROP_IMG_MODAL,
            payload: true
        });
    };

    useEffect(() => {
        if (uploadImagesLatest.length > 0) {
            setDiffValue(true);

            dispatchRedux({
                type: FileAct.SELECT_FILE,
                payload: uploadImagesLatest[0].url
            });
        }
    }, [uploadImagesLatest]);

    const handleSetToDefault = () => setDefault();

    return (
        <div className="control-panel-container">
            <div className="control-panel">
                <Button
                    className="control-panel-btn"
                    color="pink"
                    size="small"
                    disabled={
                        curFolderFiles &&
                        curFolderFiles.checked &&
                        curFolderFiles.checked.length === 0
                    }
                    onClick={handleCropImg}
                    style={{ margin: "0 3px" }}
                >
                    裁切圖片
                </Button>
                <Button
                    onClick={handleClearLink}
                    style={{
                        margin: "0 3px",
                        color: "#000000"
                    }}
                >
                    刪除圖片連結
                </Button>
                <Button
                    className="control-panel-btn"
                    color="blue"
                    size="small"
                    disabled={
                        curFolderFiles &&
                        curFolderFiles.checked &&
                        curFolderFiles.checked.length === 0
                    }
                    onClick={handlePick}
                    style={{ margin: "0 3px" }}
                >
                    選擇圖片
                </Button>
                {diffValue && (
                    <Button
                        className="control-panel-btn"
                        color="teal"
                        size="small"
                        onClick={handleSetToDefault}
                        style={{ margin: "0 3px" }}
                    >
                        取消變更
                    </Button>
                )}
            </div>
        </div>
    );
};

export default FolderControlPanel;

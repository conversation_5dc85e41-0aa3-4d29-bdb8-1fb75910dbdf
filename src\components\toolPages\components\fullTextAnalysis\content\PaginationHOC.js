import React, { useEffect, useState } from "react";
import {
    // Button,
    Grid,
    Input,
    Label,
    Pagination,
    Dropdown
} from "semantic-ui-react";
import PropTypes from "prop-types";
import useDebounce from "../../../../common/hooks/useDebounce";

const HINT_MSG = {
    DEFAULT: "",
    LOADING: "搜尋中...",
    INPUT_NUMBER: "請輸入頁碼",
    INPUT_ILLEGAL: "請輸入區間的頁碼",
    NO_RESULT: "該頁碼無資料，請選取其他頁碼"
};

const PaginationHOC = ({
    totalPages,
    activePage,
    onPageChange,
    pageSize,
    onPageSizeChange,
    loading,
    pageSizeOptions
}) => {
    const [curPage, setCurPage] = useState(activePage || 1);
    const [forwardPage, setForwardPage] = useState(0);
    const [inputHint, setInputHint] = useState("");
    const debForwardPage = useDebounce(forwardPage, 800); // debounce value

    useEffect(() => {
        if (activePage !== curPage) {
            setCurPage(activePage);
            setForwardPage(activePage);
        }
    }, [activePage]);

    useEffect(() => {
        setCurPage(debForwardPage);
    }, [debForwardPage]);

    useEffect(() => {
        onPageChange(curPage);
    }, [curPage]);

    // const goToPageAction = () => {
    //     if (forwardPage === "") {
    //         setInputHint(HINT_MSG.INPUT_NUMBER);
    //         return;
    //     }
    //     setCurPage(forwardPage);
    //     if (parseInt(forwardPage) > totalPages || parseInt(forwardPage) <= 0) {
    //         setInputHint(HINT_MSG.INPUT_ILLEGAL);
    //         return;
    //     }
    //     setInputHint(HINT_MSG.DEFAULT);
    // };

    useEffect(() => {
        if (loading) {
            setInputHint(HINT_MSG.LOADING);
        } else {
            setInputHint(HINT_MSG.DEFAULT);
        }
    }, [loading]);

    // 切換頁碼時，query 該類別實體清單
    const handlePaginationChange = (e, { activePage: _activePage }) => {
        setCurPage(_activePage);
        setForwardPage(_activePage);
    };

    // 當 頁碼 input 變更時
    const onPageInputChange = (event, data) => {
        setForwardPage(data.value);
    };

    return (
        <Grid centered>
            <Grid.Row centered>
                <div
                    style={{
                        display: "flex",
                        flexDirection: "row",
                        justifyContent: "center",
                        alignItems: "center"
                    }}
                >
                    <Pagination
                        onPageChange={handlePaginationChange}
                        activePage={parseInt(curPage, 10)}
                        totalPages={totalPages}
                    />
                    <div style={{ marginLeft: "10px" }}>
                        {totalPages > 0 && (
                            <Label style={{ marginBottom: 0 }}>
                                前往頁碼
                                <Input
                                    type="number"
                                    max={totalPages}
                                    min={1}
                                    style={{
                                        marginLeft: "15px",
                                        width: "100px"
                                    }}
                                    value={forwardPage}
                                    onChange={onPageInputChange}
                                    size="mini"
                                />
                            </Label>
                        )}
                        <span
                            style={{
                                marginLeft: "10px",
                                marginRight: "10px"
                            }}
                        >
                            {inputHint}
                        </span>
                        <Dropdown
                            defaultValue={pageSize}
                            onChange={(e, data) => {
                                onPageSizeChange(Number(data?.value));
                            }}
                            selection
                            compact
                            placeholder="每頁比數"
                            options={pageSizeOptions || []}
                        />
                    </div>
                </div>
            </Grid.Row>
        </Grid>
    );
};

PaginationHOC.defaultProps = {
    totalPages: 0,
    activePage: 0,
    onPageChange: () => null
};

PaginationHOC.propTypes = {
    totalPages: PropTypes.number,
    activePage: PropTypes.number,
    onPageChange: PropTypes.func
};

export default PaginationHOC;

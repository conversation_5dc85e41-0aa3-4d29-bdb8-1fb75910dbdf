import React from "react";
import PropTypes from "prop-types";
import { useDispatch, useSelector } from "react-redux";

// semantic ui
import { Icon, Popup } from "semantic-ui-react";

// components
import ContentModal from "../CustomModal/ContentModal";

// config
import RPAct from "../../reportIssueAction";

function PageBtn({ issueID }) {
    const dispatch = useDispatch();
    const { allData, cntModal } = useSelector(state => state.report);

    const closeCntModal = () => {
        dispatch({
            type: RPAct.SET_RPCNTMODAL,
            payload: false
        });
    };

    // open content modal
    const openCntModal = () => {
        dispatch({
            type: RPAct.SET_RPCNTMODAL,
            payload: true
        });

        const findObj = allData.find(el => el.id === issueID);
        if (findObj) {
            dispatch({
                type: RPAct.SET_RPCNTDATA,
                payload: findObj
            });
        }
    };

    return (
        <>
            <Popup
                content="查看詳細"
                trigger={
                    <Icon
                        name="file alternate"
                        size="large"
                        onClick={openCntModal}
                        style={{ cursor: "pointer" }}
                    />
                }
            />
            <ContentModal openModal={cntModal} onClose={closeCntModal} />
        </>
    );
}

PageBtn.propTypes = {
    /** 問題回報ID */
    issueID: PropTypes.string
};

PageBtn.defaultProps = {
    /** 問題回報ID */
    issueID: PropTypes.string
};

export default PageBtn;

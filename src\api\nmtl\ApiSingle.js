const ApiSingle = {
    // a
    getAcademicDegreelist: "SingleAcademicDegree",
    getAcademicDisciplinelist: "SingleAcademicDiscipline",
    getAreaOfTaiwan: "",
    getArticleList: "",
    getArticleList20: "SingleArticle",
    getAwardList: "SingleAward",
    getAward20: "SingleAwardEvent",
    // c
    getCity: "SingleCity",
    getCollection: "",
    getCollectible: "SingleCollectible",
    getCollectType: "",
    getCopyrightStatus: "",
    getCountry: "SingleCountry",
    // d
    getDynasty: "SingleDynasty",
    getDerivateWork: "",
    getDerivateWork20: "SingleDerivateWork",
    // e
    getEduOrg: "SingleEducationalOrganization",
    getEthnicGroup: "",
    getEvent: "SingleEvent",
    getEducationList20: "SingleEducation",
    getExaminationlist: "SingleExamination",
    // f
    getFoundationList: "SingleFoundation",
    getFoundationType: "",
    getFoundation20: "SingleFoundationEvent",
    getFictionalCharacter: "SingleFictionalCharacter",
    // g
    getGender: "",
    // l
    getLanguage: "",
    getLiteraryGenre: "",
    getLocationList: "SingleLocation",
    // m
    getMSList: "",
    // n
    getNanzi: "SingleNanzi",
    // o
    getOccupationlist: "SingleOccupation",
    getOrganizationList: "SingleOrganization",
    getOrganizationEvent20: "SingleOrganizationEvent",
    // p
    getPersonlist: "SinglePerson",
    getPrintBook: "",
    getProvince: "SingleProvince",
    getPublishing: "",
    getPublicationList: "",
    getPublicationList20: "SinglePublication",
    getPositionList: "SinglePosition",
    getProject: "SingleProject",
    // r
    getRelationOp: "",
    getRelationEventList20: "SingleRelationEvent",
    getReplaceOrg: "SingleOrganization",
    // t
    getTaiwanPeriod: "",
    getTlvmPeriod: "SingleTlvmPeriod",
    getTownship: "SingleTownshipOfTaiwan",
    // s
    getSpecialty: "SingleSpecialty",
    getSpecialty20: "SingleSpecialtyEvent",
    getSocialProcedureList: "SingleSocialProcedure"
};

const getSingleByApi = apiName => ApiSingle[apiName];

export default getSingleByApi;

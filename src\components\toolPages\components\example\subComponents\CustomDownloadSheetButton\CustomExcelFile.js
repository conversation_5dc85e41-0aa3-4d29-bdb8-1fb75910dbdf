import React, { useMemo } from "react";

// excel
import ReactExport from "react-data-export";

const CustomExcelFile = ({ name, header, data }) => {
    console.log("I am CustomExcelFile");
    const ExcelFile = ReactExport.ExcelFile;
    const ExcelSheet = ReactExport.ExcelFile.ExcelSheet;
    const ExcelColumn = ReactExport.ExcelFile.ExcelColumn;
    const MemoziedExcelFile = useMemo(() =>
        <ExcelFile hideElement>
            <ExcelSheet data={data} name={name}>
                {
                    header && header.map(item =>
                        <ExcelColumn
                            key={item.id}
                            label={item.label}
                            value={item.id}
                        />
                    )
                }
            </ExcelSheet>
        </ExcelFile>, [name, header, data]);
    return MemoziedExcelFile;
};

export default CustomExcelFile;

import { useContext, useEffect } from "react";
import { checkServerActive } from "../../api/firebase/cloudFirestore/nmtlDoc";
// store
import { StoreContext } from "../../store/StoreProvider";
import Act from "../../store/actions";

const FirestoreListener = () => {
    const [state, dispatch] = useContext(StoreContext);
    useEffect(() => {
        checkServerActive(dispatch);
    },[dispatch])
};

export default FirestoreListener;

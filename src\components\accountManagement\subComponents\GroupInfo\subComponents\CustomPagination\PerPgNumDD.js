import React, { useEffect, useState } from "react";

// plugins
import { Dropdown } from "semantic-ui-react";
import { useDispatch } from "react-redux";

//
import { isEmpty } from "../../../../../../commons";
import accMngAct from "../../../../../../reduxStore/accManage/accManageAction";

function PerPgNumDd() {
    const dispatch = useDispatch();
    const [options, setOptions] = useState([]);

    useEffect(() => {
        const pageOption = [5, 10, 15];
        const tmp = pageOption.map(value => ({
            key: value,
            text: value,
            value
        }));
        setOptions(tmp);
        dispatch({
            type: accMngAct.SET_PAGENUM,
            payload: pageOption[0]
        });
    }, []);

    const handleChange = (evt, data) => {
        dispatch({
            type: accMngAct.SET_PAGENUM,
            payload: data.value
        });
        dispatch({
            type: accMngAct.SET_INITCURRENTPAGE
        });
    };

    return (
        <React.Fragment>
            <div style={{ marginRight: "0.5rem" }}>
                <p>選擇單頁顯示數量</p>
            </div>
            {!isEmpty(options) && (
                <Dropdown
                    compact
                    selection
                    options={options}
                    onChange={handleChange}
                    defaultValue={options[0].value}
                />
            )}
        </React.Fragment>
    );
}

export default PerPgNumDd;

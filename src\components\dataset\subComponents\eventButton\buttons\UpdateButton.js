import React, { useContext, useState } from 'react';

// ui
import { Button, Modal, Table } from 'semantic-ui-react';

// store
import { StoreContext } from '../../../../../store/StoreProvider';
import Act from '../../../../../store/actions';

// common
import { isEmpty } from '../../../../../commons';
import { convertToGeneric, SHOW_ID } from '../../../../common/sheetCrud/sheetCrudHelper';
import {
  isNotShownInEdit,
  organizeChanged,
  isDataCorrect,
  getLookupTable,
  getReservedNewId,
} from '../../../../common/sheetCrud/utils';

// api
import { updateNmtlData } from '../../../../../api/nmtl';
import Api from '../../../../../api/nmtl/Api';
import { createHistoryEvent } from '../../../../downloadData/components/history/common/common';
import { uuidv4 } from '../../../../../commons/utility';
import { createPeakData } from '../../../datasetConfig';

const UpdateButton = () => {
  const [state, dispatch] = useContext(StoreContext);
  const { user } = state;
  const { content, sheet, mainSubject } = state.data;
  const { header, headerFields } = sheet;
  const { dataset } = mainSubject.selected;
  const { key: sheetName, contentWritePath } = sheet.selected;
  // changed: { 0:{} ,1:{} ,2:{} ,3:{} ,4:{} ,5:{} ,6:{} ,7:{} ,8:{} ,9:{} }
  const { changed, rows } = content;
  const { headerActiveName } = state.common;
  const { displayName } = state.user;
  const [open, setOpen] = useState(false);

  // 記錄在歷史訊息的欄位資訊
  const columns = [headerActiveName, mainSubject.selected.value, sheet.selected.value];

  const showChangedValue = () => {
    // 按了更新的按鈕才會開始動作
    if (!open) {
      return null;
    }
    // changed: { 0:{ hasGender: { 0: {'Female'}, 1: {'other'} } , hasEthnicGroup: { 0: {''} }} ,1:{...}}
    if (changed && !isEmpty(changed) && headerFields) {
      const newRows = {};
      const { contentLookupTable } = getLookupTable(headerFields);

      // 0-9 哪一列需要更新，紀錄被更新列的 index
      // record updated rows ids
      const changedIds = changed
        ? Object.keys(changed).filter((k) => Object.keys(changed[k]).length > 0)
        : [];

      if (!isEmpty(changedIds)) {
        changedIds.forEach((rowId) => {
          //
          if (!Object.keys(changed[rowId])) {
            return;
          }

          // 該列所有資料取出
          newRows[rowId] = JSON.parse(JSON.stringify(rows[rowId]));

          // 將有更新的資料先更新
          Object.keys(changed[rowId]).forEach((hp) => {
            newRows[rowId][hp] = changed[rowId][hp];
          });

          // 下拉式選單: key 值找到對應的 label
          Object.keys(newRows[rowId]).forEach((hp) => {
            //
            let turnToLabel = {};
            // hp: has_Publisher, srcId, label_publication
            Object.keys(newRows[rowId][hp]).forEach((key) => {
              //
              const label = newRows[rowId][hp];

              // label: "PUB51876"
              if (typeof label === 'string') {
                turnToLabel = label;
              }
              // label: {0: "PER808", 1: "PER123456"}
              else if (typeof label === 'object') {
                if (SHOW_ID.indexOf(hp) === -1 && contentLookupTable[label[key]]) {
                  // contentLookupTable: { "PER808": ["余光中@zh", "YU Kwang-chung@PER"] }
                  contentLookupTable[label[key]].forEach((item, index) => {
                    turnToLabel[`${key}-${index}`] = item || label[key];
                  });
                } else {
                  turnToLabel[key] = label[key];
                }
              }
            });
            newRows[rowId][hp] = turnToLabel;
          });
        });
      }
      return newRows;
    }
    return null;
  };

  const handleUpdate = async () => {
    if (isEmpty(content) || isEmpty(contentWritePath) || isEmpty(dataset) || isEmpty(sheetName)) {
      return;
    }

    // merge rows and changed
    if (isEmpty(changed) || isEmpty(rows)) {
      return;
    }

    // organize changed
    const { newRows, updatedIds } = organizeChanged(rows, changed);

    // data check
    const [isCorrect, errMsg] = isDataCorrect(newRows, updatedIds);
    if (!isCorrect) {
      // alert message
      dispatch({
        type: Act.DATA_MESSAGE,
        payload: {
          title: `資料錯誤：${errMsg}`,
          error: 1,
          renderSignal: `update-${new Date().getTime()}`,
        },
      });
      // refresh changed when sheet changed
      dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });
      // refresh checked when sheet changed
      dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });
      // refresh created when sheet changed
      dispatch({ type: Act.DATA_CONTENT_ROW_CREATED_CLEAN });
      setOpen(false);
      return;
    }
    const pekId = await getReservedNewId('PeakMono');

    let historyMsg = '';
    // promise list
    const promises = updatedIds.map((id) => {
      const apiUrl = Api.getGeneric;
      // const apiUrl = "http://localhost:3000/zh-tw/generic/2.0";
      const [entrySrc, entryDst] = convertToGeneric(
        rows[id],
        newRows[id],
        contentWritePath,
        dataset,
        false,
      );
      // return;
      // Create peak connection
      if (
        (isEmpty(entrySrc?.value?.peak) || entrySrc?.value?.peak[0] === 'false') &&
        !isEmpty(entryDst?.value?.peak) &&
        entryDst?.value?.peak[0] === 'true'
      ) {
        createPeakData(user, entryDst?.srcId, pekId);
      }

      // Remove peak connection
      if (
        !isEmpty(entrySrc?.value?.peak) &&
        entrySrc?.value?.peak[0] === 'true' &&
        entryDst?.value?.peak[0] !== 'true'
      ) {
        if (entryDst?.value?.hasPeakMono) {
          // eslint-disable-next-line no-unused-expressions
          // entryDst?.value?.hasPeakMono.map(i =>
          //     deletePeakData(user, i, pekId)
          // );
          delete entryDst?.value?.hasPeakMono;
        }
      }
      historyMsg = `${JSON.stringify(entrySrc)}\n變動後：\n${JSON.stringify(entryDst)}`;
      // newOptions.filter(
      //     o => Object.values(createState.value).indexOf(o.id) > -1
      // );

      return updateNmtlData(user, apiUrl, dataset, sheetName, entrySrc, entryDst);
    });

    // get all result
    Promise.allSettled(promises)
      .then((results) => {
        // count
        let successCount = 0;
        let errorCount = 0;
        results.forEach((res) => {
          if (res.value) {
            successCount += 1;
          } else {
            errorCount += 1;
          }
        });
        const message = {
          title: 'Update',
          success: successCount,
          error: errorCount,
          renderSignal: `update-${new Date().getTime()}`,
        };
        // alert message
        dispatch({
          type: Act.DATA_MESSAGE,
          payload: message,
        });
        // 歷史紀錄，更新成功

        createHistoryEvent(displayName, '更新', `${columns.join('/')}：${historyMsg}`);
      })
      .catch((error) => {
        // 歷史紀錄，更新失敗
        createHistoryEvent(displayName, '更新失敗', `${columns.join('/')}：${historyMsg}`, error);
      });

    // clean checked
    dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });

    // clean edit record after updated
    dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });

    // close
    setOpen(false);
  };

  const tableBody = (tableChanged, itemChanged) => {
    if (!tableChanged) {
      return (
        <Table.Row>
          <Table.Cell warning>
            <p>No Data to Changed</p>
          </Table.Cell>
        </Table.Row>
      );
    }

    return Object.keys(tableChanged).map((rowIdx) => (
      <Table.Row className="update-table-row" warning key={`updated-button-row-${rowIdx}`}>
        {header &&
          header.map((sh, shIdx) => {
            if (isNotShownInEdit(sh.id)) {
              return null;
            }

            const cellValue = [];
            // row[sh.id]
            // Object { 0: "3", 1: "1", 2: "丁允恭@zh", 3: "11111", 4: "2222", 5: "22222222", 6: "12312312" }
            if (tableChanged[rowIdx][sh.id]) {
              Object.values(tableChanged[rowIdx][sh.id]).forEach((v) => {
                cellValue.push(<p key={uuidv4()}>{v}</p>);
              });
            }
            return (
              <Table.Cell
                className="update-table-cell"
                key={`updated-button-row-${rowIdx}-${shIdx}-${sh}`}
                negative={
                  itemChanged &&
                  itemChanged[rowIdx] &&
                  itemChanged[rowIdx][sh.id] &&
                  Object.keys(itemChanged[rowIdx][sh.id]).length > 0
                }
              >
                {cellValue}
              </Table.Cell>
            );
          })}
      </Table.Row>
    ));
  };

  return (
    <Modal
      open={open}
      onClose={() => setOpen(false)}
      onOpen={() => setOpen(true)}
      trigger={
        <Button
          color="orange"
          floated="right"
          disabled={
            // changed: {0: {label_Person: {0: "1", 1:"2"}, label_Source:{0: "3", 1: "4"}}, 1:{...}}
            Object.values(changed).filter((rowVal) => Object.keys(rowVal).length > 0).length === 0
          }
        >
          更新
        </Button>
      }
    >
      <Modal.Header>更新確認</Modal.Header>

      <Modal.Content image scrolling>
        <Modal.Description>
          <Table className="update-table" celled selectable>
            <Table.Header className="update-table-header">
              <Table.Row className="update-table-row">
                {// handle table title(sheet)
                !isEmpty(changed) &&
                  header &&
                  header.map((sh) => {
                    if (isNotShownInEdit(sh.id)) {
                      return null;
                    }
                    return (
                      <Table.HeaderCell singleLine key={sh.id}>
                        {sh.label}
                      </Table.HeaderCell>
                    );
                  })}
              </Table.Row>
            </Table.Header>
            <Table.Body>{!isEmpty(changed) && tableBody(showChangedValue(), changed)}</Table.Body>
          </Table>
        </Modal.Description>
      </Modal.Content>

      <Modal.Actions>
        <Button onClick={handleUpdate} color="green">
          確認
        </Button>
        <Button onClick={() => setOpen(false)} color="red">
          取消
        </Button>
      </Modal.Actions>
    </Modal>
  );
};

export default UpdateButton;

import React, { useContext } from 'react';
import CustomImageInput from '../content/CustomImageInput/CustomImageInput';
import CustomFileUpload from '../content/CustomFileUpload/CustomFileUpload';
import CustomMultiDropdown from './CustomMultiDropdown';
import CustomMultiDropdownCreatable from './CustomMultiDropdownCreatable';
import CustomSingleDropdown from './CustomSingleDropdown';
import CustomSingleInput from './CustomSingleInput';
import CustomSingleLabelReadOnly from './CustomSingleLabelReadOnly';
import CustomRichInfo from './CustomRichInfo';
import CustomMarkdown from './CustomMarkdown';
import { StoreContext } from '../../../../store/StoreProvider';
import {
  multiProperties,
  MULTI_RESTRICT,
  IMAGE_URL,
  FULLTEXT_URL,
  MD_EDITOR,
  RICH_INFO,
  DISABLED_CHANGE,
  SHOW_ID,
  CREATE_ID,
  idParams,
  SINGLE_CREATABLE,
  forCreateDropdown,
  EDIT_IN_MODAL,
} from '../../../common/sheetCrud/sheetCrudHelper';
import { getApiByField } from '../../../../api/nmtl/ApiField';
import { uuidv4 } from '../../../../commons/utility';
import CustomSingleDropdownCreatable from './CustomSingleDropdownCreatable';
import { isObject } from '../../../websiteSetting/commons';
import CustomSingleDropdownForCreate from './CustomSingleDropdownForCreate';
import CustomEditModal from '../content/CustomEditModal/CustomEditModal';

const MultiOptionDefault = {
  isLoading: false,
  options: [],
  value: undefined,
  input: '',
};

// ctIdx -1 為 create
const TableCellValue = ({
  actHeader,
  defaultValue,
  ctIdx,
  shIdx,
  activePage = '0',
  createState,
  setCallback,
  unintegrated = false,
  // isCreateNew = false
}) => {
  // eslint-disable-next-line no-unused-vars
  const [state] = useContext(StoreContext);

  // get dataset(mainSubject) and sheet
  const { sheet, mainSubject } = state.data;
  const { key: sheetName } = sheet.selected;
  const { headerFields } = sheet;
  const { dataset } = mainSubject.selected;

  const getIsDiffVal = (defVal = '', curVal = '') => {
    if (!defVal && !curVal) return false;

    const oldVal = defVal ?? '';
    const newVal = curVal ?? '';

    return oldVal !== newVal;
  };

  if (Object.keys(headerFields).length === 0) {
    return null;
  }

  // newCreateState: Object { 0: "fdsafdsafdsa", 1: "fdsafdsafsadfdsafsdfdsa@zh" }
  // default 為單行的文字，斷行視為字串的一部份。
  // 全部都以 array 型式
  const newCreateState = createState || defaultValue || { 0: '' };
  const newSingleCreateState = createState || {
    isLoading: false,
    options: [],
    value: defaultValue,
    input: '',
  };
  const newMultiCreateState = createState || defaultValue || MultiOptionDefault;

  if ([IMAGE_URL].indexOf(actHeader) > -1) {
    return Object.keys(newCreateState).map((idx) => (
      <CustomImageInput
        key={`${sheetName}-image-input-${ctIdx}-${shIdx}-${activePage}-${idx}`}
        rowId={ctIdx}
        cellId={actHeader}
        idx={idx}
        defaultValue={defaultValue?.[idx] || ''}
        createState={newCreateState}
        setCallback={setCallback}
        isDiffValue={getIsDiffVal(defaultValue?.[idx], newCreateState?.[idx])}
      />
    ));
  }

  // 全文上傳
  if ([FULLTEXT_URL].indexOf(actHeader) > -1) {
    return Object.keys(newCreateState).map((idx) => (
      <CustomFileUpload
        key={`${sheetName}-image-input-${ctIdx}-${shIdx}-${activePage}-${idx}`}
        rowId={ctIdx}
        cellId={actHeader}
        idx={idx}
        defaultValue={defaultValue?.[idx] || ''}
        createState={newCreateState}
        setCallback={setCallback}
        isDiffValue={getIsDiffVal(defaultValue?.[idx], newCreateState?.[idx])}
      />
    ));
  }

  // SHOW_ID 欄位無法新增
  if (MULTI_RESTRICT.indexOf(actHeader) > -1 || SHOW_ID.indexOf(actHeader) > -1) {
    // hasEthnicGroup,hasLiteraryAreaIn
    return (
      <CustomMultiDropdown
        key={`${sheetName}-multiDropdown-${ctIdx}-${shIdx}-${activePage}-${actHeader}`}
        rowId={ctIdx}
        cellId={actHeader}
        // idx={idx}
        createState={newMultiCreateState}
        setCallback={setCallback}
        isShowId={SHOW_ID.indexOf(actHeader) > -1}
        isDiffValue={getIsDiffVal(defaultValue?.[0], newCreateState?.value?.[0])}
      />
    );
  }
  // return 出生地,辭世地點
  if (multiProperties.indexOf(actHeader) > -1) {
    return (
      <CustomMultiDropdownCreatable
        key={`${sheetName}-multiDropdown-${ctIdx}-${shIdx}-${activePage}-${actHeader}`}
        rowId={ctIdx}
        // idx={idx}
        cellId={actHeader}
        createState={newMultiCreateState}
        setCallback={setCallback}
        // isShowId={SHOW_ID.indexOf(actHeader) > -1}
        isDiffValue={getIsDiffVal(defaultValue?.[0], newCreateState?.value?.[0])}
      />
    );
  }

  // 虛擬的 id, 可以新增的 id
  // 由 CreateButton 新增
  if (idParams.indexOf(actHeader) > -1) {
    // 新增時出現下拉選單
    if (forCreateDropdown.indexOf(actHeader) > -1) {
      return (
        <CustomSingleDropdownCreatable
          key={`${sheetName}-singleDropdownCreatable-${ctIdx}-${shIdx}-${activePage}-${uuidv4()}`}
          rowId={ctIdx}
          cellId={actHeader}
          idx={CREATE_ID}
          createState={newSingleCreateState}
          setCallback={setCallback}
          isDiffValue={getIsDiffVal(defaultValue?.[0], newCreateState?.value?.[0])}
        />
      );
    }

    return (
      <CustomSingleDropdownForCreate
        key={`${sheetName}-singleDropdownCreatable-${ctIdx}-${shIdx}-${activePage}-${uuidv4()}`}
        rowId={ctIdx}
        cellId={actHeader}
        idx={CREATE_ID}
        createState={newSingleCreateState}
        setCallback={setCallback}
        isDiffValue={getIsDiffVal(defaultValue?.[0], newCreateState?.value?.[0])}
      />
    );
  }

  // 由 Component 新增
  if (SINGLE_CREATABLE.indexOf(actHeader) > -1) {
    if (
      newSingleCreateState.value &&
      Object.keys(newSingleCreateState.value).length > 1 &&
      dataset === 'authority'
    ) {
      return Object.keys(newSingleCreateState.value).map((idx) => (
        <CustomSingleDropdownCreatable
          key={`${sheetName}-singleDropdown-${ctIdx}-${shIdx}-${activePage}-${uuidv4()}`}
          rowId={ctIdx}
          cellId={actHeader}
          idx={idx}
          createState={newSingleCreateState}
          setCallback={setCallback}
          isCreateNew
          isDiffValue={getIsDiffVal(defaultValue?.[idx], newCreateState?.value?.[idx])}
        />
      ));
    }
    // 初始值
    return (
      <CustomSingleDropdownCreatable
        key={`${sheetName}-singleDropdown-${ctIdx}-${shIdx}-${activePage}-${uuidv4()}`}
        rowId={ctIdx}
        cellId={actHeader}
        idx={0}
        createState={newSingleCreateState}
        setCallback={setCallback}
        isCreateNew
        isDiffValue={getIsDiffVal(defaultValue?.[0], newCreateState?.value?.[0])}
      />
    );
  }

  const apiName = getApiByField(actHeader);
  if (apiName && Object.keys(headerFields).indexOf(apiName) > -1) {
    if (newSingleCreateState.value) {
      return Object.keys(newSingleCreateState.value).map((idx) => (
        <CustomSingleDropdown
          key={`${sheetName}-singleDropdown-${ctIdx}-${shIdx}-${activePage}-${idx}`}
          rowId={ctIdx}
          cellId={actHeader}
          idx={idx}
          createState={newSingleCreateState}
          setCallback={setCallback}
          isDiffValue={getIsDiffVal(defaultValue?.[idx], newCreateState?.value?.[idx])}
        />
      ));
    }
    // 初始值
    return (
      <CustomSingleDropdown
        key={`${sheetName}-singleDropdown-${ctIdx}-${shIdx}-${activePage}-${uuidv4()}`}
        rowId={ctIdx}
        cellId={actHeader}
        idx={0}
        createState={newSingleCreateState}
        setCallback={setCallback}
        isDiffValue={getIsDiffVal(defaultValue?.[0], newCreateState?.value?.[0])}
      />
    );
  }

  if (MD_EDITOR.indexOf(actHeader) > -1) {
    return Object.keys(newCreateState).map((idx) => (
      <CustomMarkdown
        fluid
        transparent
        /* key 必須是唯一的如果重複將會被視為相同 element
                   也會造成不進行任何更動，也就是數值不會改變的 issue
                   怎麼設定都可以就是不可以相同 */
        key={`${sheetName}-markDown-${ctIdx}-${shIdx}-${activePage}-${idx}`}
        rowId={ctIdx}
        cellId={actHeader}
        idx={idx}
        defaultValue={defaultValue?.[idx] || ''}
        createState={newCreateState}
        setCallback={setCallback}
        useEditor
        readMode={unintegrated}
        isDiffValue={getIsDiffVal(defaultValue?.[idx], newCreateState?.[idx])}
      />
    ));
  }

  if (RICH_INFO.indexOf(actHeader) > -1) {
    return Object.keys(newCreateState).map((idx) => (
      <CustomRichInfo
        fluid
        transparent
        /* key 必須是唯一的如果重複將會被視為相同 element
                       也會造成不進行任何更動，也就是數值不會改變的 issue
                       怎麼設定都可以就是不可以相同 */
        key={`${sheetName}-richInfo-${ctIdx}-${shIdx}-${activePage}-${idx}`}
        rowId={ctIdx}
        cellId={actHeader}
        idx={idx}
        defaultValue={defaultValue?.[idx] || ''}
        createState={newCreateState}
        setCallback={setCallback}
        isDiffValue={getIsDiffVal(defaultValue?.[idx], newCreateState?.[idx])}
      />
    ));
  }
  // 另開modal編輯(hasTlvmPeriod)
  if (EDIT_IN_MODAL.indexOf(actHeader) > -1) {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
        {/* 顯示所有 CHIP */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '4px', flexWrap: 'wrap' }}>
          {Object.keys(newCreateState).map((idx) => {
            const value = newCreateState[idx];
            if (!value) return null;
            return (
              <span
                key={`chip-${idx}`}
                style={{
                  backgroundColor: '#e3f2fd',
                  color: '#1976d2',
                  padding: '4px 8px',
                  borderRadius: '12px',
                  fontSize: '12px',
                  border: '1px solid #bbdefb'
                }}
              >
                {value}
              </span>
            );
          })}
        </div>

        {/* 編輯按鈕固定在下方 */}
        <div>
          <CustomEditModal
            key={`${sheetName}-editModal-${ctIdx}-${shIdx}-${activePage}-${actHeader}`}
            rowId={ctIdx}
            cellId={actHeader}
            idx={0} // 固定使用 0，因為我們會傳入完整的 createState
            defaultValue={defaultValue}
            createState={newCreateState}
            setCallback={setCallback}
            isDiffValue={Object.keys(newCreateState).some(idx =>
              getIsDiffVal(defaultValue?.[idx], newCreateState?.[idx])
            )}
          />
        </div>
      </div>
    );
  }
  // return srcId
  if (DISABLED_CHANGE.indexOf(actHeader) > -1) {
    if (isObject(defaultValue)) {
      return Object.values(defaultValue).map((val) => (
        <CustomSingleLabelReadOnly
          fluid="true"
          transparent="true"
          /* key 必須是唯一的如果重複將會被視為相同 element
                       也會造成不進行任何更動，也就是數值不會改變的 issue
                       怎麼設定都可以就是不可以相同 */
          key={`${sheetName}-readonly-${actHeader}-${ctIdx}-${shIdx}-${activePage}-${val}`}
          // rowId={ctIdx}
          cellId={actHeader}
          defaultValue={val}
          createState={newCreateState}
        />
      ));
    }
    return (
      <CustomSingleLabelReadOnly
        fluid
        transparent
        /* key 必須是唯一的如果重複將會被視為相同 element
                       也會造成不進行任何更動，也就是數值不會改變的 issue
                       怎麼設定都可以就是不可以相同 */
        key={`${sheetName}-readonly-${actHeader}-${ctIdx}-${shIdx}-${activePage}`}
        // rowId={ctIdx}
        cellId={actHeader}
        defaultValue={defaultValue}
        createState={newCreateState}
      />
    );
  }
  // console.log(actHeader, newCreateState);
  return Object.keys(newCreateState).map((idx) => (
    <CustomSingleInput
      fluid
      transparent
      /* key 必須是唯一的如果重複將會被視為相同 element
                   也會造成不進行任何更動，也就是數值不會改變的 issue
                   怎麼設定都可以就是不可以相同 */
      key={`${sheetName}-singleInput-${ctIdx}-${shIdx}-${activePage}-${idx}-${actHeader}`}
      rowId={ctIdx}
      cellId={actHeader}
      idx={idx}
      defaultValue={defaultValue?.[idx] || ''}
      createState={newCreateState}
      setCallback={setCallback}
      isDiffValue={getIsDiffVal(defaultValue?.[idx], newCreateState?.[idx])}
    />
  ));
};

export default TableCellValue;

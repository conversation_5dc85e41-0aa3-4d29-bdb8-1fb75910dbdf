import React from "react";
import { useSelector } from "react-redux";
import MenuBar from "./components/MenuBar";
import textConfig from "./textConifg";
import "./AtaiDataManagement.scss";

const AtaiDataManagement = () => {
    const state = useSelector(tmpState => tmpState.ataiMng);
    const { activeItemAtai } = state;

    return (
        <>
            <div className="ataiDataManagementGroup">
                <p className="ataiDataManagementTitle">
                    {textConfig.ataiDataManagementTitle}
                </p>
                <div className="ataiDataManagement">
                    <div className="leftArea">
                        <MenuBar />
                    </div>
                    <div className="rightArea">
                        <activeItemAtai.component />
                    </div>
                </div>
            </div>
        </>
    );
};

export default AtaiDataManagement;

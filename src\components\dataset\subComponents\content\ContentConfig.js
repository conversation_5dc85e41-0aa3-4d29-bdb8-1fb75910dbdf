import { audio } from "systeminformation";

const sortStroke = ["srcId", "isTranslationBookOf"];
const EDIT_ROW = {
    id: "edit",
    label: "編輯",
    seq: "20"
};

const PERSON_HEADERS = [
    {
        checked: true,
        label: "作者 (中文版)",
        id: "label_Person_Zh"
    },
    {
        checked: true,
        label: "作者 (外文版)",
        id: "label_Person_En"
    }
];

const INTRODUCTION_HEADERS = [
    [
        { checked: false, id: "introduction_Zh", label: "簡介 (中文版)" },
        { checked: false, id: "introduction_En", label: "簡介 (外文版)" }
    ],
    [
        { checked: false, id: "introduction_Zh", label: "摘要 (中文版)" },
        { checked: false, id: "introduction_En", label: "摘要 (外文版)" }
    ]
];

const HAS_AUTHOR_HEADERS = [
    {
        checked: true,
        label: "作者 (中文版)",
        id: "hasAuthor_Zh"
    },
    {
        checked: true,
        label: "作者 (外文版)",
        id: "hasAuthor_En"
    }
];

const AUTHOR_NAME_HEADERS = [
    {
        checked: false,
        label: "作者顯示名稱 (中文版)",
        id: "authorName_Zh"
    },
    {
        checked: false,
        label: "作者顯示名稱 (外文版)",
        id: "authorName_En"
    }
];

const HAS_TRANSLATOR_HEADERS = [
    {
        checked: false,
        label: "譯者 (中文版)",
        id: "hasTranslator_Zh"
    },
    {
        checked: false,
        label: "譯者 (外文版)",
        id: "hasTranslator_En"
    }
];

const TRANSLATOR_NAME_HEADERS = [
    {
        checked: false,
        label: "譯者顯示名稱 (中文版)",
        id: "translatorName_Zh"
    },
    {
        checked: false,
        label: "譯者顯示名稱 (外文版)",
        id: "translatorName_En"
    }
];

const HAS_EDITOR_HEADERS = [
    {
        checked: false,
        label: "編輯者 (中文版)",
        id: "hasEditor_Zh"
    },
    {
        checked: false,
        label: "編輯者 (外文版)",
        id: "hasEditor_En"
    }
];

const HAS_PUBLISHER_HEADERS = [
    {
        checked: false,
        label: "出版者 (中文版)",
        id: "hasPublisher_Zh"
    },
    {
        checked: false,
        label: "出版者 (外文版)",
        id: "hasPublisher_En"
    }
];

const REFERENCES_HEADERS = [
    {
        checked: false,
        label: "資料來源 (中文版)",
        id: "references_Zh"
    },
    {
        checked: false,
        label: "資料來源 (外文版)",
        id: "references_En"
    }
];

const sortedMethod = { ASC: "ascending", DESC: "descending" };

const editIconStyle = {
    width: "1.3em",
    height: "1.3em",
    cursor: "pointer"
};

const sortedStyle = {
    cursor: "default"
};

const widthStyle = {
    maxWidth: "150px",
    wordWrap: "break-word"
};

/* 外譯房>著作> 件層級 */
const hiddenHeightStyle = {
    overflowY: "auto",
    maxHeight: "50px"
};

const basicInfoShowHeaders = [
    "PersonID",
    "srcId",
    "label_Person",
    "hasBirthDate",
    "literary",
    "graph"
];

export {
    EDIT_ROW,
    sortedMethod,
    editIconStyle,
    sortedStyle,
    widthStyle,
    sortStroke,
    hiddenHeightStyle,
    PERSON_HEADERS,
    INTRODUCTION_HEADERS,
    HAS_AUTHOR_HEADERS,
    AUTHOR_NAME_HEADERS,
    HAS_TRANSLATOR_HEADERS,
    TRANSLATOR_NAME_HEADERS,
    HAS_EDITOR_HEADERS,
    HAS_PUBLISHER_HEADERS,
    REFERENCES_HEADERS,
    basicInfoShowHeaders
};

import {
    checkLangTag,
    AllowedLang
} from "../../../../../../../api/nmtl/ApiField";
import { splitTag } from "../config/config";
// import checkStr from "./checkStr";

const checkLangTagCol = (cell, propLabel) => {
    let tmpResStr = "";
    if (cell.value) {
        if (typeof cell.value === "string") {
            const allValue = cell.value.split(splitTag);
            // 檢查語系tag
            const checkTag = allValue.some(elStr => checkLangTag(elStr));
            if (!checkTag) {
                tmpResStr += `${cell.address}, [${cell.value}], 欄位:${propLabel}，語系有誤，請使用下列清單提供的tag，帶在每行資料後面[${AllowedLang}]。\n`;
            }
        } else if (Object.hasOwn(cell.value, "hyperlink")) {
            const allValue = cell.value.text.split(splitTag);
            // 檢查語系tag
            const checkTag = allValue.some(elStr => checkLangTag(elStr));
            if (!checkTag) {
                tmpResStr += `${cell.address}, [${cell.value.text}], 欄位:${propLabel}，語系有誤，請使用下列清單提供的tag，帶在每行資料後面[${AllowedLang}]。\n`;
            }
        }
        // else {
        //     tmpResStr += checkStr(cell, propLabel);
        // }
    }

    return tmpResStr;
};

export default checkLangTagCol;

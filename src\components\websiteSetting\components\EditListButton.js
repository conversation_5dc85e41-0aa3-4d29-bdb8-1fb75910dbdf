import React, { useContext } from "react";
import { Button } from "semantic-ui-react";
import { StoreContext } from "../../../store/StoreProvider";
import Act from "../../../store/actions";

function EditListButton() {
    const [state, dispatch] = useContext(StoreContext);
    const handleClick = () => {
        dispatch({
            type: Act.SET_ISEDITEDDISABLE,
            payload: false
        });
    };

    return (
        <Button primary onClick={handleClick}>編輯</Button>
    );
}

export default EditListButton;
import React, { useContext, useEffect, useState } from "react";
import { <PERSON>ton, Modal, Radio, Table } from "semantic-ui-react";
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";
import { createConfig, GLOBAL_CREATE_PATH } from "../createConfig";
import { isEmpty } from "../../../../../commons";

function NextStepModal({
    editId,
    createState,
    onClick,
    open,
    setOpen,
    isLoading,
    setIsLoading,
    equalValue,
    action,
    setAction,
    handleGetContent
}) {
    const [state, dispatch] = useContext(StoreContext);
    const { mainSubject } = state.data;
    const { dataset } = mainSubject.selected;
    const [checkItem, setCheckItem] = useState(null);
    const [itemExist, setItemExist] = useState(null);

    const modalStillCreate = async () => {
        setAction(createConfig.createButton);
        setOpen(false);
        dispatch({
            type: Act.DATA_INFO_MESSAGE_CLEAN
        });
    };
    const modalConfirm = async () => {
        setIsLoading(true);
        await handleGetContent(checkItem.id, checkItem.ds);
        setIsLoading(false);
        setAction(createConfig.createButton);
        setOpen(false);
        dispatch({
            type: Act.DATA_INFO_MESSAGE_CLEAN
        });
    };
    const modalCancel = () => {
        setCheckItem(null);
        setItemExist(false);
        setOpen(false);
        dispatch({
            type: Act.DATA_INFO_MESSAGE_CLEAN
        });
    };

    useEffect(() => {
        if (equalValue) {
            const allDataSet = equalValue.map(item => item?.Graph);

            if (allDataSet.indexOf(dataset) > -1) {
                setItemExist(true);
            }
        }
    }, [dataset, equalValue]);

    const modalTableBody = () =>
        equalValue.map((el, idx) => (
            <Table.Row key={`ModalTable-${idx}`}>
                <Table.Cell>
                    <Radio
                        value={el.id}
                        checked={checkItem?.index === idx}
                        onChange={(event, data) => {
                            if (data.checked) {
                                setCheckItem({
                                    id: el.id,
                                    ds: el.Graph,
                                    index: idx
                                });
                            } else {
                                setCheckItem(undefined);
                            }
                        }}
                    />
                </Table.Cell>
                <Table.Cell>{el.id}</Table.Cell>
                <Table.Cell>{el.label}</Table.Cell>
                <Table.Cell>{el.Graph}</Table.Cell>
            </Table.Row>
        ));

    const ModalExistContent = (
        <React.Fragment>
            <Modal.Header>名稱已存在資料集</Modal.Header>

            <Modal.Content>
                <Modal.Description>
                    名稱已存在，確定要新增嗎？
                </Modal.Description>
            </Modal.Content>

            <Modal.Actions>
                <Button onClick={modalCancel} color="black">
                    關閉
                </Button>
                <Button onClick={modalStillCreate} color="red">
                    確認新增
                </Button>
            </Modal.Actions>
        </React.Fragment>
    );

    const ModalShowTable = (
        <React.Fragment>
            <Modal.Header>名稱確認</Modal.Header>

            <Modal.Content image scrolling>
                <Modal.Description>
                    <span>
                        名稱已存在，請選擇ID匯入當前資料集，或是取消，重新修改名稱。
                    </span>
                    <Table celled striped>
                        <Table.Header>
                            <Table.Row>
                                {!isEmpty(equalValue) && (
                                    <React.Fragment>
                                        <Table.HeaderCell collapsing />
                                        <Table.HeaderCell>ID</Table.HeaderCell>
                                        <Table.HeaderCell>
                                            名稱
                                        </Table.HeaderCell>
                                        <Table.HeaderCell>
                                            資料集
                                        </Table.HeaderCell>
                                    </React.Fragment>
                                )}
                            </Table.Row>
                        </Table.Header>
                        <Table.Body>
                            {!isEmpty(equalValue) && modalTableBody()}
                        </Table.Body>
                    </Table>
                </Modal.Description>
            </Modal.Content>

            <Modal.Actions>
                <Button onClick={modalCancel} color="black">
                    取消
                </Button>
                <Button
                    disabled={!checkItem}
                    onClick={modalConfirm}
                    loading={isLoading}
                    positive
                >
                    確定
                </Button>
            </Modal.Actions>
        </React.Fragment>
    );

    return (
        <Modal
            open={open}
            trigger={
                <Button
                    disabled={!createState || isEmpty(createState)}
                    color={editId !== GLOBAL_CREATE_PATH ? "green" : "teal"}
                    floated="right"
                    onClick={onClick}
                >
                    {action}
                </Button>
            }
        >
            {itemExist ? ModalExistContent : ModalShowTable}
        </Modal>
    );
}

export default NextStepModal;

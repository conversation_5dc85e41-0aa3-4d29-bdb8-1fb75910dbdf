import React, { useContext, useEffect, useState } from "react";

// ui
import {
    <PERSON><PERSON>,
    Header,
    Image,
    Loader,
    Segment,
    Table
} from "semantic-ui-react";

// custom
import EditButton from "../buttons/EditButton";
import DeleteButton from "../buttons/DeleteButton";

// png
import shortParagraph from "../../../../../images/short-paragraph.png";

// store
import { StoreContext } from "../../../../../store/StoreProvider";

// api
import { getUsers } from "../../../../../api/firebase/realtimeDatabase";
import role from "../../../../../App-role";

// common
import { isEmpty } from "../../../../../commons";

const UserTable = () => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { account } = state;
    const { renderSignal } = account;
    const [users, setUsers] = useState(undefined);

    const handleGetUser = async () => {
        const usersData = await getUsers();
        const filteredUser = usersData.filter(
            user =>
                user.role !== role.anonymous &&
                Object.keys(role).includes(user.role)
        );
        setUsers(filteredUser);
    };

    useEffect(() => {
        handleGetUser();
    }, [renderSignal]);
    return (
        <Table celled fixed singleLine>
            <Table.Header>
                <Table.Row>
                    <Table.HeaderCell>姓名</Table.HeaderCell>
                    <Table.HeaderCell>電子郵件</Table.HeaderCell>
                    <Table.HeaderCell>註冊日期</Table.HeaderCell>
                    <Table.HeaderCell>註冊來源</Table.HeaderCell>
                    <Table.HeaderCell textAlign="center">操作</Table.HeaderCell>
                </Table.Row>
            </Table.Header>

            <Table.Body>
                {users ? (
                    isEmpty(users) ? (
                        <Table.Row key="user-table-empty">
                            <Table.Cell
                                colSpan="5"
                                textAlign="center"
                                verticalAlign="middle"
                                style={{ height: "100px" }}
                            >
                                <Header>目前尚未有使用者</Header>
                            </Table.Cell>
                        </Table.Row>
                    ) : (
                        users.map(user => {
                            const {
                                displayName,
                                creationTime,
                                email,
                                providerId,
                                role: userRole
                            } = user;
                            return (
                                <Table.Row
                                    key={`user-table-${user.uid}`}
                                    error={!userRole}
                                >
                                    <Table.Cell error={!displayName}>
                                        {displayName || "unKnown"}
                                        {!userRole && " (不合法)"}
                                    </Table.Cell>
                                    <Table.Cell error={!email}>
                                        {email}
                                    </Table.Cell>
                                    <Table.Cell error={!creationTime}>
                                        {creationTime}
                                    </Table.Cell>
                                    <Table.Cell error={!providerId}>
                                        {providerId}
                                    </Table.Cell>
                                    <Table.Cell>
                                        <EditButton user={user} />
                                        <DeleteButton user={user} />
                                    </Table.Cell>
                                </Table.Row>
                            );
                        })
                    )
                ) : (
                    <Table.Row>
                        <Table.Cell colSpan="5">
                            <Segment>
                                <Dimmer active inverted>
                                    <Loader size="large">Loading</Loader>
                                </Dimmer>
                                <Image fluid src={shortParagraph} />
                            </Segment>
                        </Table.Cell>
                    </Table.Row>
                )}
            </Table.Body>

            <Table.Footer>
                <Table.Row>
                    <Table.HeaderCell colSpan="5" />
                </Table.Row>
            </Table.Footer>
        </Table>
    );
};

export default UserTable;

import React, { useState, useCallback } from "react";

// ui
import { Button } from "semantic-ui-react";

import CustomExcelFile from "../../../dataset/subComponents/commonComp/CustomExcelFile";

// firebase api
import { getSheetHeader } from "../../../../api/firebase/cloudFirestore";

// nmtl api
import { getApiByAllField } from "../../../../api/nmtl/ApiField";

// common
import { isEmpty } from "../../../../commons";

// nmtl api
import { readNmtlData } from "../../../../api/nmtl";
import Api from "../../../../api/nmtl/Api";
import { convertToExport } from "../../../../commons/convertToExport";
import {createHistoryEvent} from "../history/common/common";

const getYMDNow = () => {
    const dt = new Date();
    return `${dt.getFullYear()}_${dt.getMonth() + 1}_${dt.getDate()}`;
};

const CustomDownloadAuthority = ({
    limit,
    offset,
    prefix,
    datasetName,
    sheetName,
    getContentApi,
    displayName,
    columns
}) => {
    // force refresh CustomExcelFile component for download excel
    const [refreshKey, setRefreshKey] = useState(0);
    // isLoading
    const [isLoading, setIsLoading] = useState(false);
    // excel header
    const [excelHeader, setExcelHeader] = useState(undefined);
    // excel data
    const [excelData, setExcelData] = useState(undefined);

    // get full api url
    // e.g. http://localhost:3000/dl_award/1.0?dataset=abo&limit=10&offset=10&ids=
    // useCallback 保存方法，避免 useEffect 喧染時被產生新的 element
    const getReadUrl = useCallback(() => {
        if (datasetName && getContentApi) {
            // combine url and parameter
            const urlPath = `${Api.getBaseUrl}/${getContentApi}`;
            const parameter = `dataset=${datasetName}&limit=${limit}&offset=${offset}`;
            return `${urlPath}?${parameter}`;
        }
        return undefined;
    }, [datasetName, getContentApi]);

    // get sheet header
    const handleDownload = async () => {
        if (datasetName && sheetName) {
            // set loading status
            setIsLoading(true);
            // get sheet header
            let header = await getSheetHeader(sheetName);
            // imageURL 不需要匯出
            header = header.filter(h => h.id !== "imageURL");
            //
            setExcelHeader(header);
            // console.log("header:", header);
            const headerLookupTable = {};
            header &&
                header.forEach(row => {
                    headerLookupTable[row.id] = row.label;
                });

            // add extra ID
            headerLookupTable.graph = "資料集";
            // console.log("headerLookupTable:", headerLookupTable);
            const headerIds = header && header.map(item => item.id);
            // create Api and Field mapping table for easy to find
            const apiList = headerIds
                .map(field => ({
                    field,
                    apiName: getApiByAllField(field)
                }))
                .filter(
                    item =>
                        // The getApiByField will return undefined when it not found from config(api:nmtl:ApiField.js)
                        item.apiName.length > 0
                );

            // unique apiName to avoids repeated queries
            const uniqueApiNameList = [];
            apiList.forEach(a => {
                a.apiName.forEach(api => {
                    if (uniqueApiNameList.indexOf(api) < 0) {
                        uniqueApiNameList.push(api);
                    }
                });
            });

            // query all uniqueApiNameList
            const promises = uniqueApiNameList.map(apiName =>
                readNmtlData(Api[apiName])
            );

            // get results from promises
            const results = await Promise.allSettled(promises).then(res => res);
            // create cache result dictionary
            const apiResults = {};
            // To map the api and result in a dictionary as lookup table
            results.forEach((res, idx) => {
                const { status, value } = res;
                // if Promise.allSettled has succeeded or failed, it will return 'fulfilled' or 'rejected'
                if (status === "fulfilled" && value && !isEmpty(value.data)) {
                    apiResults[uniqueApiNameList[idx]] = value.data;
                }
            });
            const contentLookupTable = {};
            // callback result to apiList by apiResults
            Object.keys(apiResults).forEach(api => {
                const resArr = apiResults[api];
                resArr.forEach(item => {
                    const { id, label } = item;
                    contentLookupTable[id] = label;
                });
            });
            // get content
            if (datasetName && sheetName && getContentApi) {
                // get full api url for fetch
                const apiUrl = getReadUrl();
                if (apiUrl) {
                    // fetch data
                    const { data: contentData } = await readNmtlData(apiUrl);

                    const transformedContent = convertToExport({
                        contentData,
                        contentLookupTable
                    });
                    if (
                        !isEmpty(transformedContent) &&
                        !isEmpty(datasetName) &&
                        !isEmpty(header)
                    ) {
                        setExcelData(transformedContent);
                        setRefreshKey(refreshKey + 1);
                        createHistoryEvent(displayName, "下載", columns.join("/"));
                    } else {
                        createHistoryEvent(displayName, "下載失敗", columns.join("/"));
                    }
                } else {
                    createHistoryEvent(displayName, "下載失敗", columns.join("/"));
                }
            } else {
                createHistoryEvent(displayName, "下載失敗", columns.join("/"));
            }
        }
        // set loading status
        setIsLoading(false);
    };

    const customStyle = {
        marginBottom: "1em",
        marginRight: "1em",
        minWidth: "200px"
    };

    const filename = `${prefix}_${getYMDNow()}`;
    return (
        <div>
            <Button
                loading={isLoading}
                color="orange"
                onClick={handleDownload}
                style={customStyle}
            >
                下載 Excel ({prefix})
            </Button>
            {!isEmpty(datasetName) &&
                !isEmpty(excelHeader) &&
                !isEmpty(excelData) &&
                refreshKey !== 0 && (
                <CustomExcelFile
                    key={refreshKey}
                    filename={filename}
                    name={sheetName}
                    data={excelData}
                    header={excelHeader}
                />
            )}
        </div>
    );
};

export default CustomDownloadAuthority;

import React, { useContext, useState, useMemo, createRef } from 'react';

// ui
import { Input, Modal, TextArea, Button, Ref } from 'semantic-ui-react';

// store
import { StoreContext } from '../../../../store/StoreProvider';
import Act from '../../../../store/actions';

import { getLangTag } from '../../../common/sheetCrud/utils';

const CustomRichInfo = ({
  rowId,
  cellId,
  idx,
  defaultValue,
  createState,
  setCallback,
  isDiffValue = false,
  ...rest
}) => {
  const [, dispatch] = useContext(StoreContext);
  const [open, setOpen] = useState(false);

  const curValue = createRef();

  const setValue = (value) => {
    const toCreateState = {
      ...createState,
      // Component 不能帶 null，所以只好定義兩個
      ...{ [idx]: value === '' ? '' : value },
    };
    const cellValue = {
      ...createState,
      // null 代表要刪除，不能帶 ""，因為還是會被存進去
      ...{ [idx]: value === '' ? null : value },
    };

    // console.log(value, toCreateState, cellValue);
    if (value !== defaultValue) {
      // 更新 changed data，最後更新資料用
      dispatch({
        type: Act.DATA_CONTENT_ROW_CHANGED,
        payload: {
          rowId,
          cellId,
          idx,
          cellValue,
        },
      });
    } else {
      // 如果有值又沒有改變，不做修正
      if (value && value.length > 0) {
        const unchangedValue = {
          ...createState,
          ...{ [idx]: defaultValue },
        };
        dispatch({
          type: Act.DATA_CONTENT_ROW_NO_CHANGED,
          payload: {
            rowId,
            cellId,
            idx,
            cellValue: unchangedValue,
          },
        });
        return;
      }

      // dispatch
      dispatch({
        type: Act.DATA_CONTENT_ROW_NO_CHANGED,
        payload: {
          rowId,
          cellId,
          idx,
        },
      });
    }

    // 顯示結果
    setCallback(cellId, rowId, idx, toCreateState);
  };

  const handleAddClick = () => {
    // -1 為新增 item
    // FIXME: 原本 newState 以新 item {value: ""} 傳入，但不知為何 createState 的值沒有改變
    const newState = Object.assign({}, createState);
    newState[Object.keys(createState).length] = '';
    setCallback(cellId, rowId, -1, Object.assign({}, newState));
  };

  const handleOnMouseDown = () => {
    setOpen(true);
  };

  const handleOnCancel = () => {
    curValue.current.value = defaultValue;
    setOpen(false);
  };

  const handleOnConfirm = () => {
    setOpen(false);
    setValue(curValue.current.value);
  };

  return useMemo(
    () => (
      <React.Fragment>
        <Input
          {...rest}
          // keep input value
          label={{
            basic: true,
            icon: 'add',
            content: getLangTag(createState[idx]),
            size: 'mini',
            onClick: handleAddClick,
          }}
          labelPosition="right"
          value={createState[idx]}
          // this is not an error, it just used to change the input background color
          error={isDiffValue}
          onMouseDown={handleOnMouseDown}
        />
        <Modal
          onClose={() => setOpen(false)}
          onOpen={() => setOpen(true)}
          open={open}
          closeOnDimmerClick={false}
        >
          <Modal.Content>
            <Ref innerRef={curValue}>
              <TextArea
                defaultValue={defaultValue}
                rows={window.innerHeight / 40}
                ref={curValue}
                style={{
                  minHeight: '50%',
                  width: '100%',
                  fontSize: '1.5rem',
                }}
              />
            </Ref>
          </Modal.Content>
          <Modal.Actions>
            <Button color="black" onClick={handleOnCancel}>
              取消
            </Button>
            <Button color="green" onClick={handleOnConfirm}>
              確認
            </Button>
          </Modal.Actions>
        </Modal>
      </React.Fragment>
    ),
    [cellId, createState, open, curValue],
  );
};

export default CustomRichInfo;

import React, { useRef } from "react";

// scss
import "./CustomMarkdownEditor.scss";

// plugins
import ReactQuill from "react-quill";
import htmlEditButton from "quill-html-edit-button";
import ResizeModule from "@botom/quill-resize-module";
import "react-quill/dist/quill.snow.css";

//
import { convert2HtmlEntities } from "../../../commons/htmlEntities";
import imageHandler from "./imageHandler";

const initToolbarOptions = (onImgVideo, editorRef, mainSubject) => {
    const resConfig = {
        container: [
            [{ header: [1, 2, 3, 4, 5, 6, false] }],
            ["bold", "italic", "underline", "strike", "blockquote", "link"], // toggled buttons
            // [{ header: 1 }, { header: 2 }], // custom button values
            [{ list: "ordered" }, { list: "bullet" }],
            [{ script: "sub" }, { script: "super" }], // superscript/subscript

            // [{ size: ["small", false, "large", "huge"] }], // custom dropdown

            // [{ color: [] }, { background: [] }], // dropdown with defaults from theme
            // [{ font: [11, 12, 13] }],
            // [{ direction: "rtl" }, { align: [] }, { indent: "-1" }, { indent: "+1" }], // text direction/align/outdent/indent
            // ["image", "video"],
            ["clean"] // remove formatting button
        ]
    };

    // 打開image、video功能
    if (onImgVideo) {
        resConfig.container = [...resConfig.container, ["image", "video"]];
        resConfig.handlers = {
            image: () => imageHandler(editorRef, mainSubject)
        };
    }

    return resConfig;
};

// const initToolbarOptions = [
//     ["bold", "italic", "underline", "strike"], // toggled buttons
//     ["blockquote", "code-block"],
//
//     [{ header: 1 }, { header: 2 }], // custom button values
//     [{ list: "ordered" }, { list: "bullet" }],
//     [{ script: "sub" }, { script: "super" }], // superscript/subscript
//     [{ indent: "-1" }, { indent: "+1" }], // outdent/indent
//     [{ direction: "rtl" }], // text direction
//
//     [{ size: ["small", false, "large", "huge"] }], // custom dropdown
//     [{ header: [1, 2, 3, 4, 5, 6, false] }],
//
//     [{ color: [] }, { background: [] }], // dropdown with defaults from theme
//     [{ font: [] }],
//     [{ align: [] }],
//
//     ["clean"] // remove formatting button
// ];

function CustomMarkdownEditor({
    curValue,
    setValue,
    style = null,
    onImgVideo = false,
    mainSubject = {}
}) {
    const editorRef = useRef(null);
    // embedded ReactQuill 第三方套件
    ReactQuill.Quill.register({
        // 顯示html樣式
        "modules/htmlEditButton": htmlEditButton,
        // resize image、video
        "modules/resize": ResizeModule
    });

    return (
        <div className="CustomMarkdownEditor">
            <ReactQuill
                ref={editorRef}
                id="CustomMarkdownEditorRQ"
                theme="snow"
                value={convert2HtmlEntities(curValue)}
                onChange={setValue}
                modules={{
                    htmlEditButton: {
                        buttonHTML: "HTML",
                        prependSelector: "div#CustomMarkdownEditorRQ"
                    },
                    toolbar: initToolbarOptions(onImgVideo, editorRef, mainSubject),
                    resize: {
                        locale: {},
                        toolbar: {
                            alingTools: false
                        }
                    }
                }}
                style={style || { minHeight: "200px" }}
            />
        </div>
    );
}

export default CustomMarkdownEditor;

import React, { useContext, useState, useEffect } from 'react';

// react
import { Link } from 'react-router-dom';

// ui
import { Icon, Card, Container, Divider, Message, Button } from 'semantic-ui-react';

// config
import home from '../../App-home';

// store
import { StoreContext } from '../../store/StoreProvider';

// common
import { isEmpty } from '../../commons';
import { filterRoute } from '../../commons/filterGroup';
import role from '../../App-role';

const Home = () => {
    // eslint-disable-next-line no-unused-vars
    const [state, _] = useContext(StoreContext);
    const { user } = state;
    const { groupInfo } = state.data;
    const safeHome = home.filter((page) => filterRoute(user, page, groupInfo));
    const [loading, setLoading] = useState(true);
    //
    useEffect(() => {
        setTimeout(() => {
            setLoading(false);
        }, 1000);
    }, []);

    const checkLoginWithoutGroup = () =>
        user?.role && user?.role !== role.anonymous && isEmpty(groupInfo.page);
    //
    return (
        <React.Fragment>
            <br />
            <br />
            <br />
            <Container textAlign="center" className="Home">
                <h1>文學好臺誌後台</h1>
                <Divider />
                <Card.Group itemsPerRow={6} centered={isEmpty(safeHome)}>
                    {/* eslint-disable-next-line no-nested-ternary */}
                    {isEmpty(safeHome) ? (
                        loading || checkLoginWithoutGroup() ? (
                            <React.Fragment>
                                <br />
                                <Message warning header="正在載入中..." content="請稍後" />
                            </React.Fragment>
                        ) : (
                            <React.Fragment>
                                <br />
                                <Message
                                    warning
                                    header="您必須登入後臺平台"
                                    list={[
                                        '登入後請等待管理人員審核',
                                        '通過審核後台平即可進行操作',
                                    ]}
                                    content={
                                        <Link to="/SignIn">
                                            <br />
                                            <Button color="orange">登入</Button>
                                        </Link>
                                    }
                                />
                                <br />
                            </React.Fragment>
                        )
                    ) : (
                        safeHome.map((page) => (
                            <Card key={`home-card-${page.id}`}>
                                <Card.Content textAlign="center">
                                    <Icon
                                        bordered
                                        inverted
                                        color="teal"
                                        name={page.icon}
                                        size="big"
                                    />
                                </Card.Content>
                                <Card.Content textAlign="center">
                                    <Link to={page.href}>{page.label}</Link>
                                </Card.Content>
                            </Card>
                        ))
                    )}
                </Card.Group>
            </Container>
        </React.Fragment>
    );
};

export default Home;

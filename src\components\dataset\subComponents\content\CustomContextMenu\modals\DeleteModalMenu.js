import React, { useContext } from "react";
// ui
import { Button, Modal, Table } from "semantic-ui-react";
// common
import { isEmpty } from "../../../../../../commons";
import { convertToGenericSingle } from "../../../../../common/sheetCrud/sheetCrudHelper";
// store
import { StoreContext } from "../../../../../../store/StoreProvider";
import { deleteNmtlData } from "../../../../../../api/nmtl";
import Act from "../../../../../../store/actions";
import Api from "../../../../../../api/nmtl/Api";

const DeleteModalMenu = ({ open, setOpen, rowData }) => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { content, sheet, mainSubject } = state.data;
    const { header } = sheet;
    const { dataset } = mainSubject.selected;
    const { key: sheetName, contentWritePath } = sheet.selected;

    // get full api url
    // e.g. http://localhost:3000/basicInfo/1.0
    // useCallback 保存方法，避免 useEffect 喧染時被產生新的 element
    const getFullUrl = () => `${Api.getBaseUrl}/generic/2.0`;

    const handleDelete = async () => {
        // console.log('I am EventButton:handleUpdate');

        if (
            !isEmpty(content) &&
            !isEmpty(contentWritePath) &&
            !isEmpty(dataset) &&
            !isEmpty(sheetName) &&
            !isEmpty(rowData)
        ) {
            const apiUrl = getFullUrl();
            const entry = convertToGenericSingle(
                rowData,
                dataset,
                contentWritePath,
                {}
            );

            const result = await deleteNmtlData(
                user,
                apiUrl,
                dataset,
                sheetName,
                entry
            );

            let successCount = 0;
            let errorCount = 0;

            if (result) {
                successCount += 1;
            } else {
                errorCount += 1;
            }

            const message = {
                title: "Delete",
                success: successCount,
                error: errorCount,
                renderSignal: `delete-${new Date().getTime()}`
            };

            // alert message
            dispatch({
                type: Act.DATA_MESSAGE,
                payload: message
            });

            // clean edit record after updated
            dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });

            // close
            setOpen(false);
        }
    };

    return (
        <Modal
            open={open}
            onClose={() => setOpen(false)}
            onOpen={() => setOpen(true)}
        >
            <Modal.Header>刪除內容</Modal.Header>

            <Modal.Content image scrolling>
                <Modal.Description>
                    {
                        <Table celled compact selectable>
                            <Table.Header>
                                <Table.Row>
                                    {// handle table title(sheet)
                                        !isEmpty(rowData) &&
                                        header &&
                                        header.map(sh => (
                                            <Table.HeaderCell
                                                singleLine
                                                key={sh.id}
                                            >
                                                {sh.label}
                                            </Table.HeaderCell>
                                        ))}
                                </Table.Row>
                            </Table.Header>
                            <Table.Body>
                                {!isEmpty(rowData) ? (
                                    <Table.Row error key="delete-button-row-">
                                        {header &&
                                            header.map((sh, shIdx) => {
                                                const cellValue = rowData[sh.id]
                                                    ? Object.values(
                                                        rowData[sh.id]
                                                    ).join("\n")
                                                    : "";
                                                return (
                                                    <Table.Cell
                                                        key={`delete-button-row-${shIdx}-${cellValue}`}
                                                    >
                                                        {cellValue}
                                                    </Table.Cell>
                                                );
                                            })}
                                    </Table.Row>
                                ) : (
                                    <Table.Row>
                                        <Table.Cell warning>
                                            <p>No Data to Delete</p>
                                        </Table.Cell>
                                    </Table.Row>
                                )}
                            </Table.Body>
                        </Table>
                    }
                </Modal.Description>
            </Modal.Content>

            <Modal.Actions>
                <Button onClick={handleDelete} color="green">
                    確認
                </Button>
                <Button onClick={() => setOpen(false)} color="red">
                    取消
                </Button>
            </Modal.Actions>
        </Modal>
    );
};

export default DeleteModalMenu;

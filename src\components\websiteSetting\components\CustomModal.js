import React, { useContext, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>con, Modal } from "semantic-ui-react";
import { StoreContext } from "../../../store/StoreProvider";
import Act from "../../../store/actions";
import { saveData } from "../commons";

function CustomModal(props) {
    const { setOpenSettingResultModal, setShowMessage, setLoading } = props;
    const [state, dispatch] = useContext(StoreContext);
    const { headerActiveName } = state.common;
    const {
        openModal,
        modalMessage,
        originData,
        menuActiveItem,
        updatedData,
        selectOption
    } = state.websiteSetting;
    const { displayName } = state.user;
    const columns = [headerActiveName, menuActiveItem.name];

    return (
        <Modal
            basic
            onClose={() => {
                // dispatch({
                //     type: Act.SET_OPENMODAL,
                //     payload: false
                // });
            }}
            open={openModal}
            size="small"
        >
            {selectOption !== null ? (
                <Header>
                    最新[
                    {
                        updatedData.find(
                            element => element.id === menuActiveItem.key
                        )[selectOption].name
                    }
                    ]資料已經被其他管理者更改
                </Header>
            ) : (
                <Header>此頁面最新資料已經被其他管理者更改</Header>
            )}
            {/* <Modal.Content> */}
            {/*    <h3>最新資料</h3> */}
            {/*    <p>{modalMessage}</p> */}
            {/* </Modal.Content> */}
            <Modal.Actions>
                <Button
                    basic
                    color="red"
                    inverted
                    onClick={() => {
                        dispatch({
                            type: Act.SET_OPENMODAL,
                            payload: false
                        });
                        setLoading(false);
                    }}
                >
                    <Icon name="remove" /> 取消
                </Button>
                <Button
                    color="green"
                    inverted
                    onClick={() => {
                        dispatch({
                            type: Act.SET_OPENMODAL,
                            payload: false
                        });
                        saveData(
                            menuActiveItem,
                            updatedData,
                            setShowMessage,
                            setOpenSettingResultModal,
                            setLoading,
                            originData,
                            displayName,
                            columns
                        );
                        dispatch({
                            type: Act.SET_ORIGINDATA,
                            payload: updatedData
                        });
                    }}
                >
                    <Icon name="checkmark" /> 更新
                </Button>
            </Modal.Actions>
        </Modal>
    );
}

export default CustomModal;

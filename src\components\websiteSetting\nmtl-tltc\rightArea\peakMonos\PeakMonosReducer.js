import { createStore } from "redux";
import PeakAct from "./PeakMonosAction";

const initialState = {
    updatePeakBrief: {},
    cateIsEdited: false,
    peakIsEdited: false,
    peakInfo: [],
    newPeakInfo: [],
    editingPeakId: "",
    isModalOpen: false,
    isDelModalOpen: false,
    peakChapter: [],
    newPeakChapter: [],
    editingPchId: "",
    chapterListLength: "",
    oldPeakChapterContent: [],
    isAddingChapter: false,
    isEditedChapterList: false,
    isDeletedChapter: false,
    deletingChapter: [],

    // chapter dropdown
    isEditDropdownChip: false,
    editingDropdownChipId: "",
    editingDropdownOptionList: [],
    editingDropdownAddValue: "",
    editingDropdownType: "",
    editingDropdownClassType: "",
    editingDropdownIsSaving: false,
    editingDropdownCreateValue: [],

    // chapter quill
    introductionZhForQuill: "",
    introductionEnForQuill: "",
    judgesCommentaryZhForQuill: "",
    judgesCommentaryEnForQuill: "",
    tlaPersonIntroZhForQuill: "",
    tlaPersonIntroEnForQuill: ""
};

function PeakMonosReducer(state = initialState, action) {
    switch (action.type) {
        case PeakAct.SET_UPDATEPEAKBRIEF:
            return { ...state, updatePeakBrief: action.payload };
        case PeakAct.SET_ISEDITEDCATE:
            return { ...state, cateIsEdited: action.payload };
        case PeakAct.SET_ISEDITEDPEAK:
            return { ...state, peakIsEdited: action.payload };
        case PeakAct.SET_PEAKINFO:
            return { ...state, peakInfo: action.payload };
        case PeakAct.SET_NEWPEAKINFO:
            return { ...state, newPeakInfo: action.payload };
        case PeakAct.SET_EDITINGPEAKID:
            return { ...state, editingPeakId: action.payload };
        case PeakAct.SET_ISMODALOPEN:
            return { ...state, isModalOpen: action.payload };
        case PeakAct.SET_ISDELMODALOPEN:
            return { ...state, isDelModalOpen: action.payload };
        case PeakAct.SET_PEAKCHAPTER:
            return { ...state, peakChapter: action.payload };
        case PeakAct.SET_NEWPEAKCHAPTER:
            return { ...state, newPeakChapter: action.payload };
        case PeakAct.SET_EDITINGPCHID:
            return { ...state, editingPchId: action.payload };
        case PeakAct.SET_CHAPTERLISTLENGTH:
            return { ...state, chapterListLength: action.payload };
        case PeakAct.SET_OLDPEAKCHAPTER:
            return { ...state, oldPeakChapterContent: action.payload };
        case PeakAct.SET_ISADDINGCHAPTER:
            return { ...state, isAddingChapter: action.payload };
        case PeakAct.SET_ISEDITEDCHAPTERLIST:
            return { ...state, isEditedChapterList: action.payload };
        case PeakAct.SET_ISDELETEDCHAPTER:
            return { ...state, isDeletedChapter: action.payload };
        case PeakAct.SET_DELETINGCHAPTER:
            return { ...state, deletingChapter: action.payload };

        // chapter dropdown
        case PeakAct.SET_ISEDITDROPDOWNCHIP:
            return { ...state, isEditDropdownChip: action.payload };
        case PeakAct.SET_EDITDROPDOWNCHIPID:
            return { ...state, editingDropdownChipId: action.payload };
        case PeakAct.SET_EDITDROPDOWNOPTIONLIST:
            return { ...state, editingDropdownOptionList: action.payload };
        case PeakAct.SET_EDITDROPDOWNADDVALUE:
            return { ...state, editingDropdownAddValue: action.payload };
        case PeakAct.SET_EDITDROPDOWNTYPE:
            return { ...state, editingDropdownType: action.payload };
        case PeakAct.SET_EDITDROPDOWNCLASSTYPE:
            return { ...state, editingDropdownClassType: action.payload };
        case PeakAct.SET_EDITDROPDOWNISSAVING:
            return { ...state, editingDropdownIsSaving: action.payload };
        case PeakAct.SET_EDITDROPDOWNCREATEVALUE:
            return { ...state, editingDropdownCreateValue: action.payload };

        // chapter quill
        case PeakAct.SET_INTRODUCTIONZHFORQUILL:
            return { ...state, introductionZhForQuill: action.payload };
        case PeakAct.SET_INTRODUCTIONENFORQUILL:
            return { ...state, introductionEnForQuill: action.payload };
        case PeakAct.SET_JUDGESCOMMENTARYZHFORQUILL:
            return { ...state, judgesCommentaryZhForQuill: action.payload };
        case PeakAct.SET_JUDGESCOMMENTARYENFORQUILL:
            return { ...state, judgesCommentaryEnForQuill: action.payload };
        case PeakAct.SET_TLAPERSONINTROZHFORQUILL:
            return { ...state, tlaPersonIntroZhForQuill: action.payload };
        case PeakAct.SET_TLAPERSONINTROENFORQUILL:
            return { ...state, tlaPersonIntroEnForQuill: action.payload };
        default:
            return state;
    }
}

const peakMonosStore = createStore(PeakMonosReducer);

export default peakMonosStore;

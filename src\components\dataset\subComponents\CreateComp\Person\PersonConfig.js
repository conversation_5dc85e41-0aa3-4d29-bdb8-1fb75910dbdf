import CustomSegmentReadOnly from "../../commonComp/specialTableComp/CustomSegmentReadOnly";
import CustomDoubleInput from "../../commonComp/specialTableComp/CustomDoubleInput";
import CustomSingleInput from "../../commonComp/specialTableComp/CustomSingleInput";
import CustomSingleInputCreatable from "../../commonComp/specialTableComp/CustomSingleInputCreatable";
import CustomDoubleTextArea from "../../commonComp/specialTableComp/CustomDoubleTextArea";
import CustomPersonImage from "./CustomPersonImage";
import CustomSingleDropdown from "../../commonComp/specialTableComp/CustomSingleDropdown";
import CustomMultiDropdown from "../../commonComp/specialTableComp/CustomMulitDropdown";

const PersonConfig = {
    topArea: [
        [
            {
                header: "srcId",
                comp: CustomSegmentReadOnly,
                defaultValue: "資料新增完成後系統將自動產生ID",
                compProp: {
                    disabled: true
                }
            },
            {
                header: "label_Person",
                comp: CustomDoubleInput,
                compProp: {
                    titleFirst: "權威中文",
                    titleSecond: "權威外文"
                },
                errorMsg:
                    "不可為空，且欄位請填入正確後綴，如：@zh, @en, @Ja ..."
            }
        ]
    ],
    bottomArea: [
        [
            {
                header: "birthName",
                comp: CustomSingleInput
            },
            {
                header: "penName",
                comp: CustomSingleInputCreatable
            }
        ],
        [
            {
                header: "otherName",
                colSpan: 3,
                comp: CustomSingleInputCreatable
            }
        ],
        [
            {
                header: "hasTranslationLanguage",
                comp: CustomMultiDropdown
            },
            {
                header: "authorOrTranslator",
                comp: CustomSegmentReadOnly,
                defaultValue: "",
                compProp: {
                    disabled: true
                }
            }
        ],
        [
            {
                header: "introduction",
                colSpan: 3,
                comp: CustomDoubleTextArea,
                compProp: {
                    subTitle: `顯示簡介`
                }
            }
        ],
        [
            {
                header: "externalLinks",
                colSpan: 3,
                comp: CustomSingleInput
            }
        ],
        [
            {
                header: "imageURL_hasURL",
                subHeader: "建議上傳尺寸建議為：852px * 852px",
                comp: CustomPersonImage,
                compProp: {
                    checkBoxLabel: "顯示人物照"
                }
            },
            {
                header: "hasCopyrightStatus_hasURL",
                comp: CustomSingleDropdown
            }
        ],
        [
            {
                header: "comment",
                colSpan: 3,
                comp: CustomSingleInput
            }
        ]
    ]
};

export default PersonConfig;

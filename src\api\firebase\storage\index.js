// get image from storage
import firebase from "firebase";

const STORAGE_PATH = {
    DEFAULT_ROOT_NAME: "root",
    imageRoot: "image"
};

// 取得可以下載的圖片連結(path + token)
// like: https://firebasestorage.googleapis.com/v0/b/hkbdb-web.appspot.com/o/image%2Fbook-library-with-open-textbook.jpg?alt=media&amp;token=5651d384-c34d-4621-82fe-e78d85d7b330
const getImage = path => {
    const storage = firebase.storage();
    const storageRef = storage.ref(path);

    return storageRef
        .getDownloadURL()
        .then(urlToken =>
            // Insert url into an <img> tag to "download"
            Promise.resolve({ path, url: urlToken })
        )
        .catch(error => {
            // A full list of error codes is available at
            // https://firebase.google.com/docs/storage/web/handle-errors
            switch (error.code) {
                case "storage/object-not-found":
                    // File doesn't exist
                    return Promise.reject(error);
                // break;
                case "storage/unauthorized":
                    // User doesn't have permission to access the object
                    return Promise.reject(error);
                // break;
                case "storage/canceled":
                    // User canceled the upload
                    return Promise.reject(error);
                // break;
                case "storage/unknown":
                    // Unknown error occurred, inspect the server response
                    return Promise.reject(error);
                // break;
            }
        });
};

/**
 * 建立 node ref location, 並整合至 locations (Array) 中
 * @ params:
 * rootName: string. rootName 不可為 "", 否則產生 hierarchy data (作為建立資料夾路徑的樹狀資料)會有問題
 * url: string. url 為 null 時，代表 root.
 * url 格式: 'image', 'image/desktop'
 * locations: array.
 *
 * return: Array
 * */
const addNodeRef = ({ rootName, url, locations }) => {
    const storage = firebase.storage();
    const storageRef = url ? storage.ref(url) : storage.ref();
    // eslint-disable-next-line no-underscore-dangle
    const _locations = locations || [];
    const safeRootName = rootName === "" ? "root" : rootName;

    // 有 url 代表非 root
    if (url) {
        locations.push({
            isRoot: storageRef.location.isRoot,
            pid:
                storageRef.parent.location.path === ""
                    ? rootName
                    : storageRef.parent.location.path,
            path: storageRef.location.path,
            id: storageRef.location.path,
            name: storageRef.name,
            parent: storageRef.parent,
            fullPath: storageRef.fullPath,
            type: "node",
            fetchAPI: true
        });
    } else {
        _locations.push({
            isRoot: storageRef.location.isRoot,
            pid: safeRootName, // root 沒有 parent
            path: storageRef.location.path,
            id: safeRootName,
            name: safeRootName,
            parent: storageRef.parent,
            fullPath: storageRef.fullPath,
            type: "node",
            fetchAPI: true
        });
    }

    return _locations;
};

/**
 * fetch firebase storage API, 取得該 url 底下所有的 資料夾()及檔案
 * 並整合至 locations (Array) 中
 * url: string. url 為 null 時，代表 root.
 * url 格式: 'image', 'image/desktop'
 * locations: array.
 *
 * return: Array
 * */
const listFolderChild = ({ rootName, url, locations }) => {
    const storage = firebase.storage();
    const storageRef = url ? storage.ref(url) : storage.ref();

    // eslint-disable-next-line no-underscore-dangle
    const _locations = Object.assign([], locations) || [];

    // 把 locations 中的 符合 url 的 item 的 fetchAPI key 改成 true
    const fmtLocations = _locations.map(lc => ({
        ...lc,
        fetchAPI: lc.path === url ? true : lc.fetchAPI
    }));

    return storageRef
        .listAll()
        .then(res => {
            // console.log('res.prefixes',res.prefixes)
            // console.log('res.items',res.items)
            // url 底下所有的資料夾
            res.prefixes.forEach(folderRef => {
                fmtLocations.push({
                    isRoot: folderRef.location.isRoot,
                    pid: folderRef.parent.location.path,
                    path: folderRef.location.path,
                    id: folderRef.location.path,
                    name: folderRef.name,
                    parent: folderRef.parent,
                    fullPath: folderRef.fullPath,
                    type: "node", // 資料夾為 節點
                    fetchAPI: false
                });
                // All the prefixes under listRef.
                // You may call listAll() recursively on them.
                // fixme: recursively has problem
                // recursively get folder path and file name
                // listFolderChild(folderRef.location.path, locations)
            });

            // url 底下所有的檔案
            res.items.forEach(itemRef => {
                fmtLocations.push({
                    isRoot: itemRef.location.isRoot,
                    pid: itemRef.parent.location.path,
                    path: itemRef.location.path,
                    id: itemRef.location.path,
                    name: itemRef.name,
                    parent: itemRef.parent,
                    fullPath: itemRef.fullPath,
                    type: "leaf" // 檔案為末梢(leaf)
                });
                // All the items under listRef.
            });
            return Promise.resolve(fmtLocations);
        })
        .catch(error =>
            // Uh-oh, an error occurred!
            Promise.reject(error)
        );
};

// get image path list under ref(參考點)
const getImages = callback => {
    const storage = firebase.storage();
    const storageRef = storage.ref("image");
    storageRef
        .listAll()
        .then(res => {
            const folders = [];
            // prefixes : 在 ref 底下的 folder
            res.prefixes.forEach(folderRef => {
                folders.push(folderRef.location.path_);
                // All the prefixes under listRef.
                // You may call listAll() recursively on them.
            });

            const imgUrls = [];
            res.items.forEach(itemRef => {
                imgUrls.push(itemRef.location.path_);
                // [
                //     {
                //         location: {
                //             bucket: "hkbdb-web.appspot.com",
                //             path_: "image/book-library-with-open-textbook.jpg"
                //         }
                //     },
                //     {...}
                // ];

                // All the items under listRef.
            });
            callback({ folders, imgUrls });
        })
        .catch(error => {
            // Uh-oh, an error occurred!
        });
};

// 取得 file 的 metadata
const getMetadata = path => {
    const storageRef = firebase.storage().ref();

    // [START storage_get_metadata]
    // Create a reference to the file whose metadata we want to retrieve
    const forestRef = storageRef.child(path);

    // Get metadata properties
    return forestRef
        .getMetadata()
        .then(
            metadata => Promise.resolve(metadata)
            // Metadata now contains the metadata for 'images/forest.jpg'
        )
        .catch(error => {
            // Uh-oh, an error occurred!
        });
    // [END storage_get_metadata]
};

const UPLOAD_MESSAGE = {
    success: "Upload finish",
    pause: "Upload is running",
    running: "Upload is running",
    fail: "Upload failed",
    error: {
        unauthorized: "User unauthorized",
        canceled: "User canceled",
        unknown: "Unknown error"
    }
};

const DELETE_MESSAGE = {
    success: "Delete finish",
    fail: "Delete failed",
    error: {
        unauthorized: "User unauthorized",
        canceled: "User canceled",
        unknown: "Unknown error"
    }
};

/**
 * upload file
 * @param {File} file
 */
const uploadHandleError = (file, path) => {
    const storageRef = firebase.storage().ref();

    // [START storage_upload_handle_error]
    // Create the file metadata
    const metadata = {
        contentType: file.type
    };

    return (
        storageRef
            // .child(`images/${file.name}`)
            .child(path)
            // .put(file, metadata);
            .put(file, metadata)
            .then(snapshot => Promise.resolve(UPLOAD_MESSAGE.success))
            .catch(error => {
                switch (error.code) {
                    case "storage/unauthorized":
                        // User doesn't have permission to access the object
                        return Promise.reject(
                            UPLOAD_MESSAGE.error.unauthorized
                        );
                    case "storage/canceled":
                        // User canceled the upload
                        return Promise.reject(UPLOAD_MESSAGE.error.canceled);
                    case "storage/unknown":
                        // Unknown error occurred, inspect error.serverResponse
                        return Promise.reject(UPLOAD_MESSAGE.error.unknown);
                    default:
                        return Promise.reject(UPLOAD_MESSAGE.fail);
                }
            })
    );
};

const deleteFile = path => {
    const storageRef = firebase.storage().ref();

    // [START storage_delete_file]
    // Create a reference to the file to delete
    const desertRef = storageRef.child(path);

    // Delete the file
    return desertRef
        .delete()
        .then(() =>
            // File deleted successfully
            Promise.resolve(DELETE_MESSAGE.success)
        )
        .catch(() =>
            // Uh-oh, an error occurred!
            Promise.resolve(DELETE_MESSAGE.fail)
        );
};

const deleteDocument = (colName, docName) =>
    firebase
        .firestore()
        .collection(colName)
        .doc(docName)
        .delete()
        .then(() => {
            // File deleted successfully
            Promise.resolve(DELETE_MESSAGE.success);
        })
        .catch(() => {
            // Uh-oh, an error occurred!
            Promise.resolve(DELETE_MESSAGE.fail);
        });

export {
    getImage,
    getImages,
    listFolderChild,
    addNodeRef,
    STORAGE_PATH,
    getMetadata,
    uploadHandleError,
    UPLOAD_MESSAGE,
    deleteFile,
    DELETE_MESSAGE,
    deleteDocument
};

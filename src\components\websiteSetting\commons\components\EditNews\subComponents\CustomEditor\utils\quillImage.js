import { Quill } from "react-quill";

/** 參考解法:
 * https://github.com/kensnyder/quill-image-resize-module/issues/10
 * */
const BaseImageFormat = Quill.import("formats/image");
const ImageFormatAttributesList = ["alt", "height", "width", "style"];

class ImageFormat extends BaseImageFormat {
    static formats(domNode) {
        return ImageFormatAttributesList.reduce((tmpF, attribute) =>
            domNode.hasAttribute(attribute)
                ? { ...tmpF, [attribute]: domNode.getAttribute(attribute) }
                : tmpF
        );
    }

    format(name, value) {
        if (ImageFormatAttributesList.indexOf(name) > -1) {
            if (value) {
                this.domNode.setAttribute(name, value);
            } else {
                this.domNode.removeAttribute(name);
            }
        } else {
            super.format(name, value);
        }
    }
}

export default ImageFormat;

import React, { useContext } from "react";
// import PropTypes from "prop-types";

// semantic ui
import { <PERSON><PERSON>, Icon } from "semantic-ui-react";

// utils
import PropTypes from "prop-types";
import { StoreContext } from "../../../../../../../store/StoreProvider";
import tltcAct from "../../../../tltcAction";
import Act from "../../../../../../../store/actions";

function SortedHeader({ children, sorted }) {
    const [state, dispatch] = useContext(StoreContext);
    const { rellinkTable } = state.websiteSetting;
    const sortMethods = { asc: "asc", desc: "desc" };
    const btnStyle = {
        padding: "0",
        margin: "0",
        background: "initial"
    };

    const btnDivStyle = {
        display: "flex",
        flexDirection: "column",
        justifyContent: "center"
    };

    const handleSorting = method => {
        const tmpSortTB = [...rellinkTable];
        switch (method) {
            case sortMethods.asc:
                tmpSortTB.sort(
                    (a, b) =>
                        parseInt(a.lastModified, 10) -
                        parseInt(b.lastModified, 10)
                );
                break;
            case sortMethods.desc:
                tmpSortTB.sort(
                    (a, b) =>
                        parseInt(b.lastModified, 10) -
                        parseInt(a.lastModified, 10)
                );
                break;
            default:
                break;
        }

        dispatch({
            type: Act.FRONTEDIT_TLTC,
            localType: tltcAct.SET_RELLINKTABLE,
            payload: tmpSortTB
        });
    };

    return (
        <div className="SortedHeader">
            <div style={{ display: "flex", alignItems: "center" }}>
                {children}
            </div>
            <div style={btnDivStyle}>
                <Button
                    size="mini"
                    icon
                    style={btnStyle}
                    onClick={() => handleSorting(sortMethods.asc)}
                    disabled={sorted}
                >
                    <Icon name="sort up" style={{ verticalAlign: "bottom" }} />
                </Button>
                <Button
                    size="mini"
                    icon
                    style={btnStyle}
                    onClick={() => handleSorting(sortMethods.desc)}
                    disabled={sorted}
                >
                    <Icon name="sort down" />
                </Button>
            </div>
        </div>
    );
}

SortedHeader.propTypes = {
    /** 排序功能狀態 */
    sorted: PropTypes.bool
};

SortedHeader.defaultProps = {
    /** 排序功能狀態 */
    sorted: false
};

export default SortedHeader;

import React, { useContext, useEffect, useState } from "react";

// semantic-ui-react
import { Icon, Table, Ref } from "semantic-ui-react";

// react-beautiful-dnd
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";

// components
import Selector from "../../components/Selector";
import LanguageSelect from "../../components/LanguageSelect";
import UpdateText from "../../components/UpdateText";
import SaveButton from "../../components/SaveButton";
import AddItemButton from "../../components/AddItemButton";
import CancelButton from "../../components/CancelButton";
import EditListButton from "../../components/EditListButton";
import RemoveItemButton from "../../components/RemoveItemButton";

// general
import { sortByPriority } from "../../commons";
import Act from "../../../../store/actions";
import { StoreContext } from "../../../../store/StoreProvider";
import { isEmpty } from "../../../../commons";

function LinkingPage() {
    const [state, dispatch] = useContext(StoreContext);
    const { originData, updatedData, menuActiveItem, selectOption, isEditedDisable } = state.websiteSetting;
    const [allData, setAllData] = useState([]);
    const [language, setLanguage] = useState("zh");
    const [dropDown, setDropDown] = useState({});

    useEffect(() => {
        // 下拉選單選項清空
        dispatch({
            type: Act.SET_SELECTOPTION,
            payload: ""
        });
        dispatch({
            type: Act.SET_ISEDITEDDISABLE,
            payload: true
        });
    }, []);

    useEffect(() => {
        if (originData.length !== 0) {
            setDropDown(originData.find(element => element.id === menuActiveItem.key));
        }
    }, [originData]);

    useEffect(() => {
        if (isEmpty(dropDown) || selectOption === "") return;
        const tmpDropDown = [];
        const keys = Object.keys(dropDown[selectOption]);
        keys.filter(key => key !== "name").forEach(key => {
            tmpDropDown.push(dropDown[selectOption][key]);
        });
        setAllData(sortByPriority(tmpDropDown));
    }, [selectOption, language]);

    const onDragEnd = result => {
        if (result.destination === null) return;
        const startIndex = result.source.index;
        const endIndex = result.destination.index;
        // swap startIndex and endIndex priority
        const tmpData = JSON.parse(JSON.stringify(allData));
        const tmpPriority = tmpData[startIndex].priority;
        tmpData[startIndex].priority = tmpData[endIndex].priority;
        tmpData[endIndex].priority = tmpPriority;
        setAllData(sortByPriority(tmpData));
        // 交換後的資料寫回去updatedData，因為keyName也需要跟priority做對應
        const tmpAllData = JSON.parse(JSON.stringify(updatedData));
        const tmpObj = tmpAllData.find(element => element.id === menuActiveItem.key);
        const findObjKey1 = Object.keys(tmpObj[selectOption])
            .find(key => tmpObj[selectOption][key].priority === tmpData[startIndex].priority);
        const findObjKey2 = Object.keys(tmpObj[selectOption])
            .find(key => tmpObj[selectOption][key].priority === tmpData[endIndex].priority);
        tmpObj[selectOption][findObjKey1] = tmpData[startIndex];
        tmpObj[selectOption][findObjKey2] = tmpData[endIndex];
        // console.log("tmpAllData ", tmpAllData);
        dispatch({
            type: Act.SET_UPDATEDDATA,
            payload: tmpAllData
        });
    };

    return (
        <div className="LinkingPage">
            <div className="topArea">
                <Selector dropDown={dropDown} />
                <LanguageSelect language={language} setLanguage={setLanguage} />
            </div>
            <div className="bottomArea">
                <DragDropContext onDragEnd={onDragEnd}>
                    <Table selectable>
                        <Table.Header>
                            <Table.Row>
                                {!isEditedDisable && <Table.Cell />}
                                <Table.Cell>
                                    <h2>標題</h2>
                                </Table.Cell>
                                <Table.Cell>
                                    <h2>說明連結</h2>
                                </Table.Cell>
                                {!isEditedDisable && <Table.Cell />}
                            </Table.Row>
                        </Table.Header>
                        <Droppable droppableId="droppableId">
                            {(provided, snapshot) => (
                                <Ref
                                    innerRef={provided.innerRef}
                                    {...provided.droppableProps}
                                >
                                    <Table.Body>
                                        {allData.length !== 0 &&
                                            allData.map((data, index) => (
                                                <Draggable
                                                    draggableId={`draggable_${index}`}
                                                    index={index}
                                                    key={index}
                                                    isDragDisabled={isEditedDisable}
                                                >
                                                    {(provided, snapshot) => (
                                                        <Ref innerRef={provided.innerRef}>
                                                            <Table.Row
                                                                className={`row_${index}`}
                                                                {...provided.draggableProps}
                                                                {...provided.dragHandleProps}
                                                            >
                                                                {!isEditedDisable && (
                                                                    <Table.Cell style={{width: "5%", textAlign: "center"}}>
                                                                        <Icon
                                                                            name="sidebar"
                                                                            style={{fontSize: "1rem", alignSelf: "center"}}
                                                                        />
                                                                    </Table.Cell>
                                                                )}
                                                                <Table.Cell style={{width: "35%"}}>
                                                                    <UpdateText
                                                                        language={language}
                                                                        dropDown={dropDown}
                                                                        option={{
                                                                            priority: data.priority,
                                                                            column: "title",
                                                                            message: data[language].title,
                                                                            data
                                                                        }}
                                                                    />
                                                                </Table.Cell>
                                                                <Table.Cell style={{width: "35%"}}>
                                                                    <UpdateText
                                                                        language={language}
                                                                        dropDown={dropDown}
                                                                        option={{
                                                                            priority: data.priority,
                                                                            column: "url",
                                                                            message: data.url,
                                                                            data
                                                                        }}
                                                                    />
                                                                </Table.Cell>
                                                                {!isEditedDisable && (
                                                                    <Table.Cell style={{width: "5%"}}>
                                                                        <RemoveItemButton
                                                                            data={data}
                                                                            setAllData={setAllData}
                                                                        />
                                                                    </Table.Cell>
                                                                )}
                                                            </Table.Row>
                                                        </Ref>
                                                    )}
                                                </Draggable>
                                            ))}
                                        {provided.placeholder}
                                    </Table.Body>
                                </Ref>
                            )}
                        </Droppable>
                    </Table>
                </DragDropContext>
            </div>
            <div className="btnArea">
                {isEditedDisable && <EditListButton />}
                {!isEditedDisable && (
                    <AddItemButton setAllData={setAllData} />
                )}
                {!isEditedDisable && <CancelButton />}
                {!isEditedDisable && <SaveButton language={language} />}
            </div>
        </div>
    );
}

export default LinkingPage;

/**
 *
 * @param id {string}
 * e.g. hasRelation__Person__Person
 * => {prop: "hasRelation", domain: "Person", range: "Person"}
 * e.g. geoLatitude__Place__string
 * => {prop: "geoLatitude", domain: "Place", range: "string"}
 * e.g. label__Person__string
 * => {prop: "label", domain: "Person", range: "string"}
 */
// eslint-disable-next-line import/prefer-default-export
export const getPropDomainRange = id => {
    try {
        if (!id) return { prop: "", domain: "", range: "" };
        const split = id.split("__");
        const prop = split?.[0] || "";
        const domain = split?.[1] || "";
        const range = split?.[2] || "";
        return {
            prop,
            domain,
            range
        };
    } catch (e) {
        return { prop: "", domain: "", range: "" };
    }
};

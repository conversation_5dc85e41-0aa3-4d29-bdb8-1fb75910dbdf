import FileAct from "../../../../reduxStore/file/fileAction";
import options from "../options";

const { SELECT_MODE } = options;

const pName = "data-img-url";
const isInList = (f, fList) => {
    if (!fList) {
        return true;
    }
    return fList.filter(fl => fl[pName] === f[pName]).length > 0;
};

const handleGalleryChange = ({
    file,
    curFolderFiles,
    dispatch,
    selectMode
}) => {
    const curFolderFilesChange = JSON.parse(JSON.stringify(curFolderFiles));

    // single select mode:
    if (selectMode === SELECT_MODE.single.name) {
        curFolderFilesChange.checked = [];
        if (file.checked) {
            curFolderFilesChange.checked.push(file);
            // 避免重覆
            if (curFolderFilesChange.checked.length > 1) {
                curFolderFilesChange.checked = curFolderFilesChange.checked.filter(
                    cff => cff[pName] !== file[pName]
                );
            }

            return dispatch({
                type: FileAct.CUR_FOLDER_FILES_STATUS,
                payload: curFolderFilesChange
            });
        }
        return dispatch({
            type: FileAct.CUR_FOLDER_FILES_STATUS,
            payload: curFolderFilesChange
        });
    }
    // multiple select mode:
    if (selectMode === SELECT_MODE.multiple.name) {
        if (file.checked) {
            // 增加
            if (!isInList(file, curFolderFilesChange.checked)) {
                curFolderFilesChange.checked.push(file);
            }

            return dispatch({
                type: FileAct.CUR_FOLDER_FILES_STATUS,
                payload: curFolderFilesChange
            });
        }

        // 刪除
        if (isInList(file, curFolderFilesChange.checked)) {
            curFolderFilesChange.checked = curFolderFilesChange.checked.filter(
                cff => cff[pName] !== file[pName]
            );
        }

        return dispatch({
            type: FileAct.CUR_FOLDER_FILES_STATUS,
            payload: curFolderFilesChange
        });
    }
    return null;
};

const getImgMeta = (url, callback) => {
    const img = new Image();
    img.src = url;
    img.onload = function() {
        callback(this.width, this.height);
    };
};

const getImgFilenameFromUrl = imgUrl => {
    if (!imgUrl) return null;
    const pathMatch = imgUrl.match(
        /(?<directory>.+)?[\\\/](?<filename>[^\\\/]+)\.(?<extension>.+)$/
    );

    if (!pathMatch) return null;

    const { directory, filename, extension } = pathMatch.groups;

    return `${filename}.${extension}`;
};

const formatDateTimeZoneTW = dateStr =>
    new Date(dateStr).toLocaleString("zh-TW", { timeZone: "Asia/Taipei" });

// eslint-disable-next-line import/prefer-default-export
export {
    handleGalleryChange,
    getImgMeta,
    getImgFilenameFromUrl,
    formatDateTimeZoneTW
};

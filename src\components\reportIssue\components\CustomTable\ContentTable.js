import React from "react";
import { useSelector } from "react-redux";

// semantic ui
import { Table } from "semantic-ui-react";

// config
import fbConfig from "../../common/fbConfig";

// components
import ReportField from "../OtherCompo/ReportField";
import AttachmentList from "../OtherCompo/AttachmentList";

/**
 * label: 顯示名稱
 * key: 對應firebase欄位名稱
 * component(optional): 欄位對應的UI component，沒有需要特別帶，就用資料基本型別顯示
 * uiSetting(optional): {}, semantic ui Table.Cell property
 */
const rowDataDef = [
    { key: fbConfig.name, label: "姓名" },
    { key: fbConfig.email, label: "E-mail" },
    { key: fbConfig.file, label: "附件", component: AttachmentList },
    { key: fbConfig.desc, label: "問題描述" },
    { key: fbConfig.staffEmail, label: "回報", component: ReportField }
];

function ContentTable() {
    const { cntData } = useSelector(state => state.report);

    const getCellComponent = (colKey, Component) => {
        switch (colKey) {
            default:
                return <Component />;
        }
    };

    const getTableCell = (rowDefObj, key) => (
        <Table.Cell>
            {rowDefObj?.component
                ? getCellComponent(key, rowDefObj.component)
                : cntData[key] || ""}
        </Table.Cell>
    );

    return (
        <Table celled padded>
            <Table.Body>
                {rowDataDef.map(el => {
                    const { key, label } = el;
                    return (
                        <Table.Row key={key}>
                            <Table.Cell
                                style={{ backgroundColor: "#f9fafb" }}
                                singleLine
                            >
                                {label}
                            </Table.Cell>
                            {getTableCell(el, key)}
                        </Table.Row>
                    );
                })}
            </Table.Body>
        </Table>
    );
}

export default ContentTable;

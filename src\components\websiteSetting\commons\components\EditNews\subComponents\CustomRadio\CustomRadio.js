import React, { useEffect, useState } from "react";

// plugins
import { Form } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";

// config
import clsName from "../../Utils/clsName";
import { linkSelection, pubStatusSelect } from "../../Utils/fixedSelect";
import { isEmpty } from "../../../../../../../commons";
import NewsAct from "../../EditNewsAction";
import { setUpdateNewsInfo } from "../../Utils/utils";
import initColumnDef from "../../Utils/initColumnDef";

// eslint-disable-next-line import/no-cycle
import NewsInfoData from "../NewsInfoArea/NewsInfoData";
import openModalControl from "../../Utils/openModalControl";

function CustomRadio({ className }) {
    const newsDispatch = useDispatch();
    const {
        newsFullInfo,
        modalSelect,
        modalCaller,
        isExternal,
        updateNewsInfo
    } = useSelector(state => state);

    const [tmpArr, setTmpArr] = useState([]);
    const [ckValue, setCkValue] = useState(null);

    useEffect(() => {
        switch (className) {
            case clsName.StatusRadio:
                setTmpArr(pubStatusSelect.filter(el => el.enable));
                break;
            case clsName.linkSelect: {
                setCkValue("false");
                newsDispatch({
                    type: NewsAct.SET_ISEXTERNAL,
                    payload: false
                });
                setTmpArr(linkSelection);
                break;
            }
            case clsName.TopRadio:
                setTmpArr(linkSelection);
                break;
            default:
                break;
        }
    }, []);

    useEffect(() => {
        // 這裡只能用newsFullInfo判斷，一開始的isExternal值
        if (isEmpty(newsFullInfo)) return;
        switch (className) {
            case clsName.StatusRadio:
                {
                    const initValue = newsFullInfo.status;
                    setCkValue(initValue);
                }
                break;
            case clsName.linkSelect: {
                // get externalLink data from fuseki with property name
                const linkPropName = [
                    initColumnDef.externalLinksZH,
                    initColumnDef.externalLinksEN,
                    initColumnDef.webcacheEN,
                    initColumnDef.webcacheZH
                ];
                const checkLinkProp = Object.keys(newsFullInfo).some(key =>
                    linkPropName.includes(key)
                );

                newsDispatch({
                    type: NewsAct.SET_ISEXTERNAL,
                    payload: checkLinkProp
                });
                setCkValue(checkLinkProp.toString());
                break;
            }
            case clsName.TopRadio:
                if (newsFullInfo.top) {
                    setCkValue(newsFullInfo.top);
                }
                break;
            default:
                break;
        }
    }, [newsFullInfo]);

    useEffect(() => {
        if (modalSelect === null) return;

        if (
            modalCaller === clsName.linkSelect &&
            className === clsName.linkSelect
        ) {
            const tmpIsExternal = !modalSelect !== !isExternal;
            setCkValue(`${tmpIsExternal}`);
            newsDispatch({
                type: NewsAct.SET_ISEXTERNAL,
                payload: tmpIsExternal
            });

            const tmpInfo = JSON.parse(JSON.stringify(updateNewsInfo));
            if (!isEmpty(tmpInfo)) {
                // 外部連結，不相關資訊清空，NewsInfoData的key name
                const linkKeyName = ["isLink", "notLink"];
                const linkorNot = tmpIsExternal
                    ? linkKeyName[1]
                    : linkKeyName[0];
                const [NotLinkProp] = NewsInfoData.filter(el =>
                    Object.hasOwn(el, linkorNot)
                );

                NotLinkProp[linkorNot]
                    .map(el => el.compProps.fusekiCol)
                    .forEach(colName => {
                        if (colName === initColumnDef.hasURL) {
                            tmpInfo[colName] = [];
                        } else {
                            tmpInfo[colName] = "";
                        }
                    });
            }
            setUpdateNewsInfo(newsDispatch, tmpInfo);
        }
    }, [modalSelect, modalCaller]);

    const handleChange = (e, data) => {
        const tmpNewsEvents = JSON.parse(JSON.stringify(updateNewsInfo));
        switch (className) {
            case clsName.StatusRadio:
                setCkValue(data.value);
                tmpNewsEvents.status = data.value;
                setUpdateNewsInfo(newsDispatch, tmpNewsEvents);
                break;
            case clsName.TopRadio:
                setCkValue(data.value);
                tmpNewsEvents.top = data.value;
                setUpdateNewsInfo(newsDispatch, tmpNewsEvents);
                break;
            case clsName.linkSelect:
                openModalControl(newsDispatch, className);
                break;
            default:
                break;
        }
    };

    return (
        <Form>
            <Form.Group inline style={{ margin: "0" }}>
                {tmpArr.map(el => {
                    const { name, value } = el;
                    return (
                        <Form.Radio
                            key={value}
                            label={name}
                            value={value}
                            checked={value === ckValue}
                            onChange={handleChange}
                        />
                    );
                })}
            </Form.Group>
        </Form>
    );
}

export default CustomRadio;

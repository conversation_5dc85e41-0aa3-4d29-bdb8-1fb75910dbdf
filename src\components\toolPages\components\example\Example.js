import React from "react";

// ui
import { Container, Divider, Segment, Message } from "semantic-ui-react";

// custom
import DropDownSheet from "./subComponents/DropDownSheet";
import CustomDownloadSheetButton from "./subComponents/CustomDownloadSheetButton";

const Example = () => {
    return (
        <Container>

            <Segment basic compact>
                <h2>
                    公版表單
                </h2>
            </Segment>

            <Divider />

            <Container text textAlign="center">
                <Message info>
                    <Message.Header>下載公版表單</Message.Header>
                    <p>請先選擇「表單」再點選「下載公版表單」，並請使用此處所下載的表單進行「批次匯入」</p>
                    <DropDownSheet />
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <CustomDownloadSheetButton />
                </Message>
            </Container>
        </Container>
    );
};

export default Example;

import React, { useState, useEffect, useContext } from "react";

// scss
import "./RelationLinks.scss";

// semantic ui
// import { Select } from "semantic-ui-react";

// utils
import { <PERSON>ton } from "semantic-ui-react";
import Api from "../../../../../api/nmtl/Api";
import { readNmtlData } from "../../../../../api/nmtl";
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";
import tltcAct from "../../tltcAction";
import { createData } from "./utils/utils";

// components
import AddButton from "./subCompo/AddButton";
import DelButton from "./subCompo/DelButton";
import RelTable from "./subCompo/RelTable/RelTable";
import EditContent from "./subCompo/EditContent";
import SortButton from "./subCompo/SortButton";

function RelationLinks() {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    // const { rellinkTypes } = state.websiteSetting;
    const [isEdited, setIsEdited] = useState(false);
    const [type, setType] = useState("");
    const [sorted, setSorted] = useState(false);

    useEffect(() => {
        /* select選項資料 */
        const apiStr = Api.getTltcRellinksType;
        readNmtlData(apiStr).then(res => {
            if (res?.data) {
                const tmpData = res?.data.map(({ sectionName }) =>
                    createData(sectionName, sectionName, sectionName)
                );
                dispatch({
                    type: Act.FRONTEDIT_TLTC,
                    localType: tltcAct.SET_RELLINKTYPES,
                    payload: tmpData
                });
                setType(tmpData[0].value);
            }
        });
    }, []);

    // const handleType = (evt, data) => {
    //     setType(data.value);
    // };

    const cancelSort = () => {
        setSorted(false);
    };

    return (
        <div className="RelationLinks">
            {!isEdited ? (
                <>
                    <div className="TopArea">
                        {/* nmtl-tltc web相關連結不分區塊，先隱藏下拉選單-Bennis 20230511 */}
                        {/* <Select */}
                        {/*    className="DropDownList" */}
                        {/*    placeholder="請選擇合作相關" */}
                        {/*    options={rellinkTypes} */}
                        {/*    onChange={handleType} */}
                        {/*    type={type} */}
                        {/* /> */}
                        {type && (
                            <div className="ButtonBox">
                                <SortButton
                                    sorted={sorted}
                                    setSorted={setSorted}
                                    type={type}
                                />
                                {!sorted ? (
                                    <>
                                        <AddButton setIsEdited={setIsEdited} />
                                        <DelButton type={type} />
                                    </>
                                ) : (
                                    <Button onClick={cancelSort}>取消</Button>
                                )}
                            </div>
                        )}
                    </div>
                    {type && (
                        <div className="ContentArea">
                            <RelTable
                                setIsEdited={setIsEdited}
                                type={type}
                                sorted={sorted}
                            />
                        </div>
                    )}
                </>
            ) : (
                type && <EditContent setIsEdited={setIsEdited} type={type} />
            )}
        </div>
    );
}

export default RelationLinks;

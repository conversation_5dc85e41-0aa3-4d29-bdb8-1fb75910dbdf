import React, { useEffect, useState } from "react";
import { But<PERSON> } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import { TypeName } from "../../../Utils/compoConfig";
import accMngAct from "../../../../../../reduxStore/accManage/accManageAction";
import { isEmpty } from "../../../../../../commons";

function DataSetInfoBtn({ compoInfo }) {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { unselectedSet, groupData } = state;
    const { typeName, btnName } = compoInfo;
    const [secBool, setSecBool] = useState(false);
    const [allSet, setAllSet] = useState([]);

    useEffect(() => {
        if (isEmpty(groupData.dataSet) && isEmpty(unselectedSet)) return;
        const tmpSelect = JSON.parse(JSON.stringify(groupData.dataSet));
        const tmpUnSelect = JSON.parse(JSON.stringify(unselectedSet));
        setAllSet([...tmpSelect, ...tmpUnSelect]);
    }, [unselectedSet, groupData]);

    useEffect(() => {
        switch (typeName) {
            case TypeName.RemoveAll:
            case TypeName.SeletAll:
                setSecBool(true);
                break;
            default:
                break;
        }
    }, []);

    const handleClick = tmpName => {
        switch (tmpName) {
            case TypeName.RemoveAll:
                {
                    dispatch({
                        type: accMngAct.SET_UNSELECTEDSET,
                        payload: allSet
                    });
                    const tmpGPData = JSON.parse(JSON.stringify(groupData));
                    tmpGPData.dataSet = [];
                    dispatch({
                        type: accMngAct.SET_GROUPDATA,
                        payload: tmpGPData
                    });
                }
                break;
            case TypeName.SeletAll:
                {
                    dispatch({
                        type: accMngAct.SET_UNSELECTEDSET,
                        payload: []
                    });
                    const tmpGPData = JSON.parse(JSON.stringify(groupData));
                    tmpGPData.dataSet = allSet;
                    dispatch({
                        type: accMngAct.SET_GROUPDATA,
                        payload: tmpGPData
                    });
                }
                break;
            default:
                break;
        }
    };

    return (
        <Button
            className={typeName}
            onClick={() => handleClick(typeName)}
            secondary={secBool}
        >
            {btnName}
        </Button>
    );
}

export default DataSetInfoBtn;

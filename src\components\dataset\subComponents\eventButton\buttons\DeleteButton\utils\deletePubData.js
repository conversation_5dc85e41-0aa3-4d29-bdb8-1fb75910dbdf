import Api from "../../../../../../../api/nmtl/Api";
import {
    contentTypeToClassType,
    PEICES_INFO,
    PublicationInfo
} from "../../../../../../common/sheetCrud/sheetCrudHelper";
import { deleteNmtlData } from "../../../../../../../api/nmtl";
import updatePubData from "./updatePubData";

export const getPubIds = (sheetName, tabClass, checked, rows) => {
    if (sheetName === PublicationInfo && tabClass === PEICES_INFO) {
        // 當翻譯書只有一本時，連原文書一起刪除
        const ids = checked
            .reduce((accArr, next) => {
                const [rIdx, cIdx] = next.rowId.split("-");

                if (!rIdx || !cIdx) return null;

                if (rows[rIdx].transList.length === 1) {
                    accArr.push(rows[rIdx].isTranslationBookOf);
                }

                return accArr;
            }, [])
            .join(",");

        return { ids };
    }

    const selectIds = checked.reduce((acc, next) => {
        acc.push(rows[next.rowId]);
        return acc;
    }, []);
    const ids = selectIds.map(({ srcId }) => srcId).join(",");

    return { ids };
};

const deletePubData = (pubData, dataset, contentType, sheetName, user) => {
    const promise = pubData.map(id => {
        const entry = {
            graph: dataset,
            srcId: id,
            classType: contentTypeToClassType[contentType],
            value: {}
        };

        const apiUrl = Api.getGeneric;
        return deleteNmtlData(user, apiUrl, dataset, sheetName, entry);
    });

    return new Promise((resolve, reject) => {
        Promise.all(promise)
            .then(resArr => {
                if (resArr.every(res => res === "OK")) {
                    resolve({ status: "OK" });
                }
                resolve({ status: "failed" });
            })
            .then(err => {
                reject(err || { status: "delete failed" });
            });
    });
};

const warningAction = async (
    warnData,
    dataset,
    apiWritePath,
    sheetName,
    tabClass,
    user
) => {
    /**
     *  CASE_INFO: 案層級刪除原文書時，需一併連翻譯書都刪除
     *  PEICES_INFO: 件層級只有一本原文書對應一本翻譯書時，連同原文書一起刪除
     * */
    if (Object.hasOwn(warnData, "transPubArr")) {
        const pubData = warnData?.transPubArr?.data.map(item =>
            tabClass === PEICES_INFO ? item.oriId : item.transId
        );

        // eslint-disable-next-line no-return-await
        return await deletePubData(
            pubData,
            dataset,
            apiWritePath,
            sheetName,
            user
        );
    }

    /**
     *  刪除人物時，同時刪除關聯著作中的作者/譯者發表名稱
     */
    if (Object.hasOwn(warnData, "pubCntArr")) {
        // eslint-disable-next-line no-return-await
        return await updatePubData(warnData, dataset, sheetName, user);
    }
    return { status: "failed" };
};
export default warningAction;

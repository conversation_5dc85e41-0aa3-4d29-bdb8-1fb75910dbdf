import React from "react";
//
import TextCell from "../cell/TextCell";
import StatusCell from "../cell/StatusCell";
import MultiLangCell from "../cell/MultiLangCell";
import { getPropDomainRange } from "../crudHelper";

const useCell = () => {
    const getReadCell = (info, cellProps) => {
        const { prop, domain, range } =
            getPropDomainRange(info?.column?.id) || {};

        switch ((domain || "").toLowerCase()) {
            case "status":
                return <StatusCell {...cellProps(info)} />;
            case "multilang":
                return <MultiLangCell {...cellProps(info)} />;
            default:
                return <TextCell {...cellProps(info)} />;
        }
    };

    return { getReadCell };
};

export default useCell;

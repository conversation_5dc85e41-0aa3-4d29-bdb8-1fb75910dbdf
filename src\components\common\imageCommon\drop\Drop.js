import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { <PERSON>rid, Header, Icon, Segment } from "semantic-ui-react";
import { useDropzone } from "react-dropzone";
import PropTypes from "prop-types";
import Instructions from "./Instructions";
// store
import { StoreContext } from "../../../../store/StoreProvider";
// alert helper
import { accept<PERSON>haracter } from "../../../../commons/utility";
import Gallery from "../gallery";
import { handleDrop, handleChange, handleDropReject } from "./dropHelper";
// options
import options from "../options";
import styles from "./dropStyle";
// import { createHistoryEvent } from "../../../downloadData/components/history/common/common";
import uploadConfig from "../../../toolPages/components/upload/uploadConfig";
import FileList from "../FileList/FileList";
import { handleGalleryChange } from "../gallery/galleryHelper";

import FileAct from "../../../../reduxStore/file/fileAction";

const { SELECT_MODE } = options;

const Drop = props => {
    const {
        type,
        config,
        DropAlertMsg,
        useSmallDrop,
        handleDropCallback
    } = props;

    const dispatchRedux = useDispatch();
    const {
        files: {
            uploadImages,
            UPLOAD_CONFIG,
            ACCEPTABLE_EXTENSIONS,
            currentFolder,
            dropFolderFiles,
            headerActiveName,
            toolPagesActiveName
        }
    } = useSelector(state => state);

    const columns = [headerActiveName];
    //
    const [instructionsOpen, setInstructionsOpen] = useState(false);

    useEffect(() => {
        if (headerActiveName === "工具相關") {
            columns.push(toolPagesActiveName);
        }
    }, []);

    useEffect(() => {
        // 當上傳的檔案有變化時, 建立 formData object, 預備 put API

        const formData = new FormData();
        if (uploadImages && uploadImages.length > 0) {
            uploadImages.forEach(file => {
                formData.append(uploadConfig.ImageFormName, file);
            });
        }
        dispatchRedux({
            type: FileAct.SET_FORM_DATA,
            payload: formData
        });
    }, [dispatchRedux, uploadImages]);

    const { getRootProps, getInputProps } = useDropzone({
        accept: ACCEPTABLE_EXTENSIONS.map(ext => `.${ext}`).join(),
        maxFiles:
            config.selectMode === SELECT_MODE.single.name
                ? 1
                : UPLOAD_CONFIG.maxCount,
        maxSize: UPLOAD_CONFIG.maxSize,
        onDrop: acceptedFiles => {
            handleDrop({
                acceptedFiles,
                handleDropCallback
            });
            // createHistoryEvent(
            //     displayName,
            //     "更新",
            //     `${columns.join("/")}${currentFolder.path_ch}`
            // );
        },
        onDropRejected: files => {
            handleDropReject(files, config, dispatchRedux);
            // createHistoryEvent(
            //     displayName,
            //     "上傳失敗",
            //     `${columns.join("/")}${currentFolder.path_ch}`
            // );
        }
    });

    useEffect(
        () => () => {
            // Make sure to revoke the data uris to avoid memory leaks
            if (uploadImages && uploadImages.length > 0)
                uploadImages.forEach(file => URL.revokeObjectURL(file.preview));
        },
        [uploadImages]
    );

    // 大的 drop area,與 Gallery 分開
    const dropAreaBig = (
        <Segment placeholder>
            <Grid centered className="dropImageTitle">
                <Grid.Row>
                    <Grid.Column>
                        <div {...getRootProps({ className: "dropzone" })}>
                            <input
                                {...getInputProps({
                                    className: "dropInput"
                                })}
                            />
                            <Header icon>
                                <Icon name="file image outline" />
                                <p>拖曳檔案至此或點擊此區以選取檔案</p>
                            </Header>
                        </div>
                    </Grid.Column>
                </Grid.Row>
            </Grid>
        </Segment>
    );

    // 小的 drop area, 內嵌在 Gallery 中
    const dropAreaSmall = (
        <div
            {...getRootProps({
                className: "dropzone-small"
            })}
        >
            <input
                {...getInputProps({
                    className: "dropInput"
                })}
            />
            <Icon name="plus" />
        </div>
    );

    // 標題旁的 info icon
    const instructions = {
        uploadInfo: (
            <div
                style={styles.instructIconBox}
                onClick={() => setInstructionsOpen(prev => !prev)}
            >
                <Icon
                    name="info"
                    size="tiny"
                    circular
                    style={styles.instructIcon}
                />
            </div>
        )
    };

    return (
        <div>
            <h2>上傳檔案{instructions.uploadInfo}</h2>

            {/* 圖片上傳規範 */}
            {instructionsOpen && (
                <Instructions
                    maxSize={UPLOAD_CONFIG?.maxSize}
                    fileCountLimit={
                        config?.selectMode === SELECT_MODE.multiple.name
                            ? UPLOAD_CONFIG.maxCount
                            : "1"
                    }
                    extensions={ACCEPTABLE_EXTENSIONS}
                    miniDpi={UPLOAD_CONFIG?.minDpi}
                    acceptCharacter={acceptCharacter?.ch}
                />
            )}
            <h3>上傳資料夾：{currentFolder.path_ch}</h3>
            <Segment placeholder>
                <Grid className="drop-file-container">
                    <Grid.Row>
                        <Grid.Column textAlign="center">
                            <h4>預覽區</h4>
                            <h5>數量：{uploadImages.length}</h5>
                            {/* 大型 drop 區塊 */}
                            {!useSmallDrop && dropAreaBig}
                            {/* control panel */}
                            {/* {ControlPanel && <ControlPanel config={config} />} */}
                            {/* alert message */}
                            {DropAlertMsg && <DropAlertMsg />}
                            {/* display uploadImages with lazy loading */}
                            <aside
                                className="thumbsContainer"
                                style={{ textAlign: "left" }}
                            >
                                {/* 鑲嵌 小型 drop 在 gallery 中 */}
                                {uploadConfig.image === type ? (
                                    <Gallery
                                        images={uploadImages}
                                        withCheckbox={config.withCheckbox}
                                        selectMode={config.selectMode}
                                        onChange={(e, data) =>
                                            handleChange(
                                                e,
                                                data,
                                                dropFolderFiles,
                                                dispatchRedux,
                                                config
                                            )
                                        }
                                        firstChild={
                                            (useSmallDrop && dropAreaSmall) ||
                                            null
                                        }
                                    />
                                ) : (
                                    <FileList
                                        files={uploadImages}
                                        onChange={(e, data) =>
                                            handleGalleryChange({
                                                file: data,
                                                uploadImages,
                                                dispatchRedux,
                                                config
                                            })
                                        }
                                        firstChild={
                                            (useSmallDrop && dropAreaSmall) ||
                                            null
                                        }
                                        ctlType={uploadConfig.DropDownPreview}
                                    />
                                )}
                            </aside>
                        </Grid.Column>
                    </Grid.Row>
                </Grid>
            </Segment>
        </div>
    );
};

Drop.defaultProps = {
    type: uploadConfig.image,
    config: { selectMode: SELECT_MODE.multiple.name },
    DropAlertMsg: null,
    useSmallDrop: true
};

Drop.propTypes = {
    type: PropTypes.string,
    config: PropTypes.instanceOf(Object),
    DropAlertMsg: PropTypes.elementType,
    useSmallDrop: PropTypes.bool
};

export default Drop;

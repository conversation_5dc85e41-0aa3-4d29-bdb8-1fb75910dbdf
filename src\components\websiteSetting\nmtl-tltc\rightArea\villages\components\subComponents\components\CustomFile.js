import React, { useEffect, useState } from "react";
import { Button, Grid, Icon, Input } from "semantic-ui-react";
import axios from "axios";
import { getFileName } from "../../../../../../commons/components/EditNews/Utils/saveDataUtils";
import { isEmpty } from "../../../../../../../../commons";
import { uploadFile } from "../../../utils/utils";
import Api from "../../../../../../../../api/nmtl/Api";
import "../../EditVillagesDetail.scss";

const CustomFile = ({ type, rowName, updateFct, subRowName, updatedData }) => {
    // eslint-disable-next-line no-unused-vars
    const [fileValue, setFileValue] = useState("");

    const [files, setFiles] = useState([]);

    const acceptFile = t => {
        switch (t) {
            case "hasURL":
                return ".jpg,.jpeg,.png";
            case "VillageTidbits":
                return ".jpg,.jpeg,.png";
            default:
                return "image/*";
        }
    };

    const handleCancel = () => {
        setFiles([]);
        updateFct(updatedData, type, "");
    };

    const handleChange = async (e, tmpData) => {
        const tmpUpdatedData = JSON.parse(JSON.stringify(updatedData));
        if (isEmpty(tmpUpdatedData[type])) {
            tmpUpdatedData[type] = [];
        }
        const folderName =
            type === "hasURL" ? "villages/cover" : `villages/${type}`;
        setFileValue(tmpData.value);
        const fileURL = await uploadFile(
            e.currentTarget,
            "settings",
            tmpUpdatedData[type],
            folderName,
            type
        );
        if (isEmpty(fileURL)) return;
        const postURLEvent = async () => {
            const imageName = getFileName(fileURL[0]);
            const entry = {
                graph: "settings",
                classType: "URLEvent",
                value: {
                    imageName,
                    imagePath: `settings/${folderName}`
                },
                srcId: ""
            };
            await axios.post(Api.getGeneric, { entry });
            return imageName;
        };

        const getURLEvent = async name => {
            const api = Api.getImageId
                .replace("{name}", name)
                .replace("{ds}", "settings");
            const result = await axios.get(api);
            return result?.data?.data[0].id;
        };

        const imageName = await postURLEvent();
        const res = await getURLEvent(imageName);
        updateFct(updatedData, type, [res]);
    };

    useEffect(() => {
        if (isEmpty(updatedData) || files === updatedData[type]) return;
        const getImageName = async id => {
            const api = Api.getImageName.replace("{id}", id);
            await axios.get(api).then(res => {
                setFiles(res?.data?.data[0].imageName);
            });
        };
        if (!isEmpty(updatedData[type])) {
            getImageName(updatedData[type]);
        }
    }, [updatedData]);

    return (
        <Grid.Row>
            <Grid.Column
                width={3}
                style={{
                    backgroundColor: "#e0e1e2"
                }}
            >
                <div className="topArea__left">
                    <span>{rowName}</span>
                    {subRowName && (
                        <div className="topArea__left--subRowName">
                            {subRowName.includes("、") ? (
                                <>
                                    <div>{subRowName.split("、")[0]}</div>
                                    <div>{subRowName.split("、")[1]}</div>
                                </>
                            ) : (
                                <span>{subRowName}</span>
                            )}
                        </div>
                    )}
                </div>
            </Grid.Column>
            <Grid.Column width={13} style={{ display: "flex" }}>
                <div className="topArea__right">
                    <div className="topArea__right--box">
                        <Input type="text" placeholder="Search..." action>
                            <Input value={files && files} readOnly disabled />
                            {files && (
                                <Button
                                    icon
                                    style={{ borderRight: "1px solid black" }}
                                >
                                    <Icon
                                        name="cancel"
                                        style={{
                                            color: "red"
                                        }}
                                        onClick={handleCancel}
                                    />
                                </Button>
                            )}
                            <Button
                                type="submit"
                                htmlFor={type}
                                style={{ padding: "0" }}
                            >
                                {/* eslint-disable-next-line jsx-a11y/label-has-associated-control,jsx-a11y/label-has-for */}
                                <label
                                    htmlFor={type}
                                    style={{
                                        fontSize: "22px",
                                        cursor: "pointer",
                                        textAlign: "center",
                                        padding: "1rem",
                                        borderRadius: "4px"
                                    }}
                                >
                                    +
                                </label>
                                <Input
                                    id={type}
                                    onChange={handleChange}
                                    value={fileValue}
                                    style={{
                                        visibility: "hidden",
                                        display: "none"
                                    }}
                                    type="file"
                                    accept={acceptFile(type)}
                                />
                            </Button>
                        </Input>
                    </div>
                </div>
            </Grid.Column>
        </Grid.Row>
    );
};

export default CustomFile;

import CustomInput from "./components/CustomInput";
import CustomDoubleInput from "./components/CustomDoubleInput";
import CustomDropdown from "./components/CustomDropdown";
import CustomFile from "./components/CustomFile";

const switchPropertyName = [
    { hasTLAAwardName: "TLAAwardNames" },
    { hasTLACategory: "TLACategorys" },
    { hasAuthor: "authors" },
    { hasPublisher: "publishers" },
    { hasDescribedTarget: "describedTargets" }
];

/**
 * - component: The component to render.
 * - type: Unique identifier for the input.
 * - rowName: The label for the input field.
 * - optionListKey: The data to be displayed in the dropdown.
 * - valueKeys: The key to extract the value from the data object.
 * - titles: The titles for the double input fields.
 * - subRowName: The sub label for the input fields.
 * - required: Whether the input is required.
 */
const formConfig = [
    {
        section: "section1",
        component: CustomInput,
        props: {
            type: "id",
            rowName: "書目ID",
            valueKey: "id"
        }
    },
    {
        section: "section1",
        component: CustomDoubleInput,
        props: {
            rowName: "書名",
            titles: ["中文版網站、顯示名稱", "英文版網站、顯示名稱"],
            type: ["labelZh", "labelEn"],
            valueKeys: ["labelZh", "labelEn"],
            valueKey: "label",
            required: true
        }
    },
    {
        section: "section1",
        component: CustomFile,
        props: {
            type: "hasURL",
            rowName: "書封照",
            valueKey: "hasURL",
            subRowName: "上傳尺寸建議為：、852*852px"
        }
    },
    {
        section: "section1",
        component: CustomDropdown,
        props: {
            type: "hasAuthor",
            rowName: "作者名稱",
            optionListKey: "writerList",
            valueKey: "hasAuthor",
            subRowName: "(可複選)",
            classType: "Person"
        }
    },
    {
        section: "section1",
        component: CustomFile,
        props: {
            type: "hasTlaPersonPhoto",
            rowName: "作者照片",
            valueKey: "hasTlaPersonPhoto"
        }
    },
    {
        section: "section1",
        component: CustomInput,
        props: {
            type: "tlaTranslator",
            rowName: "摘譯內容的譯者",
            valueKey: "tlaTranslator"
        }
    },
    {
        section: "section2",

        component: CustomDropdown,
        props: {
            type: "hasTLAAwardName",
            rowName: "獎項名稱",
            optionListKey: "tlaAwardsList",
            valueKey: "hasTLAAwardName",
            subRowName: "(可複選)",
            classType: "TLAAward"
        }
    },
    {
        section: "section2",
        component: CustomInput,
        props: {
            type: "awardCategory",
            rowName: "得獎/入圍",
            valueKey: "awardCategory"
        }
    },
    {
        section: "section2",
        component: CustomInput,
        props: {
            type: "hasTLAAwardDate",
            rowName: "得獎年份",
            valueKey: "hasTLAAwardDate"
        }
    },
    {
        section: "section3",
        component: CustomDropdown,
        props: {
            type: "hasTLACategory",
            rowName: "文類",
            optionListKey: "tlaCategoryList",
            valueKey: "hasTLACategory",
            subRowName: "(可複選)",
            classType: "TLACategory"
        }
    },
    {
        section: "section3",
        component: CustomDropdown,
        props: {
            type: "hasPublisher",
            rowName: "出版社",
            optionListKey: "publisherList",
            valueKey: "hasPublisher",
            subRowName: "(可複選)",
            classType: "Organization"
        }
    },
    {
        section: "section3",
        component: CustomInput,
        props: {
            type: "hasInceptionDate",
            rowName: "出版日期",
            valueKey: "hasInceptionDate"
        }
    },
    {
        section: "section3",
        component: CustomInput,
        props: {
            type: "ISBN",
            rowName: "ISBN",
            valueKey: "ISBN"
        }
    },
    {
        section: "section3",
        component: CustomInput,
        props: {
            type: "totalPage",
            rowName: "頁數",
            valueKey: "totalPage"
        }
    },
    {
        section: "section3",
        component: CustomInput,
        props: {
            type: "characters",
            rowName: "字數",
            valueKey: "characters"
        }
    },
    {
        section: "section4",
        component: CustomDoubleInput,
        props: {
            rowName: "版權聯絡人",
            titles: ["中文版網站、顯示名稱", "英文版網站、顯示名稱"],
            type: ["rightsContactZh", "rightsContactEn"],
            valueKeys: ["rightsContactZh", "rightsContactEn"],
            valueKey: "rightsContact"
        }
    },
    {
        section: "section4",
        component: CustomInput,
        props: {
            type: "rightsContactEmail",
            rowName: "版權聯絡人Email",
            valueKey: "rightsContactEmail"
        }
    },
    {
        section: "section5",
        component: CustomInput,
        props: {
            type: "rightsSold",
            rowName: "售出語種",
            valueKey: "rightsSold",
            subRowName: "(以,分隔)"
        }
    },
    {
        section: "section5",
        component: CustomFile,
        props: {
            type: "tlaFileAvailableAt",
            rowName: "摘譯內容的PDF檔",
            valueKey: "tlaFileAvailableAt"
        }
    }
];

export { switchPropertyName, formConfig };

import React, { useContext } from "react";

// react components
import { Route, Switch } from "react-router";

// route config
import routes from "../App-route";

// router
import RouteProtectedHoc from "./RouteProtectedHoc";

// custom page
import NotFound from "../components/pages/NotFound";

// store
import { StoreContext } from "../store/StoreProvider";
import { filterRoute } from "../commons/filterGroup";

const RouteManager = () => {
    // eslint-disable-next-line no-unused-vars
    const [state, _] = useContext(StoreContext);
    const { user } = state;
    const { groupInfo } = state.data;
    return (
        <Switch>
            {routes &&
                routes
                    .filter(route => filterRoute(user, route, groupInfo))
                    .map(route => (
                        <RouteProtectedHoc
                            exact
                            key={route.id}
                            path={route.path}
                            public={route.public}
                            component={route.component}
                        />
                    ))}
            <Route component={NotFound} />
        </Switch>
    );
};

export default RouteManager;

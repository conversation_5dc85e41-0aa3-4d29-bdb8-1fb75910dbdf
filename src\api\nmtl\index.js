// read nmtl data
import { createElasticData } from "../elastic";
import ESApi from "../elastic/Api";

// common
import { isEmpty } from "../../commons";

import axios from "axios";
import Api from "./Api";

const getUnintegratedData = async searchIds => {
    if (!searchIds) return;
    try {
        const url = Api.getUnintegratedData;
        const newUrl = url.replace("{ids}", searchIds);
        const res = await axios.get(newUrl);
        return res.data.data;
    } catch (e) {
        console.log(e);
        return [];
    }
};

const readNmtlData = async (api, timeout = 60000, options = {}) => {
    if (api === "") {
        return { data: [] };
    }
    // console.log("I am readNmtlData", api);

    // abort if it tyr too long
    const controller = new AbortController();
    const { signal } = controller;
    options.signal = signal;

    // to stop fetch if timeout
    setTimeout(() => controller.abort(), timeout);

    // fetch nmtl api
    return await fetch(api, options)
        // handle data to json
        .then(res => res.json())
        // return data
        .then(data => data)
        // handle catch
        .catch(err => {
            // handle AbortController event and return error message
            if (err.name === "AbortError") {
                console.error("api:nmtl:fetch:get:aborted:connection:timeout");
                return { error: err.message };
            }
            // handle other error event and return error message

            console.error("api:nmtl:fetch:get:error:", err.message);
            return { error: err.message };
        });
};

// create nmtl data
const createNmtlData = async (user, api, dataset, sheetName, entry) => {
    if (isEmpty(user)) {
        console.error("user parameter no exist");
        return { error: "user parameter no exist" };
    }

    if (isEmpty(api)) {
        console.error("api parameter no exist");
        return { error: "api parameter no exist" };
    }

    if (isEmpty(dataset)) {
        console.error("dataset parameter no exist");
        return { error: "dataset parameter no exist" };
    }

    if (isEmpty(sheetName)) {
        console.error("sheetName parameter no exist");
        return { error: "sheetName parameter no exist" };
    }

    if (isEmpty(entry)) {
        console.error("entry parameter no exist");
        return { error: "entry parameter no exist" };
    }

    // replace api prefix for production
    // if (process.env.NODE_ENV === "production") {
    //     api = api.replace(process.env.REACT_APP_NMTL_API_NODE, "");
    // }

    const { uid, displayName, email } = user;

    // abort if it tyr too long
    const controller = new AbortController();
    const { signal } = controller;

    // to stop fetch if timeout
    setTimeout(() => controller.abort(), 5000);

    const options = {
        signal,
        method: "POST",
        headers: {
            "content-type": "application/json",
            "Accept-Encoding": "gzip"
        },
        body: JSON.stringify({ entry })
    };

    // fetch nmtl api
    return await fetch(api, options)
        // handle data to json
        .then(res => res.json())
        // get data
        .then(data => {
            // log content to elsatic
            const logData = {
                uid,
                name: displayName,
                email,
                type: "create",
                ip: "",
                time: new Date(),
                timestamp: new Date().getTime(),
                dataset,
                sheet: sheetName,
                entrySrc: entry,
                entryDst: {}
            };
            // log it if success
            createElasticData(ESApi.putLog, logData);

            return data.data || data;
        })
        // handle catch
        .catch(err => {
            // handle AbortController event
            if (err.name === "AbortError") {
                console.error(
                    "api:nmtl:fetch:create:aborted:connection:timeout"
                );
                return { error: err.message };
            }
            // handle other error event

            console.error("api:nmtl:fetch:create:error:", err.message);
            return { error: err.message };
        });
};

const processDatasetChange = async (
    entrySrc,
    entryDst,
    user,
    sheetName,
    dataset
) => {
    const apiUrl = Api.getGeneric;

    // 處理新增項目
    const processAddition = async (newArr, oldArr) => {
        const newItems = newArr.filter(item => !oldArr.includes(item));

        for (const item of newItems) {
            const newItem = {
                graph: item,
                classType: "Person",
                srcId: entryDst.srcId,
                value: {}
            };

            await createNmtlData(user, apiUrl, item, sheetName, newItem);
        }
    };

    // 處理刪除項目
    const processDeletion = async (newArr, oldArr) => {
        const oldItems = oldArr.filter(item => !newArr.includes(item));

        for (const item of oldItems) {
            const newItem = {
                graph: item,
                classType: "Person",
                srcId: entryDst.srcId,
                value: entryDst.value
            };

            await deleteNmtlData(user, apiUrl, dataset, sheetName, newItem);
        }
    };

    // 處理 literary 資料的變更
    const handleLiteraryChanges = async () => {
        const newLiterary = entryDst.value.literary || [];
        const oldLiterary = entrySrc.value.literary || [];

        // 檢查是否有新增項目
        if (
            (!entrySrc.value.hasOwnProperty("literary") &&
                entryDst.value.hasOwnProperty("literary")) ||
            (Array.isArray(newLiterary) &&
                Array.isArray(oldLiterary) &&
                newLiterary.length > oldLiterary.length)
        ) {
            await processAddition(newLiterary, oldLiterary);
        }

        // 檢查是否有刪除項目
        if (
            (entrySrc.value.hasOwnProperty("literary") &&
                !entryDst.value.hasOwnProperty("literary")) ||
            (Array.isArray(newLiterary) &&
                Array.isArray(oldLiterary) &&
                newLiterary.length < oldLiterary.length)
        ) {
            await processDeletion(newLiterary, oldLiterary);
        }

        delete entryDst.value.literary;
        delete entrySrc.value.literary;
    };

    // 處理 graph 資料的變更
    const handleGraphChanges = async () => {
        const newGraph = entryDst.value.graph || [];
        const oldGraph = entrySrc.value.graph || [];

        // 檢查是否有新增項目
        if (
            (!entrySrc.value.hasOwnProperty("graph") &&
                entryDst.value.hasOwnProperty("graph")) ||
            (Array.isArray(newGraph) &&
                Array.isArray(oldGraph) &&
                newGraph.length > oldGraph.length)
        ) {
            await processAddition(newGraph, oldGraph);
        }

        // 檢查是否有刪除項目
        if (
            (entrySrc.value.hasOwnProperty("graph") &&
                !entryDst.value.hasOwnProperty("graph")) ||
            (Array.isArray(newGraph) &&
                Array.isArray(oldGraph) &&
                newGraph.length < oldGraph.length)
        ) {
            await processDeletion(newGraph, oldGraph);
        }

        delete entryDst.value.graph;
        delete entrySrc.value.graph;
    };

    // 判斷並處理 literary 和 graph 的變更
    if (
        entryDst.value.hasOwnProperty("literary") ||
        entrySrc.value.hasOwnProperty("literary")
    ) {
        await handleLiteraryChanges();
    }

    if (
        entryDst.value.hasOwnProperty("graph") ||
        entrySrc.value.hasOwnProperty("graph")
    ) {
        await handleGraphChanges();
    }

    return { entryDst, entrySrc };
};

// update nmtl data
const updateNmtlData = async (
    user,
    api,
    dataset,
    sheetName,
    entrySrc,
    entryDst
) => {
    if (isEmpty(user)) {
        console.error("user parameter no exist");
        return { error: "user parameter no exist" };
    }

    if (isEmpty(api)) {
        console.error("api parameter no exist");
        return { error: "api parameter no exist" };
    }

    if (isEmpty(dataset)) {
        console.error("dataset parameter no exist");
        return { error: "dataset parameter no exist" };
    }

    if (isEmpty(sheetName)) {
        console.error("sheetName parameter no exist");
        return { error: "sheetName parameter no exist" };
    }

    if (isEmpty(entrySrc) || isEmpty(entryDst)) {
        console.error("entrySrc or entryDst parameter no exist");
        return { error: "entrySrc or entryDst parameter no exist" };
    }

    // replace api prefix for production
    // if (process.env.NODE_ENV === "production") {
    //     api = api.replace(process.env.REACT_APP_NMTL_API_NODE, "");
    // }

    let copyEntrySrc = JSON.parse(JSON.stringify(entrySrc));
    let copyEntryDst = JSON.parse(JSON.stringify(entryDst));

    const { uid, displayName, email } = user;

    // abort if it tyr too long
    const controller = new AbortController();
    const { signal } = controller;

    // to stop fetch if timeout
    setTimeout(() => controller.abort(), 5000);

    // 針對權威檔 literary 或 graph 有更新做特殊處理
    const {
        entrySrc: finalEntrySrc,
        entryDst: finalEntryDst
    } = await processDatasetChange(
        copyEntrySrc,
        copyEntryDst,
        user,
        sheetName,
        dataset
    );

    const options = {
        signal,
        method: "PUT",
        headers: {
            "content-type": "application/json",
            "Accept-Encoding": "gzip"
        },
        body: JSON.stringify({
            entrySrc: finalEntrySrc,
            entryDst: finalEntryDst
        })
    };

    // fetch nmtl api
    return await fetch(api, options)
        // handle data to json
        .then(res => res.json())
        // get data
        .then(data => {
            // log content to elsatic
            const logData = {
                uid,
                name: displayName,
                email,
                type: "update",
                ip: "",
                time: new Date(),
                timestamp: new Date().getTime(),
                dataset,
                sheet: sheetName,
                entrySrc,
                entryDst
            };
            // log it if success
            createElasticData(ESApi.putLog, logData);

            return data.data || data;
        })
        // handle catch
        .catch(err => {
            // handle AbortController event
            if (err.name === "AbortError") {
                console.error("api:nmtl:fetch:put:aborted:connection:timeout");
                return { error: err.message };
            }
            // handle other error event

            console.error("api:nmtl:fetch:put:error:", err.message);
            return { error: err.message };
        });
};

// delete nmtl data
const deleteNmtlData = async (user, api, dataset, sheetName, entry) => {
    if (isEmpty(user)) {
        console.error("user parameter no exist");
        return { error: "user parameter no exist" };
    }

    if (isEmpty(api)) {
        console.error("api parameter no exist");
        return { error: "api parameter no exist" };
    }

    if (isEmpty(dataset)) {
        console.error("dataset parameter no exist");
        return { error: "dataset parameter no exist" };
    }

    if (isEmpty(sheetName)) {
        console.error("sheetName parameter no exist");
        return { error: "sheetName parameter no exist" };
    }

    if (isEmpty(entry)) {
        console.error("entry parameter no exist");
        return { error: "entry parameter no exist" };
    }

    // replace api prefix for production
    // if (process.env.NODE_ENV === "production") {
    //     api = api.replace(process.env.REACT_APP_NMTL_API_NODE, "");
    // }

    const { uid, displayName, email } = user;

    // abort if it tyr too long
    const controller = new AbortController();
    const { signal } = controller;

    // to stop fetch if timeout
    setTimeout(() => controller.abort(), 5000);

    const options = {
        signal,
        method: "DELETE",
        headers: {
            "content-type": "application/json",
            "Accept-Encoding": "gzip"
        },
        body: JSON.stringify({ entry })
    };

    // fetch nmtl api
    return await fetch(api, options)
        // handle data to json
        .then(res => res.json())
        // get data
        .then(data => {
            // log content to elsatic
            const logData = {
                uid,
                name: displayName,
                email,
                type: "delete",
                ip: "",
                time: new Date(),
                timestamp: new Date().getTime(),
                dataset,
                sheet: sheetName,
                entrySrc: entry,
                entryDst: {}
            };
            // log it if success
            createElasticData(ESApi.putLog, logData);

            return data.data || data;
        })
        // handle catch
        .catch(err => {
            // handle AbortController event
            if (err.name === "AbortError") {
                console.error(
                    "api:nmtl:fetch:delete:aborted:connection:timeout"
                );
                return { error: err.message };
            }
            // handle other error event

            console.error("api:nmtl:fetch:delete:error:", err.message);
            return { error: err.message };
        });
};

export {
    createNmtlData,
    readNmtlData,
    updateNmtlData,
    deleteNmtlData,
    getUnintegratedData
};

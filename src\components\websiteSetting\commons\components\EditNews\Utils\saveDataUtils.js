// 取得儲存資料需要的欄位名稱、語系資料...
import { frontEditPrefix } from "../../../../../../api/nmtl/classPrefix";
import initColumnDef, { imageColDef } from "./initColumnDef";
import NewsAct from "../EditNewsAction";
import textConfig from "./textConfig";
import openModalControl from "./openModalControl";
import { DATE_EVENTS } from "../../../../../common/sheetCrud/sheetCrudHelper";
import { isEmpty } from "../../../../../../commons";
import { fsFrontEdit } from "../../../../../../api/fileServer";

export const getKeyData = keyIdName =>
    // 設定keyData
    ({
        // 要傳給api "srcId"欄位
        keyIdName,
        // 要傳給api "value"欄位的資料內容
        // dbPropName: 要傳給api "value"欄位的 database property name
        // prop: 從apiStr抓到資料內容的欄位名稱
        // lang: 要帶給api的語系名稱 (optional)
        dataValue: Object.keys(initColumnDef).map(key => {
            if (["ZH", "EN"].includes(key.slice(-2).toUpperCase())) {
                return {
                    dbPropName: key.slice(0, -2),
                    prop: initColumnDef[key],
                    lang: key.slice(-2).toLowerCase()
                };
            }
            return { dbPropName: key, prop: initColumnDef[key] };
        })
    });

// 根據classPrefix取得event class type
export const getClassType = tmpID => {
    const findClassType = frontEditPrefix.find(
        el => tmpID.startsWith(el.prefix) && el.prefix
    );
    return findClassType?.eventType;
};

export const saveDataCallBack = (res, dispatch) => {
    if (res !== "OK") {
        // 儲存資料遇到錯誤顯示視窗
        dispatch({
            type: NewsAct.SET_MODALMSG,
            payload: textConfig.ErrorMessage.SAVEERROR
        });
        openModalControl(dispatch, textConfig.ErrorMessage.eventCaller);
    } else {
        openModalControl(dispatch, textConfig.SuccessMessage.eventCaller);
        console.log("Save Data success !!");
    }
};

// 不處理has property，移除entry.value裡面的相關data
export const rmHasProp = entry => {
    if (isEmpty(entry)) return {};
    const copyEntry = JSON.parse(JSON.stringify(entry));
    const entryHasProp = Object.keys(copyEntry?.value).filter(
        // DATE_EVENTS prop，需要保留
        prop => prop.startsWith("has") && !DATE_EVENTS.includes(prop)
    );
    entryHasProp.forEach(key => {
        if (copyEntry?.value?.[key]) {
            delete copyEntry.value[key];
        }
    });
    return copyEntry;
};

export const getFileName = tmpURL =>
    tmpURL.substring(tmpURL.lastIndexOf("/") + 1);

export const getImgKeyData = keyIdName => {
    // 設定keyData
    const keyData = {
        // 要傳給api "srcId"欄位
        keyIdName,
        // 要傳給api "value"欄位的資料內容
        // dbPropName: 要傳給api "value"欄位的 database property name
        // prop: 從apiStr抓到資料內容的欄位名稱
        dataValue: [
            { dbPropName: "imagePath", prop: "imagePath" },
            { dbPropName: "imageName", prop: "imageName" },
            { dbPropName: imageColDef.order, prop: imageColDef.order },
            { dbPropName: "desc", prop: imageColDef.imgText }
        ]
    };

    return keyData;
};

// transform url object format with dbPropName
export const formatURLObj = (tmpObj, subject) => {
    if (isEmpty(tmpObj) || !subject) return {};
    const imageForm = {
        imagePath: "imagePath",
        imageName: "imageName",
        order: "order",
        urlId: "urlId",
        desc: imageColDef.imgText
    };

    const resObj = {};
    Object.keys(imageColDef).forEach(key => {
        if (key === imageColDef.imgText) {
            resObj[imageForm.desc] = tmpObj[key];
        } else if (key === imageColDef.imgUrl) {
            resObj[imageForm.imageName] = getFileName(tmpObj[key]);
            resObj[imageForm.imagePath] = `${fsFrontEdit}/${subject}/news`;
        } else if (key === imageColDef.order) {
            resObj[imageForm.order] = tmpObj[key];
        } else if (key === imageColDef.urlId) {
            resObj[imageForm.urlId] = tmpObj[key];
        }
    });

    return resObj;
};

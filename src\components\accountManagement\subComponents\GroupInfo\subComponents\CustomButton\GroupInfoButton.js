import React, { useEffect, useState } from "react";

// plugins
import { Button } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";

//
import { TypeName } from "../../../Utils/compoConfig";
import accMngAct from "../../../../../../reduxStore/accManage/accManageAction";
import { getRandomID } from "../../../../../../api/firebase/cloudFirestore";
import frontendSettingsConfig from "../../../../../../config/config-frontendSettings";

function GroupInfoButton({ compoInfo }) {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { tableSelectPool } = state;
    const { typeName, btnName } = compoInfo;
    const [primBool, setPrimBool] = useState(false);
    const [negBool, setNegBool] = useState(false);
    const [disable, setDisable] = useState(false);

    useEffect(() => {
        switch (typeName) {
            case TypeName.GroupAdd:
                setPrimBool(true);
                break;
            case TypeName.GroupDelete:
                setNegBool(true);
                setDisable(true);
                break;
            default:
                break;
        }
    }, []);

    useEffect(() => {
        if (typeName === TypeName.GroupDelete) {
            setDisable(tableSelectPool?.groups?.length === 0);
        }
    }, [tableSelectPool]);

    const handleClick = () => {
        switch (typeName) {
            case TypeName.GroupAdd:
                {
                    const gid = getRandomID(frontendSettingsConfig.GROUPINFO);
                    dispatch({
                        type: accMngAct.SET_EDITGROUPMODE,
                        payload: true
                    });
                    dispatch({
                        type: accMngAct.SET_GROUPFIRESTOREID,
                        payload: gid
                    });
                    dispatch({
                        type: accMngAct.INIT_GROUPDATA
                    });
                }
                break;
            case TypeName.GroupDelete:
                dispatch({
                    type: accMngAct.SET_MODALCALLER,
                    payload: typeName
                });
                break;
            default:
                break;
        }
    };
    return (
        <Button
            className={typeName}
            onClick={() => handleClick()}
            primary={primBool}
            negative={negBool}
            disabled={disable}
        >
            {btnName}
        </Button>
    );
}

export default GroupInfoButton;

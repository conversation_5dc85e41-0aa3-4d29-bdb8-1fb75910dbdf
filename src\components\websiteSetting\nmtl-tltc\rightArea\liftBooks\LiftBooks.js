import React, { useCallback, useEffect, useState } from "react";

// css
import "./LiftBooks.scss";

// components
import { Form } from "semantic-ui-react";
import LanguageSelect from "../../../components/LanguageSelect";
import SaveButton from "../../../commons/components/SaveButton";

// store & api
import Api from "../../../../../api/nmtl/Api";
import { readNmtlData } from "../../../../../api/nmtl";

// utils

function LiftBooks() {
    const [language, setLanguage] = useState("");
    const [srcliftData, setSrcLiftData] = useState({}); // 資料原始值
    const [liftData, setLiftData] = useState({}); // 資料更新值

    const getData = () => {
        const apiStr = Api.getTltcliftPageData;
        readNmtlData(apiStr).then(res => {
            if (res?.data) {
                setSrcLiftData(res?.data[0] || {});
                setLiftData(res?.data[0] || {});
            }
        });
    };

    useEffect(() => {
        getData();
    }, []);

    // 用useCallback減少component UpdateText re-render問題
    const handleChange = useCallback(
        evt => {
            if (language === "zh") {
                setLiftData({ ...liftData, liftDescZH: evt.target.value });
            } else {
                setLiftData({ ...liftData, liftDescEN: evt.target.value });
            }
        },
        [language, liftData]
    );

    return (
        <div className="LiftBooks">
            <div className="topArea">
                <h1>LiFT書系</h1>
                <LanguageSelect language={language} setLanguage={setLanguage} />
            </div>
            <div className="textArea">
                <Form style={{ height: "100%", width: "100%" }}>
                    <textarea
                        style={{ resize: "none", height: "100%" }}
                        // disabled={isEditedDisable}
                        value={
                            language === "zh"
                                ? liftData.liftDescZH
                                : liftData.liftDescEN
                        }
                        onChange={handleChange}
                    />
                </Form>
            </div>
            <div className="btnArea">
                <SaveButton
                    srcData={srcliftData}
                    dstData={liftData}
                    closeCallBack={getData}
                />
            </div>
        </div>
    );
}

export default LiftBooks;

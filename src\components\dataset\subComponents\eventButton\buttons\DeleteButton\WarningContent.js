import React from "react";
import PropTypes from "prop-types";
import { Modal } from "semantic-ui-react";

// components
import PubContent from "./PubContent";

function WarningContent({ warnData }) {
    return (
        <Modal.Content>
            {Object.keys(warnData).map(warnKey => (
                // todo: 目前只有一種warning data，未來有需要增加再改寫 - 20240130(Bennis)
                <PubContent
                    key={warnKey}
                    warnKey={warnKey}
                    warningData={warnData[warnKey]}
                />
            ))}
        </Modal.Content>
    );
}

WarningContent.propTypes = {
    warnData: PropTypes.objectOf(
        PropTypes.objectOf(
            PropTypes.arrayOf(
                PropTypes.oneOfType([
                    PropTypes.string, // head
                    PropTypes.objectOf(PropTypes.string) // data
                ])
            )
        )
    )
};

WarningContent.defaultProps = {
    warnData: {}
};

export default WarningContent;

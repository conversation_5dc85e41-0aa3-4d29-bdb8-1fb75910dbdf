import React, { useState } from "react";
import { Provider } from "react-redux";

// semantic ui
import { Button } from "semantic-ui-react";

// components
import ImportModal from "./subComponents/ImportModal";

// store
import importStore from "./ImportReducer";

const SpecialImportButton = () => {
    const [open, setOpen] = useState(false);

    return (
        <Provider store={importStore}>
            <Button color="blue" floated="right" onClick={() => setOpen(true)}>
                匯入
            </Button>
            {open && <ImportModal open={open} setOpen={setOpen} />}
        </Provider>
    );
};

export default SpecialImportButton;

import React, { useContext, useEffect, useState } from 'react';
import { Select } from 'semantic-ui-react';
import { sortedResutls } from 'twchar';
import { StoreContext } from '../../../store/StoreProvider';
import Act from '../../../store/actions';
import { sortByPriority } from '../commons';

function Selector({ dropDown, placeholder = '選擇說明簡介選項' }) {
    const [state, dispatch] = useContext(StoreContext);
    const [sortOption, setSortOption] = useState([]);
    useEffect(() => {
        if (dropDown) {
            const tmpSelectOption = [];
            for (const [key, value] of Object.entries(dropDown)) {
                if (key !== 'id') {
                    if (value.priority) {
                        tmpSelectOption.push({
                            key,
                            value: key,
                            text: value.name,
                            priority: value.priority,
                        });
                    } else {
                        tmpSelectOption.push({
                            key,
                            value: key,
                            text: value.name,
                        });
                    }
                }
            }
            let sortResultData = tmpSelectOption;
            if (!tmpSelectOption[0]?.priority) {
                // select option has no priority property, order by "text" property
                sortResultData = sortedResutls(tmpSelectOption, 'text');
            } else {
                // select option has priority property
                sortResultData = sortByPriority(sortResultData);
            }
            setSortOption(sortResultData);
        }
    }, [dropDown]);

    return (
        <Select
            placeholder={placeholder}
            options={sortOption}
            onChange={(event, data) => {
                dispatch({
                    type: Act.SET_SELECTOPTION,
                    payload: data.value,
                });
            }}
            style={{ width: '50%' }}
            className="DropdownInfo"
        />
    );
}

export default Selector;

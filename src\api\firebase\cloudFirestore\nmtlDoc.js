import firebase from "firebase";
import storageDb from "./storageDb";
import frontendSettingsConfig from "../../../config/config-frontendSettings";

import Act from "../../../store/actions";

export const getNMTLDoc = docName =>
    storageDb.apiCollDocListener(
        firebase.firestore(),
        frontendSettingsConfig.NMTLWEB_COL,
        docName
    );

// export const writeNMTLKeyword = (keyword, serverKey) => {
//     const dataObj = { keyword };
//     storageDb.apiCollDocWriteSync(
//         firebase.firestore(),
//         frontendSettingsConfig.NMTLWEB_COL,
//         serverKey,
//         dataObj
//     );
// };
//
// export const writeNMTLStatus = (status, serverKey) => {
//     const dataObj = { status };
//     storageDb.apiCollDocWriteSync(
//         firebase.firestore(),
//         frontendSettingsConfig.NMTLWEB_COL,
//         serverKey,
//         dataObj
//     );
// };

export const writeNMTLDoc = (docName, dataObj, callback) => {
    storageDb.apiCollDocWriteSync(
        firebase.firestore(),
        frontendSettingsConfig.NMTLWEB_COL,
        docName,
        dataObj,
        callback
    );
};

export const writeNMTLDocRootPath = (rootPath, docName, dataObj, callback) => {
    storageDb.apiCollDocWriteSync(
        firebase.firestore(),
        rootPath,
        docName,
        dataObj,
        callback
    );
};

// export const removeNMTLDoc = serverKey => {
//     storageDb.apiCollDocDeleteSync(
//         firebase.firestore(),
//         frontendSettingsConfig.NMTLWEB_COL,
//         serverKey,
//         null
//     );
// };

// Server data change will return callback
export const checkServerActive = (dispatch) => {
    // console.log(`${whosecall} Call`);
    // wait for response
    storageDb.apiColMonitor(
        firebase.firestore(),
        frontendSettingsConfig.NMTLWEB_COL,
        data => {
            if (!data) {
                return;
            }

            const sheets = []; // 整理及時抓到的frontend-settings collection data
            for (const [key, value] of Object.entries(data)) {
                if (Object.keys(value).length === 0) {
                    sheets.push({ id: key });
                } else {
                    sheets.push({ id: key, ...value });
                }
            }
            // 讀取資料或是資料庫有變動時，及時存到realtimeData
            // 按下保存的時候，用來確認資料有無變動
            dispatch({
                type: Act.SET_REALTIMEDATA,
                payload: sheets
            });
        }
    );
};

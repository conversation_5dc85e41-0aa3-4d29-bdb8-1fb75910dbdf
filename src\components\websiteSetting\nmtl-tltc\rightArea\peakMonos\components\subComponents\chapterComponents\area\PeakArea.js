import React, { useCallback, useRef, useState } from "react";
import "../../../EditCate.scss";
import { debounce } from "lodash";
import { Grid } from "semantic-ui-react";
import CustomQuill from "../../CustomQuill";

import CustomDropdown from "../components/CustomDropdown";

const PeakArea = ({ updateFct, data, updatedData, optionLists }) => {
    const editorRef = useRef(null);
    const [value, setValue] = useState(data?.content);

    const debouncedUpdateFct = useCallback(
        debounce((tmpUpdatedData, tmpType, tmpValue) => {
            updateFct(tmpUpdatedData, tmpType, tmpValue);
        }, 300),
        []
    );

    return (
        <>
            <div className="topArea">
                <h1>Peak內頁編輯區</h1>
            </div>

            <Grid celled>
                <CustomDropdown
                    type="hasDescribedTarget"
                    rowName="作品描述之作家"
                    value={data?.hasDescribedTarget}
                    updatedData={updatedData}
                    subRowName="(可複選)"
                    debouncedUpdateFct={debouncedUpdateFct}
                    classType="Person"
                    optionList={optionLists?.writerList}
                />
            </Grid>
            <div key="editor">
                <CustomQuill
                    quillId="CustomEditorCate"
                    tmpRef={editorRef}
                    tmpValue={value}
                    onChangeFct={e => {
                        setValue(e);
                        debouncedUpdateFct(updatedData, "content", e);
                    }}
                />
            </div>
        </>
    );
};

export default PeakArea;

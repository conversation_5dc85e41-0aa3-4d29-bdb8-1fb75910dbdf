import React, { useEffect } from "react";
import "../../EditVillagesDetail.scss";
import { <PERSON><PERSON> } from "semantic-ui-react";
// import Gallery from "../../../../../../../common/imageCommon/gallery";
import axios from "axios";
import { useDispatch, useSelector } from "react-redux";
import Gallery from "../components/Gallery";
import {
    fileServerAPI,
    fileServerApiRoute,
    fileServerMethod
} from "../../../../../../../../api/fileServer";
import { uuidv4 } from "../../../../../../../../commons/utility";
import VillagesAct from "../../../VillagesAction";
import UploadConfig from "../../../../../../../toolPages/components/upload/uploadConfig";
import Api from "../../../../../../../../api/nmtl/Api";
import { readNmtlData } from "../../../../../../../../api/nmtl";

const TidbitsArea = ({ updateFct, data, updatedData }) => {
    const dispatch = useDispatch();
    const {
        curFolderFiles,
        curFolderFilesUrl,
        notifyUploadTidbits,
        editingVillageId
    } = useSelector(state => state);

    const handleImgUrl = (imageName, imageSize = UploadConfig.ImageSize) => {
        const pathMatch = imageName.match(
            // eslint-disable-next-line no-useless-escape
            /(?<directory>.+)?[\\\/](?<filename>[^\\\/]+)\.(?<extension>.+)$/
        );

        if (!pathMatch) return imageName;
        const { directory, filename, extension } = pathMatch.groups;
        return `${directory}/${imageSize}_${filename}.${extension}`;
    };

    const handleDelete = async () => {
        const middlePath = fileServerApiRoute.readUploadImage;
        let actionType = "";

        const images = curFolderFiles.checked
            .map(f => {
                const { value, actiontype } = f;
                if (actiontype === "image") {
                    actionType = "image";
                    const urlSplit = value.split(middlePath);
                    // 取得 /read/upload 後面的字串,並去掉 420x420_(或 200x200_等字串)
                    return (
                        urlSplit.length > 1 &&
                        urlSplit[1].replace(/[0-9]{3}[xX×╳][0-9]{3}_/, "")
                    );
                }
                return false;
            })
            .filter(url => url !== false);

        const uniImages = [...new Set(images)];
        const apiStr = Api.getVillageTidbitsList.replace("{ds}", "settings");
        const hasVillageTidbitsRes = await readNmtlData(apiStr);

        const disConnection = async () => {
            const imgIds = uniImages.map(url => {
                const imageName = url.split("/").pop();
                const match = hasVillageTidbitsRes?.data.find(
                    item => item.imageName === imageName
                );
                return match ? match.imgId : null;
            });
            const ogHasVillageTidbits = updatedData?.hasVillageTidbits.split(
                "、"
            );
            const leftData = ogHasVillageTidbits.filter(
                item => !imgIds.includes(item)
            );
            const entrySrc = {
                graph: "settings",
                classType: "VillageEvent",
                srcId: editingVillageId,
                value: {
                    hasVillageTidbits: [...ogHasVillageTidbits]
                }
            };
            const entryDst = {
                graph: "settings",
                classType: "VillageEvent",
                srcId: editingVillageId,
                value: {
                    hasVillageTidbits: [...leftData]
                }
            };

            await axios.put(Api.getGeneric, { entrySrc, entryDst }).then(() => {
                updateFct(
                    updatedData,
                    "hasVillageTidbits",
                    [...leftData].join("、")
                );
            });
        };
        await disConnection();

        return axios({
            method: fileServerMethod.deleteFile,
            url: fileServerAPI.deleteFile.replace("[type]", actionType),
            headers: {
                "Access-Control-Allow-Origin": "*"
            },
            data: {
                files: uniImages
            }
        })
            .then(res => {
                if (res.status === 200 && res.data.status === "success") {
                    const filesRemain = curFolderFilesUrl.filter(cff => {
                        const { type, originalUrl } = cff;
                        let furl;
                        if (type === UploadConfig.image) {
                            // eslint-disable-next-line prefer-destructuring
                            furl = originalUrl.split(middlePath)[1];
                        } else if (type === UploadConfig.file) {
                            furl = originalUrl.slice(
                                UploadConfig.FilePrePath.length
                            );
                        }
                        return uniImages.indexOf(furl) < 0;
                    });

                    dispatch({
                        type: VillagesAct.FOLDER_FILES_URL,
                        payload: filesRemain
                    });
                    // curFolderFiles.checked 清空
                    dispatch({
                        type: VillagesAct.CLEAR_CUR_FOLDER_FILES_STATUS
                    });
                }
            })
            .catch(() => {});
    };

    // 抓fileServer上的圖檔
    useEffect(() => {
        const getData = async () => {
            const res = await axios({
                method: fileServerMethod.getFolderFiles,
                url: fileServerAPI.getFolderFiles,
                headers: {
                    "Access-Control-Allow-Origin": "*"
                },
                // 增加 refresh key, 可以避掉 api cache
                params: {
                    folder: `/settings/villages/${process.env.REACT_APP_VILLAGES_TIDBITS_FILE_SERVER_URL}/${editingVillageId}`,
                    type: "image",
                    refresh: uuidv4()
                }
            });
            if (res.status === 200) {
                let fileList = res.data.data;
                fileList =
                    (fileList &&
                        fileList.length > 0 &&
                        fileList.map(({ imgUrl, imgInfo, imgFileName }) => ({
                            url: handleImgUrl(`${imgUrl}`),
                            originalUrl: imgUrl,
                            imgInfo,
                            imgFileName,
                            type: "image"
                        }))) ||
                    [];
                dispatch({
                    type: VillagesAct.FOLDER_FILES_URL,
                    payload: JSON.parse(JSON.stringify(fileList))
                });
            }
        };
        getData();
    }, [notifyUploadTidbits, editingVillageId]);

    return (
        <>
            <div className="topArea">
                <div className="topArea__title">
                    <h1>活動花絮</h1>
                </div>
                <div className="topArea__btn">
                    <Button
                        content="刪除"
                        style={{
                            background: "red",
                            color: "#fff",
                            fontSize: "12px",
                            cursor: "pointer",
                            textAlign: "center",
                            padding: "0.5rem 1rem",
                            borderRadius: "4px",
                            height: "100%"
                        }}
                        onClick={handleDelete}
                    />
                </div>
            </div>
            <aside
                className="thumbsContainer"
                style={{
                    textAlign: "left",
                    height: "auto"
                }}
            >
                <Gallery
                    images={curFolderFilesUrl}
                    updateFct={updateFct}
                    data={data}
                    updatedData={updatedData}
                />
            </aside>
        </>
    );
};

export default TidbitsArea;

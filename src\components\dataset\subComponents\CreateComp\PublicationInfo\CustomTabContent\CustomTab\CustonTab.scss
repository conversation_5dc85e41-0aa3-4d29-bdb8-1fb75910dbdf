.tab{
  &__wrapped{
    display: flex;
    flex-wrap: wrap;
    gap: .5rem;
    &__firstMenu{
      border: 1px solid #21ba45 !important;
      background-color: #fff !important;
      color :#21ba45 !important;
      &__iconPlus{
        margin: 0 !important;
        //margin: 0 !important;
      }
      //&__textContent{
      //  margin-left: 1rem !important;
      //}
      &:hover {
        background-color: #e4f5e8 !important;
      }
    }
    &__menuItem{
      background-color: #e4f5e8 !important;
      color:#21ba45 !important;
      &__deleteIcon{
        display: flex  !important;
        justify-content: flex-end  !important;
        align-Items: center  !important;
        margin-left: 1rem  !important;
        margin-right: 0  !important;
        &:hover{
          color: #e4f5e8;
        }
      }
      &:hover {
        background-color: #21ba45 !important;
        color:#e4f5e8 !important;
      }
    }

    &__menuItemFocus{
      background-color: #21ba45 !important;
      color: #fff !important;
      &__deleteIcon{
        display: flex  !important;
        justify-content: flex-end  !important;
        align-Items: center  !important;
        margin-left: 1rem  !important;
        margin-right: 0  !important;
        &:hover{
          color: #e4f5e8;
        }
      }
    }
  }

}
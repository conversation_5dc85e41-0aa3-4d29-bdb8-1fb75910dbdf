import React from "react";

// plugins
import { useSelector } from "react-redux";

//
import textConfig from "../../../Utils/textConifg";

// diffTables
import UserTable from "./diffTables/UserTable";
import GroupTable from "./diffTables/GroupTable";

function CustomTable() {
    const state = useSelector(tmpState => tmpState.accMng);
    const { isEditGroup, groDSelectItem } = state;

    const showUserTable = () => {
        switch (groDSelectItem.name) {
            case textConfig.GROUPINFO_MENUBAR_MEMBERINFO:
                return <UserTable />;
            default:
                return <span>目前無資料</span>;
        }
    };

    return (
        <React.Fragment>
            {isEditGroup ? showUserTable() : <GroupTable />}
        </React.Fragment>
    );
}

export default CustomTable;

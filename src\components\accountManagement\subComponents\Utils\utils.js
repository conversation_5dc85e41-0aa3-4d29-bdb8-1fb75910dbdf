import { writeNMTLDocRootPath } from "../../../../api/firebase/cloudFirestore/nmtlDoc";
import { GROUPINFO } from "../../../../config/config-frontendSettings";
import accMngAct from "../../../../reduxStore/accManage/accManageAction";
import textConfig from "./textConifg";
import { getSingleLayerCollection } from "../../../../api/firebase/cloudFirestore";
import getAllGPInfo from "../../../../data/firebase/utils";

const saveDataCallBack = (res, dispatch, user, dispatchContext) => {
    if (res.error) {
        // 儲存資料遇到錯誤顯示視窗
        console.log(res.error);
        dispatch({
            type: accMngAct.SET_MODALMESSAGE,
            payload: textConfig.ErrorMessage.ErrorInternal
        });
        dispatch({
            type: accMngAct.SET_MODALCALLER,
            payload: textConfig.ErrorMessage.callerName
        });
    } else {
        res.then(async () => {
            dispatch({
                type: accMngAct.SET_MODALMESSAGE,
                payload: textConfig.SuccessMessage.SuccessBody
            });
            dispatch({
                type: accMngAct.SET_MODALCALLER,
                payload: textConfig.SuccessMessage.callerName
            });

            // update user groupInfo
            getAllGPInfo(user, dispatchContext);

            console.log("Save Data success !!");
        }).catch(error => {
            dispatch({
                type: accMngAct.SET_MODALMESSAGE,
                payload: textConfig.ErrorMessage.ErrorBody
            });
            dispatch({
                type: accMngAct.SET_MODALCALLER,
                payload: textConfig.ErrorMessage.callerName
            });
            console.log("error ", error);
        });
    }
};

const saveGroupData = (docName, groupData, dispatch, user, dispatchContext) => {
    if (groupData.name) {
        const tmpObj = JSON.parse(JSON.stringify(groupData));
        delete tmpObj.id;
        writeNMTLDocRootPath(GROUPINFO, docName, tmpObj, (emptyParam, res) =>
            saveDataCallBack(res, dispatch, user, dispatchContext)
        );
    } else {
        dispatch({
            type: accMngAct.SET_MODALCALLER,
            payload: textConfig.ColumnCheck.callerName
        });
        dispatch({
            type: accMngAct.SET_MODALMESSAGE,
            payload: textConfig.ColumnCheck.EmptyGroupName
        });
    }
};

const getGroupData = dispatch => {
    getSingleLayerCollection(GROUPINFO)
        .then(result => {
            dispatch({
                type: accMngAct.SET_ALLGROUPDATA,
                payload: result
            });
        })
        .catch(error => {
            console.log(error);
        });
};

export { saveGroupData, getGroupData };

import { splitTag } from "../config/config";

const checkLangCol = (cell, propLabel, langList) => {
    let tmpResStr = "";

    if (cell.value) {
        const allLang = langList.map(el => el.label);
        if (typeof cell.value === "string") {
            const check = cell.value
                .split(splitTag)
                .some(val => !allLang.includes(val));

            if (check) {
                tmpResStr += `${cell.address}, [${
                    cell.value
                }], 欄位:${propLabel}，只能填寫下列語系，若有多個請換行填寫。\n [${allLang.join(
                    "、"
                )}]。\n`;
            }
        }
    }

    return tmpResStr;
};

export default checkLangCol;

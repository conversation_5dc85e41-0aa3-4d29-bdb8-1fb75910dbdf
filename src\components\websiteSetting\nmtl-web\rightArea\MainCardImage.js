import React, { useContext, useEffect, useState } from 'react';

// semantic-ui-react
import { Image } from 'semantic-ui-react';

// components
import Selector from '../../components/Selector';
import SaveButton from '../../components/SaveButton';
import UploadImageModal from '../../components/UploadImageModal';

// general
import dragImage from '../../../../images/dragImage.svg';
import { StoreContext } from '../../../../store/StoreProvider';
import Act from '../../../../store/actions';
import { setDropDownFromMsListData } from '../../commons';

function MainCardImage() {
    const [state, dispatch] = useContext(StoreContext);
    const {
        originData,
        updatedData,
        menuActiveItem,
        selectOption,
        msListData,
    } = state.websiteSetting;
    const [dropDown, setDropDown] = useState({});
    const [openModal, setOpenModal] = useState(false);
    const [imageUrl, setImageUrl] = useState('');

    useEffect(() => {
        // 下拉選單選項清空
        dispatch({
            type: Act.SET_SELECTOPTION,
            payload: '',
        });
    }, [menuActiveItem]);

    useEffect(() => {
        if (originData.length !== 0) {
            const tmpObj = originData.find((element) => element.id === menuActiveItem.key);
            if (selectOption !== null && selectOption !== '' && tmpObj[selectOption]) {
                setImageUrl(tmpObj[selectOption].url);
            }
        }
    }, [selectOption]);

    useEffect(() => {
        if (originData.length !== 0 && msListData.length !== 0) {
            const originObj = originData.find((element) => element.id === menuActiveItem.key);
            const originArray = Object.keys(originObj);
            const msListArray = msListData.filter((element) => element.type === 'ms');
            const newDropDown = Object.assign(
                {},
                originObj,
                setDropDownFromMsListData(originArray, msListArray, 'MainCardImage'),
            );
            setDropDown(newDropDown);
            // 更新 updatedData
            const tmpAllData = JSON.parse(JSON.stringify(updatedData));
            const tmpObjIndex = tmpAllData.findIndex(
                (element) => element.id === menuActiveItem.key,
            );
            tmpAllData[tmpObjIndex] = newDropDown;
            // console.log("tmpAllData ", tmpAllData);
            dispatch({
                type: Act.SET_UPDATEDDATA,
                payload: tmpAllData,
            });
        }
    }, [originData, msListData]);

    return (
        <div className="MainCardImage">
            <div className="Selector">
                <Selector dropDown={dropDown} />
            </div>
            <div className="DropFileRegion">
                <Image
                    style={{
                        cursor: selectOption === '' ? 'default' : 'pointer',
                    }}
                    src={imageUrl !== '' ? imageUrl : dragImage}
                    onClick={() => {
                        setOpenModal(true);
                    }}
                />
            </div>
            <div className="btnArea">
                <SaveButton language="" />
            </div>
            {openModal && <UploadImageModal setOpenModal={setOpenModal} setAllData={setImageUrl} />}
        </div>
    );
}

export default MainCardImage;

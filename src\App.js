// React
import React from "react";
import { BrowserRouter } from "react-router-dom";

// styled-components
import { createGlobalStyle } from "styled-components";

// firebase
import firebase from "firebase/app";
import firebaseConfig from "./config/config-firebase";

// components
import Header from "./main/Header";
import Body from "./main/Body";
// import Footer from './components/Footer';

// CSS
import "semantic-ui-css/semantic.min.css";

// store
import StoreProvider from "./store/StoreProvider";

// reduxStore
import IndexProvider from "./reduxStore/IndexProvider";

// data
import DataLayer from "./data/DataLayer";

firebase.initializeApp(firebaseConfig);

// override semantic-ui css style
const GlobalStyle = createGlobalStyle`
  body, textarea, .ui.input>input {
    font-family: tauhu-oo, "Noto Sans TC";
  }
`;

// main
function App() {
    return (
        // 使用 BrowserRouter 建立 history object
        <BrowserRouter>
            {/* 在最上層提供 global store */}
            <StoreProvider>
                <IndexProvider>
                    {/* 載入資料相關區 */}
                    <DataLayer />
                    {/* 網站樣式 */}
                    <GlobalStyle />
                    {/* 頁面頂層連結區, 如要變更內容請至 APP-layout.js */}
                    <Header />
                    {/* 頁面跳轉處理區, 如要變更內容請至 APP-layout.js */}
                    <Body />
                    {/* <Footer /> */}
                </IndexProvider>
            </StoreProvider>
        </BrowserRouter>
    );
}

export default App;

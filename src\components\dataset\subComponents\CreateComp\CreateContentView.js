import React, { useContext, useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { Grid, Container, Button } from 'semantic-ui-react';
import { isEqual } from 'lodash';
import { StoreContext } from '../../../../store/StoreProvider';

import { createConfig, GLOBAL_CREATE_PATH, sheetCreateView } from './createConfig';
import './CreateContentView.scss';

import { isEmpty, isObjectEqual } from '../../../../commons';
import Api from '../../../../api/nmtl/Api';
import { assignSubValues, getReservedNewId } from '../../../common/sheetCrud/utils';
import {
  bindSP,
  checkCellValue,
  splitMultiValues,
  convertToGeneric,
  IMAGE_URL,
  SubValues,
  TranslationBookName,
  IsTranslationBookOf,
  LabelPublication,
  PublicationInfo,
  TranslationDefault,
  LOCATION_LABEL,
  LOCATION_KEY,
  LOCATION_LAT,
  LOCATION_TYPE,
  LOCATION_LONG,
  checkPropertyData,
  LOCATION_NAME,
  GLOBAL_DEFAULT,
  mappingLabel,
} from '../../../common/sheetCrud/sheetCrudHelper';
import { readNmtlData, updateNmtlData } from '../../../../api/nmtl';
import Act from '../../../../store/actions';
import { addSuffix, getApiByAllField, isCorrectSuffix } from '../../../../api/nmtl/ApiField';
import arrayMerge from '../../../../commons/arrayMerge';
import { createHistoryEvent } from '../../../downloadData/components/history/common/common';
import AlertMessage from './alertMessage';
import { hasTranslationBook } from './PublicationInfo/PublicationInfoConfig';

import NextStepModal from './NextStepModal/NextStepModal';

import {
  findSameIdList,
  getCreateNmtlItemResult,
  handleCreateSrcId,
  mergeStates,
  transformDefaultLabelPublication,
} from './helper';
import { createPeakData } from '../../datasetConfig';

const GLOBAL_PUBINFO_API = 'backend/dl_publicationInfo/caseInfo/edit/2.0';

const CreateContentView = () => {
  const [state, dispatch] = useContext(StoreContext);
  const { user } = state;
  const {
    mainSubject,
    sheet,
    content: ct,
    isFillAllAuthorOtherName,
    isFillAllTranslatorOtherName,
    cloneCreateStateForUpdate,
    cloneLocalCreateState2,
  } = state.data;
  const { dataset } = mainSubject.selected;
  const {
    key: sheetName,
    contentWritePath,
    contentReadPath,
    contentInfoPath,
    hasTab,
  } = sheet.selected;
  const { displayName } = state.user;
  const { headerActiveName } = state.common;
  const { tabIndex } = sheet.tabKey;
  const { headerFields } = sheet;

  const history = useHistory();
  const [open, setOpen] = useState(false);
  const [action, setAction] = useState();
  const [createState, setCreateState] = useState({ [GLOBAL_DEFAULT]: {} });
  const [srcState, setSrcState] = useState(null);
  const [header, setHeader] = useState(sheet.header || []);
  const [equalValue, setEqualValue] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const { id: editId } = useParams();
  const columns = [headerActiveName, mainSubject.selected.value, sheet.selected.value];

  // FIXME: Vincent 新增相同 id 的時候，抓資料的 API 要改
  const apiReadPath =
    contentInfoPath ||
    contentReadPath ||
    (hasTab ? hasTab[tabIndex]?.contentReadPath || null : null);

  useEffect(() => {
    setHeader(sheet.header || []);
  }, [sheetName, sheet.header]);

  const getInformation = async (path, id, ds) => {
    const urlPath = `${Api.getBaseUrl}/${path}`;
    const parameter = `ds=${ds}&limit=-1&offset=0&ids=${id}`;
    const readPath = `${urlPath}?${parameter}`;
    const { data } = await readNmtlData(readPath);
    if (!data) return { data: { [GLOBAL_DEFAULT]: {} } };

    const mergedData = arrayMerge.sheet(data);
    const subData = await assignSubValues(mergedData, dataset);

    // 進入編輯頁只會有一筆id
    // const finalData = subData[0];
    const finalData = {};
    const transbookData = {};
    if (sheetName === PublicationInfo) {
      subData.forEach((sdata) => {
        let objKey = '';
        if (Object.hasOwn(sdata, TranslationBookName)) {
          objKey = `${sdata[TranslationBookName][0]}_${sdata.srcId}`;
        } else if (Object.hasOwn(sdata, LabelPublication)) {
          objKey = TranslationDefault;
        }

        transbookData[objKey] = {};

        Object.keys(sdata).forEach((key) => {
          if (key === 'srcId') {
            transbookData[objKey][key] = sdata[key];
            if (objKey !== TranslationDefault) {
              transbookData[objKey][hasTranslationBook] = sdata[key];
            }
            return;
          }

          if (key === IMAGE_URL) {
            transbookData[objKey][key] = splitMultiValues(key, sdata[key]).join('\n');
            return;
          }

          transbookData[objKey][key] = splitMultiValues(key, sdata[key]);
        });
      });
    } else {
      Object.keys(subData[0]).forEach((key) => {
        if (key === 'srcId') {
          finalData[key] = subData[0][key];
          return;
        }

        if (key === IMAGE_URL) {
          const [imgUrl] = splitMultiValues(key, subData[0][key]);
          finalData[key] = imgUrl;
          return;
        }

        finalData[key] = splitMultiValues(key, subData[0][key]);
      });
    }

    // transbookData:
    // {default: {...}, default0: {...}, default1: {...}}
    if (Object.keys(transbookData).length > 0) {
      return transbookData;
    }
    return { [GLOBAL_DEFAULT]: finalData };
  };

  const handleGetContent = async (id, ds) => {
    if (!apiReadPath) {
      return;
    }

    let data;

    if (sheetName === PublicationInfo) {
      data = await getInformation(GLOBAL_PUBINFO_API, id, ds);
    } else {
      data = await getInformation(apiReadPath, id, ds);
    }

    setCreateState(JSON.parse(JSON.stringify(data)));
    setSrcState(data);
  };

  const setCallback = (cellId, jsonVal, menuName) =>
    setCreateState((preState) => {
      const newMenuName = menuName || GLOBAL_DEFAULT;

      const newState = {
        ...preState,
        [newMenuName]: {
          ...(preState && preState[newMenuName] ? preState[newMenuName] : {}),
          ...{ [cellId]: jsonVal },
        },
      };
      // delete newState[menuName || "default"];

      // richInfo
      // newRow[cellId] = JSON.parse(JSON.stringify(jsonVal));

      // subValue 特殊處理
      Object.keys(SubValues).forEach((subKey) => {
        const { type, property } = SubValues[subKey];

        if (cellId.endsWith(subKey) || cellId.endsWith(type)) {
          const DotCom = process.env.REACT_APP_DOMAIN_SUFFIX;
          // 其它的值都要複製一份出來
          property.forEach((subP) => {
            const bindSubP = subP === 'label' ? bindSP(subP, type) : bindSP(subP, subKey);
            if (Object.keys(newState[newMenuName]).indexOf(bindSubP) < 0) {
              // eslint-disable-next-line no-param-reassign
              newState[newMenuName][bindSubP] = {};
            }
            // eslint-disable-next-line no-param-reassign
            newState[newMenuName][bindSubP] = isEmpty(newState[newMenuName][bindSubP])
              ? null
              : newState[newMenuName][bindSubP];
          });

          if (Object.keys(newState[newMenuName]).indexOf(subKey) < 0) {
            // eslint-disable-next-line no-param-reassign
            newState[newMenuName][subKey] = {};
          }
          // arrangeSubValue 依靠 hasURL/hasSource/hasFormat 來判斷有幾筆
          // eslint-disable-next-line no-param-reassign
          newState[newMenuName][subKey] = isEmpty(newState[newMenuName][subKey])
            ? null
            : newState[newMenuName][subKey];

          // Special Cases: 需要產生新的 property
          // imageURL_hasURL: 其它的值也要一起改，如：imagePath_hasURL, imageName_hasURL
          // "https://fs-root.daoyidh.com/read/upload/portrait/420x420_forest.tiff"
          if (cellId === IMAGE_URL) {
            // const fullImgUrl = cellValue[idx];
            const fullImgUrl = jsonVal;
            let imgPath = null;
            let imgName = null;
            // 需要同時修正 imagePath_hasURL, imageName_hasURL
            const posCom = fullImgUrl.indexOf(DotCom);
            if (posCom > -1) {
              // found: read/upload/portrait/420x420_forest.tiff
              const imgUrl = fullImgUrl.slice(posCom + DotCom.length + 1);

              // 取 imagePath, imageName
              const startSlash = imgUrl.indexOf('/');
              const posSlash = imgUrl.lastIndexOf('/');

              imgPath = imgUrl.slice(startSlash + 1, posSlash);
              imgName = imgUrl.slice(posSlash + 1);
              // 400x400_filename
              const posUnder = imgName.indexOf('_');
              imgName = imgName.slice(posUnder + 1);
            }

            // FIXME: 應該要有個機制來做這個對應
            // eslint-disable-next-line no-param-reassign
            newState[newMenuName].imagePath_hasURL = imgPath;
            // eslint-disable-next-line no-param-reassign
            newState[newMenuName].imageName_hasURL = imgName;
          }

          // 針對地點修改
          if (cellId === LOCATION_LABEL) {
            const v = jsonVal ? jsonVal[0] : null;
            if (v) {
              const { newApi } = isCorrectSuffix(cellId, v);
              if (Object.keys(headerFields).indexOf(newApi) > -1) {
                const tmpObj = headerFields[newApi].find((el) => el.id === v);
                if (!tmpObj) {
                  return;
                }
                newState[newMenuName][LOCATION_NAME] = tmpObj.label;

                newState[newMenuName][subKey] = jsonVal;
              }
            } else {
              newState[newMenuName][LOCATION_NAME] = null;
              newState[newMenuName][LOCATION_LAT] = null;
              newState[newMenuName][LOCATION_LONG] = null;
              newState[newMenuName][subKey] = null;
            }
          }
        }
      });

      // console.log("newState ::", newState);
      return newState;
    });

  const getEditNmtlItemResult = async (data, srcData) => {
    const apiWritePath = contentWritePath || hasTab[tabIndex]?.contentWritePath || null;
    //
    if (isEmpty(data) || isEmpty(dataset) || isEmpty(sheetName)) {
      return null;
    }

    const apiUrl = Api.getGeneric;
    const dt = mappingLabel(data, headerFields);

    // 取消拖拉編輯模式
    dispatch({
      type: Act.DATA_SET_IS_AUTHOR_IN_DRAGGINGMODE,
      payload: false,
    });
    dispatch({
      type: Act.DATA_SET_IS_TRANSLATOR_IN_DRAGGINGMODE,
      payload: false,
    });

    const updateSpecificKeys = (source, target, keys) => {
      if (isEmpty(source)) return;
      keys.forEach((key) => {
        if (key === 'authorName') {
          if (target?.hasAuthor?.includes('create')) {
            // eslint-disable-next-line no-param-reassign
            target[key] = [''];
            return;
          }
        }
        if (key === 'translatorName') {
          if (target?.hasTranslator?.includes('create')) {
            // eslint-disable-next-line no-param-reassign
            target[key] = [''];
            return;
          }
        }
        // eslint-disable-next-line no-param-reassign
        target[key] = source[key];
      });
    };

    function transformLabelPerson(tmpData) {
      if (isEmpty(tmpData)) return [];
      return tmpData.map((item) => {
        if (item.value && item.value.label_Person && item.classType === 'Person') {
          const labelPerson = item.value.label_Person;
          const match = labelPerson.match(/^([^(]+)/);
          if (match) {
            const mainName = match[1].trim();
            const langTag = labelPerson.split('@').pop();
            // eslint-disable-next-line no-param-reassign

            // 如果 mainName 已經包含語言標籤，則不再添加
            if (!mainName.endsWith(`@${langTag}`)) {
              // eslint-disable-next-line no-param-reassign
              item.value.label_Person = `${mainName}@${langTag}`;
            } else {
              // eslint-disable-next-line no-param-reassign
              item.value.label_Person = `${mainName}`;
            }
          }
        }
        return item;
      });
    }

    // 修改 hasAuthor, hasTranslator, hasEditor, hasPublisher 的值
    const processEntry = (entryDst) => {
      const fields = ['hasEditor', 'hasPublisher', 'hasAuthor', 'hasTranslator'];

      fields.forEach((field) => {
        if (entryDst?.value?.[field]) {
          // eslint-disable-next-line no-param-reassign
          entryDst.value[field] = transformLabelPerson(entryDst.value[field]);
        }
      });

      return entryDst;
    };

    // 將以下四個key的value更換成拖拉的結果
    updateSpecificKeys(
      // cloneCreateStateForUpdate,
      cloneLocalCreateState2.filter((item) => item.srcId === data.srcId)[0],
      dt,
      ['hasAuthor', 'hasTranslator', 'authorName', 'translatorName'],
    );

    // 清空作者及譯者拖拉的結果
    dispatch({
      type: Act.DATA_CLEAR_CLONE_CREATE_STATE,
    });

    const [entrySrc, entryDst] = convertToGeneric(srcData, dt, apiWritePath, dataset, true);
    processEntry(entryDst);

    const pekId = await getReservedNewId('PeakMono');

    // 不用更新
    if (isObjectEqual(entrySrc, entryDst)) {
      return {
        updateResult: '資料相同，不用更新',
        LocationUpdateResult: true,
        historyMsg: '資料相同，不用更新',
      };
    }

    if (entrySrc && entryDst) {
      // Create peak connection
      if (
        (isEmpty(entrySrc?.value?.peak) || entrySrc?.value?.peak[0] === 'false') &&
        !isEmpty(entryDst?.value?.peak) &&
        entryDst?.value?.peak[0] === 'true'
      ) {
        createPeakData(user, entryDst?.srcId, pekId);
      }

      // Remove peak connection
      if (
        !isEmpty(entrySrc?.value?.peak) &&
        entrySrc?.value?.peak[0] === 'true' &&
        entryDst?.value?.peak[0] !== 'true'
      ) {
        if (entryDst?.value?.hasPeakMono) {
          delete entryDst?.value?.hasPeakMono;
        }
      }

      const historyMsg = `${JSON.stringify(entrySrc)}\n變動後：\n${JSON.stringify(entryDst)}`;

      const updateResult = await updateNmtlData(
        user,
        apiUrl,
        dataset,
        sheetName,
        entrySrc,
        entryDst,
      ).then((res) => res === 'OK');

      // 針對地點修改
      if (Object.hasOwn(data, LOCATION_KEY)) {
        const dstLoc = data[LOCATION_KEY] || [];
        const srcLoc = srcData[LOCATION_KEY] || [];

        // 比對兩陣列內容物是否相同，相同時代表修改Instance內容，不相同表示連結至新的Instance
        const checkSame = checkPropertyData(Object.values(dstLoc), Object.values(srcLoc));

        if (Object.hasOwn(entryDst.value, LOCATION_KEY) && !checkSame) {
          const newLocation = entryDst.value[LOCATION_KEY][0];

          if (newLocation) {
            const tmpValue = await assignSubValues(
              [{ [LOCATION_KEY]: { 0: newLocation } }],
              dataset,
            );

            const oldValue = tmpValue[0];

            const tmpLat = oldValue[LOCATION_LAT] ? Object.values(oldValue[LOCATION_LAT]) : null;
            const tmpLong = oldValue[LOCATION_LONG] ? Object.values(oldValue[LOCATION_LONG]) : null;

            const newSrcJson = {
              graph: dataset,
              srcId: newLocation,
              classType: LOCATION_TYPE,
              value: {
                geoLongitude: tmpLong || null,
                geoLatitude: tmpLat || null,
              },
            };
            const newDstJson = {
              graph: dataset,
              srcId: newLocation,
              classType: LOCATION_TYPE,
              value: {
                geoLongitude: data[LOCATION_LONG] || null,
                geoLatitude: data[LOCATION_LAT] || null,
              },
            };

            if (isObjectEqual(newSrcJson, newDstJson)) {
              return {
                updateResult,
                LocationUpdateResult: true,
                historyMsg,
              };
            }

            const LocationUpdateResult = await updateNmtlData(
              user,
              apiUrl,
              dataset,
              sheetName,
              newSrcJson,
              newDstJson,
            ).then((res) => res === 'OK');

            return {
              updateResult,
              LocationUpdateResult,
              historyMsg,
            };
          }
        }
      }

      return { updateResult, LocationUpdateResult: true, historyMsg };
    }

    return { historyMsg: `${entrySrc.srcId} 更新失敗` };
  };

  const handleCancel = () => {
    setCreateState({ [GLOBAL_DEFAULT]: {} });

    dispatch({
      type: Act.DATA_CONTENT_CURRENT_BOOK_LANGUAGE,
      payload: '',
    });

    // 取消拖拉編輯模式
    dispatch({
      type: Act.DATA_SET_IS_AUTHOR_IN_DRAGGINGMODE,
      payload: false,
    });
    dispatch({
      type: Act.DATA_SET_IS_TRANSLATOR_IN_DRAGGINGMODE,
      payload: false,
    });

    // 清空作者及譯者拖拉的結果
    dispatch({
      type: Act.DATA_CLEAR_CLONE_CREATE_STATE,
    });
    history.push('/Dataset');
  };

  const handleCreate = async () => {
    if (!createState || !header) return;

    let errMsg = '';
    let successCount = 0;
    let errorCount = 0;
    const createInfo = [];

    // 檢查作者發表名稱、譯者顯示名稱是否有填寫完成
    if (!isFillAllAuthorOtherName || !isFillAllTranslatorOtherName) {
      dispatch({
        type: Act.DATA_SET_IS_FILL_ALL_OTHERNAME_MODAL_OPEN,
        payload: true,
      });
      return;
    }

    dispatch({
      type: Act.DATA_SET_IS_AUTHOR_IN_DRAGGINGMODE,
      payload: false,
    });
    dispatch({
      type: Act.DATA_SET_IS_TRANSLATOR_IN_DRAGGINGMODE,
      payload: false,
    });

    if (
      Object.hasOwn(createState, GLOBAL_DEFAULT) &&
      Object.hasOwn(createState[GLOBAL_DEFAULT], LabelPublication)
    ) {
      const apiWritePath = contentWritePath || hasTab[tabIndex]?.contentWritePath || null;

      const { label_Publication: pubLabel, srcId } = createState[GLOBAL_DEFAULT];

      // eslint-disable-next-line no-nested-ternary
      const label = pubLabel ? (Array.isArray(pubLabel) ? pubLabel[0] : pubLabel) : null;

      if (!label) {
        errMsg += `label_Publication 不可為空*。\n`;
      }

      const { newApi } = isCorrectSuffix(sheetName, label);

      const { newSrcId, createResult } = await handleCreateSrcId(
        user,
        apiWritePath,
        dataset,
        sheetName,
        action,
        newApi,
        pubLabel,
        srcId,
        headerFields,
      );

      if (newSrcId && createResult) {
        createState[GLOBAL_DEFAULT].srcId = newSrcId;
        successCount += 1;
        createInfo.push(newSrcId);
      } else if (newSrcId && !createResult) {
        // 編輯時創建翻譯書，但不需要創建原文書
        createState[GLOBAL_DEFAULT].srcId = newSrcId;
      } else {
        // eslint-disable-next-line camelcase
        errMsg += `新增 ${pubLabel} 失敗。\n`;
        dispatch({
          type: Act.DATA_INFO_MESSAGE,
          payload: {
            title: `新增失敗, ${errMsg}`,
            error: 1,
            renderSignal: `update-${new Date().getTime()}`,
          },
        });
        errorCount += 1;
      }
    }
    // createState: {default: {label_Publication: xxxx}, default0: {xxx: 111}, default1: {xxx: 222}}
    for (const transBook in createState) {
      const data = createState[transBook];
      // console.log(data);
      // FIXME::
      if (transBook === GLOBAL_DEFAULT && sheetName === PublicationInfo) {
        continue;
      }

      if (
        sheetName === PublicationInfo &&
        Object.hasOwn(data, 'srcId') &&
        !data?.srcId?.startsWith('default')
      ) {
        // 編輯時新增翻譯書，如果有srcId不需要新增
        continue;
      }

      // FIXME::
      // 確認必填欄位是否填寫, 將有問題的欄位回傳
      const { incorrectData, errorMsg } = checkCellValue(header, data);

      // 填寫格式錯誤或是空白
      if (!isEmpty(incorrectData)) {
        dispatch({
          type: Act.DATA_INFO_MESSAGE,
          payload: {
            title: `新增失敗，請重新檢查資料是否填寫正確,${errorMsg}`,
            error: 1,
            renderSignal: `update-${new Date().getTime()}`,
          },
        });
        return;
      }

      // specialTable : val="", 因為值不只有姓名，不再丟進去判斷
      const { newApi } = isCorrectSuffix(sheetName, '');
      if (!data || !newApi || !sheetName) {
        return;
      }

      const apiWritePath = contentWritePath || hasTab[tabIndex]?.contentWritePath || null;

      // 轉換 property, 在翻譯書目才有
      if (Object.hasOwn(data, TranslationBookName)) {
        data[IsTranslationBookOf] = createState[GLOBAL_DEFAULT].srcId;
        data[LabelPublication] = [
          `${data[TranslationBookName]}@zh`,
          `${data[TranslationBookName]}@en`,
        ];
      }

      // console.log("data", data);
      const { newSrcId, createResult } = await getCreateNmtlItemResult(
        user,
        apiWritePath,
        dataset,
        sheetName,
        data,
        newApi,
        headerFields,
        cloneLocalCreateState2.filter((item) => item.srcId === data.srcId)[0],
      );

      // 清空作者及譯者拖拉的結果
      dispatch({
        type: Act.DATA_CLEAR_CLONE_CREATE_STATE,
      });

      if (newSrcId && createResult) {
        createInfo.push(newSrcId);
        successCount += 1;
      } else {
        errorCount += 1;
      }
    }

    if (errorCount === 0) {
      dispatch({
        type: Act.DATA_MESSAGE,
        payload: {
          title: `${createInfo.join('、')} 已創建完成`,
          success: successCount,
          error: errorCount,
          renderSignal: `update-${new Date().getTime()}`,
        },
      });

      createHistoryEvent(displayName, '創建', `${columns.join('/')}：${createInfo.join('、')}`);
    } else {
      dispatch({
        type: Act.DATA_MESSAGE,
        payload: {
          title: `${createInfo.join('、')}  創建失敗`,
          success: successCount,
          error: errorCount,
          renderSignal: `update-${new Date().getTime()}`,
        },
      });

      createHistoryEvent(displayName, '創建失敗', `${columns.join('/')}：${createInfo.join('、')}`);
    }

    setCreateState({ [GLOBAL_DEFAULT]: {} });
    history.push('/Dataset');
  };

  const handleEdit = async () => {
    if (!createState || !header) return;

    // 檢查作者發表名稱、譯者顯示名稱是否有填寫完成
    if (!isFillAllAuthorOtherName || !isFillAllTranslatorOtherName) {
      dispatch({
        type: Act.DATA_SET_IS_FILL_ALL_OTHERNAME_MODAL_OPEN,
        payload: true,
      });
      return;
    }

    let successCount = 0;
    let errorCount = 0;
    const editInfo = [];

    for (const transBook in createState) {
      const data = createState[transBook];
      const srcData = srcState[transBook];

      // 在件層級編輯時，判斷是否有編輯過(原文書不判斷，須更新)
      if (transBook !== 'default' && ct.curBookLang !== '') {
        const cloneCreateStateForAuthorAndTranslator = cloneLocalCreateState2.filter(
          (item) => item.srcId === data.srcId,
        )[0];
        const newCreateState = mergeStates(cloneCreateStateForAuthorAndTranslator, data);
        const filteredObj = Object.keys(newCreateState).reduce((acc, key) => {
          if (newCreateState[key] !== null) acc[key] = newCreateState[key];
          return acc;
        }, {});

        const isUpdateAnyValue = isEqual(filteredObj, srcData);
        if (isUpdateAnyValue) continue;
      }
      // FIXME::
      // 確認必填欄位是否填寫, 將有問題的欄位回傳
      const { incorrectData, errorMsg } = checkCellValue(header, data, transBook, sheetName);

      // 填寫格式錯誤或是空白
      if (!isEmpty(incorrectData)) {
        dispatch({
          type: Act.DATA_INFO_MESSAGE,
          payload: {
            title: `新增失敗，請重新檢查資料是否填寫正確,${errorMsg}`,
            error: 1,
            renderSignal: `update-${new Date().getTime()}`,
          },
        });
        return;
      }

      if (!data || !sheetName) {
        return;
      }

      if (!srcData) {
        // 表示在編輯時，有新增翻譯書
        handleCreate();
        return;
      }

      // 轉換 property, 在翻譯書目才有
      if (Object.hasOwn(data, TranslationBookName)) {
        data[IsTranslationBookOf] = createState[GLOBAL_DEFAULT].srcId;
        data[LabelPublication] = [
          `${data[TranslationBookName]}@zh`,
          `${data[TranslationBookName]}@en`,
        ];

        srcData[LabelPublication] = [
          `${srcData[TranslationBookName]}@zh`,
          `${srcData[TranslationBookName]}@en`,
        ];
      }

      // 特殊情況：原文書 label 修改
      if (transBook === GLOBAL_DEFAULT && sheetName === PublicationInfo) {
        data[LabelPublication] = [`${data[LabelPublication]}@zh`, `${data[LabelPublication]}@en`];

        srcData[LabelPublication] = [
          `${transformDefaultLabelPublication(
            JSON.parse(JSON.stringify(srcData[LabelPublication])),
            'zh',
          )}`,
          `${transformDefaultLabelPublication(
            JSON.parse(JSON.stringify(srcData[LabelPublication])),
            'en',
          )}`,
        ]
          .map((item) => item.split(','))
          .flat();

        // srcData[LabelPublication] = [
        //     `${srcData[LabelPublication]}@zh`,
        //     `${srcData[LabelPublication]}@en`
        // ];
      }

      const { updateResult, LocationUpdateResult, historyMsg } = await getEditNmtlItemResult(
        data,
        srcData,
      );

      if (updateResult && LocationUpdateResult && historyMsg) {
        // editInfo.push(updateResult);
        editInfo.push(historyMsg);
        successCount += 1;
      } else {
        errorCount += 1;
      }
      // }
    }

    if (errorCount === 0) {
      dispatch({
        type: Act.DATA_MESSAGE,
        payload: {
          title: `更新`,
          success: successCount,
          error: errorCount,
          renderSignal: `update-${new Date().getTime()}`,
        },
      });

      for (const eI of editInfo) {
        createHistoryEvent(displayName, '更新', `${columns.join('/')}：${eI}`);
      }
      // createHistoryEvent(
      //     displayName,
      //     "更新",
      //     `${columns.join("/")}：${editInfo}`
      // );
    } else {
      dispatch({
        type: Act.DATA_MESSAGE,
        payload: {
          title: `更新失敗`,
          success: successCount,
          error: errorCount,
          renderSignal: `update-${new Date().getTime()}`,
        },
      });

      createHistoryEvent(displayName, '更新失敗', `${columns.join('/')}：${editInfo}`);
    }

    dispatch({
      type: Act.DATA_CONTENT_CURRENT_BOOK_LANGUAGE,
      payload: '',
    });

    history.push('/Dataset');
  };

  const msgSetting = (title) => {
    dispatch({
      type: Act.DATA_INFO_MESSAGE,
      payload: {
        title,
        error: 1,
        renderSignal: `update-${new Date().getTime()}`,
      },
    });
  };

  const handleNextStep = async () => {
    const checkHeader = sheetCreateView[sheetName].checkKey;

    // 欄位尚未填寫
    if (!createState || !createState[GLOBAL_DEFAULT][checkHeader]) {
      msgSetting(`${sheetCreateView[sheetName].keyErrorMsg}`);
      return;
    }

    let inputValue = createState[GLOBAL_DEFAULT][checkHeader];

    // special case
    if (sheetName === 'Person') {
      const zhValue = inputValue.find((el) => el?.endsWith('@zh')) || null;

      // 找不到 zh 時跳出確認訊息
      if (!zhValue) {
        msgSetting(`${sheetCreateView[sheetName].keyErrorMsg}`);
        return;
      }

      inputValue = zhValue.replace('@zh', '');
    }

    const sameList = await findSameIdList(
      inputValue,
      sheetName,
      sheetCreateView[sheetName].apiPath,
    );

    if (!isEmpty(sameList)) {
      setOpen(true);
      setEqualValue(sameList);
    } else {
      setAction(createConfig.createButton);
    }
  };

  const handleAction = (act) => {
    switch (act) {
      case createConfig.createButton:
        return handleCreate();
      case createConfig.nextStep:
        return handleNextStep();
      case createConfig.editButton:
        return handleEdit();
      default:
        return null;
    }
  };

  useEffect(() => {
    if (editId !== GLOBAL_CREATE_PATH && apiReadPath) {
      handleGetContent(editId, dataset);
    }
  }, [editId, apiReadPath]);

  useEffect(() => {
    if (editId !== GLOBAL_CREATE_PATH) {
      setAction(createConfig.editButton);
      return;
    }

    setAction(createConfig.nextStep);
  }, []);

  const sheetView = () => {
    // 編輯時 createState 必須存在
    // if (editId && !createState) return null;
    if (editId !== GLOBAL_CREATE_PATH && isEmpty(createState[GLOBAL_DEFAULT])) {
      return null;
    }

    const { components: Comp } = sheetCreateView[sheetName];
    return (
      <Comp
        isLoading={isLoading}
        sheetName={sheetName}
        setCallback={setCallback}
        setCreateState={setCreateState}
        createState={createState}
        header={header}
        action={action}
      />
    );
  };

  useEffect(() => {
    // 預設作者及譯者都已填寫完成，避免使用者未打開tab，只編輯原文書名時，會沒辦法儲存
    dispatch({
      type: Act.DATA_IS_FILL_ALL_AUTHOR_OTHERNAME,
      payload: true,
    });
    dispatch({
      type: Act.DATA_IS_FILL_ALL_TRANSLATOR_OTHERNAME,
      payload: true,
    });
  }, []);

  return (
    <Container className="createContent" style={{ width: '90%' }}>
      <Grid className="createContent__grid" padded>
        <Grid.Row>
          <AlertMessage />
          {/* <AlertMsg message={msg} /> */}
        </Grid.Row>
        <Grid.Row className="createContent__grid__TopArea">
          <Grid.Column width={13}>
            <h2>
              {editId !== GLOBAL_CREATE_PATH ? createConfig.editTitle : createConfig.createTitle}
            </h2>
          </Grid.Column>
          <Grid.Column width={3}>
            <Button floated="right" onClick={handleCancel}>
              {createConfig.cancelButton}
            </Button>

            <NextStepModal
              sheetName={sheetName}
              editId={editId}
              createState={createState}
              open={open}
              setOpen={setOpen}
              isLoading={isLoading}
              setIsLoading={setIsLoading}
              onClick={() => handleAction(action)}
              action={action}
              equalValue={equalValue}
              handleGetContent={handleGetContent}
              setAction={setAction}
            />
          </Grid.Column>
        </Grid.Row>
        <Grid.Row className="createContent__grid__BottomArea">
          {sheetName && sheetCreateView && sheetView()}
        </Grid.Row>
      </Grid>
    </Container>
  );
};

export default CreateContentView;

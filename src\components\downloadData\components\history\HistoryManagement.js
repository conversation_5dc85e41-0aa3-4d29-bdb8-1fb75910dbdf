import React, { useContext, useEffect } from "react";

// ui
import { Container, Divider, Segment, Grid, Icon } from "semantic-ui-react";

// custom
import TopContent from "./subComponents/TopContent";

import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import { getSingleLayerCollectionLimit } from "../../../../api/firebase/cloudFirestore";
import { sortHistoryEvent } from "./common/common";

const HistoryManagement = () => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);

    useEffect(() => {
        getSingleLayerCollectionLimit(
            process.env.REACT_APP_HISTORY_LOG_FIREBASE_DOC_NAME,
            500
        )
            .then(result => {
                const tmpLog = {};
                sortHistoryEvent(result).forEach(element => {
                    const keys = Object.keys(element).filter(
                        key => key !== "id"
                    );
                    const tmpObj = {};
                    keys.forEach(key => {
                        tmpObj[key] = element[key];
                    });
                    tmpLog[element.id] = tmpObj;
                });
                dispatch({
                    type: Act.HISTORY_LOG,
                    payload: tmpLog
                });
            })
            .catch(err => {
                console.log(err);
            });
    }, []);

    return (
        <Container style={{ width: "95%" }}>
            <Segment basic compact>
                <h2>
                    歷史紀錄
                    <Icon color="green" name="check circle outline" />
                    <Icon color="red" name="exclamation circle" />
                </h2>
            </Segment>

            <Divider />

            <Grid celled>
                <Grid.Row>
                    <Grid.Column width={16}>
                        <TopContent />
                    </Grid.Column>
                </Grid.Row>
            </Grid>
        </Container>
    );
};

export default HistoryManagement;

import { writeNMTLDoc } from '../../../api/firebase/cloudFirestore/nmtlDoc';
import { createHistoryEvent } from '../../downloadData/components/history/common/common';
import { isEmpty } from '../../../commons';

const reorder = (list, startIndex, endIndex) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);

    return result;
};

function isObject(obj) {
    return (typeof obj === 'object' || typeof obj === 'function') && obj !== null;
}

// Deep equality
const keyPath = []; // 紀錄比對值不同時的路徑
const objectCompare = (obj1, obj2) => {
    const obj1Keys = Object.keys(obj1);
    const obj2Keys = Object.keys(obj2);
    if (obj1Keys.length !== obj2Keys.length) {
        return false;
    }

    for (const key of obj1Keys) {
        const val1 = obj1[key];
        const val2 = obj2[key];
        const areObjects = isObject(val1) && isObject(val2);
        if ((areObjects && !objectCompare(val1, val2)) || (!areObjects && val1 !== val2)) {
            keyPath.unshift(key);
            return false;
        }
    }
    return true;
};

// 找到特定object再寫入資料
const saveData = (
    menuActiveItem,
    updatedData,
    setShowMessage,
    setOpenSettingResultModal,
    setLoading,
    originData,
    displayName,
    columns,
) => {
    const docName = menuActiveItem.key; // document name
    const updateDataObj = Object.assign(
        {},
        updatedData.find((element) => element.id === docName),
    );
    const originalDataObj = Object.assign(
        {},
        originData.find((element) => element.id === docName),
    );

    delete updateDataObj.id;
    writeNMTLDoc(docName, updateDataObj, (param1, result) => {
        result
            .then(() => {
                setShowMessage('資料更改成功');
                setLoading(false);
                setOpenSettingResultModal(true);

                const historyMsg = `${JSON.stringify(originalDataObj)}\n變動後：\n${JSON.stringify(
                    updateDataObj,
                )}`;

                // 建立歷史紀錄
                createHistoryEvent(displayName, '更新', `${columns.join('/')}：${historyMsg}`);
            })
            .catch((error) => {
                setShowMessage(`資料更改失敗: ${error}`);
                setLoading(false);
                setOpenSettingResultModal(true);
            });
    });
};

function sortByPriority(tmpData) {
    const sortedResult = [];
    tmpData
        .map((data) => data.priority)
        .sort((a, b) => a - b)
        .forEach((number) => {
            sortedResult.push(tmpData.find((item) => item.priority === number));
        });
    return sortedResult;
}

function returnEmptyKeyName(selectOption) {
    switch (selectOption) {
        case 'museumLink':
            return 'museum_';
        case 'normalLink':
            return 'normal_';
        case 'carousel':
            return 'carousel_';
        default:
            console.log('Select Option Not Exist !');
            return 'EmptyKeyName';
    }
}

function returnDefaultValue(componentName) {
    switch (componentName) {
        case 'LinkingPage':
            return {
                en: {
                    title: '',
                },
                zh: {
                    title: '',
                },
                priority: 0,
                url: '',
            };
        case 'MainCarousel':
            return {
                mobileImageUrl: '',
                priority: 1,
                url: '',
                websiteUrl: '',
                subjectId: '',
            };
        case 'SubjectLiterature':
            return {
                name: '',
                priority: 0,
                en: {
                    introduction: '',
                    overview: '',
                },
                zh: {
                    introduction: '',
                    overview: '',
                },
            };
        case 'CityLiterature':
            return {
                name: '',
                en: {
                    introduction: '',
                    overview: '',
                },
                zh: {
                    introduction: '',
                    overview: '',
                },
            };
        case 'VrMuseum':
            return {
                name: '',
                priority: 0,
                en: {
                    description: '',
                },
                zh: {
                    description: '',
                },
                watermark: '',
                url: {
                    main: '',
                    watermark: '',
                },
            };
        case 'MainCardImage':
            return {
                name: '',
                url: '',
                priority: 0,
            };
        default:
            console.log('Component Not Exist !');
            return {};
    }
}

const imagePath = {
    root: 'frontend-settings',
    MainIntroduction: 'MainIntroduction',
    MainCarousel: 'MainCarousel',
    MainCardImage: 'MainCardImage',
    VrMuseum: {
        VrMuseum: 'VrMuseum',
        main: 'main',
        watermark: 'watermark',
    },
    CardImageUpdater: 'CardImageUpdater',
};

// 選單項目主要從msListData給，如果originData有msListData就不處理originData裡面的相同資料
function setDropDownFromMsListData(originArray, msListArray, subjectName) {
    const newDropDown = {};
    msListArray.forEach((element) => {
        const findSubject = originArray.find((subjectName) => subjectName === element.id);
        if (!findSubject) {
            const emptySubjectLiteratureObj = returnDefaultValue(subjectName);
            emptySubjectLiteratureObj.name = element.label;
            emptySubjectLiteratureObj.priority = parseInt(element.seq, 10);
            newDropDown[element.id] = emptySubjectLiteratureObj;
        }
    });
    return newDropDown;
}

// 對應到firebase欄位名稱
const imageTypeTrans = {
    web: 'url',
    mobile: 'mobileImageUrl',
};

export {
    isObject,
    reorder,
    objectCompare,
    keyPath,
    saveData,
    sortByPriority,
    returnEmptyKeyName,
    returnDefaultValue,
    imagePath,
    setDropDownFromMsListData,
    imageTypeTrans,
};

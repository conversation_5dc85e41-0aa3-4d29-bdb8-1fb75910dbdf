import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import PropTypes from "prop-types";

// semantic ui
import { Dropdown } from "semantic-ui-react";

// config
import { statusConfig } from "../../common/statusConfig";
import textConfig from "../../common/textConfig";
import fbConfig from "../../common/fbConfig";

// utils
import { setNewAllData } from "../../common/utils";
import { isEmpty } from "../../../../commons";

function StatusDropdown({ fsID }) {
    const dispatch = useDispatch();
    const { newAllData } = useSelector(state => state.report);

    const [dropOptions, setDropOptions] = useState([]);
    const [opVal, setOpVal] = useState("");

    useEffect(() => {
        const tmpArr = statusConfig
            .filter(el => el.showOption)
            .map(({ text, status }) => ({ key: status, text, value: status }));
        setDropOptions(tmpArr);
    }, []);

    useEffect(() => {
        if (isEmpty(newAllData)) return;
        const findObj = newAllData.find(el => el.id === fsID);
        if (findObj) {
            setOpVal(findObj[fbConfig.status]);
        }
    }, [newAllData]);

    const handleChange = (evt, data) => {
        setOpVal(data.value);

        // update issue data
        const tmpAllData = JSON.parse(JSON.stringify(newAllData));
        const findObj = tmpAllData.find(el => el.id === fsID);
        if (findObj) {
            findObj[fbConfig.status] = data.value;
            setNewAllData(dispatch, tmpAllData);
        }
    };

    return (
        <Dropdown
            selection
            options={dropOptions}
            placeholder={textConfig.placeholder.statusDD}
            onChange={handleChange}
            value={opVal}
        />
    );
}

StatusDropdown.propTypes = {
    /** firestore id */
    fsID: PropTypes.string
};

StatusDropdown.defaultProps = {
    /** issueID */
    fsID: ""
};

export default StatusDropdown;

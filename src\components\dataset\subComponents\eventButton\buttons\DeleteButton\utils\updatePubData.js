import { updateNmtlData } from "../../../../../../../api/nmtl";
import Api from "../../../../../../../api/nmtl/Api";

const splitValue = (tmpArr, targetVal) =>
    tmpArr
        .split("、")
        .filter(str => str !== targetVal)
        .join("、");

const getPubDstData = warnData => {
    const tmpRes = [];
    warnData.pubCntArr.data.forEach(el => {
        const { pubId, pubCheckPropLabel, perName } = el;
        const findData = tmpRes.find(
            obj =>
                obj.pubId === pubId &&
                obj.pubCheckPropLabel === pubCheckPropLabel
        );
        if (!findData) {
            // 找到checkValue並移除perName value
            const tmpObj = {
                ...el,
                checkValue: splitValue(el.checkValue, perName)
            };

            tmpRes.push(tmpObj);
        } else {
            // 找到checkValue並移除perName value
            findData.checkValue = splitValue(findData.checkValue, perName);
        }
    });
    return tmpRes;
};

// update Publication authorName、translatorName by specific perName
const updatePubData = (warnData, graph, sheetName, user) => {
    const dstDataArr = getPubDstData(warnData);
    const promises = dstDataArr.map(dstEl => {
        const findSrcData = warnData.pubCntArr.data.find(
            obj =>
                obj.pubId === dstEl.pubId &&
                obj.pubCheckPropLabel === dstEl.pubCheckPropLabel
        );

        const entrySrc = {};
        if (findSrcData) {
            entrySrc.classType = findSrcData.classType;
            entrySrc.srcId = findSrcData.pubId;
            entrySrc.graph = graph;
            if (findSrcData.checkValue) {
                entrySrc.value = {
                    [findSrcData.pubCheckProp]: [
                        `${findSrcData.checkValue}@${findSrcData.lang}`
                    ]
                };
            } else {
                entrySrc.value = {};
            }
        }

        const entryDst = {};
        entryDst.classType = dstEl.classType;
        entryDst.srcId = dstEl.pubId;
        entryDst.graph = graph;
        entryDst.value = dstEl.checkValue
            ? { [dstEl.pubCheckProp]: [`${dstEl.checkValue}@${dstEl.lang}`] }
            : {};

        // console.log("entrySrc ", entrySrc);
        // console.log("entryDst ", entryDst);
        return updateNmtlData(
            user,
            Api.getGeneric,
            graph,
            sheetName,
            entrySrc,
            entryDst
        );
    });

    return new Promise((resolve, reject) => {
        Promise.all(promises)
            .then(resArr => {
                if (resArr.every(resStr => resStr === "OK")) {
                    resolve({ status: "OK" });
                }
                resolve({ status: "failed" });
            })
            .then(err => {
                reject(err || { status: "update failed" });
            });
    });
};

export default updatePubData;

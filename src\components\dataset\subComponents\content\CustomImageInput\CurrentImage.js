import React from 'react';

import { Grid, Image, Segment } from 'semantic-ui-react';

import noImage from '../../../../../images/No_image_200x200.png';
import {
    // formatDateTimeZoneTW,
    getImgFilenameFromUrl,
} from '../../../../common/imageCommon/gallery/galleryHelper';
import getYouTubeId from './utils/getYouTubeId';

const noImgPlaceholderUrl = 'https://dummyimage.com/200x200/fff/000&text=No_image';

// const popupContent = contentStr => {
//     if (!(typeof contentStr === "string")) return "";
//     const content = contentStr.split("\n");
//     const dtReg = /(?<prefix>.+)(?<datetime>\d{4}-\d{2}-\d{2}[A-Z\s]\d{2}:\d{2}:\d{2}\+\d{2}:\d{2})/;
//     return content.map(ct => {
//         // 若符合時間格式, 則轉換成特定時區格式
//         const dtMatch = ct.match(dtReg);
//         if (dtMatch) {
//             const { prefix, datetime } = dtMatch.groups;
//             return (
//                 <div key={uuidv4()}>
//                     {`${prefix}${formatDateTimeZoneTW(datetime)}`}
//                 </div>
//             );
//         }
//         return <div key={uuidv4()}>{ct}</div>;
//     });
// };

const getYouTubeThumbUrl = (id) => (id ? `https://i.ytimg.com/vi/${id}/hqdefault.jpg` : null);

const getDisplayImageSrc = (url) => {
    const ytId = getYouTubeId(url);
    if (ytId) {
        return getYouTubeThumbUrl(ytId);
    }
    return (url && url.length > 0 && url) || noImage || noImgPlaceholderUrl;
};

const CurrentImage = ({ defaultValue, currentValue }) => {
    const defaultImgSrc = getDisplayImageSrc(defaultValue);
    const currentImgSrc = getDisplayImageSrc(currentValue);

    return (
        <React.Fragment>
            <Segment>
                <Grid>
                    <Grid.Row>
                        <Grid.Column width={8}>
                            <h3>原始圖片</h3>
                            <Image
                                src={defaultImgSrc}
                                size="small"
                                bordered
                                rounded
                                alt="原始圖片預覽"
                            />
                            <br />
                            <div style={{ wordBreak: 'break-all', whiteSpace: 'normal' }}>
                                檔案名稱：
                                {getImgFilenameFromUrl(defaultValue) ||
                                    getYouTubeId(defaultValue) ||
                                    '（無）'}
                            </div>
                        </Grid.Column>

                        <Grid.Column width={8}>
                            <h3>目前選擇圖片</h3>
                            <Image
                                src={currentImgSrc}
                                size="small"
                                bordered
                                rounded
                                alt="目前選擇圖片預覽"
                            />
                            <br />
                            <div style={{ wordBreak: 'break-all', whiteSpace: 'normal' }}>
                                檔案名稱：
                                {getImgFilenameFromUrl(currentValue) ||
                                    getYouTubeId(currentValue) ||
                                    '（無）'}
                            </div>
                            {/* <div>{popupContent(currentImgInfo)}</div> */}
                        </Grid.Column>
                    </Grid.Row>
                </Grid>
            </Segment>
        </React.Fragment>
    );
};

export default CurrentImage;

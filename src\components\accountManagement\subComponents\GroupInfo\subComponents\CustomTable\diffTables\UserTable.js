import React, { useEffect, useState } from "react";
import { Checkbox, Table } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import { isEmpty } from "../../../../../../../commons";
import textConfig from "../../../../Utils/textConifg";
import { clickChildChB, clickTitleChB } from "../commonUtils/commonUtils";
import accMngAct from "../../../../../../../reduxStore/accManage/accManageAction";
import { tableNames } from "../../../../Utils/compoConfig";

function UserTable() {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { groupData, tableSelectPool, currentPage, pageNum } = state;
    // const userTableHeader = ["", "姓名", "電子郵件", "所屬群組"];
    const userTableHeader = ["", "姓名", "電子郵件"];
    const [detailInfo, setDetailInfo] = useState({});

    useEffect(() => {
        setDetailInfo({
            tableName: tableNames.users,
            tableNames,
            data: groupData.members,
            tableSelectPool
        });
    }, [groupData, tableSelectPool]);

    useEffect(() => {
        dispatch({
            type: accMngAct.SET_TOTALPAGE,
            payload: Math.ceil(groupData.members.length / pageNum)
        });
    }, [groupData, pageNum]);

    return (
        <Table celled structured size="small">
            <Table.Header>
                <Table.Row>
                    {userTableHeader.map((content, idx) => {
                        if (idx === 0) {
                            return (
                                <Table.HeaderCell key={content} collapsing>
                                    <Checkbox
                                        onClick={() =>
                                            clickTitleChB(detailInfo, dispatch)
                                        }
                                        checked={
                                            tableSelectPool.users.length ===
                                                groupData.members.length &&
                                            groupData.members.length !== 0
                                        }
                                    />
                                </Table.HeaderCell>
                            );
                        }
                        return (
                            <Table.HeaderCell key={content}>
                                {content}
                            </Table.HeaderCell>
                        );
                    })}
                </Table.Row>
            </Table.Header>
            <Table.Body>
                {isEmpty(groupData.members) ? (
                    <Table.Row>
                        <Table.Cell
                            content={textConfig.NO_MEMBER}
                            colSpan={userTableHeader.length}
                            textAlign="center"
                        />
                    </Table.Row>
                ) : (
                    groupData.members
                        .slice(
                            (currentPage - 1) * pageNum,
                            currentPage * pageNum
                        )
                        .map(el => {
                            const { uid, displayName, email } = el;
                            return (
                                <Table.Row key={uid}>
                                    <Table.Cell>
                                        <Checkbox
                                            onClick={() => {
                                                const infoObj = {
                                                    rowId: uid,
                                                    tableSelectPool,
                                                    poolName: tableNames.users
                                                };
                                                clickChildChB(
                                                    infoObj,
                                                    dispatch
                                                );
                                            }}
                                            checked={
                                                tableSelectPool.users.indexOf(
                                                    el.uid
                                                ) !== -1
                                            }
                                        />
                                    </Table.Cell>
                                    <Table.Cell content={displayName} />
                                    <Table.Cell content={email} />
                                </Table.Row>
                            );
                        })
                )}
            </Table.Body>
        </Table>
    );
}

export default UserTable;

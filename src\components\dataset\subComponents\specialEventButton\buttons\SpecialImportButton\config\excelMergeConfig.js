export const publicationHeader = [
    "原文書ID\nisTranslationBookOf",
    "原文書名\nlabel_Publication",
    "作者(@PER @ORG)\nhasAuthor",
    "翻譯書ID\nsrcId",
    "外譯語言\nhasLanguageOfWorkOrName",
    "翻譯書名\ntranslationBookName",
    "作者發表名稱\nauthorName",
    "翻譯者(@PER @ORG)\nhasTranslator",
    "譯者顯示名稱\ntranslatorName",
    "編輯者(@PER @ORG)\nhasEditor",
    "出版者(@PER @ORG)\nhasPublisher",
    "出版年份\nhasInceptionDate",
    "出版地點\nsrcId_hasPlaceOfPublication",
    "文學分類\nLiteraryGenre",
    "總頁數\ntotalPage",
    "ISBN\nISBN",
    "資料來源\nreferences",
    "摘要\nintroduction",
    "目次\ntableOfContents",
    "電子書連結與上傳\nfileAvailableAt",
    "全文著作狀態\nhasFullWorkCopyright",
    "書封照\nimageURL_hasURL",
    "圖像權利標註\nhasCopyrightStatus_hasURL",
    "備註\ncomment",
    "是否為Lift書系\nlift",
    "是否為peak書系\npeak",
    "友善連結\nfriendlyLink",
    "經度\ngeoLatitude_hasPlaceOfPublication",
    "緯度\ngeoLongitude_hasPlaceOfPublication"
];

export const personHeader = [
    "人物ID\nsrcId",
    "姓名\nlabel_Person",
    "翻譯語言\nhasTranslationLanguage",
    "作/譯者\nauthorOrTranslator",
    "筆名\npenName",
    "本名\nbirthName",
    "其他名稱\notherName",
    "簡介\nintroduction",
    "相關連結\nexternalLinks",
    "人物照\nimageURL_hasURL",
    "圖像權利標註\nhasCopyrightStatus_hasURL",
    "備註\ncomment",
    "顯示人物照\npictureDisplay"
];

export const publicationMergeConfig = [
    {
        zhHeader: "hasAuthor_Zh",
        enHeader: "hasAuthor_En",
        newHeader: "作者(@PER @ORG)\nhasAuthor"
    },
    {
        zhHeader: "authorName_Zh",
        enHeader: "authorName_En",
        newHeader: "作者發表名稱\nauthorName"
    },
    {
        zhHeader: "hasTranslator_Zh",
        enHeader: "hasTranslator_En",
        newHeader: "翻譯者(@PER @ORG)\nhasTranslator"
    },
    {
        zhHeader: "translatorName_Zh",
        enHeader: "translatorName_En",
        newHeader: "譯者顯示名稱\ntranslatorName"
    },
    {
        zhHeader: "hasEditor_Zh",
        enHeader: "hasEditor_En",
        newHeader: "編輯者(@PER @ORG)\nhasEditor"
    },
    {
        zhHeader: "hasPublisher_Zh",
        enHeader: "hasPublisher_En",
        newHeader: "出版者(@PER @ORG)\nhasPublisher"
    },
    {
        zhHeader: "references_Zh",
        enHeader: "references_En",
        newHeader: "資料來源\nreferences"
    },
    {
        zhHeader: "introduction_Zh",
        enHeader: "introduction_En",
        newHeader: "摘要\nintroduction"
    }
];

export const personMergeConfig = [
    {
        zhHeader: "label_Person_Zh",
        enHeader: "label_Person_En",
        newHeader: "姓名\nlabel_Person"
    },
    {
        zhHeader: "introduction_Zh",
        enHeader: "introduction_En",
        newHeader: "簡介\nintroduction"
    }
];

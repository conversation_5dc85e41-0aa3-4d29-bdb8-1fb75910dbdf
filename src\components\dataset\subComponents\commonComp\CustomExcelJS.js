import React, { useMemo } from "react";
import {saveAs} from 'file-saver';
import ExcelJs from "exceljs";
import {Workbook, Worksheet, Row} from 'exceljs';
import JsZip from 'jszip'

const downloadData = [
    {
        sheetName: `工作表1`,
        thead: ['姓名','年齡','電話'],
        tbody: [['小明','20','0987654321'],['小美','23','0912345678']],
        columnWidths: [{number: 1, width:20},{number: 2, width:10},{number: 3, width:40}]
    },
    {
        sheetName: `工作表2`,
        thead: ['姓名','座號'],
        tbody: [['小明','1'],['小美','2']],
        columnWidths: [{number: 1, width:20}]
    }
];

const CustomExcelJS = ({ name, filename, header, data }) => {
    console.log(name, filename, header, data);
    // const style = {
    //     borderRadius: '5px',
    //     ...props.style
    // }

    const startMs = Date.now();
    console.log("startMs ms:", startMs, data.length);

    const MemoziedExcelFile = () => {
        console.log("startMs ms:", startMs);
        const workbook = new ExcelJs.Workbook();
        downloadData.forEach((sheetData)=>{
            const sheet = workbook.addWorksheet(sheetData.sheetName);
            sheet.addTable({
                name: sheetData.sheetName,
                ref: `A1`, // 從A1開始
                headerRow: true,
                columns: sheetData.thead.map((s)=>{ return {name: s}}),
                rows: sheetData.tbody
            });
            if (sheetData.columnWidths) {
                sheetData.columnWidths.forEach((column)=>{
                    sheet.getColumn(column.number).width = column.width
                });
            }
        })

        // 表格裡面的資料都填寫完成之後，訂出下載的callback function
        // 異步的等待他處理完之後，創建url與連結，觸發下載
        workbook.xlsx.writeBuffer().then((content) => {
            const link = document.createElement("a");
            const blob = new Blob([content], {
                type: "application/vnd.ms-excel;charset=utf-8;"
            });
            link.download = `${filename}.xlsx`;
            link.href = URL.createObjectURL(blob);
            link.click();
        });
        console.log("startMs ms:", startMs);
        return workbook;
    };

    console.log("startMs ms:", startMs);
    return MemoziedExcelFile
};

export default CustomExcelJS;

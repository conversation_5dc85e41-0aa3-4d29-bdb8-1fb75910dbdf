@mixin rightComponentMain {
  width: 100%;
  padding: 20px;
  //height:90%;
  display: flex;
  flex-direction: column;
}

@mixin btnArea {
  display: flex;
  justify-content: flex-end;
}

.EditCate {
  @include rightComponentMain;
  border: none !important;
  box-shadow: none !important;
  .topArea{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px;
    &__left{
      display: flex;
      flex-direction: column;
      width:100%;
      justify-content: center;
      align-items: flex-start;
      height: 100%;
      font-size: 12px;
      &--subRowName{
        color: #9E9E9E;
      }
    }
    &__right{
      display: flex;
      gap: 5px;
      width: 100%;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      &--box{
          width:100%;
          .input{
            width:100%;
          }
        }
    }
    &__double{
      display: flex;
      flex-direction: column;
      gap: 5px;
      width: 100%;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      &--box{
        width:100%;
        display: flex;
        &--label{
          max-width: 80px;
          width:100%;
          display: flex;
          justify-content: center;
          flex-direction: column;
          p{
            line-height: 0;
          }
        }
        &--field{
          flex: 0 0 1;
          width:100%;
          display: flex;
        }
        .input{
          width:100%;
        }
      }
    }
  }
  .quill{
    .ql-editor{
      min-height: 400px;
    }
  }
  .uploadArea{
    margin-bottom: 5px;
    height: 40%;
    display: flex;
  }
  .btnArea{
    @include btnArea;
    button {
      margin: 0;
    }
  }
  .bookArea{
    height:80%;
    overflow-y: scroll;
    margin-top: 2rem;
  }
}


.ui.disabled.input, .ui.input:not(.disabled) input[disabled]{
  opacity:1 !important;
}

// modal外屬性
.dimmer {
  background-color: rgba(0,0,0,0.2) !important;
}
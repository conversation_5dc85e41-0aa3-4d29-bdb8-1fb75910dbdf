import React from "react";
import { Image, Popup } from "semantic-ui-react";
import infoIcon from "../../../../images/icon_info.png";

function CustomHeaderInformation({ id }) {
    const styleImage = {
        width: "1.5em",
        height: "auto",
        bottom: "0.1em",
        margin: "0 0.1em"
    };
    return (
        <Popup
            trigger={<Image src={infoIcon} avatar style={styleImage} />}
            content={id}
            size="mini"
        />
        // <Button icon circular size="mini" style={{ width: "1em", height: "auto" }}>
    );
}

export default CustomHeaderInformation;

// <Icon circular inverted name="info" size="small" />
// </Button>

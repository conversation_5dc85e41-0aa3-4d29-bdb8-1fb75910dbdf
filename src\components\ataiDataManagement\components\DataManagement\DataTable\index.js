import React, { useContext, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { StoreContext } from "../../../../../store/StoreProvider";
import { Table } from "semantic-ui-react";
import "./DataTable.scss";
import axios from "axios";
import viewIcon from "../../../images/viewIcon.svg";
import ataiMngAct from "../../../../../reduxStore/ataiManage/ataiManageAction";
import Panel from "../Panel";
import AtaiPagination from "../AtaiPagination";
import getAtaiData from "../../../utils/utils";
import Api from "../../../../../api/nmtl/Api";
import viewIconDisabled from "../../../images/viewIconDisabled.svg";

const AtaiDataTable = () => {
    const state = useSelector(tmpState => tmpState.ataiMng);
    const { currentPage, itemPerPage, ataiData, totalPage, isUpdating } = state;
    const dispatch = useDispatch();

    const [contextState] = useContext(StoreContext);
    const { user } = contextState;

    const handleCheckboxChange = md5_hash => {
        const checkedAtaiData = ataiData.map(data =>
            data.md5_hash === md5_hash
                ? { ...data, checked: !data.checked }
                : data
        );
        dispatch({ type: ataiMngAct.SET_ATAI_DATA, payload: checkedAtaiData });
    };

    const handleViewData = (filename, md5_hash) => {
        if (!md5_hash) {
            const foundViewData = ataiData.find(el => el.filename === filename);
            window.open(foundViewData.url, "_blank");
        } else {
            window.open(
                `${Api.openAtaiDataPdf}/${md5_hash}/${filename}`,
                "_blank"
            );
        }
    };

    const checkUpdateLock = async () => {
        while (true) {
            try {
                const res = await axios.post(Api.ataiUpdateLock);
                if (res.status === 200) {
                    dispatch({
                        type: ataiMngAct.SET_UPDATE_TIME,
                        payload: res.data.current_time
                    });

                    getAtaiData(dispatch);

                    dispatch({
                        type: ataiMngAct.SET_IS_UPDATING,
                        payload: false
                    });

                    break;
                } else if (res.status === 201) {
                    dispatch({
                        type: ataiMngAct.SET_IS_UPDATING,
                        payload: true
                    });
                }
            } catch (e) {
                dispatch({
                    type: ataiMngAct.SET_UPDATE_TIME,
                    payload: "無法取得上次"
                });
            }
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
    };

    const handleUpdate = async () => {
        dispatch({ type: ataiMngAct.SET_IS_UPDATING, payload: true });
        try {
            const res = await axios.post(Api.updateAtaiData, {
                email: user.email
            });
            if (res.status === 200) {
                await checkUpdateLock();
            }
        } catch (e) {
            alert("更新失敗: ", e);
        }
    };

    const handleDelete = async () => {
        try {
            let deletedAtaiData = ataiData
                .filter(ataiDataItem => ataiDataItem.checked)
                .map(item => item.md5_hash);

            if (deletedAtaiData.length > 0) {
                deletedAtaiData = { md5_list: deletedAtaiData };
                await axios.post(Api.deleteAtaiData, deletedAtaiData);
            }
        } catch (e) {
            alert("刪除失敗: ", e);
        } finally {
            getAtaiData(dispatch);
        }
    };

    useEffect(() => {
        getAtaiData(dispatch);
        checkUpdateLock();
    }, []);

    useEffect(() => {
        dispatch({
            type: ataiMngAct.SET_TOTAL_PAGE,
            payload: Math.ceil(ataiData.length / itemPerPage)
        });
    }, [ataiData]);

    useEffect(() => {
        dispatch({
            type: ataiMngAct.SET_CURRENT_PAGE,
            payload: 1
        });
    }, [totalPage]);

    return (
        <div>
            <Panel
                isUpdating={isUpdating}
                handleUpdate={handleUpdate}
                handleDelete={handleDelete}
            />
            <div className="dataTable">
                <Table celled fixed singleLine>
                    <Table.Header
                        style={{ position: "sticky", top: "0", zIndex: "100" }}
                    >
                        <Table.Row>
                            <Table.HeaderCell textAlign="center" width={1}>
                                勾選
                            </Table.HeaderCell>
                            <Table.HeaderCell width={8}>名稱</Table.HeaderCell>
                            <Table.HeaderCell width={2}>
                                上傳日期
                            </Table.HeaderCell>
                            <Table.HeaderCell textAlign="center" width={1}>
                                檢視
                            </Table.HeaderCell>
                        </Table.Row>
                    </Table.Header>
                    <Table.Body>
                        {ataiData
                            .slice(
                                (currentPage - 1) * itemPerPage,
                                (currentPage - 1) * itemPerPage + itemPerPage
                            )
                            .map(data => (
                                <Table.Row key={data.filename}>
                                    <Table.Cell
                                        textAlign="center"
                                        width={2}
                                        style={{
                                            backgroundColor: data.hasOwnProperty(
                                                "checked"
                                            )
                                                ? "transparent"
                                                : "#FAFAFA"
                                        }}
                                    >
                                        {data.hasOwnProperty("checked") && (
                                            <input
                                                className="checkbox"
                                                type="checkbox"
                                                checked={data.checked}
                                                disabled={isUpdating}
                                                onChange={() =>
                                                    handleCheckboxChange(
                                                        data.md5_hash
                                                    )
                                                }
                                            />
                                        )}
                                    </Table.Cell>
                                    <Table.Cell>
                                        <div className="dataGroup">
                                            <p>{data.filename}</p>
                                            <p className="dataSize">
                                                {data.file_size}
                                            </p>
                                        </div>
                                    </Table.Cell>
                                    <Table.Cell>{data.upload_time}</Table.Cell>
                                    <Table.Cell textAlign="center">
                                        <button
                                            className="viewBtn"
                                            onClick={() =>
                                                handleViewData(
                                                    data.filename,
                                                    data.md5_hash
                                                )
                                            }
                                            disabled={isUpdating}
                                            style={{
                                                cursor: isUpdating && "default"
                                            }}
                                        >
                                            <img
                                                src={
                                                    !isUpdating
                                                        ? viewIcon
                                                        : viewIconDisabled
                                                }
                                                alt=""
                                            ></img>
                                        </button>
                                    </Table.Cell>
                                </Table.Row>
                            ))}
                    </Table.Body>
                </Table>
            </div>
            <AtaiPagination />
        </div>
    );
};

export default AtaiDataTable;

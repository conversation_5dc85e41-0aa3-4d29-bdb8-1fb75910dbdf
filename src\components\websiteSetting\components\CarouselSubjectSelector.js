import React, { useContext, useEffect, useState } from "react";
import { Select } from "semantic-ui-react";
import Act from "../../../store/actions";
import { StoreContext } from "../../../store/StoreProvider";
import { sortByPriority } from "../commons";
import SubjectSelectModal from "./SubjectSelectModal";

function checkSelectOption(tmpObj, value) {
    const keys = Object.keys(tmpObj).filter(key => key !== "id");
    let existed = false;
    keys.forEach(key => {
        if (tmpObj[key].subjectId === value) {
            existed = true;
        }
    });
    return existed;
}

function CarouselSubjectSelector({ dropDown, listData }) {
    const [state, dispatch] = useContext(StoreContext);
    const {
        updatedData,
        menuActiveItem,
        isEditedDisable
    } = state.websiteSetting;
    const [sortOption, setSortOption] = useState([]);
    const [selectorValue, setSelectorValue] = useState(listData.subjectId);
    const [openModal, setOpenModal] = useState(false);

    useEffect(() => {
        setSelectorValue(listData.subjectId);
    }, [listData]);

    useEffect(() => {
        if (dropDown) {
            const tmpSelectOption = [];
            // eslint-disable-next-line no-restricted-syntax
            for (const [key, value] of Object.entries(dropDown)) {
                tmpSelectOption.push({
                    key,
                    value: key,
                    text: value.name,
                    priority: value.priority
                });
            }
            setSortOption(sortByPriority(tmpSelectOption));
        }
    }, [dropDown]);

    return (
        <div>
            <Select
                placeholder={
                    dropDown[listData.subjectId]
                        ? dropDown[listData.subjectId].name
                        : "請選擇主題"
                }
                options={sortOption}
                onChange={(event, data) => {
                    const tmpUpdatedData = JSON.parse(
                        JSON.stringify(updatedData)
                    );
                    const tmpObj = tmpUpdatedData.find(
                        element => element.id === menuActiveItem.key
                    );

                    // 防止重複選取相同主題
                    if (checkSelectOption(tmpObj, data.value)) {
                        setOpenModal(true);
                        return;
                    }
                    const keys = Object.keys(tmpObj);
                    const findKey = keys.find(
                        key => tmpObj[key].priority === listData.priority
                    );
                    if (findKey) {
                        tmpObj[findKey].subjectId = data.value;
                    }
                    dispatch({
                        type: Act.SET_UPDATEDDATA,
                        payload: tmpUpdatedData
                    });
                    setSelectorValue(data.value);
                }}
                style={{ width: "100%", marginBottom: "5px" }}
                value={selectorValue}
                disabled={isEditedDisable}
            />
            <SubjectSelectModal
                openModal={openModal}
                setOpenModal={setOpenModal}
            />
        </div>
    );
}

export default CarouselSubjectSelector;

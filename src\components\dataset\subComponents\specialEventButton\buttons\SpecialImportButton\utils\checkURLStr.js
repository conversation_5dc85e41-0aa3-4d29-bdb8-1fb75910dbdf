import { checkURLPattern } from "../../../../../../../commons/utility";

const checkURLStr = (cell, propLabel) => {
    let tmpResStr = "";
    const reason = "請輸入URL格式";
    if (cell.value) {
        if (typeof cell.value === "string") {
            const correct = checkURLPattern(cell.value);
            if (!correct) {
                tmpResStr += `${cell.address}, [${cell.value}], 欄位:${propLabel}，${reason}。\n`;
            }
        } else if (Object.hasOwn(cell.value, "hyperlink")) {
            const correct = checkURLPattern(cell.value.text);
            if (!correct) {
                tmpResStr += `${cell.address}, [${cell.value.text}], 欄位:${propLabel}，${reason}。\n`;
            }
        }
    }

    return tmpResStr;
};

export default checkURLStr;

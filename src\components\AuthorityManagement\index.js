import React, { useContext, useEffect } from 'react';

import { Container, Divider, Grid, Segment, Checkbox } from 'semantic-ui-react';

import AuthoritySearchBar from '../dataset/subComponents/searchBar/AuthoritySearchBar';
import Sidebar from './components/Sidebar';
import ContentView from '../dataset/subComponents/content/ContentView';
// import Pagination from "../dataset/subComponents/pagination";
import EventButton from '../dataset/subComponents/eventButton';
import AlertMessage from '../dataset/subComponents/alertMessage';
import { StoreContext } from '../../store/StoreProvider';
import CustomPagination from '../dataset/subComponents/pagination/CustomPagination/CustomPagination';
import Act from '../../store/actions';

const BasicContent = ({ showOnlyUnintegratedData, handleCheckboxChange }) => (
  <>
    <div
      style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: '8px',
      }}
    >
      {/* search content from topic and sheet  */}
      <AuthoritySearchBar />

      <Checkbox
        label="開啟同ID卻有不同資料的列表"
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
        }}
        checked={showOnlyUnintegratedData}
        onChange={handleCheckboxChange}
      />

      {/* handle update and delete event  */}
      <EventButton />
    </div>

    <Divider hidden style={{ margin: '.4rem 0' }} />
    {/* show topic and sheet content */}
    <ContentView />

    {/* show page */}
    {/* <Pagination /> */}
    <CustomPagination />
  </>
);

const AuthorityManagement = () => {
  const [state, dispatch] = useContext(StoreContext);
  const { content } = state.data;

  const handleCheckboxChange = (e, { checked }) => {
    dispatch({
      type: Act.DATA_CONTENT_SHOW_ONLY_UNINTEGRATED_DATA,
      payload: checked,
    });
  };

  useEffect(() => {
    dispatch({
      type: Act.DATA_MAINSUBJECT,
      payload: {
        key: 'authority',
        value: '權威檔',
        text: '權威檔',
        dataset: 'authority',
        seq: '76',
      },
    });

    dispatch({ type: Act.DATA_SHEET_CLEAN, payload: {} });
    dispatch({ type: Act.DATA_SHEET_ACTIVATE_HEADER_CLEAN, payload: {} });

    // 離開Authority也要把Sheet資料清乾淨，避免影響DataSet頁面使用
    return () => {
      dispatch({ type: Act.DATA_SHEET_CLEAN, payload: {} });
      dispatch({ type: Act.DATA_SHEET_ACTIVATE_HEADER_CLEAN, payload: {} });
    };
  }, []);

  return (
    <Container style={{ width: '95%' }} className="Authority">
      <Segment basic compact>
        <h2>權威檔管理</h2>
      </Segment>
      <Divider />
      <AlertMessage />
      <Grid celled>
        <Grid.Row>
          <Grid.Column width={2}>
            {/* SideBar for MainSubject and Sheet */}
            <Sidebar />
          </Grid.Column>
          <Grid.Column width={14}>
            <BasicContent
              showOnlyUnintegratedData={content.showOnlyUnintegratedData}
              handleCheckboxChange={handleCheckboxChange}
            />
          </Grid.Column>
        </Grid.Row>
      </Grid>
      <br />
    </Container>
  );
};

export default AuthorityManagement;

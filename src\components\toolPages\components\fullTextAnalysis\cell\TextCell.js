import React, { useEffect, useState } from "react";
import { Popup, Icon } from "semantic-ui-react";

const TextCell = ({
    // 所有 cell 共用的 props
    colId,
    rowIdx,
    cellValue,
    column,
    rowData,
    domain,
    range,
    graph,
    classType
    // 特定 cell 才有的 props

    // 除了 table 之外的使用,可以自定義其他的 property 擴充功能
}) => {
    //
    const [popContent, setPopContent] = useState("");
    const [sliceContent, setSliceContent] = useState("");
    const [usePopup, setUsePopup] = useState(false);
    const maxContentLength = 20; // 文字顯示長度,超過的部分以 ... 取代
    const isCellValueObject =
        (cellValue && typeof cellValue === "object") || false;

    //
    useEffect(() => {
        if (!cellValue) return;
        let tmpSlice = "";
        if (typeof cellValue === "object") {
            Object.keys(cellValue).forEach(key => {
                if (key.startsWith("label")) {
                    tmpSlice = cellValue[key];
                }
            });
        } else if (String(cellValue).length >= maxContentLength) {
            setUsePopup(true);
            tmpSlice = `${cellValue.slice(0, maxContentLength)}......`;
        } else {
            setUsePopup(false);
            tmpSlice = cellValue;
        }
        setPopContent(cellValue);
        setSliceContent(tmpSlice);
    }, [cellValue]);

    // 複製文字的 Icon
    const CopyEl = () => {
        const [show, setShow] = useState(false);
        const colorDef = "grey";
        const [color, setColor] = useState(colorDef);
        const copy = async () => {
            try {
                await navigator.clipboard.writeText(cellValue);
                setShow(true);
                setColor("blue");
                setTimeout(() => {
                    setShow(false);
                    setColor(colorDef);
                }, [2000]);
            } catch (e) {
                //
            }
        };

        const iconProps =
            (color && {
                color
            }) ||
            {};

        if (!cellValue) return null;

        return (
            <React.Fragment>
                <div
                    onClick={() => copy()}
                    style={{ cursor: "pointer" }}
                    onMouseOver={e => {
                        e.stopPropagation();
                    }}
                >
                    <Icon name="copy outline" {...iconProps} />
                    {show && <div style={{ color: "#2185d0" }}>Copied!</div>}
                </div>
            </React.Fragment>
        );
    };

    if (usePopup) {
        return (
            <Popup
                on={["hover"]}
                hoverable
                wide
                position="right center"
                trigger={<div>{JSON.stringify(sliceContent)}</div>}
            >
                <Popup.Content>
                    <div style={{ maxHeight: "300px", overflow: "auto" }}>
                        {(popContent || "").split("\n").map((text, idx) => (
                            <div key={idx.toString()}>{text}</div>
                        ))}
                    </div>
                    <CopyEl />
                </Popup.Content>
            </Popup>
        );
    }
    if (isCellValueObject) {
        return (
            <div>
                <div>{sliceContent}</div>
                {Object.entries(cellValue)
                    .filter(
                        ([key, val]) =>
                            !(
                                (key || "").startsWith("id") ||
                                (key || "").startsWith("label")
                            )
                    )
                    .map(([key, val], idx) => (
                        <div key={idx.toString()}>
                            {key}: {val}
                        </div>
                    ))}
                <CopyEl />
            </div>
        );
    }
    return (
        <div>
            {sliceContent} <CopyEl />
        </div>
    );
};

export default TextCell;

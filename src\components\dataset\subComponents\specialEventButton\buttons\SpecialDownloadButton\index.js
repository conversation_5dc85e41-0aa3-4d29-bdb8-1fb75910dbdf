import React, { useContext, useEffect, useMemo, useState } from 'react';

// ui
import { <PERSON><PERSON>, Icon, Modal } from 'semantic-ui-react';
import ExcelJs from 'exceljs';
// custom

// common
import columns from 'elasticsearch/src/lib/log';
import { displayName } from 'react-quill';
import { saveAs } from 'file-saver';
import { isEmpty } from '../../../../../../commons';

// store
import { StoreContext } from '../../../../../../store/StoreProvider';
import CustomMenu from './CustomMenu';
import CustomContent from './CustomContent/CustomContent';
import { FILTER_HEADER, ONLY_ACTIVE_HEADER, ONLY_CASEINFO, TABLE_KEY } from './downloadConfig';

import { exportToFile, getYMDNow } from '../../../../../common/sheetCrud/utils';
import { LAST_MODIFIED, PEICES_INFO } from '../../../../../common/sheetCrud/sheetCrudHelper';
import Api from '../../../../../../api/nmtl/Api';
import { readNmtlData } from '../../../../../../api/nmtl';
import {
    EDIT_ROW,
    PERSON_HEADERS,
    INTRODUCTION_HEADERS,
    HAS_AUTHOR_HEADERS,
    AUTHOR_NAME_HEADERS,
    HAS_TRANSLATOR_HEADERS,
    TRANSLATOR_NAME_HEADERS,
    HAS_EDITOR_HEADERS,
    HAS_PUBLISHER_HEADERS,
    REFERENCES_HEADERS,
} from '../../../content/ContentConfig';
import { createHistoryEvent } from '../../../../../downloadData/components/history/common/common';

const SpecialDownloadButton = () => {
    const [open, setOpen] = useState(false);

    // eslint-disable-next-line no-unused-vars
    const [state] = useContext(StoreContext);
    // get dataset(mainSubject) and sheet
    const { content, mainSubject, sheet, search } = state.data;
    const { dataset: datasetLabel } = mainSubject.selected;
    const { key: sheetName, hasTab, contentInfoPath, contentSearchPath } = sheet.selected;
    const { header: h, activeHeader, tabKey } = sheet;
    const { tabClass } = tabKey;
    const { checked, rows } = content;
    const [curMenu, setCurMenu] = useState((hasTab ? hasTab[0].tabClass : sheetName) || undefined);
    const [checkHeader, setCheckHeader] = useState([]);
    const [record, setRecord] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const { keyword, searchColumn: searchCol } = search;

    const activeHeaderIds = useMemo(() => activeHeader[sheetName]?.map((item) => item.id), [
        activeHeader,
        sheetName,
    ]);

    const splitHeader = (allHeader, activeHeaderId) => {
        const initialResult = {
            [TABLE_KEY.infoArr]: [],
            [TABLE_KEY.tableArr]: [],
        };

        const result = allHeader.reduce((acc, cur) => {
            const defaultCheck = activeHeaderId.includes(cur.id);
            const tmpObj = { ...cur, checked: defaultCheck };

            if (activeHeaderId.includes(cur.id)) {
                acc[TABLE_KEY.tableArr].push(tmpObj);
            } else {
                acc[TABLE_KEY.infoArr].push(tmpObj);
            }
            return acc;
        }, initialResult);

        if (sheetName === 'Person') {
            if (initialResult[TABLE_KEY.tableArr].length > 0) {
                result[TABLE_KEY.tableArr] = [
                    result[TABLE_KEY.tableArr][0],
                    ...PERSON_HEADERS,
                    ...result[TABLE_KEY.tableArr].slice(2),
                ];
            }
            if (initialResult[TABLE_KEY.infoArr].length > 0) {
                result[TABLE_KEY.infoArr] = [
                    ...result[TABLE_KEY.infoArr].slice(0, 3),
                    ...INTRODUCTION_HEADERS[0],
                    ...result[TABLE_KEY.infoArr].slice(4),
                ];
            }
        }

        if (sheetName === 'PublicationInfo') {
            if (initialResult[TABLE_KEY.tableArr].length > 0) {
                result[TABLE_KEY.tableArr] = [
                    ...result[TABLE_KEY.tableArr].slice(0, 2),
                    ...HAS_AUTHOR_HEADERS,
                    ...result[TABLE_KEY.tableArr].slice(3),
                ];
            }
            if (initialResult[TABLE_KEY.infoArr].length > 0) {
                result[TABLE_KEY.infoArr] = [
                    ...AUTHOR_NAME_HEADERS,
                    ...HAS_TRANSLATOR_HEADERS,
                    ...TRANSLATOR_NAME_HEADERS,
                    ...HAS_EDITOR_HEADERS,
                    ...HAS_PUBLISHER_HEADERS,
                    ...result[TABLE_KEY.infoArr].slice(5, 10),
                    ...REFERENCES_HEADERS,
                    ...INTRODUCTION_HEADERS[1],
                    ...result[TABLE_KEY.infoArr].slice(12),
                ];
            }
        }

        return result;
    };

    useEffect(() => {
        if (hasTab) {
            const head = hasTab.map((obj) => {
                const { tabClass: type, displayHeader } = obj;
                if (ONLY_ACTIVE_HEADER.includes(type)) {
                    const filterIdHeader = displayHeader.filter(
                        (el) => !FILTER_HEADER.includes(el),
                    );

                    const defaultHeader = obj.headers.filter((el) =>
                        filterIdHeader.includes(el.id),
                    );

                    return {
                        sheetKey: type,
                        allHeaders: defaultHeader,
                        checkHeader: filterIdHeader,
                    };
                }

                const defaultHeader = displayHeader.filter((el) => !FILTER_HEADER.includes(el));
                // 翻譯本數只有 caseInfo 會出現
                const filterHeader = obj.headers
                    .filter((el) => el.id !== ONLY_CASEINFO)
                    .filter((el) => !FILTER_HEADER.includes(el.id));

                return {
                    sheetKey: type,
                    allHeaders: filterHeader,
                    checkHeader: defaultHeader,
                };
            });
            setCheckHeader(head);
            return;
        }

        const activeHeaderId =
            activeHeader &&
            activeHeader[sheetName]?.map((el) => el.id).filter((el) => !FILTER_HEADER.includes(el));

        const filterHeader = h.filter((el) => !FILTER_HEADER.includes(el.id));

        setCheckHeader([
            {
                sheetKey: sheetName,
                allHeaders: filterHeader,
                checkHeader: activeHeaderId,
            },
        ]);
    }, [curMenu, activeHeader, h]);

    useEffect(() => {
        const records = checkHeader
            .filter((info) => info.sheetKey === curMenu)
            .reduce((acc, item) => {
                const { allHeaders, checkHeader: activeHeaderId, sheetKey } = item;

                if (allHeaders && activeHeaderId) {
                    const { tableArr, infoArr } = splitHeader(allHeaders, activeHeaderId);

                    // console.log("infoArr, tableArr", infoArr, tableArr);
                    acc[sheetKey] = {
                        [TABLE_KEY.tableArr]: tableArr,
                        [TABLE_KEY.infoArr]: infoArr,
                        default: activeHeaderId,
                    };
                }

                return acc;
            }, {});

        setRecord(records);
    }, [checkHeader, curMenu]);

    const handleCancel = () => {
        setOpen(false);
    };

    const searchNmtlData = async (searchPath) => {
        const searchColumn = ['all'].concat(activeHeaderIds);

        const searchArg = `search_${searchCol}=${keyword}`;
        const otherArg = searchColumn
            .filter((el) => el !== EDIT_ROW.id && el !== LAST_MODIFIED && el !== searchCol)
            .map((el) => `&search_${el}=`)
            .join('');

        const totalArg = `${searchArg}${otherArg}`;

        const searchApiUrl = searchPath
            .replace('{ds}', datasetLabel)
            .replace('keyword={keyword}', totalArg);

        if (!searchApiUrl) {
            setIsLoading(false);
            return { data: [], total: null, error: 'Not ready' };
        }
        return readNmtlData(searchApiUrl);
    };

    const parseToInt = (index) => {
        const [row, transListIndex] = index.split('-');
        const rowAt = parseInt(row, 10);
        const transAt = parseInt(transListIndex, 10);
        return { rowAt, transAt };
    };

    const filename = `${datasetLabel}_${sheetName}_${getYMDNow()}`;

    const handToExcel = (excelInfo) => {
        const workbook = new ExcelJs.Workbook();

        excelInfo.forEach((infoObj) => {
            const { sheetKey, exportHeader: head, contentData: data } = infoObj;

            const newRows = [];
            data.forEach((ed) => {
                const edRow = [];
                head.forEach((el) => {
                    if (Object.hasOwn(ed, el.id)) {
                        // console.log(ed, h.id);
                        edRow.push(ed[el.id]);
                    } else {
                        edRow.push('');
                    }
                });
                newRows.push(edRow);
            });

            const sh = workbook.addWorksheet(sheetKey);
            sh.addRow(head.map((s) => `${s.label}\n${s.id}`));
            sh.addRows(newRows);
        });

        workbook.xlsx
            .writeBuffer()
            .then((buffer) => {
                const blob = new Blob([buffer], {
                    type: 'application/vnd.ms-excel;charset=utf-8;',
                });
                saveAs(blob, `${filename}.xlsx`);
            })
            .catch((err) => console.log('download Error', err));

        setIsLoading(false);
    };
    const handleDownload = async () => {
        setIsLoading(true);
        const multiData = [];
        let ids = [];
        let sheets = record;

        if (!isEmpty(checked) && !isEmpty(hasTab)) {
            sheets = { [tabClass]: record[tabClass] };
        }

        // if search
        if (keyword) {
            let pathUrl = contentSearchPath;
            if (hasTab) {
                const curInfo = hasTab.find((el) => el.tabClass === tabClass);
                pathUrl = curInfo.contentSearchPath;
                sheets = { [tabClass]: record[tabClass] };
                const { data: searchData } = await searchNmtlData(pathUrl);
                ids = searchData.map((el) => el.srcId);
            } else {
                const { data: searchData } = await searchNmtlData(pathUrl);
                ids = searchData.map((el) => el.srcId);
                sheets = { [sheetName]: record[sheetName] };
            }

            // const searchTotal = searchData.length;
            // 挑出搜尋結果的所有id

            // const { data } = await searchNmtlData();
            // searchData = data;
            // searchTotal = searchData.length;
            // // 挑出搜尋結果的所有id
            // searchIds = searchData.map(el => el.id).join(",");
            // if (searchTotal === 0) {
            //     return;
            // }
        }

        // const sh = Object.keys(sheets);
        for (const name in sheets) {
            // console.log("name", name);
            // let ids = [];
            const { tableArr, infoArr } = sheets[name];
            if (tableArr && infoArr) {
                const tableChecked = tableArr.filter((el) => el.checked) || [];
                const infoChecked = infoArr.filter((el) => el.checked) || [];
                const downloadHeader = [...tableChecked, ...infoChecked].sort(
                    (a, b) => a.seq - b.seq,
                );

                // console.log(checked, rows);
                // 勾選
                if (!isEmpty(checked) && !isEmpty(rows)) {
                    const rowIdx = checked.map((el) => el.rowId);
                    const checkedId = rowIdx.map((index) => {
                        if (name === PEICES_INFO) {
                            // 字串取第一個數字
                            const { rowAt, transAt } = parseToInt(index);
                            return rows[rowAt].transList[transAt].srcId;
                        }
                        return rows[index].srcId;
                    });
                    ids = isEmpty(ids) ? checkedId : ids.filter((el) => checkedId.includes(el));
                }

                let apiReadPath = hasTab
                    ? hasTab.find((el) => el.tabClass === name).contentDownloadPath
                    : contentInfoPath;

                apiReadPath = apiReadPath.replace(
                    'dl_publicationInfo/peicesInfo/edit/2.1',
                    'dl_publicationInfo/peicesInfo/figure/1.0',
                );

                // getData function
                const { data: contentData, head: exportHeader } = await exportToFile(
                    datasetLabel,
                    downloadHeader,
                    apiReadPath,
                    isEmpty(ids) ? '' : ids.join(','),
                );

                if (!isEmpty(contentData) && !isEmpty(datasetLabel) && !isEmpty(exportHeader)) {
                    multiData.push({
                        sheetKey: name,
                        exportHeader,
                        contentData,
                    });
                    // 歷史紀錄，下載成功
                    createHistoryEvent(datasetLabel, '下載', columns.join('/'));
                } else {
                    console.log('失敗');
                    // 歷史紀錄，下載失敗
                    createHistoryEvent(displayName, '下載失敗', columns.join('/'));
                }
            }
        }

        // start download
        if (!isEmpty(multiData)) {
            handToExcel(multiData);
        }
    };

    return (
        !isEmpty(datasetLabel) &&
        !isEmpty(sheetName) && (
            <Modal
                size="large"
                onClose={() => setOpen(false)}
                onOpen={() => setOpen(true)}
                open={open}
                trigger={
                    <Button color="orange" floated="right">
                        下載
                    </Button>
                }
            >
                <Modal.Header>下載表單</Modal.Header>
                <Modal.Content scrolling>
                    <Modal.Description>
                        <CustomMenu curMenu={curMenu} setCurMenu={setCurMenu} />
                        <CustomContent
                            curMenu={curMenu}
                            setCurMenu={setCurMenu}
                            record={record}
                            setRecord={setRecord}
                        />
                    </Modal.Description>
                </Modal.Content>
                <Modal.Actions>
                    <Button onClick={handleCancel}>關閉</Button>
                    <Button color="orange" onClick={handleDownload}>
                        {isLoading && <Icon name="circle notched" loading={isLoading} />}
                        下載
                    </Button>
                </Modal.Actions>
            </Modal>
        )
    );
};

export default SpecialDownloadButton;

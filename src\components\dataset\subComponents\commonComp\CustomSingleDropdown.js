import React, { useContext, useState, useMemo, useEffect } from 'react';

// store
import { createFilter } from 'react-select';
import CreatableSelect from 'react-select/creatable';
import { StoreContext } from '../../../../store/StoreProvider';
import { getMenuPlacement } from '../../datasetConfig';

// custom ui
import MenuList from './MenuList';

// common
import { MAX_OPTION } from '../../../common/sheetCrud/sheetCrudHelper';

// api
import Act from '../../../../store/actions';

/** options:
 * [{id: "", label: "", value: ""}, ...]
 * */
// filter duplicate option
const filterOption = (options = []) => {
  let uniqueArray = [];

  // step 1: create mapping object => {[key: string]: boolean}
  const uniqeLabelObj = options.reduce((acc, next) => {
    if (next.label) {
      acc[next.label] = false;
    }
    return acc;
  }, {});

  // step 2: filter by uniqeLabelObj
  uniqueArray = options.filter((el) => {
    if (el.label) {
      if (!uniqeLabelObj[el.label]) {
        uniqeLabelObj[el.label] = true;
        return true;
      }
    }

    return false;
  });

  return uniqueArray;
};

const CustomSingleDropdown = ({
  cellId,
  rowId,
  // default 0
  idx = 0,
  defaultValue,
  createState,
  setCallback,
  isDiffValue = false,
}) => {
  const [, dispatch] = useContext(StoreContext);
  const [inputValue, setInputValue] = useState(defaultValue);

  const handleInputChange = (value) => {
    if (!value) {
      return;
    }
    setInputValue(value);
    // 不用更新 createState
    // setCallback(cellId, rowId, idx, { isOption: true, input: inputValue });
  };

  // change input value
  const handleChange = (selectValue) => {
    const sltVal = selectValue === null ? { [idx]: null } : { [idx]: selectValue.id };
    const cellValue = { ...createState.value, ...sltVal };
    const curValue = createState && createState.value ? createState.value[idx] : null;

    if (curValue === selectValue?.id) {
      // dispatch
      dispatch({
        type: Act.DATA_CONTENT_ROW_NO_CHANGED,
        payload: {
          rowId,
          cellId,
          idx,
        },
      });
    } else {
      // dispatch
      dispatch({
        type: Act.DATA_CONTENT_ROW_CHANGED,
        payload: {
          rowId,
          cellId,
          // Object { 0: "EthnicAmis" }
          cellValue,
        },
      });
    }

    // 最後再 setCallback 去 change CreateState
    setCallback(cellId, rowId, idx, {
      isOption: true,
      value: cellValue,
    });
  };

  const customStyles = {
    container: (styles) => ({
      ...styles,
      margin: '-9px',
      minWidth: '100%',
    }),
    control: (styles, { selectProps: { controlColor } }) => ({
      ...styles,
      borderStyle: 'none',
      borderRadius: 'unset',
      backgroundColor: controlColor,
    }),
  };

  const customPlacement = getMenuPlacement(rowId);
  const customControlBgColor = isDiffValue ? '#f9c09a66' : '';
  const uniqueArray = filterOption(createState.options);

  return useMemo(
    () => (
      <CreatableSelect
        isClearable
        styles={customStyles}
        isDisabled={createState.isLoading}
        isLoading={createState.isLoading}
        options={
          // 僅針對現今縣市名做特別篩選，移除擁有同id及label的選項並保留其一
          // eslint-disable-next-line no-nested-ternary
          cellId !== 'hasCurrentCity'
            ? createState.input || inputValue
              ? createState.options
                  .filter((o) => o.label.includes(createState.input || inputValue))
                  .slice(0, MAX_OPTION)
              : createState.options.slice(0, MAX_OPTION)
            : uniqueArray
        }
        value={
          createState.value
            ? createState.options
                .filter((o) => o.id === createState.value[idx])
                .slice(0, MAX_OPTION)
            : null
        }
        onChange={handleChange}
        onInputChange={handleInputChange}
        isValidNewOption={() => false}
        components={{ MenuList }}
        menuPlacement={customPlacement}
        controlColor={customControlBgColor}
        // [fixed] select lag issue: https://github.com/JedWatson/react-select/issues/3128 by M1K3Yio commented on 19 Oct 2018
        filterOption={createFilter({ ignoreAccents: false })}
      />
    ),
    [cellId, createState, createState.isLoading, inputValue],
  );
};

export default CustomSingleDropdown;

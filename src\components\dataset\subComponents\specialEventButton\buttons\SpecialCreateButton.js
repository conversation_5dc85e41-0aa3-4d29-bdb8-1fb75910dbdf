import React, { useContext } from "react";
import { Button } from "semantic-ui-react";
import { useHistory } from "react-router-dom";
import { isEmpty } from "../../../../../commons";
import { StoreContext } from "../../../../../store/StoreProvider";

const SpecialCreateButton = () => {
    const [state] = useContext(StoreContext);
    const { sheet } = state.data;
    const { headerFields } = sheet;
    const history = useHistory();

    return (
        <Button
            color="teal"
            floated="right"
            onClick={() => history.push("/Dataset/Information/create")}
            disabled={!headerFields || isEmpty(headerFields)}
        >
            新增
        </Button>
    );
};

export default SpecialCreateButton;

import React, { useContext } from "react";
import { StoreContext } from "../../../../store/StoreProvider";
import TableCellValue from "./TableCellValue";
import {
    DISABLED_CHANGE,
    EDIT_BUTTON
} from "../../../common/sheetCrud/sheetCrudHelper";
import { isObject } from "../../../websiteSetting/commons";
import CustomSingleLabelReadOnly from "./CustomSingleLabelReadOnly";
import { uuidv4 } from "../../../../commons/utility";

const MultiOptionDefault = {
    isLoading: false,
    options: [],
    value: undefined,
    input: ""
};

// ctIdx -1 為 create
const SpeacialTableCellValue = ({
    actHeader,
    cellValue,
    ctIdx,
    shIdx,
    activePage = "0",
    createState,
    setCallback
    // isCreateNew = false
}) => {
    // eslint-disable-next-line no-unused-vars
    const [state] = useContext(StoreContext);

    // get dataset(mainSubject) and sheet
    const { sheet } = state.data;
    // const { key: sheetName } = sheet.selected;
    const { headerFields } = sheet;

    if (Object.keys(headerFields).length === 0) {
        return null;
    }

    // newCreateState: Object { 0: "fdsafdsafdsa", 1: "fdsafdsafsadfdsafsdfdsa@zh" }
    // default 為單行的文字，斷行視為字串的一部份。
    // 全部都以 array 型式
    // const newCreateState = createState || cellValue || { 0: "" };
    // const newSingleCreateState = createState || {
    //     isLoading: false,
    //     options: [],
    //     value: cellValue,
    //     input: ""
    // };
    // const newMultiCreateState = createState || cellValue || MultiOptionDefault;
    //
    // if (DISABLED_CHANGE.indexOf(actHeader) > -1) {
    //     if (isObject(cellValue)) {
    //         return Object.values(cellValue).map(val => (
    //             <div key={uuidv4()}>
    //                 {val}
    //                 <br />
    //             </div>
    //         ));
    //     }
    //     return cellValue;
    // }
};
export default SpeacialTableCellValue;

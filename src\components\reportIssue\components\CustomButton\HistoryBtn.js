import React, { useState } from "react";
import PropTypes from "prop-types";
import { useSelector, useDispatch } from "react-redux";

// semantic ui
import { Button } from "semantic-ui-react";

// components
import HistoryModal from "../CustomModal/HistoryModal";
import RPAct from "../../reportIssueAction";

function HistoryBtn({ fsID }) {
    const dispatch = useDispatch();
    const { allData } = useSelector(state => state.report);

    const [openHSModal, setOpenHSModal] = useState(false);

    const handleClick = () => {
        const findObj = allData.find(el => el.id === fsID);
        if (findObj) {
            setOpenHSModal(true);

            dispatch({
                type: RPAct.SET_RPHISDATA,
                payload: {
                    id: findObj?.id || "",
                    history: findObj?.history || []
                }
            });
        }
    };

    const onClose = () => {
        setOpenHSModal(false);
    };

    return (
        <>
            <Button
                basic
                size="mini"
                circular
                icon="time"
                onClick={handleClick}
            />
            <HistoryModal openModal={openHSModal} onClose={onClose} />
        </>
    );
}

HistoryBtn.propTypes = {
    /** firestore id */
    fsID: PropTypes.string
};

HistoryBtn.defaultProps = {
    /** firestore id */
    fsID: ""
};

export default HistoryBtn;

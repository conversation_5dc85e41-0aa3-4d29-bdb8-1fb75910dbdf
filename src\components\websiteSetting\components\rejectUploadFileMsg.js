export default function rejectUploadFileMsg(file, action) {
  const { reason, setWarningMsg, warningMsg } = action;
  switch (reason) {
    case "INVALID_FORMAT":
      setWarningMsg(
        warningMsg ||
          `File type is not belong to one of following "jpg", "jpeg", "png", "webp", "avif". \n`
      );
      break;
    case "EXCEED_MAX_SIZE":
      warningMsg || setWarningMsg(`${file.name} size is exceed 4MB. \n`);
      break;
    default:
      break;
  }
}

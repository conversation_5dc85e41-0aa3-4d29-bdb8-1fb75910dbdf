import React from "react";
import { useSelector } from "react-redux";

// component
import FolderControlPanel from "./FolderControlPanel";
import Folder from "../../../../../../common/imageCommon/folder";
import FilePickerModal from "../../../../../../toolPages/components/upload/uploadImage/filePicker/FilePickerModal";

const CurrentFolder = ({ type }) => {
    const {
        files: { pickConfig }
    } = useSelector(state => state);

    return (
        <Folder
            type={type}
            pickConfig={pickConfig.datasetPage}
            FolderControlPanel={FolderControlPanel}
            galleryFirstChild={<FilePickerModal />}
        />
    );
};

export default CurrentFolder;

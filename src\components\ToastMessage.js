import React, { useContext, useEffect, useCallback } from 'react';
import toast, { Toaster } from 'react-hot-toast';

// store
import { StoreContext } from '../store/StoreProvider';
import Act from '../store/actions';

const AlertMessage = () => {
  const [state, dispatch] = useContext(StoreContext);

  const { data } = state;
  const { message } = data;
  const { title, success, error } = message;

  const handleAlert = useCallback(() => {
    if (title) {
      const msg = `${title || 'unKnown'}, Success: ${success || 0}, Error: ${error || 0}`;

      toast(msg, {
        duration: 3000,
        style: {
          background: error ? '#ffe8e6' : '#e5f9e7',
          color: error ? '#db2828' : '#1ebc30',
          padding: '12px 18px',
          borderRadius: '8px',
          border: error ? '1px solid #db2828' : '1px solid #1ebc30',
          fontSize: '1rem',
        },
      });

      dispatch({ type: Act.DATA_MESSAGE_CLEAN });
    }
  }, [title, success, error, dispatch]);

  useEffect(() => {
    handleAlert();
  }, [handleAlert]);

  return <Toaster position="top-center" />;
};

export default AlertMessage;

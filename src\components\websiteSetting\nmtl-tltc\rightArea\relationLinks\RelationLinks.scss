@mixin rightComponentMain {
  width: 95%;
  height: 95%;
  padding: 20px;
}

@mixin nameRow {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  div:first-child {
    width: 15%;
    margin-right: 0.5rem;
  }
  div:nth-child(2) {
    width: 85%;
  }
}

.RelationLinks {
  @include rightComponentMain;
  .TopArea {
    display: grid;
    //grid-template-columns: 45% 1fr;
    margin-bottom: 1%;
    .DropDownList {
      justify-self: start;
      width: 100%;
      height: 7%;
      padding: 12px;
    }
    .ButtonBox {
      justify-self: end;
      button:last-child {
        margin: 0;
      }
    }
  }

  .flexBox {
    display: flex;
    justify-content: space-between;
  }
  
  .infoIcon {
    background-color: black;
    color:white;
    width: 20px;
    height: 20px;
    text-align: center;
    font-size: xx-small;
    margin-left: 10px;
    border-radius: 50%;
  }

  .ContentArea {
    overflow-y: scroll;
    height: inherit;
    .SortedHeader{
      display: flex;
      justify-Content: space-between;
      button {
        font-size: 0.4rem;
      }
    }
  }

  .EditContent {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
    .TopArea {
      justify-content: start;
      align-self: start;
    }
    .TableArea {
      &__CHName {
        @include nameRow;
        margin-bottom: 3%;
      }
      &__ENName {
        @include nameRow;
      }
    }
    .BottomArea {
      display: flex;
      justify-content: end;
      margin-top: 20%
    }
  }
}




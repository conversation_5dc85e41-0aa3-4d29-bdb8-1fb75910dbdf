import React, { useState, useMemo, useEffect } from "react";

// ui
import { Form, Input, Label } from "semantic-ui-react";
// import { StoreContext } from "../../../../../store/StoreProvider";
import useDebounce from "../../../../common/hooks/useDebounce";

// store
const CustomDoubleInput = ({
    cellId,
    createState,
    disabled = false,
    setCallback,
    // Array
    defaultValue,
    titleFirst,
    titleSecond,
    menuName,
    isDisableSecondInput
}) => {
    // input value
    const [firstInput, setFirstInput] = useState(null);
    const [secondInput, setSecondInput] = useState(null);
    const debFirstInput = useDebounce(firstInput, 300); // debounce value
    const debSecondInput = useDebounce(secondInput, 300); // debounce value

    useEffect(() => {
        if (defaultValue) {
            let newDefaultValue = defaultValue;
            // FIXME:
            if (!Array.isArray(defaultValue)) {
                newDefaultValue = defaultValue.split("\n");
            }
            // 權威中文
            const zhContent =
                newDefaultValue.find(label => label?.endsWith("@zh")) || null;
            // 權威外文
            const enContent =
                newDefaultValue.find(label => label?.endsWith("@en")) || null;

            if (zhContent) setFirstInput(zhContent);
            if (enContent) setSecondInput(enContent);
        }
    }, [menuName, defaultValue]);

    const handleZhChange = value => {
        if (disabled) {
            return;
        }

        if (!value.endsWith("@zh")) {
            setFirstInput(value);
            return;
        }

        setFirstInput(value);
    };
    //
    const handleLangChange = value => {
        if (disabled) {
            return;
        }

        setSecondInput(value);
    };
    //
    useEffect(() => {
        if (debFirstInput === null) return;

        // debFirstInput === "" 存 null
        if (debFirstInput === "") {
            setCallback(cellId, [null, debSecondInput], menuName);
            return;
        }

        setCallback(cellId, [debFirstInput, debSecondInput], menuName);
    }, [debFirstInput]);
    //

    useEffect(() => {
        if (debSecondInput === null) return;

        // debSecondInput === "" 存 null
        if (debSecondInput === "") {
            setCallback(cellId, [debFirstInput, null], menuName);
            return;
        }

        setCallback(cellId, [debFirstInput, debSecondInput], menuName);
    }, [debSecondInput]);

    return useMemo(
        () => (
            <Form>
                <Form.Field inline>
                    <Label basic> {titleFirst || "中文"}</Label>
                    <Input
                        id={titleFirst}
                        fluid
                        value={firstInput || ""}
                        onChange={(e, data) => handleZhChange(data.value)}
                        style={{ marginTop: "0.5rem" }}
                    />
                </Form.Field>
                <Form.Field inline>
                    <Label basic> {titleSecond || "外文"}</Label>
                    <Input
                        id={titleSecond}
                        fluid
                        value={secondInput || ""}
                        onChange={(e, data) => handleLangChange(data.value)}
                        disabled={!isDisableSecondInput}
                        style={{
                            backgroundColor: "gray",
                            cursor: "not-allowed",
                            marginTop: "0.5rem"
                        }}
                    />
                </Form.Field>
            </Form>
        ),
        [cellId, firstInput, secondInput, createState, menuName]
    );
};

export default CustomDoubleInput;

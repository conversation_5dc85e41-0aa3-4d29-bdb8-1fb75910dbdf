import React from "react";
import { useSelector } from "react-redux";
import "./Panel.scss";
import textConfig from "../../../textConifg";

const Panel = ({ isUpdating, handleUpdate, handleDelete }) => {
    const state = useSelector(tmpState => tmpState.ataiMng);
    const { updateTime } = state;

    return (
        <div className="panel">
            {!isUpdating ? (
                <span className="updateTime">
                    {updateTime}
                    {textConfig.update}
                </span>
            ) : (
                <div className="updatingMsg">
                    <p>{textConfig.dataUpdating}</p>
                    <p>{textConfig.dataUpdateExplain}</p>
                </div>
            )}
            <button
                className="updateDbBtn"
                disabled={isUpdating}
                onClick={handleUpdate}
                style={{
                    backgroundColor: isUpdating && "#FBFAC6",
                    cursor: isUpdating && "default"
                }}
            >
                {textConfig.updateDbBtnTitle}
            </button>
            <button
                className="deletBtn"
                disabled={isUpdating}
                onClick={handleDelete}
                style={{
                    backgroundColor: isUpdating && "#FEE2E2",
                    cursor: isUpdating && "default"
                }}
            >
                {textConfig.deleteBtnTitle}
            </button>
        </div>
    );
};

export default Panel;

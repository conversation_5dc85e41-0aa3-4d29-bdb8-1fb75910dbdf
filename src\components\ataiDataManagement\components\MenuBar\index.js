import React, { useEffect } from "react";
import { Menu } from "semantic-ui-react";
import { useSelector, useDispatch } from "react-redux";
import ataiMngAct from "../../../../reduxStore/ataiManage/ataiManageAction";
import menuItem from "./menuItem";

const MenuBar = () => {
    const state = useSelector(tmpState => tmpState.ataiMng);
    const { activeItemAtai } = state;
    const dispatch = useDispatch();

    useEffect(() => {
        dispatch({
            type: ataiMngAct.SET_ACTIVE_ITEM_ATAI,
            payload: menuItem[0]
        });
    }, []);

    const handleClick = el => {
        dispatch({
            type: ataiMngAct.SET_ACTIVE_ITEM_ATAI,
            payload: el
        });
    };

    return (
        <Menu pointing vertical style={{ width: "100%" }}>
            {/* 清單顯示 */}
            {menuItem.map(el => {
                const { key, name } = el;
                return (
                    <Menu.Item
                        key={key}
                        name={name}
                        active={activeItemAtai.name === name}
                        onClick={() => handleClick(el)}
                    />
                );
            })}
        </Menu>
    );
};

export default MenuBar;

import React, { useContext, useState } from "react";
import PropTypes from "prop-types";

// semantic ui
import { But<PERSON> } from "semantic-ui-react";

// store & api
import { StoreContext } from "../../../../store/StoreProvider";
import Api from "../../../../api/nmtl/Api";

// uilts
import textMsg from "../textMsg";
import { convertSimpleEntrySD } from "../../../common/sheetCrud/sheetCrudHelper";
import { updateNmtlData } from "../../../../api/nmtl";
import {
    connEvent,
    createInstIDonly,
    getClassType
} from "../../nmtl-tltc/rightArea/relationLinks/utils/utils";
import { isEmpty } from "../../../../commons";

// components
import SaveResultModal from "./SaveResultModal";
import { createHistoryEvent } from "../../../downloadData/components/history/common/common";
import useGetSubjectOPs from "../../../common/hooks/useGetSubjectOPs";

function SaveButton(props) {
    const { srcData, dstData, closeCallBack, checkCallBack } = props;
    const { setPass } = props;
    const [state] = useContext(StoreContext);
    const { modal, sheetName, graph } = textMsg;
    const { user, websiteSetting } = state;
    const { displayName } = user;
    const { menuActiveItem, websiteSubject } = websiteSetting;
    const { headerActiveName } = state.common;
    const { groupInfo } = state.data;
    const dropOptions = useGetSubjectOPs(groupInfo);

    const [loading, setLoading] = useState(false);
    const [opSaveRModal, setOpSaveRModal] = useState(false);
    const [modalMessage, setModalMessage] = useState("");

    const sheetSelectedValue = dropOptions.find(it => it.key === websiteSubject)
        ?.text;
    const columns = [headerActiveName, sheetSelectedValue, menuActiveItem.name];

    const openModal = () => {
        setOpSaveRModal(true);
    };

    const closeModal = () => {
        setOpSaveRModal(false);

        if (closeCallBack) {
            closeCallBack();
        }
    };

    const successCallBack = res => {
        openModal();
        setModalMessage(res === "OK" ? modal.success : modal.wrong);
        setLoading(false);
    };

    const failedCallBack = err => {
        openModal();
        setModalMessage(`${modal.failed} \n ${err}`);
        setLoading(false);
    };

    // 取得儲存資料需要的欄位名稱、語系資料...
    const getKeyData = tmpDstData => {
        // 設定keyData
        const keyIdName = "id";
        return {
            // 要傳給api "srcId"欄位
            keyIdName,
            // 要傳給api "value"欄位的資料內容
            // dbPropName: 要傳給api "value"欄位的 database property name
            // prop: 從apiStr抓到資料內容的欄位名稱
            // lang: 要帶給api的語系名稱 (optional)
            dataValue: Object.keys(tmpDstData)
                .filter(key => key !== keyIdName)
                .map(key => {
                    if (["ZH", "EN"].includes(key.slice(-2).toUpperCase())) {
                        return {
                            dbPropName: key.slice(0, -2),
                            prop: key,
                            lang: key.slice(-2).toLowerCase()
                        };
                    }
                    return { dbPropName: key, prop: key };
                })
        };
    };

    const handleSave = async () => {
        setLoading(true);
        const apiStr = Api.getGeneric;

        const classType = getClassType(dstData.id);
        if (!classType) {
            failedCallBack(modal.noClassPrefix);
            return;
        }

        if (checkCallBack && setPass) {
            // 檢查必填欄位
            const pass = checkCallBack(dstData);
            setPass(pass);
            if (!pass) {
                setLoading(false);
                return;
            }
        }

        const keyData = getKeyData(dstData);
        const entryCol = {
            graph,
            classType,
            ...keyData
        };

        const { entrySrc, entryDst } = convertSimpleEntrySD(
            srcData,
            dstData,
            entryCol
        );

        // console.log(entrySrc, entryDst);
        if (isEmpty(entrySrc)) {
            // create with 3 steps
            // step 1: create new instance connection with current page ID
            let connRes = await connEvent(graph, user, entryDst);
            // step 2: create new instance ID
            // (因為API在建立新的ＩＤ時, 如果value有帶label,會被當成instance id,所以需要單獨建立ＩＤ)
            if (connRes === "OK") {
                connRes = "";
                connRes = await createInstIDonly(graph, user, entryDst);
            }
            // step 3: update new instance data
            if (connRes === "OK") {
                const tmpEntrySrc = Object.assign({}, entryDst, {
                    value: {}
                });
                updateNmtlData(
                    user,
                    apiStr,
                    graph,
                    sheetName,
                    tmpEntrySrc,
                    entryDst
                )
                    .then(res => {
                        successCallBack(res);
                    })
                    .catch(err => {
                        failedCallBack(err);
                    });

                const historyMsg = `${JSON.stringify(entryDst)}`;

                // 建立歷史紀錄
                createHistoryEvent(
                    displayName,
                    "創建",
                    `${columns.join("/")}：${historyMsg}`
                );
            }
        } else {
            // update
            updateNmtlData(user, apiStr, graph, sheetName, entrySrc, entryDst)
                .then(res => {
                    successCallBack(res);
                })
                .catch(err => {
                    failedCallBack(err);
                });

            const historyMsg = `${JSON.stringify(
                entrySrc
            )}\n變動後：\n${JSON.stringify(entryDst)}`;

            // 建立歷史紀錄
            createHistoryEvent(
                displayName,
                "更新",
                `${columns.join("/")}：${historyMsg}`
            );
        }
    };

    return (
        <>
            <Button positive onClick={handleSave} loading={loading}>
                保存
            </Button>
            {/* 更新結果視窗 */}
            <SaveResultModal
                openModal={opSaveRModal}
                onClose={closeModal}
                onClick={closeModal}
                modalMessage={modalMessage}
            />
        </>
    );
}

SaveButton.propTypes = {
    /** 原始資料 */
    srcData: PropTypes.objectOf(PropTypes.any),
    /** 資料變更 */
    dstData: PropTypes.objectOf(PropTypes.any),
    /** 關閉modal視窗做的其他動作 */
    closeCallBack: PropTypes.func,
    /** 檢查必填欄位(optional) */
    checkCallBack: PropTypes.func,
    /** 設定檢查必填欄位結果(optional) */
    setPass: PropTypes.func
};

SaveButton.defaultProps = {
    /** 原始資料 */
    srcData: {},
    /** 資料變更 */
    dstData: {},
    /** 關閉modal視窗做的其他動作 */
    closeCallBack: undefined,
    /** 檢查必填欄位(optional) */
    checkCallBack: undefined,
    /** 設定檢查必填欄位結果(optional) */
    setPass: undefined
};

export default SaveButton;

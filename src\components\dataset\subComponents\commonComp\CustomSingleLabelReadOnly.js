import React, { useMemo, useState } from "react";

// ui
import { Input } from "semantic-ui-react";

const CustomSingleLabelReadOnly = ({
    cellId,
    defaultValue,
    createState,
    ...rest
}) => {
    const [inputValue] = useState(defaultValue);
    return useMemo(
        () => (
            <Input
                readOnly
                className="srcIdStyle"
                {...rest}
                value={inputValue || ""}
            />
        ),
        [cellId, inputValue, createState]
    );

    // return useMemo(() => <Label {...rest} content={inputValue || ""} />, [
    //     cellId,
    //     inputValue,
    //     createState
    // ]);
};

export default CustomSingleLabelReadOnly;

import React, { useContext, useState, useEffect } from "react";
import PropTypes from "prop-types";

// semantic ui
import { Button } from "semantic-ui-react";

// uilts
import { StoreContext } from "../../../../../../store/StoreProvider";
import Act from "../../../../../../store/actions";
import { getClassType, loadTBData } from "../utils/utils";
import textMsg from "../../../../commons/textMsg";

// components
import SaveResultModal from "../../../../commons/components/SaveResultModal";
import tltcAct from "../../../tltcAction";
import { updateNmtlData } from "../../../../../../api/nmtl";
import Api from "../../../../../../api/nmtl/Api";

function SortButton({ sorted, setSorted, type }) {
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { rellinkTable } = state.websiteSetting;
    const { modal, sheetName, graph } = textMsg;
    const [loading, setLoading] = useState(false);
    const [opSaveRModal, setOpSaveRModal] = useState(false);
    const [modalMessage, setModalMessage] = useState("");
    const [classType, setClassType] = useState("");

    useEffect(() => {
        if (rellinkTable.length === 0 || classType) return;
        // 只設定1次
        setClassType(getClassType(rellinkTable[0].urlId));
    }, [rellinkTable]);

    const openModal = () => {
        setOpSaveRModal(true);
    };

    const closeModal = () => {
        setOpSaveRModal(false);
        loadTBData(type, dispatch);
    };

    const successCallBack = res => {
        openModal();
        setModalMessage(res === "OK" ? modal.success : modal.wrong);
        setLoading(false);
    };

    const failedCallBack = err => {
        openModal();
        setModalMessage(`${modal.failed} \n ${err}`);
        setLoading(false);
    };

    const updateOrder = () => {
        const apiStr = Api.getGeneric;
        // 結束排序後，所有order重新編號更新到資料庫
        // console.log("rellinkTable ", rellinkTable);
        let tmpTable = [...rellinkTable];
        tmpTable = tmpTable.map((el, idx) => ({
            ...el,
            order: `${idx + 1}`
        }));

        // console.log("tmpTable ", tmpTable);
        return rellinkTable.reduce((acc, cur, idx) => {
            const tmpAcc = [...acc];
            if (
                cur.urlId === tmpTable[idx].urlId &&
                cur.order !== tmpTable[idx].order
            ) {
                const commonProp = {
                    graph,
                    classType,
                    srcId: cur.urlId
                };
                const entrySrc = {
                    ...commonProp,
                    value: { order: cur.order }
                };

                const entryDst = {
                    ...commonProp,
                    value: { order: tmpTable[idx].order }
                };

                // console.log(entrySrc, entryDst);
                tmpAcc.push(
                    updateNmtlData(
                        user,
                        apiStr,
                        graph,
                        sheetName,
                        entrySrc,
                        entryDst
                    )
                );
            }
            return tmpAcc;
        }, []);
    };

    const handleSort = () => {
        setSorted(!sorted);
        if (!sorted) {
            // 開始排序時，回到order排序
            dispatch({
                type: Act.FRONTEDIT_TLTC,
                localType: tltcAct.SET_RELLINKTABLE,
                payload: rellinkTable.sort(
                    (a, b) => parseInt(a.order, 10) - parseInt(b.order, 10)
                )
            });
        }

        if (sorted) {
            setLoading(true);
            const upOrderProms = updateOrder();
            Promise.all(upOrderProms)
                .then(res => {
                    const check = res.some(val => val !== "OK");
                    successCallBack(check ? "Not OK" : "OK");
                })
                .catch(err => {
                    const concatMsg = err.join("\n");
                    failedCallBack(concatMsg);
                });
        }
    };

    return (
        <>
            <Button
                color="olive"
                onClick={() => handleSort()}
                loading={loading}
                disabled={rellinkTable.length <= 1}
            >
                {sorted ? "儲存" : "排序"}
            </Button>
            <SaveResultModal
                openModal={opSaveRModal}
                onClose={closeModal}
                onClick={closeModal}
                modalMessage={modalMessage}
            />
        </>
    );
}

SortButton.propTypes = {
    /** 排序功能狀態 */
    sorted: PropTypes.bool,
    /** 排序功能開關 */
    setSorted: PropTypes.func,
    /** 相關連結分類: ["合作夥伴", "相關資源"] */
    type: PropTypes.oneOf(["", "合作夥伴", "相關資源"])
};

SortButton.defaultProps = {
    /** 排序功能狀態 */
    sorted: false,
    /** 排序功能開關 */
    setSorted: () => null,
    /** 相關連結分類: ["合作夥伴", "相關資源"] */
    type: ""
};

export default SortButton;

import React, { useContext, useMemo, useState } from 'react';

// store
import Select from 'react-select';
import { StoreContext } from '../../../../store/StoreProvider';
import Act from '../../../../store/actions';
import { getMenuPlacement } from '../../datasetConfig';

// custom ui
import MenuList from './MenuList';

// common
import { isEmpty, isArrayEqual } from '../../../../commons';
import {
  MAX_OPTION,
  notShowingExistedModalHeader,
  SHOW_ID,
} from '../../../common/sheetCrud/sheetCrudHelper';
import { removeDuplicateId } from '../../../common/sheetCrud/utils';
import {
  checkDataInGraph,
  existedDataInOtherGraph,
  returnClasstype,
} from '../../../../api/nmtl/ApiField';
import ExistedModalForOldForm from './ExistedModalForOldForm';

// 可以新增
const CustomMultiDropdown = ({
  cellId,
  rowId,
  idx = 0,
  createState,
  setCallback,
  isShowId,
  isDiffValue = false,
}) => {
  const [state, dispatch] = useContext(StoreContext);
  const { mainSubject } = state.data;
  const { dataset } = mainSubject.selected;
  const [equalValue, setEqualValue] = useState(null);
  const [open, setOpen] = useState(false);
  const [inputIds, setInputIds] = useState([]);
  const inSHOWID = SHOW_ID.includes(cellId);
  const notShowingExistedModal = notShowingExistedModalHeader.includes(cellId);

  // change dropdown value
  const handleChange = async (selectValue) => {
    const cellValue = isEmpty(selectValue) ? [] : selectValue;
    // extract and combine id, e.g. ORG1/ORG2
    const selectCombinedIds = cellValue.map((item) => item.id).sort();
    setInputIds(selectCombinedIds);
    // 如果selectValue已存在其他資料集，跳modal讓使用者選擇要搬動的資料
    const isInGraph = !isEmpty(cellValue)
      ? await checkDataInGraph(cellValue?.at(-1).id, dataset)
      : true;
    const tmpClasstype =
      !isEmpty(selectValue) && returnClasstype(cellId, selectValue.at(-1)?.label);

    if (!isInGraph && !isEmpty(selectValue) && inSHOWID && !notShowingExistedModal) {
      const sameList = await existedDataInOtherGraph(
        tmpClasstype,
        selectValue.at(-1).label,
        cellId,
      );
      setEqualValue(sameList);
      setOpen(true);
      return;
    }

    const splitValues = !isEmpty(createState.value) ? Object.values(createState?.value || {}) : [];
    if (isArrayEqual(selectCombinedIds, splitValues)) {
      // dispatch
      dispatch({
        type: Act.DATA_CONTENT_ROW_NO_CHANGED,
        payload: {
          rowId,
          cellId,
          idx,
        },
      });
    } else {
      // dispatch
      dispatch({
        type: Act.DATA_CONTENT_ROW_CHANGED,
        payload: {
          rowId,
          cellId,
          idx,
          // cellData: {0: "value1", 1: "value2"}
          cellValue: Object.assign({}, selectCombinedIds),
        },
      });
    }

    // keep input value when it changed
    if (cellValue.length === 0) {
      // clear
      setCallback(cellId, rowId, idx, { isOption: true, value: null });
    } else {
      setCallback(cellId, rowId, idx, {
        isOption: true,
        value: Object.assign({}, selectCombinedIds),
      });
    }
  };

  const handleInputChange = (inputValue) => {
    setCallback(cellId, rowId, idx, { isOption: true, input: inputValue });
  };

  const customStyles = {
    container: (styles) => ({
      ...styles,
      margin: '-9px',
      minWidth: '100%',
    }),
    control: (styles, { selectProps: { controlColor } }) => ({
      ...styles,
      borderStyle: 'none',
      borderRadius: 'unset',
      backgroundColor: controlColor,
    }),
  };

  const customPlacement = getMenuPlacement(rowId);
  const customControlBgColor = isDiffValue ? '#f9c09a66' : '';

  const newOptions = useMemo(() => {
    if (!createState) {
      return [];
    }
    if (!createState.options) {
      return [];
    }

    if (isShowId) {
      return removeDuplicateId(createState);
    }
    return createState.options;
  }, [cellId, createState.options]);

  // console.log(cellId, createState);
  return useMemo(
    () => (
      <>
        <Select
          isMulti
          isClearable
          styles={customStyles}
          isDisabled={createState.isLoading}
          isLoading={createState.isLoading}
          options={
            createState && createState.input && newOptions
              ? newOptions.filter((o) => o.label.includes(createState.input)).slice(0, MAX_OPTION)
              : newOptions.slice(0, MAX_OPTION)
          }
          value={
            createState.value
              ? newOptions.filter((o) => Object.values(createState.value).indexOf(o.id) > -1)
              : null
          }
          onChange={handleChange}
          onInputChange={handleInputChange}
          components={{ MenuList }}
          menuPlacement={customPlacement}
          controlColor={customControlBgColor}
          autosize
        />
        {inSHOWID && (
          <ExistedModalForOldForm
            equalValue={equalValue}
            open={open}
            setOpen={setOpen}
            inputIds={inputIds}
            cellId={cellId}
            rowId={rowId}
            idx={idx}
            setCallback={setCallback}
          />
        )}
      </>
    ),
    [cellId, createState.value, createState.input, createState.isLoading, open, inputIds],
  );
};

export default CustomMultiDropdown;

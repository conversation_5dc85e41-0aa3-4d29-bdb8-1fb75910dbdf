import React, { useContext, useEffect, useState } from 'react';

// semantic ui
import { Dropdown } from 'semantic-ui-react';

// utils
import comTextConfig from '../../../../../commons/comTextConfig';
import { isEmpty } from '../../../../../commons';
import { StoreContext } from '../../../../../store/StoreProvider';
import Act from '../../../../../store/actions';
import menuMapping from '../../menuMapping';
import useGetSubjectOPs from '../../../../common/hooks/useGetSubjectOPs';

function SubjectDropdown() {
    const [state, dispatch] = useContext(StoreContext);
    const { groupInfo } = state.data;

    const dropOptions = useGetSubjectOPs(groupInfo);
    const [opVal, setOpVal] = useState('');

    useEffect(() => {
        if (isEmpty(dropOptions)) return;
        const hasMenuSub = menuMapping.find(
            (el) => dropOptions.findIndex((op) => op.key === el.subject) !== -1,
        );
        setOpVal(hasMenuSub?.subject || dropOptions[0].value);
    }, [dropOptions]);

    useEffect(() => {
        dispatch({
            type: Act.SET_WEBSITESUBJECT,
            payload: opVal,
        });
    }, [opVal]);

    const handleChange = (evt, data) => {
        setOpVal(data.value);
    };

    return (
        <Dropdown
            selection
            options={dropOptions}
            style={{ border: 'none' }}
            placeholder={comTextConfig.Header_Selector}
            onChange={handleChange}
            value={opVal}
            className="Dropdown"
        />
    );
}

export default SubjectDropdown;

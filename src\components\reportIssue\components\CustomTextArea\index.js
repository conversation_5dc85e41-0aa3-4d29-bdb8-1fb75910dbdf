import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { useDispatch, useSelector } from "react-redux";

// semantic ui
import { Form, TextArea } from "semantic-ui-react";

// config
import fbConfig from "../../common/fbConfig";

// utils
import { setNewAllData } from "../../common/utils";
import { isEmpty } from "../../../../commons";

function CustomTextArea({ fsID }) {
    const dispatch = useDispatch();
    const { newAllData } = useSelector(state => state.report);

    const [curData, setCurData] = useState({});

    useEffect(() => {
        if (isEmpty(newAllData)) return;
        const findObj = newAllData.find(el => el.id === fsID);
        if (findObj) {
            setCurData(findObj);
        }
    }, [newAllData]);

    const onChange = (evt, data) => {
        const tmpData = JSON.parse(JSON.stringify(newAllData));
        const findObj = tmpData.find(el => el.id === fsID);
        if (findObj) {
            findObj[fbConfig.response] = data.value;
            setCurData(findObj);
            setNewAllData(dispatch, tmpData);
        }
    };

    return (
        <Form>
            <TextArea
                placeholder="處理摘要"
                onChange={onChange}
                value={curData[fbConfig.response]}
            />
        </Form>
    );
}

CustomTextArea.propTypes = {
    /** firestore id */
    fsID: PropTypes.string
};

CustomTextArea.defaultProps = {
    /** firestore id */
    fsID: ""
};

export default CustomTextArea;

// firebase
import firebase from 'firebase/app';
import 'firebase/firestore';

// common
import { isEmpty } from '../../../commons';
// FIXME: 將 API 從 firebase 移到 Api.js，方便做 API testing。firebase 上的 API 日後需要移除
import datasetSheet from '../../../components/dataset/datasetSheet';

// const SPECIAL_SHEET = ["PublicationInfo", "Person"];
const DisableMS = '0';
const EnableAuthority = '1';
const getMainSubject = () => {
    const ref = firebase
        .firestore()
        .collection('setting')
        .doc('dataset')
        .collection('mainSubject');
    return ref
        .orderBy('seq', 'asc')
        .get()
        .then((querySnapshot) => {
            const sheets = [];
            querySnapshot.forEach((doc) => sheets.push({ id: doc.id, ...doc.data() }));

            const enabledSheets = sheets.filter((sh) => sh.enable !== DisableMS);
            return !isEmpty(enabledSheets)
                ? enabledSheets
                : { error: "doc or collection doesn't exist" };
        });
};

const getSingleLayerCollection = (collectionName) => {
    const ref = firebase.firestore().collection(collectionName);
    return ref.get().then((querySnapshot) => {
        const sheets = [];
        querySnapshot.forEach((doc) => sheets.push({ id: doc.id, ...doc.data() }));
        return !isEmpty(sheets) ? sheets : { error: "doc or collection doesn't exist" };
    });
};

/**
 * 依據 collection 並且限制 where
 * @param collectionName {string}
 * @param wheres {string[]}
 * @returns {Promise<*[] | {error: string}>}
 */
const getSingleLayerCollectionWhere = (collectionName, wheres) => {
    let ref;
    if (Array.isArray(wheres) && wheres.length === 3) {
        ref = firebase
            .firestore()
            .collection(collectionName)
            .where(wheres[0], wheres[1], wheres[2]);
    } else {
        ref = firebase.firestore().collection(collectionName);
    }
    return ref.get().then((querySnapshot) => {
        const sheets = [];
        querySnapshot.forEach((doc) => sheets.push({ id: doc.id, ...doc.data() }));
        return !isEmpty(sheets) ? sheets : { error: "doc or collection doesn't exist" };
    });
};

/**
 * 依據 collection 及 docIds 來取得 documents
 * @param collectionName {string}
 * @param docIds {string[]}
 * @returns {Promise<(*&{id: *})[] | {error: string}>}
 */
const getSingleLayerCollectionDocs = (collectionName, docIds) => {
    if (!Array.isArray(docIds)) return { error: "doc or collection doesn't exist" };
    const queries = docIds.map((docId) =>
        firebase
            .firestore()
            .collection(collectionName)
            .doc(docId)
            .get(),
    );
    return Promise.all(queries).then((res) => {
        const sheets = res.map((doc) => ({ id: doc.id, ...doc.data() }));
        // console.log("sheets", sheets);
        return !isEmpty(sheets) ? sheets : { error: "doc or collection doesn't exist" };
    });
};

const getSingleLayerCollectionLimit = (collectionName, limit = 10) => {
    const ref = firebase.firestore().collection(collectionName);
    return ref
        .orderBy('time', 'desc')
        .limit(limit)
        .get()
        .then((querySnapshot) => {
            const sheets = [];
            querySnapshot.forEach((doc) => sheets.push({ id: doc.id, ...doc.data() }));
            return !isEmpty(sheets) ? sheets : { error: "doc or collection doesn't exist" };
        });
};

const getSheets = () => {
    const ref = firebase
        .firestore()
        .collection('setting')
        .doc('dataset')
        .collection('sheet');
    return ref
        .orderBy('order', 'asc')
        .get()
        .then((querySnapshot) => {
            const sheets = [];
            querySnapshot.forEach((doc) => {
                // FIXME: 將 API 從 firebase 移到 Api.js，方便做 API testing。firebase 上的 API 日後需要移除
                sheets.push({ id: doc.id, ...doc.data(), ...datasetSheet[doc.id] });
            });

            return !isEmpty(sheets) ? sheets : { error: "doc or collection does't exist" };
        });
};

const getSheetHeader = (sheetName) => {
    const ref = firebase
        .firestore()
        .collection('setting')
        .doc('dataset')
        .collection('sheet')
        .doc(sheetName);
    return ref.get().then((doc) => {
        if (doc.exists) {
            // Convert to object
            // console.log(doc.data().headers);
            return doc.data().headers.sort((itemA, itemB) => itemA.seq - itemB.seq);
        }
        return { error: "doc or collection does't exist" };
    });
};

const getSheetTabHeader = (sheetName, tabClass) => {
    const ref = firebase
        .firestore()
        .collection('setting')
        .doc('dataset')
        .collection('sheet')
        .doc(sheetName);
    return ref.get().then((doc) => {
        if (doc.exists) {
            // Convert to object
            // console.log(doc.data().headers);
            return doc
                .data()
                .hasTab[tabClass].headers.sort((itemA, itemB) => itemA.seq - itemB.seq);
        }
        return { error: "doc or collection does't exist" };
    });
};

const getDownloadSheet = (sheetName) => {
    const ref = firebase
        .firestore()
        .collection('setting')
        .doc('dataset')
        .collection('sheet')
        .doc(sheetName);
    return ref.get().then((doc) => {
        if (doc.exists) {
            // Convert to object
            // console.log(doc.data().headers);
            const headers = doc.data().headers.sort((itemA, itemB) => itemA.seq - itemB.seq);
            return {
                contentApi: doc.data().getTable2,
                searchApi: doc.data().searchApi,
                getAuthority: doc.data().getTable,
                headers,
            };
        }
        return { error: "doc or collection does't exist" };
    });
};

const getAuthoritySheets = () => {
    const ref = firebase
        .firestore()
        .collection('setting')
        .doc('dataset')
        .collection('sheet');
    return ref
        .orderBy('order', 'asc')
        .get()
        .then((querySnapshot) => {
            const sheets = [];

            // FIXME: 將 API 從 firebase 移到 Api.js，方便做 API testing。firebase 上的 API 日後需要移除
            // querySnapshot.forEach((doc) => sheets.push({ id: doc.id, ...doc.data() }));
            querySnapshot.forEach((doc) =>
                sheets.push({ id: doc.id, ...doc.data(), ...datasetSheet[doc.id] }),
            );

            const enabledSheets = sheets.filter((sh) => sh.authority === EnableAuthority);
            return !isEmpty(enabledSheets)
                ? enabledSheets
                : { error: "doc or collection doesn't exist" };
        });
};

const getUploadSettings = () => {
    const ref = firebase
        .firestore()
        .collection('setting')
        .doc('upload');
    return ref
        .get()
        .then((querySnapshot) => querySnapshot.data())
        .catch((error) => ({ error }));
};

const getImgFolder = () => {
    const ref = firebase
        .firestore()
        .collection('setting')
        .doc('upload')
        .collection('imageFolder');
    return ref
        .orderBy('seq', 'asc')
        .get()
        .then((querySnapshot) => {
            const sheets = [];
            querySnapshot.forEach((doc) => sheets.push({ id: doc.id, ...doc.data() }));
            return !isEmpty(sheets) ? sheets : { error: "doc or collection doesn't exist" };
        });
};

// 產生randomId，用在後來指定儲存文件、圖片的路徑名稱
const getRandomID = (collectionPath) =>
    firebase
        .firestore()
        .collection(collectionPath)
        .doc().id;

// 用本身的設定
const getFileServerUrl = () => process.env.REACT_APP_FILE_SERVER_URL;
// const ref = firebase
//     .firestore()
//     .collection("api")
//     .doc("api-config");
// return ref.get().then(doc => doc.data().fileserver);

export {
    // sidebar
    getMainSubject,
    getSheets,
    // content
    getSheetHeader,
    getSheetTabHeader,
    getDownloadSheet,
    getFileServerUrl,
    // authority
    getAuthoritySheets,
    // websiteSetting、historyEvents
    getSingleLayerCollection,
    getSingleLayerCollectionWhere,
    getSingleLayerCollectionLimit,
    getSingleLayerCollectionDocs,
    getUploadSettings,
    getImgFolder,
    getRandomID,
};

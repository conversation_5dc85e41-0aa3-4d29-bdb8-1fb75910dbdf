/* eslint-disable no-unused-vars */
import axios from "axios";
import textMsg from "../../../../commons/textMsg";
import Api from "../../../../../../api/nmtl/Api";
import { convertSimpleEntrySD } from "../../../../../common/sheetCrud/sheetCrudHelper";
import { isEmpty } from "../../../../../../commons";
import { createNmtlData, updateNmtlData } from "../../../../../../api/nmtl";
import { fileServerAPI } from "../../../../../../api/fileServer";
import uploadConfig from "../../../../../toolPages/components/upload/uploadConfig";
import { getFileName } from "../../../../commons/components/EditNews/Utils/saveDataUtils";
import VillagesAct from "../VillagesAction";

const graph = "settings";

const areObjectsEqual = (obj1, obj2) => {
    // Get the keys of the objects
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    // Check if the number of keys is the same
    if (keys1.length !== keys2.length) {
        return false;
    }

    // Check if the values of each key are the same
    // eslint-disable-next-line no-restricted-syntax
    for (const key of keys1) {
        if (obj1[key] !== obj2[key]) {
            return false;
        }
    }

    // If all checks passed, the objects are equal
    return true;
};

const getKeyData = (keyIdName, data) =>
    // 設定keyData
    ({
        // 要傳給api "srcId"欄位
        keyIdName,
        // 要傳給api "value"欄位的資料內容
        // dbPropName: 要傳給api "value"欄位的 database property name
        // prop: 從apiStr抓到資料內容的欄位名稱
        // lang: 要帶給api的語系名稱 (optional)
        dataValue: Object.keys(data).map(key =>
            // if (["ZH", "EN"].includes(key.slice(-2).toUpperCase())) {
            //     return {
            //         dbPropName: key.slice(0, -2),
            //         prop: initColumnDef[key],
            //         lang: key.slice(-2).toLowerCase()
            //     };
            // }
            ({ dbPropName: key, prop: data[key] })
        )
    });

const saveActivityInfoData = (
    originObj,
    updateObj,
    subject,
    user,
    classType,
    villageId
) =>
    new Promise(async (resolve, reject) => {
        const { sheetName } = textMsg;
        const apiStr = Api.getGeneric;

        const initColumnTemp = {
            id: "id",
            order: "order"
            // title: "title"
        };

        if (!classType) {
            resolve("No classType");
        }

        const keyData = getKeyData("id", initColumnTemp);
        const entryCol = {
            graph,
            classType,
            ...keyData
        };

        const { entrySrc, entryDst } = convertSimpleEntrySD(
            originObj,
            updateObj,
            entryCol
        );

        // // remove newsIdStr property
        delete entrySrc?.value?.[initColumnTemp.id];

        // // create
        if (isEmpty(entrySrc) && !isEmpty(entryDst)) {
            const aciId = entryDst?.value?.[initColumnTemp.id];
            const tmpEntry = {
                classType: "VillageEvent",
                graph: "settings",
                srcId: villageId,
                value: {
                    hasActivityInfo: aciId
                }
            };

            const firstRes = await createNmtlData(
                user,
                apiStr,
                graph,
                sheetName,
                tmpEntry
            );
            // create new News instance
            if (firstRes === "OK") {
                delete entryDst?.value?.[initColumnTemp.id];
                createNmtlData(user, apiStr, graph, sheetName, entryDst)
                    .then(res => resolve(res))
                    .catch(err => reject(err));
            }
        } else {
            delete entryDst?.value?.[initColumnTemp.id];
            // update
            updateNmtlData(user, apiStr, graph, sheetName, entrySrc, entryDst)
                .then(res => resolve(res))
                .catch(err => reject(err));
        }
    });

const saveActivityInfo = async (
    dispatch,
    activityInfo,
    newActivityInfo,
    user,
    subject,
    classType,
    villageId
) => {
    const copySrc = JSON.parse(JSON.stringify(activityInfo));
    const copyDst = JSON.parse(JSON.stringify(newActivityInfo));
    const isSameSrcAndDst = areObjectsEqual(copySrc, copyDst);
    if (isSameSrcAndDst) return;

    const collectRes = [];
    const res = await saveActivityInfoData(
        copySrc,
        copyDst,
        subject,
        user,
        classType,
        villageId
    );
    collectRes.push(res);
};

// Delete peak chapter
const deleteActivityInfoData = (villageId, aciId, subject, user) =>
    new Promise(async (resolve, reject) => {
        const { sheetName } = textMsg;
        const apiStr = Api.getGeneric;
        const tmpEntry = {
            classType: "VillageEvent",
            graph,
            srcId: villageId,
            value: {
                hasActivityInfo: aciId
            }
        };
        const tmpEntryDst = {
            classType: "ActivityInfo",
            graph,
            srcId: aciId,
            value: {
                hasActivityInfo: []
            }
        };
        updateNmtlData(user, apiStr, graph, sheetName, tmpEntry, tmpEntryDst)
            .then(res => resolve(res))
            .catch(err => reject(err));
    });

const deleteActivityInfo = async (
    dispatch,
    villageId,
    aciId,
    user,
    subject
) => {
    const collectRes = [];
    const peakRes = await deleteActivityInfoData(
        villageId,
        aciId,
        subject,
        user
    );
    collectRes.push(peakRes);
};

const handleFileError = (reqUrl, formData) =>
    new Promise((resolve, reject) => {
        axios({
            method: "PUT",
            url: reqUrl,
            headers: {
                "Access-Control-Allow-Origin": "*"
            },
            data: formData,
            responseType: "json" // important
        })
            .then(res => {
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });

const uploadFile = (targetEl, subject, curAllUpload, fsFolderName, type) => {
    const allUpload = [];
    if (targetEl.files) {
        // eslint-disable-next-line no-restricted-syntax
        for (const file of targetEl.files) {
            const commonUrl = fileServerAPI.uploadFile.replace(
                "[type]",
                file.type.indexOf("image") > -1
                    ? uploadConfig.image
                    : uploadConfig.file
            );

            const reqUrl = `${commonUrl}/${subject}/${fsFolderName}`;
            const formData = new FormData();

            // 更改檔案名稱
            // const modifiedFile = new File(
            //     [file],
            //     `${file.lastModified}_${file.name}`,
            //     { type: file.type }
            // );

            formData.append("image", file);

            const path = `${
                file.type.indexOf("image") > -1
                    ? fileServerAPI.readUploadImage
                    : fileServerAPI.readFile
            }/${subject}/${fsFolderName}/`;
            allUpload.push({
                promiseFun: handleFileError(reqUrl, formData),
                path
            });
        }
    }

    return new Promise((resolve, reject) => {
        Promise.all(allUpload.map(el => el.promiseFun))
            .then(allResult => {
                const allURL = allResult
                    .filter(obj => obj.data.message)
                    .map((tmpObj, idx) => {
                        const tmpFileName = getFileName(
                            tmpObj.data.images[0].imgUrl
                        );
                        return `${allUpload[idx].path}${tmpFileName}`;
                    });
                resolve([...allURL]);
            })
            .catch(error => reject(error));
    });
};

const SELECT_MODE = {
    multiple: {
        name: "multiple"
    },
    single: {
        name: "single"
    }
};
const pName = "data-img-url";

const isInList = (f, fList) => {
    if (!fList) {
        return true;
    }
    return fList.filter(fl => fl[pName] === f[pName]).length > 0;
};

const handleGalleryChange = ({
    file,
    curFolderFiles,
    dispatch,
    selectMode
}) => {
    const curFolderFilesChange = JSON.parse(JSON.stringify(curFolderFiles));

    // single select mode:
    if (selectMode === SELECT_MODE.single.name) {
        curFolderFilesChange.checked = [];
        if (file.checked) {
            curFolderFilesChange.checked.push(file);
            // 避免重覆
            if (curFolderFilesChange.checked.length > 1) {
                curFolderFilesChange.checked = curFolderFilesChange.checked.filter(
                    cff => cff[pName] !== file[pName]
                );
            }

            return dispatch({
                type: VillagesAct.CUR_FOLDER_FILES_STATUS,
                payload: curFolderFilesChange
            });
        }
        return dispatch({
            type: VillagesAct.CUR_FOLDER_FILES_STATUS,
            payload: curFolderFilesChange
        });
    }
    // multiple select mode:
    if (selectMode === SELECT_MODE.multiple.name) {
        if (file.checked) {
            // 增加
            if (!isInList(file, curFolderFilesChange.checked)) {
                curFolderFilesChange.checked.push(file);
            }

            return dispatch({
                type: VillagesAct.CUR_FOLDER_FILES_STATUS,
                payload: curFolderFilesChange
            });
        }

        // 刪除
        if (isInList(file, curFolderFilesChange.checked)) {
            curFolderFilesChange.checked = curFolderFilesChange.checked.filter(
                cff => cff[pName] !== file[pName]
            );
        }

        return dispatch({
            type: VillagesAct.CUR_FOLDER_FILES_STATUS,
            payload: curFolderFilesChange
        });
    }
    return null;
};

export {
    saveActivityInfo,
    deleteActivityInfo,
    uploadFile,
    areObjectsEqual,
    handleGalleryChange
};

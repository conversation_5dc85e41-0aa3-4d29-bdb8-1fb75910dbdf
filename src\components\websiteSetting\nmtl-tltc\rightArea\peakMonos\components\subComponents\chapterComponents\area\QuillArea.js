import React, { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { debounce } from "lodash";
import CustomQuill from "../components/CustomQuill";
import PeakAct from "../../../../PeakMonosAction";
import { isEmpty } from "../../../../../../../../../commons";
import htmlToMD from "../../../../../../../../../commons/htmlToMD";

const useDebouncedDispatch = (dispatch, delay = 300) => {
    const debouncedFuncs = useRef({});

    return useCallback(
        type => {
            if (!debouncedFuncs.current[type]) {
                debouncedFuncs.current[type] = debounce(payload => {
                    dispatch({ type, payload });
                }, delay);
            }
            return debouncedFuncs.current[type];
        },
        [dispatch, delay]
    );
};

const QuillArea = ({ updateFct, data, updatedData }) => {
    const dispatch = useDispatch();
    const getDebouncedDispatch = useDebouncedDispatch(dispatch);

    const refs = {
        bookCh: useRef(null),
        bookEn: useRef(null),
        judgeCh: useRef(null),
        judgeEn: useRef(null),
        writerCh: useRef(null),
        writerEn: useRef(null)
    };

    const [values, setValues] = useState({
        bookValueCh: "",
        bookValueEn: "",
        judgeValueCh: "",
        judgeValueEn: "",
        writerValueCh: "",
        writerValueEn: ""
    });

    const handleChange = (type, key) => e => {
        setValues(prevValues => ({
            ...prevValues,
            [key]: e
        }));

        getDebouncedDispatch(type)(htmlToMD(e));
    };

    const quillConfig = [
        {
            id: "CustomEditorBookValueCh",
            ref: refs.bookCh,
            title: "作品簡介+標題 (中文版)",
            value: values.bookValueCh,
            type: PeakAct.SET_INTRODUCTIONZHFORQUILL,
            key: "bookValueCh"
        },
        {
            id: "CustomEditorBookValueEn",
            ref: refs.bookEn,
            title: "作品簡介+標題 (外文版)",
            value: values.bookValueEn,
            type: PeakAct.SET_INTRODUCTIONENFORQUILL,
            key: "bookValueEn"
        },
        {
            id: "CustomJudgeValueCh",
            ref: refs.judgeCh,
            title: "評審推薦+標題 (中文版)",
            value: values.judgeValueCh,
            type: PeakAct.SET_JUDGESCOMMENTARYZHFORQUILL,
            key: "judgeValueCh"
        },
        {
            id: "CustomJudgeValueEn",
            ref: refs.judgeEn,
            title: "評審推薦+標題 (外文版)",
            value: values.judgeValueEn,
            type: PeakAct.SET_JUDGESCOMMENTARYENFORQUILL,
            key: "judgeValueEn"
        },
        {
            id: "CustomWriterValueCh",
            ref: refs.writerCh,
            title: "作家簡介+標題 (中文版)",
            value: values.writerValueCh,
            type: PeakAct.SET_TLAPERSONINTROZHFORQUILL,
            key: "writerValueCh"
        },
        {
            id: "CustomWriterValueEn",
            ref: refs.writerEn,
            title: "作家簡介+標題 (外文版)",
            value: values.writerValueEn,
            type: PeakAct.SET_TLAPERSONINTROENFORQUILL,
            key: "writerValueEn"
        }
    ];

    useEffect(() => {
        if (isEmpty(updatedData)) return;
        const updateValues = {
            bookValueCh: updatedData?.introductionZh,
            bookValueEn: updatedData?.introductionEn,
            judgeValueCh: updatedData?.judgesCommentaryZh,
            judgeValueEn: updatedData?.judgesCommentaryEn,
            writerValueCh: updatedData?.tlaPersonIntroZh,
            writerValueEn: updatedData?.tlaPersonIntroEn
        };

        setValues(prevValues => ({
            ...prevValues,
            ...Object.fromEntries(
                Object.entries(updateValues).filter(
                    ([key, value]) => value && value !== prevValues[key]
                )
            )
        }));
    }, [
        updatedData?.introductionZh,
        updatedData?.introductionEn,
        updatedData?.judgesCommentaryZh,
        updatedData?.judgesCommentaryEn,
        updatedData?.tlaPersonIntroZh,
        updatedData?.tlaPersonIntroEn
    ]);

    return (
        <>
            {quillConfig.map(({ id, ref, title, value, type, key }) => (
                <div style={{ marginTop: "1rem" }} key={id}>
                    <div className="topArea">
                        <h1>{title}</h1>
                    </div>
                    <CustomQuill
                        key={id}
                        quillId={id}
                        tmpRef={ref}
                        tmpValue={value}
                        onChangeFct={handleChange(type, key)}
                    />
                </div>
            ))}
        </>
    );
};

export default QuillArea;

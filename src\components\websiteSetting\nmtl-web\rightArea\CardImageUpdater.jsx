import React, { useContext, useEffect, useState } from 'react';
import { Image } from 'semantic-ui-react';
import SaveButton from '../../components/SaveButton';
import UploadImageModal from '../../components/UploadImageModal';
import dragImage from '../../../../images/dragImage.svg';
import { StoreContext } from '../../../../store/StoreProvider';
import Act from '../../../../store/actions';
import Selector from '../../components/Selector';

const CardImageUpdater = () => {
    const [state, dispatch] = useContext(StoreContext);
    const { originData, updatedData, selectOption, menuActiveItem } = state.websiteSetting;

    const [dropDown, setDropDown] = useState({});
    const [imageUrl, setImageUrl] = useState('');
    const [openModal, setOpenModal] = useState(false);

    useEffect(() => {
        // 下拉選單選項清空
        dispatch({
            type: Act.SET_SELECTOPTION,
            payload: '',
        });
    }, [menuActiveItem]);

    useEffect(() => {
        if (originData.length !== 0) {
            const tmpObj = originData.find((element) => element.id === menuActiveItem.key);

            if (selectOption !== null && selectOption !== '' && tmpObj[selectOption]) {
                setImageUrl(tmpObj[selectOption].url);
            }
        }
    }, [selectOption]);

    useEffect(() => {
        if (originData.length !== 0) {
            const newDropDown = originData.find((element) => element.id === menuActiveItem.key);
            setDropDown(newDropDown);

            // 更新 updatedData
            const tmpAllData = JSON.parse(JSON.stringify(updatedData));
            const tmpObjIndex = tmpAllData.findIndex(
                (element) => element.id === menuActiveItem.key,
            );

            tmpAllData[tmpObjIndex] = newDropDown;

            dispatch({
                type: Act.SET_UPDATEDDATA,
                payload: tmpAllData,
            });
        }
    }, [originData]);

    return (
        <div className="CardImageUpdater">
            <div className="Selector">
                <Selector dropDown={dropDown} placeholder="請選擇卡片類型" />
            </div>
            <div className="DropFileRegion">
                <Image
                    style={{
                        cursor: selectOption === '' ? 'default' : 'pointer',
                    }}
                    src={imageUrl !== '' ? imageUrl : dragImage}
                    onClick={() => {
                        setOpenModal(true);
                    }}
                />
            </div>
            <div className="btnArea">
                <SaveButton language="" />
            </div>
            {openModal && <UploadImageModal setOpenModal={setOpenModal} setAllData={setImageUrl} />}
        </div>
    );
};

export default CardImageUpdater;

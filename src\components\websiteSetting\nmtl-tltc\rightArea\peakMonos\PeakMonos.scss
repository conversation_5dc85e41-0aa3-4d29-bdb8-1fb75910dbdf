@mixin rightComponentMain {
  width: 90%;
  height: 90%;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

@mixin btnArea {
  display: flex;
  justify-content: flex-end;
}

.PeakMonos {
  @include rightComponentMain;
  .topArea{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px;
  }
  .textArea{
    margin-bottom: 5px;
    height: 40%;
  }
  .btnArea{
    @include btnArea;
    button {
      margin: 0;
    }
  }
  .bookArea{
    height:80%;
    overflow-y: scroll;
    margin-top: 2rem;
  }
}

#CustomEditorPeak{
  .ql-container.ql-snow {
    min-height: 250px;
    height:250px;
  }
}

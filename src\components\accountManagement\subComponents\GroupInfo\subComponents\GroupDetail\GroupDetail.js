import React, { useEffect, useState } from "react";

// plugins
import { useSelector } from "react-redux";
import { Divider, Segment } from "semantic-ui-react";

//
import { BtnName, segmentName, TypeName } from "../../../Utils/compoConfig";
// eslint-disable-next-line import/no-cycle
import CustomButton from "../CustomButton/CustomButton";
import GroupDetailBtn from "../CustomButton/GroupDetailBtn";
import CustomInput from "../CustomInput/CustomInput";
import CustomSelectBar from "../CustomSelectBar/CustomSelectBar";
import textConfig from "../../../Utils/textConifg";

// scss
import "./GroupDetail.scss";

function GroupDetail() {
    const state = useSelector(tmpState => tmpState.accMng);
    const { activeItemACC, groDSelectItem } = state;
    const [tmpGPBtn, setTmpGPBtn] = useState([]);

    const setGroupBtn = gpName => {
        switch (gpName) {
            case textConfig.GROUPINFO_MENUBAR_MEMBERINFO:
                return BtnName.filter(
                    ({ segment }) => segment === segmentName.userInfoByGroup
                );
            default:
                return [];
        }
    };

    useEffect(() => {
        setTmpGPBtn(setGroupBtn(groDSelectItem.name));
    }, [groDSelectItem.name]);

    return (
        <div className="GroupDetail">
            <Segment basic compact>
                <h2>{activeItemACC.name}</h2>
            </Segment>
            <Divider />
            <CustomSelectBar />
            <div className="GroupDetail__TopArea">
                <CustomInput />
                <div className="GroupDetail__TopArea__Btn">
                    {tmpGPBtn.map(btnInfo => (
                        <GroupDetailBtn
                            compoInfo={btnInfo}
                            key={btnInfo.typeName}
                        />
                    ))}
                    {BtnName.filter(
                        ({ typeName }) =>
                            typeName === TypeName.GroupDetailCancel
                    ).map(btnInfo => (
                        <CustomButton
                            compoInfo={btnInfo}
                            key={btnInfo.typeName}
                        />
                    ))}
                </div>
            </div>
            <div className="GroupDetail__InfoArea">
                <groDSelectItem.component />
            </div>
            <div className="GroupDetail__BottomArea">
                {BtnName.filter(
                    ({ typeName }) => typeName === TypeName.SaveData
                ).map(btnInfo => (
                    <CustomButton compoInfo={btnInfo} key={btnInfo.typeName} />
                ))}
            </div>
        </div>
    );
}

export default GroupDetail;

.statCardContainer,
.statCardWithCountContainer {
    display: flex;
    flex-direction: column;

    .statCard,
    .statCardFocused,
    .statCardWithCount {
        display: flex;
        flex-direction: column;
        border-radius: 4px;
        padding: 8px;
        width: 100%;
        min-height: 54px;
        row-gap: 4px;
        p {
            margin: 0;
            font-size: 12px;
            font-weight: 500;
            line-height: 17.38px;
        }
    }

    .statCard,
    .statCardFocused {
        border: 1px solid #e4f5e8;
    }

    .statCardFocused {
        background-color: #e4f5e8;
        p {
            color: #21ba45;
        }
    }

    .statCardWithCount {
        border: none;
    }

    .detailStatsContainer {
        position: absolute;
        top: 0;
        left: 40px;
        padding: 16px;
        border-radius: 4px;
        background-color: #fafafa;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        .detailStats {
            flex: 0 0 calc(25% - 12px);
            display: flex;
            flex-direction: column;
            row-gap: 4px;
            border-radius: 4px;
            padding: 8px;
            min-height: 33px;
            p {
                margin: 0;
                font-size: 12px;
                font-weight: 500;
                line-height: 17.38px;
            }
        }
    }
}

.statCardContainer {
    flex: 0 0 calc(33.33% - 10.66px);
}

.statCardWithCountContainer {
    flex: 0 0 calc(25% - 24.75px);
}

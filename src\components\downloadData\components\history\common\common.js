import addHistoryEvent from "./storeDB";

const defaultHistoryEventFormat = {
    classification: "",
    name: "",
    operation: "",
    time: "",
    serverFrom: ""
};

/** 定義
 * Info: 對資料庫沒有做更改的所有動作
 * Warning: 對資料庫有做更改資料的動作且成功
 * Error: 對資料庫有做更改資料動作且失敗或是其他非預期錯誤
 * */
const eventClassified = {
    Info: {
        login: "登入",
        logout: "登出",
        search: "查詢",
        download: "下載",
        update: "資料變動"
    },
    Warning: {
        create: "新增",
        updated: "更新",
        delete: "刪除",
        import: "匯入"
    },
    Error: {
        systemError: "系統錯誤"
    }
};

function getEventClassification(eventStr) {
    let classified = "Error"; // default是Error
    const keys = Object.keys(eventClassified);
    keys.forEach(key => {
        const values = Object.values(eventClassified[key]);
        const findValue = values.find(element => element === eventStr);
        if (findValue) {
            classified = key;
        }
    });
    return classified;
}

function sortHistoryEvent(historyEvent) {
    const tmpHistoryEvent = JSON.parse(JSON.stringify(historyEvent));
    // eslint-disable-next-line no-plusplus
    for (let i = 1; i < tmpHistoryEvent.length; i++) {
        let flag = i; // 紀錄目前比對位置
        // eslint-disable-next-line no-plusplus
        for (let j = 0; j < i; j++) {
            if (
                Date.parse(tmpHistoryEvent[i].time) >
                Date.parse(tmpHistoryEvent[j].time)
            ) {
                flag = j; // insert position
                break;
            }
        }

        if (flag !== i) {
            const tmp = tmpHistoryEvent[i]; // 先儲存目前historyEvent[i](要更換的位置資料)
            // eslint-disable-next-line no-plusplus
            for (let j = i - 1; j >= flag; j--) {
                // 將比對資料到insert position所有資料往後移動
                tmpHistoryEvent[j + 1] = tmpHistoryEvent[j];
            }
            tmpHistoryEvent[flag] = tmp; // 在insert位置放入比對資料
        }
    }
    return tmpHistoryEvent;
}

function createHistoryEvent(
    displayName,
    eventStr,
    columns = "",
    errorMsg = ""
) {
    const tmpHistoryEvent = JSON.parse(
        JSON.stringify(defaultHistoryEventFormat)
    );
    // const date = new Date();
    // tmpHistoryEvent.time = date.toString();
    // 用毫秒數才能在 Firebase 端做排序。
    tmpHistoryEvent.time = Date.now();
    tmpHistoryEvent.name = displayName;
    tmpHistoryEvent.classification = getEventClassification(eventStr);

    // record the server from.
    tmpHistoryEvent.serverFrom =
        process.env.REACT_APP_NMTL_API_NODE || "localhost";

    switch (eventStr) {
        case "登入":
            tmpHistoryEvent.operation = "登入";
            break;
        case "登出":
            tmpHistoryEvent.operation = "登出";
            break;
        default:
            // eslint-disable-next-line no-lone-blocks
            {
                tmpHistoryEvent.operation = `${eventStr}，資料欄位: ${columns}`;
                if (errorMsg !== "") {
                    tmpHistoryEvent.operation += ` \n Error: ${errorMsg}`;
                }
            }
            break;
    }
    addHistoryEvent(tmpHistoryEvent);
}

export { sortHistoryEvent, createHistoryEvent };

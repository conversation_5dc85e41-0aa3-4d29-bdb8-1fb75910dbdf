import React from "react";
import { Button } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import CustomModal from "./CustomModal";
import PeakAct from "../../PeakMonosAction";

const CustomButton = ({
    onClick,
    disableBool,
    message,
    color,
    content,
    icon
}) => {
    const dispatch = useDispatch();
    const { isModalOpen } = useSelector(state => state);
    const onOpen = () => {
        dispatch({
            type: PeakAct.SET_ISMODALOPEN,
            payload: !isModalOpen
        });
    };
    return (
        <>
            <Button
                color={color}
                content={content}
                onClick={onClick}
                disabled={disableBool}
                icon={icon || null}
            />
            <CustomModal onClick={onOpen} message={message} />
        </>
    );
};

export default CustomButton;

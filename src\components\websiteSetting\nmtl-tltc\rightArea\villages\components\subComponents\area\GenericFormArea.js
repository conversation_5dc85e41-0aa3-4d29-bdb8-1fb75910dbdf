import React, { useCallback } from "react";
import { Grid } from "semantic-ui-react";
import { debounce } from "lodash";
import { isEmpty } from "../../../../../../../../commons";

const GenericFormArea = ({
    updateFct,
    data,
    updatedData,
    config,
    additionalComponents,
    optionLists,
    editingPchId
}) => {
    const groupedFields = config.reduce((acc, field) => {
        if (!acc[field.section]) {
            acc[field.section] = [];
        }
        acc[field.section].push(field);
        return acc;
    }, {});

    const debouncedUpdateFct = useCallback(
        debounce((tmpUpdatedData, tmpType, tmpValue) => {
            updateFct(tmpUpdatedData, tmpType, tmpValue);
        }, 300),
        []
    );

    return (
        <>
            {Object.keys(groupedFields).map(section => (
                <Grid celled key={section}>
                    {groupedFields[section].map(field => {
                        const Component = field.component;

                        let values = field.props.valueKeys
                            ? field.props.valueKeys.map(key => data[key])
                            : [data[field.props.valueKey]];

                        if (field.props.valueKey === "id") {
                            values = [editingPchId];
                        }

                        let formattedValues =
                            values.length === 1 ? values[0] : values;

                        if (
                            !isEmpty(formattedValues) &&
                            field.props.valueKey === "hasInceptionDate"
                        ) {
                            formattedValues = formattedValues.replace(
                                "DAE",
                                ""
                            );
                        }

                        if (
                            !isEmpty(formattedValues) &&
                            field.props.valueKey === "hasTLAAwardDate"
                        ) {
                            formattedValues = formattedValues
                                .replace("DAE", "")
                                .substring(0, 4);
                        }

                        const optionList = field.props.optionListKey
                            ? optionLists[field.props.optionListKey]
                            : null;

                        return (
                            <Component
                                key={field.props.valueKey}
                                {...field.props}
                                updateFct={updateFct}
                                value={formattedValues}
                                optionList={optionList}
                                fullData={data}
                                updatedData={updatedData}
                                debouncedUpdateFct={debouncedUpdateFct}
                            />
                        );
                    })}
                </Grid>
            ))}
            {additionalComponents &&
                additionalComponents.map((Component, index) => (
                    <Component key={index} data={data} updateFct={updateFct} />
                ))}
        </>
    );
};

export default GenericFormArea;

import React, { useCallback, useContext, useEffect, useState } from "react";

// ui
import { Table } from "semantic-ui-react";

// store
import _ from "lodash";
import axios from "axios";
import { StoreContext } from "../../../../../../store/StoreProvider";
// common
import { isEmpty } from "../../../../../../commons";
import CustomPagination from "../../../../../common/CustomPagination/CustomPagination";
import DownloadModal from "../DownloadModal";
import { fileServerAPI } from "../../../../../../api/fileServer";

const pageOption = [5, 10, 25, 50];

const TopContent = () => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { log } = state.history;

    // page control
    const [curPage, setCurPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [perPageNum, setPerPageNum] = useState(5);
    const [searchText, setSearchText] = useState("");
    const [curLog, setCurLog] = useState({});
    const [avaliableDownloadLogTime, setAvaliableDownloadLogTime] = useState(
        []
    );

    // const fixFirstChild = {
    //     position: "sticky",
    //     top: "0"
    //     // zIndex: "1",
    // };

    const headerCells = [
        { label: "name", value: "姓名" },
        { label: "operation", value: "操作" },
        // { label: "serverFrom", value: "操作對象" },
        { label: "classification", value: "等級" },
        { label: "time", value: "時間" }
    ];

    const objectToArray = obj =>
        Object.entries(obj).map(([key, value]) => ({
            id: key,
            ...value
        }));

    useEffect(() => {
        const logArray = objectToArray(curLog);
        setTotalPages(Math.ceil(logArray.length / perPageNum));
    }, [curLog, perPageNum]);

    const handlePage = (evt, { activePage }) => {
        setCurPage(activePage);
    };

    const handleDDPage = (evt, data) => {
        setCurPage(data.value);
    };

    const handlePerPageNum = (evt, data) => {
        setCurPage(1);
        setPerPageNum(data.value);
    };

    // 獲取當前頁面的數據
    const getCurrentPageData = () => {
        const logArray = objectToArray(curLog);
        const startIndex = (curPage - 1) * perPageNum;
        const endIndex = startIndex + perPageNum;
        return logArray.slice(startIndex, endIndex);
    };

    // 表格渲染組件
    const LogTable = () => {
        const currentData = getCurrentPageData();

        return (
            <Table.Body>
                {currentData.map((item, idx) => (
                    <Table.Row key={`src-${idx}-${item.id}`}>
                        <Table.Cell
                            style={{
                                width: "15%",
                                wordWrap: "break-word",
                                overflowWrap: "break-word"
                            }}
                        >
                            {item.name}
                        </Table.Cell>
                        <Table.Cell
                            style={{
                                width: "55%",
                                wordWrap: "break-word",
                                overflowWrap: "break-word"
                            }}
                        >
                            {item.operation}
                        </Table.Cell>
                        {/* <Table.Cell>{item.serverFrom}</Table.Cell> */}
                        <Table.Cell
                            style={{
                                width: "15%",
                                wordWrap: "break-word",
                                overflowWrap: "break-word"
                            }}
                        >
                            {item.classification}
                        </Table.Cell>
                        <Table.Cell
                            style={{
                                width: "15%",
                                wordWrap: "break-word",
                                overflowWrap: "break-word"
                            }}
                        >
                            {new Date(item.time).toString()}
                        </Table.Cell>
                    </Table.Row>
                ))}
            </Table.Body>
        );
    };

    const debouncedSearch = useCallback(
        _.debounce(value => {
            setSearchText(value);
        }, 500),
        []
    );

    const handleInputChange = e => {
        debouncedSearch(e.target.value);
    };

    useEffect(() => {
        const today = new Date();
        const todayTimestamp = today.getTime();

        // 計算三個月前的日期
        const threeMonthsAgo = new Date(today);
        threeMonthsAgo.setMonth(today.getMonth() - 3);
        const threeMonthsAgoTimestamp = threeMonthsAgo.getTime();

        // 篩選距離三個月內的資料
        const filteredDateData = Object.values(log).filter(
            item =>
                item.time >= threeMonthsAgoTimestamp &&
                item.time <= todayTimestamp
        );

        if (searchText) {
            const filteredData = filteredDateData?.filter(item => {
                const lowerCaseSearchText = searchText.toLowerCase();
                return (
                    item.name.toLowerCase().includes(lowerCaseSearchText) ||
                    item.operation
                        .toLowerCase()
                        .includes(lowerCaseSearchText) ||
                    item.classification
                        .toLowerCase()
                        .includes(lowerCaseSearchText)
                );
            });

            setCurPage(1);
            setCurLog(filteredData);
        } else {
            setCurLog(filteredDateData);
        }
    }, [log, searchText]);

    useEffect(() => {
        const getAvaliableDownloadLogTime = async () => {
            const time = await axios.post(fileServerAPI.historyLogList, {
                filePath: process.env.REACT_APP_HISTORY_LOG_FILESERVER_PATH
            });
            setAvaliableDownloadLogTime(time?.data?.yearMonthList);
        };
        getAvaliableDownloadLogTime();
    }, []);

    return (
        <>
            <div style={{ overflowX: "hidden" }}>
                <div
                    style={{
                        display: "flex",
                        justifyContent: "flex-end",
                        marginBottom: "1rem"
                    }}
                >
                    <DownloadModal
                        avaliableDownloadLogTime={avaliableDownloadLogTime}
                    />
                </div>
                <input
                    placeholder="Search"
                    onChange={handleInputChange}
                    style={{
                        width: "100%",
                        padding: "10px 15px",
                        border: "1px solid #ccc",
                        borderRadius: "4px",
                        fontSize: "16px",
                        transition: "all 0.3s ease"
                    }}
                />
                {!isEmpty(log) ? (
                    <Table
                        celled
                        selectable
                        compact
                        style={{ width: "100%", tableLayout: "fixed" }}
                    >
                        <Table.Header>
                            <Table.Row>
                                {headerCells.map((item, idx) => (
                                    <Table.HeaderCell
                                        key={`logs-header-${idx}`}
                                        // style={fixFirstChild}
                                        style={{
                                            width: idx === 1 ? "55%" : "15%",
                                            wordWrap: "break-word",
                                            overflowWrap: "break-word",
                                            position: "sticky",
                                            top: "0"
                                        }}
                                    >
                                        {item.value}
                                    </Table.HeaderCell>
                                ))}
                            </Table.Row>
                        </Table.Header>
                        {/* <Table.Body> */}
                        {/* {Object.keys(log).map((keyName, idx) => ( */}
                        {/*    <Table.Row key={`src-${idx}-${keyName}`}> */}
                        {/*        <Table.Cell>{log[keyName].name}</Table.Cell> */}
                        {/*        <Table.Cell> */}
                        {/*            {log[keyName].operation} */}
                        {/*        </Table.Cell> */}
                        {/*        <Table.Cell> */}
                        {/*            {log[keyName].serverFrom} */}
                        {/*        </Table.Cell> */}
                        {/*        <Table.Cell> */}
                        {/*            {log[keyName].classification} */}
                        {/*        </Table.Cell> */}
                        {/*        <Table.Cell> */}
                        {/*            {new Date(log[keyName].time).toString()} */}
                        {/*        </Table.Cell> */}
                        {/*    </Table.Row> */}
                        {/* ))} */}
                        <LogTable />
                        {/* </Table.Body> */}
                    </Table>
                ) : (
                    <Table>
                        <Table.Body>
                            <Table.Row>
                                <Table.Cell textAlign="center">
                                    No Content
                                </Table.Cell>
                            </Table.Row>
                        </Table.Body>
                    </Table>
                )}
                {/* <Table celled selectable compact> */}

                {/*    <Table.Header> */}
                {/*        <Table.Row> */}
                {/*            { */}
                {/*                headerCells.map((item, idx) => */}
                {/*                    <Table.HeaderCell */}
                {/*                        key={`logs-header-${idx}`} */}
                {/*                        style={fixFirstChild} */}
                {/*                    > */}
                {/*                        {item.value} */}
                {/*                    </Table.HeaderCell> */}
                {/*                ) */}
                {/*            } */}
                {/*        </Table.Row> */}
                {/*    </Table.Header> */}

                {/*    <Table.Body> */}
                {/*        /!*{*!/ */}
                {/*        /!*    data.map((item, idx) => {*!/ */}
                {/*        /!*        const { _source:src } = item;*!/ */}
                {/*        /!*        return(*!/ */}
                {/*        /!*            <Table.Row*!/ */}
                {/*        /!*                key={`log-${idx}`}*!/ */}
                {/*        /!*                onClick={() => handleClick(idx, src)}*!/ */}
                {/*        /!*                active={activeRow === idx}*!/ */}
                {/*        /!*                style={{ cursor: 'pointer' }}*!/ */}
                {/*        /!*            >*!/ */}
                {/*        /!*                <Table.Cell>{src.name}</Table.Cell>*!/ */}
                {/*        /!*                <Table.Cell>{src.email}</Table.Cell>*!/ */}
                {/*        /!*                <CustomOperateTableRowCell*!/ */}
                {/*        /!*                    type={src.type}*!/ */}
                {/*        /!*                />*!/ */}
                {/*        /!*                <CustomTimeTableRowCell*!/ */}
                {/*        /!*                    time={src.time}*!/ */}
                {/*        /!*                    timestamp={src.timestamp}*!/ */}
                {/*        /!*                />*!/ */}
                {/*        /!*            </Table.Row>*!/ */}
                {/*        /!*        );*!/ */}
                {/*        /!*    })*!/ */}
                {/*        /!*}*!/ */}
                {/*        {Object.keys(log).map((keyName, idx) => { */}
                {/*            return ( */}
                {/*                <Table.Row key={`src-${idx}-${keyName}`}> */}
                {/*                    <Table.Cell>{log[keyName].name}</Table.Cell> */}
                {/*                    <Table.Cell positive>{log[keyName].loginTime}</Table.Cell> */}
                {/*                    <Table.Cell>{log[keyName].logoutTime}</Table.Cell> */}
                {/*                    <Table.Cell>{log[keyName].operation}</Table.Cell> */}
                {/*                    <Table.Cell>{log[keyName].eventTime}</Table.Cell> */}
                {/*                </Table.Row> */}
                {/*            ); */}
                {/*        })} */}
                {/*    </Table.Body> */}
                {/* </Table> */}
            </div>
            <div style={{ marginBottom: "1rem" }}>
                <CustomPagination
                    currentPage={curPage}
                    totalPages={totalPages}
                    handlePage={handlePage}
                    handlePerPageNum={handlePerPageNum}
                    handleDDPage={handleDDPage}
                    pageOption={pageOption}
                />
            </div>
        </>
    );
};

export default TopContent;

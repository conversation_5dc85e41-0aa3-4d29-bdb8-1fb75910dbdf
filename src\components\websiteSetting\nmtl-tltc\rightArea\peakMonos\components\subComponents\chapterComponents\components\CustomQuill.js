import React, { useContext, useEffect, useState } from "react";
import ReactQuill, { Quill } from "react-quill";
import { isEmpty } from "../../../../../../../../../commons";
import { convert2HtmlEntities } from "../../../../../../../../../commons/htmlEntities";
import { StoreContext } from "../../../../../../../../../store/StoreProvider";
import "../../../../PeakMonos.scss";

export const initToolbarOptions = {
    container: [[{ header: [2, false] }], ["bold", "italic", "underline"]]
};

const CustomQuill = props => {
    const { quillId, onChangeFct, tmpValue, tmpRef } = props;
    const [toolBar, setToolBar] = useState({});
    const [globalState] = useContext(StoreContext);
    const { websiteSubject } = globalState.websiteSetting;

    const Block = Quill.import("blots/block");
    Block.className = "peakDiv";
    Block.tagName = "div";
    Quill.register(Block);

    useEffect(
        () => () => {
            setToolBar(initToolbarOptions);
        },
        []
    );
    useEffect(() => {
        const tmpToolBar = JSON.parse(JSON.stringify(initToolbarOptions));
        setToolBar(tmpToolBar);
    }, [tmpRef, websiteSubject]);

    return (
        <div>
            {!isEmpty(toolBar) && (
                <ReactQuill
                    id={quillId}
                    theme="snow"
                    value={convert2HtmlEntities(tmpValue)}
                    onChange={onChangeFct}
                    modules={{
                        toolbar: toolBar,
                        resize: {
                            locale: {},
                            toolbar: {
                                alignTools: false
                            }
                        }
                    }}
                    ref={tmpRef}
                    style={{ minHeight: "200px" }}
                />
            )}
        </div>
    );
};

export default CustomQuill;

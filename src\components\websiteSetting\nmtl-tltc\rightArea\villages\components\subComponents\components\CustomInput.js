import React, { useEffect, useState } from "react";
import { Grid, Input } from "semantic-ui-react";
import "../../EditVillagesDetail.scss";

const CustomInput = ({
    type,
    rowName,
    value,
    updatedData,
    subRowName,
    debouncedUpdateFct
}) => {
    const [localValue, setLocalValue] = useState(value);

    useEffect(() => {
        setLocalValue(value);
    }, [value]);

    const onChangeFct = e => {
        const newValue = e.target.value;
        setLocalValue(newValue);
        debouncedUpdateFct(updatedData, type, newValue);
    };

    return (
        <Grid.Row>
            <Grid.Column
                width={3}
                style={{
                    backgroundColor: "#e0e1e2"
                }}
            >
                <div className="topArea__left">
                    <span>{rowName}</span>
                    {subRowName && (
                        <div className="topArea__left--subRowName">
                            {subRowName.includes("、") ? (
                                <>
                                    <span>{subRowName.split("、")[0]}</span>
                                    <span>{subRowName.split("、")[1]}</span>
                                </>
                            ) : (
                                <span>{subRowName}</span>
                            )}
                        </div>
                    )}
                </div>
            </Grid.Column>
            <Grid.Column width={13}>
                <div className="topArea__right">
                    <div className="topArea__right--box">
                        <Input
                            onChange={e => onChangeFct(e)}
                            type="text"
                            value={localValue}
                            readOnly={type === "id"}
                            disabled={type === "id"}
                            input={{
                                style: {
                                    backgroundColor:
                                        type === "id" ? "#EEEEEE" : ""
                                }
                            }}
                            maxLength={type === "hasTLAAwardDate" ? "4" : ""}
                            style={{ width: "100%" }}
                        />
                    </div>
                </div>
            </Grid.Column>
        </Grid.Row>
    );
};

export default CustomInput;

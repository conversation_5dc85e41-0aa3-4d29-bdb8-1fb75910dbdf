import React, { useEffect, useState } from "react";

// plugins
import { useDispatch, useSelector } from "react-redux";
import { Modal, Button } from "semantic-ui-react";

// utils
import { isEmpty } from "../../../../../../../commons";
import getNewsTB from "../../Utils/getNewsTB";

//
import textConfig from "../../Utils/textConfig";
import NewsAct from "../../EditNewsAction";
import message from "./modalText";
import { langSelction } from "../../Utils/fixedSelect";
import clsName from "../../Utils/clsName";

// hooks
import useCusContext from "../../../../../../common/hooks/useCusContext";

function CustomModal() {
    const [oldState] = useCusContext();
    const { websiteSubject } = oldState.websiteSetting;

    const newsDispatch = useDispatch();
    const { modalCaller, openModal, modalMsg, newsBriefInfo } = useSelector(
        state => state
    );

    const [title, setTitle] = useState("");
    const [content, setContent] = useState([]);

    useEffect(() => {
        if (!modalCaller || !openModal) return;
        const {
            linkSelect,
            RemoveBtn,
            carouselRM,
            AttachmentRM,
            SendBtn
        } = message;
        switch (modalCaller) {
            case clsName.linkSelect:
                setTitle(linkSelect.title);
                setContent(linkSelect.content);
                break;
            case clsName.DeleteBtn:
            case clsName.RemoveBtn:
                {
                    setTitle(RemoveBtn.title);
                    const allHeader = [];
                    langSelction.forEach(({ name, value }) => {
                        if (value === "zh-tw" && newsBriefInfo.titleZH) {
                            allHeader.push(
                                `(${name}): ${newsBriefInfo.titleZH}`
                            );
                        } else if (value === "en" && newsBriefInfo.titleEN) {
                            allHeader.push(
                                `(${name}): ${newsBriefInfo.titleEN}`
                            );
                        }
                    });

                    setContent([RemoveBtn.contentPrefix, ...allHeader]);
                }
                break;
            // todo: "carouselRM"、"AttachmentRM"再補上刪除檔名資訊在content
            case clsName.carouselRM:
                setTitle(carouselRM.title);
                break;
            case clsName.AttachmentRM:
                setTitle(AttachmentRM.title);
                break;
            case clsName.SendBtn:
                setTitle(SendBtn.title);
                setContent([modalMsg]);
                break;
            case textConfig.ErrorMessage.eventCaller:
                setTitle(textConfig.ErrorMessage.ErrorTitle);
                setContent([modalMsg]);
                break;
            case textConfig.SuccessMessage.eventCaller:
                setTitle(textConfig.SuccessMessage.SuccessTitle);
                setContent([]);
                break;
            default:
                break;
        }
    }, [openModal, modalCaller, modalMsg]);

    const closeModal = () => {
        newsDispatch({ type: NewsAct.SET_OPENMODAL, payload: false });
        newsDispatch({
            type: NewsAct.SET_MODALMSG,
            payload: ""
        });
        setTitle("");
        setContent([]);

        if (modalCaller === textConfig.SuccessMessage.eventCaller) {
            // 離開EditMode
            newsDispatch({
                type: NewsAct.SET_NOTEDITED
            });
            getNewsTB(newsDispatch, websiteSubject);
        }
    };

    const modalSelect = value => {
        newsDispatch({ type: NewsAct.SET_MODALSELECT, payload: value });
        closeModal();
    };

    return (
        <Modal size="tiny" open={openModal}>
            <Modal.Header>{title}</Modal.Header>
            {!isEmpty(content) && (
                <Modal.Content>
                    {content.map(tmpContent => (
                        <p key={tmpContent}>{tmpContent}</p>
                    ))}
                </Modal.Content>
            )}
            <Modal.Actions>
                <Button negative onClick={() => modalSelect(false)}>
                    取消
                </Button>
                <Button primary onClick={() => modalSelect(true)}>
                    確認
                </Button>
            </Modal.Actions>
        </Modal>
    );
}

export default CustomModal;

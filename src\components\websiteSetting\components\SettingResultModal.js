import React, { useContext } from "react";
import { Button, Modal } from "semantic-ui-react";
import { StoreContext } from "../../../store/StoreProvider";
import Act from "../../../store/actions";

function closeEditMode(menuActiveItem, dispatch) {
    const pages = ["MainCarousel", "LinkingPage"];
    if (pages.find(element => element === menuActiveItem.key)) {
        dispatch({
            type: Act.SET_ISEDITEDDISABLE,
            payload: true
        });
    }
}

function SettingResultModal({
    openSettingResultModal,
    setOpenSettingResultModal,
    showMessage
}) {
    const [state, dispatch] = useContext(StoreContext);
    const { menuActiveItem } = state.websiteSetting;

    return (
        <Modal
            size="mini"
            open={openSettingResultModal}
            onClose={() => setOpenSettingResultModal(false)}
        >
            <Modal.Content>
                <p>{showMessage}</p>
            </Modal.Content>
            <Modal.Actions>
                <Button
                    positive
                    onClick={() => {
                        setOpenSettingResultModal(false);
                        closeEditMode(menuActiveItem, dispatch);
                    }}
                >
                    關閉視窗
                </Button>
            </Modal.Actions>
        </Modal>
    );
}

export default SettingResultModal;

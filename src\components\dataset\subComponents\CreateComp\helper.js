import {
    createNmtlData,
    readNmtlData,
    updateNmtlData
} from "../../../../api/nmtl";
import { isCorrectSuffix } from "../../../../api/nmtl/ApiField";
import getKeyBySingle from "../../../../api/nmtl/ApiKey";
import getSingleByApi from "../../../../api/nmtl/ApiSingle";
import {
    convertToGenericSingle,
    LOCATION_KEY,
    LOCATION_LAT,
    LOCATION_LONG,
    LOCATION_TYPE,
    mappingLabel
} from "../../../common/sheetCrud/sheetCrudHelper";
import { createConfig } from "./createConfig";
import { isEmpty, isObjectEqual } from "../../../../commons";
import Api from "../../../../api/nmtl/Api";
import {
    assignSubValues,
    getReservedNewId
} from "../../../common/sheetCrud/utils";

export const findSameIdList = async (value, sheetName, url) => {
    const { newApi } = isCorrectSuffix(sheetName, "");
    const classType = getKeyBySingle(getSingleByApi(newApi));

    const apiPath = url
        .replace("{keyword}", value)
        .replace("{type}", classType);
    // 判斷原文書名是否已存在
    const { data } = await readNmtlData(apiPath);

    return data;
};

export const getCreateNmtlItemResult = async (
    user,
    apiPath,
    dataset,
    sheetName,
    item,
    newApi,
    headerFields,
    cloneCreateStateForUpdate
) => {
    //
    if (isEmpty(item) || isEmpty(dataset) || isEmpty(sheetName)) {
        return { newSrcId: null };
    }

    const apiUrl = Api.getGeneric;
    const itemKey = getKeyBySingle(getSingleByApi(newApi));

    let newSrcId;

    // if (Object.hasOwn(item, "srcId")) {
    //     newSrcId = item.srcId;
    // } else {
    //     console.log("itemKey", itemKey);
    //     newSrcId = await getReservedNewId(itemKey);
    //     console.log("newSrcId", newSrcId);
    //     item.srcId = newSrcId || "";
    // }

    if (Object.hasOwn(item, "srcId")) {
        if (item.srcId.startsWith("default")) {
            newSrcId = await getReservedNewId(itemKey);
            item.srcId = newSrcId || "";
        } else {
            newSrcId = item.srcId;
        }
    } else {
        newSrcId = await getReservedNewId(itemKey);
        item.srcId = newSrcId || "";
    }

    if (apiUrl && itemKey) {
        const dt = mappingLabel(item, headerFields);

        const updateSpecificKeys = (source, target, keys) => {
            keys.forEach(key => {
                if (key === "authorName") {
                    if (target.hasAuthor.includes("create")) {
                        // eslint-disable-next-line no-param-reassign
                        target[key] = [""];
                        return;
                    }
                }
                if (key === "translatorName") {
                    if (target.hasTranslator.includes("create")) {
                        // eslint-disable-next-line no-param-reassign
                        target[key] = [""];
                        return;
                    }
                }
                // eslint-disable-next-line no-param-reassign
                target[key] = source[key];
            });
        };
        if (!isEmpty(cloneCreateStateForUpdate)) {
            updateSpecificKeys(cloneCreateStateForUpdate, dt, [
                "hasAuthor",
                "hasTranslator",
                "authorName",
                "translatorName"
            ]);
        }

        const entry = convertToGenericSingle(dt, dataset, apiPath);

        function transformLabelPerson(tmpData) {
            if (isEmpty(tmpData)) return [];
            return tmpData.map(item => {
                if (item.value && item.value.label_Person) {
                    const labelPerson = item.value.label_Person;
                    const match = labelPerson.match(/^([^(]+)/);
                    if (match) {
                        const mainName = match[1].trim();
                        const langTag = labelPerson.split("@").pop();
                        // eslint-disable-next-line no-param-reassign
                        item.value.label_Person = `${mainName}@${langTag}`;
                    }
                }
                return item;
            });
        }

        if (entry?.value?.hasEditor) {
            // eslint-disable-next-line no-undef
            transformLabelPerson(entry?.value?.hasEditor);
        }

        if (entry?.value?.hasPublisher) {
            // eslint-disable-next-line no-undef
            transformLabelPerson(entry?.value?.hasPublisher);
        }

        if (!entry) {
            return null;
        }

        const createResult = await createNmtlData(
            user,
            apiUrl,
            dataset,
            sheetName,
            entry
        ).then(res => res === "OK");

        // 針對地點修改
        if (Object.hasOwn(entry.value, LOCATION_KEY)) {
            if (Object.hasOwn(entry.value, LOCATION_KEY)) {
                const newLocation = entry.value[LOCATION_KEY][0];
                if (newLocation) {
                    const tmpValue = await assignSubValues(
                        [{ [LOCATION_KEY]: { 0: newLocation } }],
                        dataset
                    );

                    const oldValue = tmpValue[0];

                    const tmpLat = oldValue[LOCATION_LAT]
                        ? Object.values(oldValue[LOCATION_LAT])
                        : null;
                    const tmpLong = oldValue[LOCATION_LONG]
                        ? Object.values(oldValue[LOCATION_LONG])
                        : null;

                    const newSrcJson = {
                        graph: dataset,
                        srcId: newLocation,
                        classType: LOCATION_TYPE,
                        value: {
                            geoLongitude: tmpLong || null,
                            geoLatitude: tmpLat || null
                        }
                    };
                    const newDstJson = {
                        graph: dataset,
                        srcId: newLocation,
                        classType: LOCATION_TYPE,
                        value: {
                            geoLongitude: item[LOCATION_LAT] || null,
                            geoLatitude: item[LOCATION_LONG] || null
                        }
                    };

                    if (isObjectEqual(newSrcJson, newDstJson)) {
                        return {
                            newSrcId,
                            createResult,
                            LocationUpdateResult: true
                        };
                    }

                    const LocationUpdateResult = await updateNmtlData(
                        user,
                        apiUrl,
                        dataset,
                        sheetName,
                        newSrcJson,
                        newDstJson
                    ).then(res => res === "OK");

                    return {
                        newSrcId,
                        createResult,
                        LocationUpdateResult
                    };
                }
            }
        }
        return { newSrcId, createResult, LocationUpdateResult: true };
        // return null;
    }
    return { newSrcId: null };
};

export const handleCreateSrcId = async (
    user,
    apiPath,
    dataset,
    sheetName,
    action,
    newApi,
    labelPublication,
    srcId,
    headerFields
) => {
    // 直接新增ID
    if (!srcId) {
        const { newSrcId, createResult } = await getCreateNmtlItemResult(
            user,
            apiPath,
            dataset,
            sheetName,
            {
                label_Publication: [
                    // eslint-disable-next-line camelcase
                    `${labelPublication}@zh`,
                    // eslint-disable-next-line camelcase
                    `${labelPublication}@en`
                ]
            },
            newApi,
            headerFields
        );

        return { newSrcId, createResult };
    }

    // 表示拉入其他資料集的ID，新增該著作在當前資料集
    if (action === createConfig.createButton && srcId) {
        const { newSrcId, createResult } = await getCreateNmtlItemResult(
            user,
            apiPath,
            dataset,
            sheetName,
            {
                srcId,
                label_Publication: [
                    // eslint-disable-next-line camelcase
                    `${labelPublication}@zh`,
                    // eslint-disable-next-line camelcase
                    `${labelPublication}@en`
                ]
            },
            newApi,
            headerFields
        );

        return {
            newSrcId,
            createResult
        };
    }

    // 編輯時新增翻譯書，不需要新增原文書
    if (action === createConfig.editButton && srcId) {
        return { newSrcId: srcId, createResult: false };
    }

    return { createResult: false };
};

export const updateObjectValue = (obj, key, value) => {
    const tmpObj = JSON.parse(JSON.stringify(obj));
    tmpObj[key] = value;

    // // eslint-disable-next-line no-prototype-builtins
    // if (tmpObj.hasOwnProperty(key)) {
    //     // eslint-disable-next-line no-param-reassign
    //     tmpObj[key] = value;
    // } else {
    //     console.warn(`Key "${key}" does not exist in the object.`);
    // }
    return tmpObj;
};

export const updateMultipleValues = (obj, updates) => {
    let updatedObj = { ...obj };
    updates.forEach(([key, value]) => {
        updatedObj = updateObjectValue(updatedObj, key, value);
    });
    return updatedObj;
};

export const checkLabels = array => {
    // eslint-disable-next-line no-restricted-syntax
    for (const obj of array) {
        if (obj.label === "") {
            return false;
        }
    }
    return true;
};

// 處理原文書名
export const transformDefaultLabelPublication = (tmpData, lang) => {
    if (isEmpty(tmpData)) return [];
    return tmpData.map(part => `${part}@${lang}`);
};

export const mergeStates = (
    cloneCreateStateForAuthorAndTranslator,
    createState
) => {
    if (
        isEmpty(cloneCreateStateForAuthorAndTranslator) ||
        isEmpty(createState)
    ) {
        return createState;
    }
    const newCreateState = JSON.parse(JSON.stringify(createState));

    const keysToOverride = [
        "hasAuthor",
        "hasTranslator",
        "authorName",
        "translatorName"
    ];

    keysToOverride.forEach(key => {
        if (cloneCreateStateForAuthorAndTranslator.hasOwnProperty(key)) {
            // hasAuthor、hasTranslator在沒有輸入任何值的時候，會是['create']，如果為['create']須將其變成null，讓更新時能判斷實際是否有更新值
            if (
                (key === "hasAuthor" || key === "authorName") &&
                cloneCreateStateForAuthorAndTranslator?.hasAuthor.includes(
                    "create"
                )
            ) {
                newCreateState[key] = null;
            } else if (
                (key === "hasTranslator" || key === "translatorName") &&
                cloneCreateStateForAuthorAndTranslator?.hasTranslator.includes(
                    "create"
                )
            ) {
                newCreateState[key] = null;
            } else {
                newCreateState[key] =
                    cloneCreateStateForAuthorAndTranslator[key];
            }
        }
    });

    return newCreateState;
};

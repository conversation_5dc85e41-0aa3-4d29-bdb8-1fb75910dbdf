import axios from "axios";
import { fileServerAPI, fileServerMethod } from "../../../api/fileServer";
import uploadConfig from "../../toolPages/components/upload/uploadConfig";

const imagePathName = "dataSet";

const imageHandler = (editorRef, mainSubject) => {
    const inputEl = document.createElement("input");
    inputEl.setAttribute("type", "file");
    inputEl.setAttribute("accept", "image/*");
    inputEl.setAttribute("multiple", "");
    inputEl.click();

    inputEl.onchange = () => {
        const preReqUrl = `${fileServerAPI.uploadFile.replace(
            "[type]",
            "image"
        )}`;
        const reqUrl = mainSubject?.selected?.dataset
            ? `${preReqUrl}/${imagePathName}/${mainSubject.selected.dataset}`
            : `${preReqUrl}/${imagePathName}`;

        // eslint-disable-next-line no-restricted-syntax
        for (const file of inputEl.files) {
            // create formData
            const formData = new FormData();
            formData.append(uploadConfig.ImageFormName, file);

            // upload image
            axios({
                method: fileServerMethod.uploadFile,
                url: reqUrl,
                headers: {
                    "Access-Control-Allow-Origin": "*"
                },
                data: formData
            })
                .then(res => {
                    if (res.status === 200) {
                        const { imgUrl } = res.data.images[0];
                        // 上傳圖片後顯示在畫面上
                        editorRef.current
                            .getEditor()
                            .insertEmbed(null, "image", imgUrl);
                    }
                })
                .catch(err => {
                    console.log("err ", err);
                });
        }
    };
};

export default imageHandler;

import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";

import { Button } from "semantic-ui-react";

import FileAct from "../../../../../reduxStore/file/fileAction";

const FileFolderControlPanel = () => {
    const dispatchRedux = useDispatch();
    const {
        files: { curFolderFiles, defaultValue }
    } = useSelector(state => state);

    const [diffValue, setDiffValue] = useState(false);
    const [originalValue] = useState(defaultValue);

    const setDefault = () => {
        // reset it to default(false)
        setDiffValue(false);

        // clear currentValue
        dispatchRedux({
            type: FileAct.SELECT_FILE,
            payload: ""
        });
        dispatchRedux({
            type: FileAct.SET_DEFAULT_VALUE,
            payload: originalValue
        });
    };

    const handlePick = () => {
        if (curFolderFiles.checked.length === 0) {
            return dispatchRedux({
                type: FileAct.PICKER_CUR_FOLDER_MESSAGE,
                payload: {
                    type: "warning",
                    title: "尚未選取檔案",
                    text: ""
                }
            });
        }
        const imgUrlChecked = curFolderFiles.checked.map(dff => dff.value);

        if (imgUrlChecked[0] !== originalValue) {
            // update diff state
            setDiffValue(true);

            // set selected image to currentValue
            return dispatchRedux({
                type: FileAct.SELECT_FILE,
                payload: imgUrlChecked[0]
            });
        }
        return setDefault();
    };

    const handleSetToDefault = () => setDefault();

    return (
        <div className="control-panel-container">
            <div className="control-panel">
                <Button
                    className="control-panel-btn"
                    color="blue"
                    size="small"
                    disabled={
                        curFolderFiles &&
                        curFolderFiles.checked &&
                        curFolderFiles.checked.length === 0
                    }
                    onClick={handlePick}
                >
                    選擇檔案
                </Button>
                {diffValue && (
                    <Button
                        className="control-panel-btn"
                        color="teal"
                        size="small"
                        onClick={handleSetToDefault}
                    >
                        取消變更
                    </Button>
                )}
            </div>
        </div>
    );
};

export default FileFolderControlPanel;

// import checkStr from "./checkStr";
import { splitTag } from "../config/config";

const checkInstTag = (cell, propLabel) => {
    const limitList = ["@PER", "@ORG"];
    let tmpResStr = "";

    const reason = `請在每個名稱後面加上其中一個選項，${limitList.join("、")}`;
    if (cell.value) {
        if (typeof cell.value === "string") {
            const allValue = cell.value.split(splitTag);
            const check = allValue.every(
                str => !limitList.includes(str.slice(-4))
            );
            if (check) {
                tmpResStr += `${cell.address}, [${cell.value}]， 欄位:${propLabel}，${reason}。\n`;
            }
        } else if (Object.hasOwn(cell.value, "hyperlink")) {
            if (typeof cell.value.text === "string") {
                const allValue = cell.value.text.split(splitTag);
                const check = allValue.every(
                    str => !limitList.includes(str.slice(-4))
                );
                if (check) {
                    tmpResStr += `${cell.address}, [${cell.value.text}]， 欄位:${propLabel}，${reason}。\n`;
                }
            }
        }
        // else {
        //     tmpResStr += checkStr(cell, propLabel);
        // }
    }

    return tmpResStr;
};

export default checkInstTag;

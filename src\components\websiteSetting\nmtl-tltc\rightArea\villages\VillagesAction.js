const VillagesAct = {
    SET_IS_EDITED_VILLAGES: "SET_IS_EDITED_VILLAGES",
    SET_EDITING_VILLAGEID: "SET_EDITING_VILLAGEID",
    DATA_CONTENT_ROW_CHECKED: "DATA_CONTENT_ROW_CHECKED",
    DATA_CONTENT_ROW_CHECKED_ALL: "DATA_CONTENT_ROW_CHECKED_ALL",
    DATA_CONTENT_ROW_NO_CHECKED: "DATA_CONTENT_ROW_NO_CHECKED",
    DATA_CONTENT_ROW_CHECKED_CLEAN: "DATA_CONTENT_ROW_CHECKED_CLEAN",
    DATA_SET_ROWS: "DATA_SET_ROWS",
    SET_TRANSLATOR_CONTENT_ZH_FOR_QUILL: "SET_TRANSLATOR_CONTENT_ZH_FOR_QUILL",
    SET_TRANSLATOR_CONTENT_EN_FOR_QUILL: "SET_TRANSLATOR_CONTENT_EN_FOR_QUILL",
    SET_IS_EDITED_ACI: "SET_IS_EDITED_ACI",
    SET_EDITING_ACI_ID: "SET_EDITING_ACI_ID",
    SET_EDITING_ACI_DATA: "SET_EDITING_ACI_DATA",
    SET_NOTIFY_UPDATE_ACVIVITY_INFO: "SET_NOTIFY_UPDATE_ACVIVITYINFO",
    SET_ACI_LIST_LENGTH: "SET_ACI_LIST_LENGTH",
    SET_ACI_LIST: "SET_ACI_LIST",
    SET_IS_EDITED_ACTIVITY_INFO_LIST: "SET_IS_EDITED_ACTIVITY_INFO_LIST",
    SET_DELETING_ACTIVITY_INFO: "SET_DELETING_ACTIVITY_INFO",
    SET_IS_DEL_MODAL_OPEN: "SET_IS_DEL_MODAL_OPEN",
    SET_IS_MODAL_OPEN: "SET_IS_MODAL_OPEN",
    SET_VILLAGE_LIST: "SET_VILLAGE_LIST",

    CUR_FOLDER_FILES_STATUS: "CUR_FOLDER_FILES_STATUS",
    CLEAR_CUR_FOLDER_FILES_STATUS: "CLEAR_CUR_FOLDER_FILES_STATUS",
    UPLOAD_IMAGE: "UPLOAD_IMAGE",
    CLEAN_PICKER_CUR_FOLDER_MESSAGE: "CLEAN_PICKER_CUR_FOLDER_MESSAGE",
    CLEAN_PICKER_DROP_MESSAGE: "CLEAN_PICKER_DROP_MESSAGE",
    FOLDER_FILES_URL: "FOLDER_FILES_URL",
    SET_NOTIFY_UPLOAD_TIDBITS: "SET_NOTIFY_UPLOAD_TIDBITS"
};

export default VillagesAct;

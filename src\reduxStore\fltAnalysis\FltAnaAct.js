const FltAnaAct = {
    clearCache: "CLEAR_CACHE",
    setReadTable: "SET_READ_TABLE",
    setEditTable: "SET_EDIT_TABLE",
    setRefreshTableSignal: "SET_REFRESH_TABLE_SIGNAL",
    setBasicData: "SET_BASIC_DATA",
    setBasicDataFilt: "SET_BASIC_DATA_FILTERED",
    setDetailData: "SET_DETAIL_DATA",
    setInitUpdatedData: "SET_INIT_UPDATED_DATA",
    setUpdatedData: "SET_UPDATED_DATA",
    setFetchDetailFunc: "SET_FETCH_DETAIL_FUNC",
    fetchSignalBasic: "FETCH_SIGNAL_BASIC",
    fetchSignalFilter: "FETCH_SIGNAL_FILTER",
    fetchSignalDetail: "FETCH_SIGNAL_DETAIL",
    fetchSignalFrstore: "FETCH_SIGNAL_FIRESTORE",
    fetchSignalDbAndFr: "FETCH_SIGNAL_DB_AND_FR",
    putMemoFirestoreColDocIds: "PUT_MEMO_FR_COL_DOCIDS",
    clearMemoFirestoreColDocIds: "CLEAR_MEMO_FR_COL_DOCIDS",
    updateData: "UPDATE_DATA",
    setSearchKeyword: "SET_SEARCH_KEYWORD",
    pushQuery: "PUSH_QUERY",
    popQuery: "POP_QUERY",
    setEditModalOpen: "SET_EDIT_MODAL_OPEN",
    setEditModalContext: "SET_EDIT_MODAL_CONTEXT",
    setBypassModalOpen: "SET_BYPASS_MODAL_OPEN",
    setBypassModalContext: "SET_BYPASS_MODAL_CONTEXT",
    setBypassModalContextInit: "SET_BYPASS_MODAL_CONTEXT_INIT",
    clearEditModalContext: "CLEAR_EDIT_MODAL_CONTEXT",
    setMessage: "SET_MESSAGE",
    setData: "SET_DATA",
    setMainSubject: "SET_MAIN_SUBJECT"
};

export default FltAnaAct;

import React, { useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";

// semantic ui
import { Button } from "semantic-ui-react";

// utils
import { StoreContext } from "../../../../../../store/StoreProvider";
import { deleteNmtlData, readNmtlData } from "../../../../../../api/nmtl";
import textMsg from "../../../../commons/textMsg";
import Api from "../../../../../../api/nmtl/Api";
import { loadTBData, reduceSubData } from "../utils/utils";

// components
import SaveResultModal from "../../../../commons/components/SaveResultModal";
import useGetSubjectOPs from "../../../../../common/hooks/useGetSubjectOPs";
import { createHistoryEvent } from "../../../../../downloadData/components/history/common/common";

function DelButton({ type }) {
    const [state, dispatch] = useContext(StoreContext);
    const { modal, sheetName, graph } = textMsg;
    const { user } = state;
    const {
        rellinkTable,
        menuActiveItem,
        websiteSubject
    } = state.websiteSetting;
    const [enable, setEnable] = useState(false);
    const classType = "URLEvent";
    const [loading, setLoading] = useState(false);
    const [opSaveRModal, setOpSaveRModal] = useState(false);
    const [modalMessage, setModalMessage] = useState("");

    const { displayName } = user;
    const { headerActiveName } = state.common;
    const { groupInfo } = state.data;
    const dropOptions = useGetSubjectOPs(groupInfo);

    const sheetSelectedValue = dropOptions.find(it => it.key === websiteSubject)
        ?.text;
    const columns = [headerActiveName, sheetSelectedValue, menuActiveItem.name];

    const openModal = () => {
        setOpSaveRModal(true);
    };

    const closeModal = () => {
        setOpSaveRModal(false);
        loadTBData(type, dispatch);
    };

    const successCallBack = res => {
        openModal();
        setModalMessage(res === "OK" ? modal.success : modal.wrong);
        setLoading(false);
    };

    const failedCallBack = err => {
        openModal();
        setModalMessage(`${modal.failed} \n ${err}`);
        setLoading(false);
    };

    useEffect(() => {
        setEnable(rellinkTable.some(el => el.check));
    }, [rellinkTable]);

    const delCheckData = () => {
        const delItems = rellinkTable.filter(({ check }) => check);
        const genApiStr = Api.getGeneric;
        const apiStr = Api.getSubValue2
            .replace("{ds}", graph)
            .replace("{ids}", "")
            .replace("{type}", classType);

        return delItems.map(({ urlId }) => {
            const entry = { ids: urlId };
            const options = {
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "Accept-Encoding": "gzip"
                },
                body: JSON.stringify({ entry })
            };

            return new Promise((resolve, reject) => {
                // 取得ID所有詳細資料
                readNmtlData(apiStr, 60000, options)
                    .then(res => {
                        const tmpEntryVal = reduceSubData(res.data, classType);
                        delete tmpEntryVal.id;
                        const tmpEntry = {
                            graph,
                            classType,
                            srcId: urlId,
                            value: tmpEntryVal
                        };
                        resolve(
                            deleteNmtlData(
                                user,
                                genApiStr,
                                graph,
                                sheetName,
                                tmpEntry
                            )
                        );
                        const historyMsg = `${JSON.stringify(tmpEntry)}`;

                        // 建立歷史紀錄
                        createHistoryEvent(
                            displayName,
                            "刪除",
                            `${columns.join("/")}：${historyMsg}`
                        );
                    })
                    .catch(err => {
                        reject(err);
                    });
            });
        });
    };

    const handleClick = async () => {
        setLoading(true);
        const delPromises = delCheckData();
        Promise.all(delPromises)
            .then(res => {
                const check = res.some(val => val !== "OK");
                successCallBack(check ? "Not OK" : "OK");
            })
            .catch(err => {
                const concatMsg = err.join("\n");
                failedCallBack(concatMsg);
            });
    };

    return (
        <>
            <Button
                disabled={!enable}
                negative
                onClick={handleClick}
                loading={loading}
            >
                刪除
            </Button>
            <SaveResultModal
                openModal={opSaveRModal}
                onClose={closeModal}
                onClick={closeModal}
                modalMessage={modalMessage}
            />
        </>
    );
}

DelButton.propTypes = {
    /** 相關連結分類: ["合作夥伴", "相關資源"] */
    type: PropTypes.oneOf(["", "合作夥伴", "相關資源"])
};

DelButton.defaultProps = {
    /** 相關連結分類: ["合作夥伴", "相關資源"] */
    type: ""
};

export default DelButton;

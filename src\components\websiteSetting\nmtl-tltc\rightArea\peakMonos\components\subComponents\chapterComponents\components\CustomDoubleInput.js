import React, { useState } from "react";
import { Grid, Input } from "semantic-ui-react";
import "../../../EditCate.scss";

const CustomDoubleInput = ({
    rowName,
    titles,
    type,
    value,
    updatedData,
    subRowName,
    debouncedUpdateFct,
    required
}) => {
    const [firstValue, setFirstValue] = useState(value[0]);
    const [secondValue, setSecondValue] = useState(value[1]);

    const onChangeFct = (index, tmpValue) => {
        if (index === 0) {
            setFirstValue(tmpValue);
        } else {
            setSecondValue(tmpValue);
        }
        debouncedUpdateFct(updatedData, type[index], tmpValue);
    };

    return (
        <Grid.Row>
            <Grid.Column
                width={3}
                style={{
                    backgroundColor: "#e0e1e2"
                }}
            >
                <div className="topArea__left">
                    <div>
                        <span>{rowName}</span>
                        {required && <span style={{ color: "red" }}>*</span>}
                    </div>

                    {subRowName && (
                        <div className="topArea__left--subRowName">
                            {subRowName.includes("、") ? (
                                <>
                                    <span>{subRowName.split("、")[0]}</span>
                                    <span>{subRowName.split("、")[1]}</span>
                                </>
                            ) : (
                                <span>{subRowName}</span>
                            )}
                        </div>
                    )}
                </div>
            </Grid.Column>
            <Grid.Column width={13}>
                <div className="topArea__double">
                    <div className="topArea__double--box">
                        <div className="topArea__double--box--label">
                            {titles[0].split("、").map(i => (
                                <p key={`zh_${i}`}>{i}</p>
                            ))}
                        </div>
                        <Input
                            onChange={e => onChangeFct(0, e.target.value)}
                            type="text"
                            value={firstValue}
                            className="topArea__double--box--field"
                        />
                    </div>
                    <div className="topArea__double--box">
                        <div className="topArea__double--box--label">
                            {titles[1].split("、").map(i => (
                                <p key={`en_${i}`}>{i}</p>
                            ))}
                        </div>
                        <Input
                            onChange={e => onChangeFct(1, e.target.value)}
                            type="text"
                            value={secondValue}
                            className="topArea__double--box--field"
                        />
                    </div>
                </div>
            </Grid.Column>
        </Grid.Row>
    );
};

export default CustomDoubleInput;

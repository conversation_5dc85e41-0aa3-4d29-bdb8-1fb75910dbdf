import React, { useEffect, useState } from "react";
import { Dropdown } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import { isEmpty } from "../../../../../../commons";
import accMngAct from "../../../../../../reduxStore/accManage/accManageAction";

function PgDD() {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { totalPage, currentPage } = state;
    const [optionArr, setOptionArr] = useState([]);

    useEffect(() => {
        const tmpOption = [];
        for (let i = 0; i < totalPage; i += 1) {
            tmpOption.push({
                key: i + 1,
                value: i + 1,
                text: i + 1
            });
        }
        setOptionArr(tmpOption);

        dispatch({
            type: accMngAct.SET_CURRENTPAGE,
            payload: 1
        });
    }, [totalPage]);

    const handleChange = (evt, data) => {
        dispatch({
            type: accMngAct.SET_CURRENTPAGE,
            payload: data.value
        });
    };

    return (
        <React.Fragment>
            <div style={{ marginRight: "0.5rem" }}>
                <p>現在是第</p>
            </div>
            {!isEmpty(optionArr) && (
                <Dropdown
                    compact
                    selection
                    options={optionArr}
                    onChange={handleChange}
                    value={optionArr[currentPage - 1].value}
                />
            )}
            <div style={{ marginLeft: "0.5rem" }}>
                <p>頁，共 {totalPage} 頁</p>
            </div>
        </React.Fragment>
    );
}

export default PgDD;

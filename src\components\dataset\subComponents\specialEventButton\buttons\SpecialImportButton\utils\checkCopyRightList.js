const checkCopyRightList = (cell, propLabel, cpList) => {
    let tmpResStr = "";
    const cpListLabel = cpList.map(el => el.label);

    const reason = `請填選以下選項[${cpListLabel}]`;
    if (typeof cell.value === "string") {
        if (!cpListLabel.includes(cell.value)) {
            tmpResStr += `${cell.address}, [${cell.value}], 欄位:${propLabel}，${reason}`;
        }
    }

    return tmpResStr;
};

export default checkCopyRightList;

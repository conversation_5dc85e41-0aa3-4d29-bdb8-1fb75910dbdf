import LiftBooks from "./rightArea/liftBooks/LiftBooks";
import RelationLinks from "./rightArea/relationLinks/RelationLinks";
import EditNewsProvider from "../commons/components/EditNews/EditNewsProvider";
import PeakMonosIndex from "./rightArea/peakMonos";
import VillagesIndex from "./rightArea/villages";
/**
 * name: 後台畫面顯示文字
 * component: 搭配元件
 * key: unique ID
 * */

const menuItem = [
    {
        name: "最新消息",
        component: EditNewsProvider,
        key: "EditNews"
    },
    {
        name: "LiFT書系",
        component: LiftBooks,
        key: "LiftBooks"
    },
    {
        name: "外部網站",
        component: RelationLinks,
        key: "RelationLinks"
    },
    {
        name: "臺灣文學獎",
        component: PeakMonosIndex,
        key: "PeakMonos"
    },
    {
        name: "駐村計畫",
        component: VillagesIndex,
        key: "Villages"
    }
];

export default menuItem;

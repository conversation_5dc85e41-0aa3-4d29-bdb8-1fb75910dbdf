// 這個檔案由 API 複製而來，兩者共享同一個檔案
// RelationEvent
const RelationEventType = "RelationEvent";
const RelationEventPrefix = "RETEVT";
const hasRelationship = "relationType";

// FoundationEvent
const FoundationEventType = "FoundationEvent";
const FoundationEventPrefix = "FOUEVT";
const hasFoundationType = "hasFoundationType";

const DateEventType = "DateEvent";
const AwardEventType = "AwardEvent";

const PersonType = "Person";
const AwardType = "Award";
const hasAwardReceived = "hasAwardReceived";
const hasAwardReceivedInverse = "isAwardReceivedOf";

// instanceRandomID 為需要建立 random ID 的 class 列表
const instanceRandomID = [
    { prefix: "PER", eventType: PersonType },
    { prefix: "EVT", eventType: "Event" },
    { prefix: "AWAEVT", eventType: AwardEventType },
    { prefix: "ORGEVT", eventType: "OrganizationEvent" },
    { prefix: "EDUEVT", eventType: "EducationEvent" },
    // { prefix: "EMPEVT", eventType: "EmploymentEvent" },
    { prefix: FoundationEventPrefix, eventType: FoundationEventType },
    { prefix: "URLEVT", eventType: "URLEvent" },
    { prefix: "ART", eventType: "Article" },
    { prefix: "PUB", eventType: "Publication" },
    { prefix: "OTW", eventType: "OtherWork" },
    { prefix: "PRO", eventType: "Project" },
    { prefix: "ORG", eventType: "Organization" },
    { prefix: "COL", eventType: "Collectible" },
    { prefix: "FND", eventType: "Foundation" },
    { prefix: "LOC", eventType: "Location" },
    { prefix: "SOU", eventType: "Source" },
    { prefix: "EXA", eventType: "Examination" },
    { prefix: "ORG", eventType: "EducationalOrganization" },
    { prefix: "DYN", eventType: "Dynasty" },
    { prefix: "CTR", eventType: "Country" },
    { prefix: "PRV", eventType: "Province" },
    { prefix: "CTY", eventType: "City" },
    { prefix: "TOS", eventType: "TownshipOfTaiwan" },
    { prefix: "DRW", eventType: "DerivateWork" },
    { prefix: "FIC", eventType: "FictionalCharacter" },
    { prefix: "NZI", eventType: "Nanzi" }
];

const eventPrefix = [
    { prefix: "EVT", eventType: "Event" },
    { prefix: RelationEventPrefix, eventType: RelationEventType },
    { prefix: "AWAEVT", eventType: AwardEventType },
    { prefix: "ORGEVT", eventType: "OrganizationEvent" },
    { prefix: "EDUEVT", eventType: "EducationEvent" },
    // { prefix: "EMPEVT", eventType: "EmploymentEvent" },
    { prefix: "SPEEVT", eventType: "SpecialtyEvent" },
    { prefix: FoundationEventPrefix, eventType: FoundationEventType },
    // { prefix: "URLEVT", eventType: "URLEvent" },
    { prefix: "VIL", eventType: "VillageEvent" }
];

const virtualEventPrefix = [{ prefix: "MEMEVT", eventType: "memberEvent" }];

const classPrefix = [
    { prefix: "PER", eventType: PersonType },
    { prefix: "LOC", eventType: "Location" },
    { prefix: "ORG", eventType: "Organization" },
    { prefix: "ORG", eventType: "EducationalOrganization" },
    // VR文學館
    { prefix: "CARD", eventType: "Organization" },
    { prefix: "ART", eventType: "Article" },
    { prefix: "COL", eventType: "Collectible" },
    { prefix: "PRO", eventType: "Project" },
    { prefix: "DAE", eventType: DateEventType },
    { prefix: "OTW", eventType: "OtherWork" },
    { prefix: "PUB", eventType: "Publication" },
    { prefix: "ADC", eventType: "AcademicDiscipline" },
    { prefix: "ADG", eventType: "AcademicDegree" },
    { prefix: "EXA", eventType: "Examination" },
    { prefix: "SOC", eventType: "SocialProcedure" },
    { prefix: "WEB", eventType: "WebConfig" },
    { prefix: "POS", eventType: "Position" },
    { prefix: "OCP", eventType: "Occupation" },
    { prefix: "SPE", eventType: "Specialty" },
    { prefix: "", eventType: "Gender" },
    { prefix: "", eventType: "EthnicGroup" },
    { prefix: "", eventType: "CopyrightStatus" },
    { prefix: "", eventType: "AreaOfTaiwan" },
    { prefix: "Taiwan", eventType: "TaiwanPeriod" },
    { prefix: "", eventType: "Language" },
    { prefix: "DRW", eventType: "DerivateWork" },
    { prefix: "AWA", eventType: AwardType },
    { prefix: "FND", eventType: "Foundation" },
    { prefix: "SOU", eventType: "Source" },
    { prefix: "URLEVT", eventType: "URLEvent" },
    { prefix: "FOR", eventType: "Format" },
    { prefix: "DYN", eventType: "Dynasty" },
    { prefix: "CTR", eventType: "Country" },
    { prefix: "PRV", eventType: "Province" },
    { prefix: "CTY", eventType: "City" },
    { prefix: "TLP", eventType: "TlvmPeriod" },
    { prefix: "TOS", eventType: "TownshipOfTaiwan" },
    { prefix: "DRW", eventType: "DerivateWork" },
    { prefix: "FIC", eventType: "FictionalCharacter" },
    { prefix: "NZI", eventType: "Nanzi" },
    { prefix: "NEW", eventType: "News" },
    { prefix: "PCH", eventType: "PeakChapter" },
    { prefix: "PEK", eventType: "PeakMono" },
    { prefix: "TLA", eventType: "TLAAward" },
    { prefix: "TCA", eventType: "TLACategory" },
    { prefix: "ACI", eventType: "ActivityInfo" }
].concat(eventPrefix, virtualEventPrefix);

const PropertyAClass = [
    "Publishing",
    "PrintBookType",
    "CollectibleType",
    "MainSubject",
    "LiteraryGenre"
];

const frontEditPrefix = [
    { prefix: "URLEVT", eventType: "URLEvent" },
    { prefix: "CARDtltc", eventType: "FrontEdit" },
    { prefix: "NEW", eventType: "News" }
];

// URLEvent FrontEditType
const hasFrontEditType = {
    TLTCPartner: "TLTCPartner" // 外譯房相關連結
};

module.exports = {
    RelationEventType,
    RelationEventPrefix,
    hasRelationship,
    FoundationEventType,
    FoundationEventPrefix,
    hasFoundationType,
    DateEventType,
    AwardEventType,
    hasAwardReceived,
    hasAwardReceivedInverse,
    PersonType,
    AwardType,
    instanceRandomID,
    eventPrefix,
    classPrefix,
    PropertyAClass,
    frontEditPrefix,
    hasFrontEditType
};

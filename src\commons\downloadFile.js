import { getFileName } from "./utility";
import getBlobData from "./getBlobData";

const getFile = (res, fileName) => {
    const aElement = document.createElement("a");
    aElement.setAttribute("download", fileName);
    const href = URL.createObjectURL(res);
    aElement.href = href;
    aElement.setAttribute("target", "_blank");
    aElement.click();
    URL.revokeObjectURL(href);
};

const downloadFile = async url => {
    const fileName = getFileName(url);
    const data = await getBlobData(url);
    getFile(data, fileName);
};

export default downloadFile;

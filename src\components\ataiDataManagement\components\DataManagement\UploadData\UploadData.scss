.uploadData {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    border: 1px solid #eeeeee;
    border-radius: 8px;
    padding: 24px 0px 24px 0px;
    margin-bottom: 24px;
    height: 189px;
    justify-content: center;
    .ui.loader:after {
        border-color: #21ba45 transparent transparent;
    }
    .dragTitle {
        font-weight: 500;
        font-size: 20px;
        margin-bottom: 8px;
    }
    .or,
    .explain {
        font-weight: 500;
        font-size: 14px;
        margin-bottom: 8px;
        color: #bdbdbd;
    }
    .uploadBtn {
        border-radius: 8px;
        padding: 8px 16px 8px 8px;
        border: 1px solid #21ba45;
        color: #21ba45;
        font-weight: 500;
        font-size: 14px;
        background-color: white;
        display: flex;
        align-items: center;
        cursor: pointer;
        margin-bottom: 8px;
    }
}

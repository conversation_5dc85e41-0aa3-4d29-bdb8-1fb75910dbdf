import React, { useContext, useState, useEffect } from "react";

// ui
import {
    Input,
    Button,
    Icon,
    Dropdown,
    Select,
    Grid,
    Container
} from "semantic-ui-react";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

import { createHistoryEvent } from "../../../downloadData/components/history/common/common";
import { specialHeaderRemove } from "../../../common/sheetCrud/sheetCrudHelper";

import "./SpecialSearchBar.scss";

const SpecialSearchBar = ({ curState }) => {
    // console.log('I am CustomSearchBar');
    // keep input value
    const [inputValue, setInputValue] = useState("");

    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { mainSubject, sheet, search } = state.data;
    const { headerActiveName } = state.common;
    const { activeHeader } = sheet;
    // const { contentSearchPath, key: sheetName } = sheet.selected;
    const { contentSearchPath, key: sheetName } = curState;
    const { displayName } = state.user;
    const { keyword } = search;

    const [option, setOption] = useState([]);
    const [filterCol, setFilterCol] = useState("all");
    const handleInputChange = (event, { value }) => {
        setInputValue(value || "");
    };

    // refresh all parameter
    const handleRefreshAll = () => {
        // refresh pagination when sheet changed
        dispatch({ type: Act.DATA_PAGINATION_ACTIVE_PAGE_INIT });
        // refresh changed when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });
        // refresh checked when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });
        // refresh created when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CREATED_CLEAN });
    };

    const handleInputClick = () => {
        const columns = [
            headerActiveName,
            mainSubject.selected.value,
            sheet.selected.value
        ];

        createHistoryEvent(displayName, "查詢", columns.join("/"));
        // dispatch search keyword
        dispatch({
            type: Act.DATA_SEARCH_KEYWORD,
            payload: inputValue
        });

        dispatch({
            type: Act.DATA_SEARCH_SEARCH_COLUMN,
            payload: filterCol || "all"
        });

        handleRefreshAll();
    };

    const handleInputKeyPress = event => {
        if (event.key === "Enter") {
            const columns = [
                headerActiveName,
                mainSubject.selected.value,
                sheet.selected.value
            ];
            createHistoryEvent(displayName, "查詢", columns.join("/"));
            // dispatch search keyword
            dispatch({
                type: Act.DATA_SEARCH_KEYWORD,
                payload: inputValue
            });

            dispatch({
                type: Act.DATA_SEARCH_SEARCH_COLUMN,
                payload: filterCol || "all"
            });

            handleRefreshAll();
        }
    };

    useEffect(() => {
        setInputValue(keyword || "");
    }, [keyword]);

    useEffect(() => {
        if (!activeHeader[sheetName]) return;

        setOption(
            [{ key: "all", text: "全部", value: "all" }].concat(
                activeHeader[sheetName]
                    .filter(item => item?.infoIcon)
                    .map(h => {
                        if (specialHeaderRemove.includes(h.id)) return null;
                        return {
                            key: h.id,
                            text: h.label,
                            value: h.id
                        };
                    })
                    .filter(hd => hd)
            )
        );

        setInputValue("");
    }, [activeHeader, sheetName]);

    const handleChange = data => {
        setFilterCol(data.value);
    };

    const handleClear = () => {
        setInputValue("");
        setFilterCol("all");
        dispatch({
            type: Act.DATA_SEARCH_KEYWORD,
            payload: ""
        });

        dispatch({
            type: Act.DATA_SEARCH_SEARCH_COLUMN,
            payload: "all"
        });

        handleRefreshAll();
    };

    return (
        <Container className="SearchBarContainer">
            <Dropdown
                className="SearchBarContainer__filterColumn"
                value={filterCol}
                selection
                options={option}
                search
                placeholder="Filter..."
                onChange={(evt, data) => handleChange(data)}
            />
            <Input
                className="SearchBarContainer__keywordInput"
                type="text"
                placeholder="搜尋..."
                value={inputValue}
                disabled={!contentSearchPath}
                onChange={handleInputChange}
                onKeyPress={handleInputKeyPress}
                icon
            >
                <input />
                <Icon link name="delete" onClick={handleClear} />
            </Input>
            <Button
                className="SearchBarContainer__searchButton"
                animated="fade"
                disabled={!contentSearchPath}
                onClick={handleInputClick}
            >
                <Button.Content hidden>搜尋</Button.Content>
                <Button.Content visible>
                    <Icon name="search" />
                </Button.Content>
            </Button>
        </Container>
    );
};

export default SpecialSearchBar;

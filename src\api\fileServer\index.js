// file server API
const baseUrl = process.env.REACT_APP_FILE_SERVER_URL;
// 圖片 CRUD 指到 fs-root ("https://fs-root.daoyidh.com")
const fsRootUrl = process.env.REACT_APP_FILE_SERVER_URL;

const fileServerAPI = {
    uploadFile: `${fsRootUrl}/upload/[type]/batch`,
    uploadLogo: `${fsRootUrl}/upload/image/logo/vr-logo`,
    getFolderPattern: `${fsRootUrl}/folder/folderPattern/nmtl`,
    getFolderFiles: `${fsRootUrl}/folder/getFolderFiles/nmtl`,
    readImage: `${fsRootUrl}/read`,
    readUploadImage: `${fsRootUrl}/read/upload`,
    readLogoImage: `${fsRootUrl}/read/vr-logo`,
    deleteFile: `${fsRootUrl}/delete/[type]`,
    downloadDB: `${baseUrl}/database/download/[filename]`,
    uploadDB: `${baseUrl}/database/upload/[filename]`,
    // fullText(全文字詞分析)
    fullText: `${fsRootUrl}/fullText`,
    readFile: `${fsRootUrl}/static/file/upload`,
    bookCover: `${fsRootUrl}/bookCover`,
    historyLogList: `${fsRootUrl}/historyLog/frontend/historyLogList`,
    historyLog: `${fsRootUrl}/historyLog/export/historyLog`
};

const fileServerApiRoute = {
    readImage: "/read",
    readUploadImage: "/read/upload"
};

const HTTP_METHOD = {
    get: "get",
    put: "put",
    post: "post",
    delete: "delete"
};

// API method
const fileServerMethod = {
    // upload
    uploadFile: HTTP_METHOD.put,
    // get info
    getFolderPattern: HTTP_METHOD.get,
    getFolderFiles: HTTP_METHOD.get,
    deleteFile: HTTP_METHOD.post,
    // download
    downloadDB: HTTP_METHOD.get,
    uploadDB: HTTP_METHOD.post
};

// File upload path, which should be the same as in nmtl-file-server defined.
const FILE_UPLOAD_PATH = "imgupload";

// 網頁設定選項圖片上傳root路徑
const fsFrontEdit = "FrontEdit";

export {
    fileServerAPI,
    fileServerMethod,
    fileServerApiRoute,
    baseUrl,
    FILE_UPLOAD_PATH,
    fsFrontEdit
};

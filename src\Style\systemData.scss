*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

@mixin center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.SystemData{
  display: flex;
  height: 85vh;
  width: 100%;
  padding: 10px;
  justify-content: center;
  .leftArea{
    align-self: flex-start;
    margin-right: 5px;
    width: 15%;
  }
  .rightArea{
    width: 85%;
    border: 1px black solid;
    overflow-y: scroll;
  }
}

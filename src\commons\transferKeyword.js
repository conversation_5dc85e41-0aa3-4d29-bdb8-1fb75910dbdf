export const getTextAndLabel = word => {
    // word: '[吳吴][顯显][貴贵]'

    // removeFirstAndLast: '吳吴][顯显][貴贵'
    const removeFirstAndLast = word.slice(1, -1);
    // strToArr: ["吳吴","顯显","貴贵"]
    const strToArr = removeFirstAndLast.split("][");
    // splitToArr: [["吳","吴"],["顯","显"],["貴","贵"]]
    const splitToArr = strToArr.map(words => words.split(""));

    const combineWord = splitToArr.reduce((accumulator, currentValue) => {
        // 確保都是 array
        if (!Array.isArray(currentValue) || !Array.isArray(accumulator)) {
            return null;
        }

        const tmp = [];
        // [吳, 吴]
        accumulator.forEach(str => {
            // currentValue: [顯, 显]
            tmp.push(...currentValue.map(curValue => str + curValue));
        });

        // [吳顯, 吳显, 吴顯, 吴显]
        // accumulator = tmp;
        return tmp;
    });

    const starKeyword = combineWord.map(keyword => `*${keyword}*`);
    return { textArr: starKeyword, labelArr: combineWord };
};

export const combineSearchArgs = (textArr, labelArr) => {
    // text: (*賴和* OR *赖和*)
    // label: (賴和 OR 赖和)
    const textStr = `(${textArr.join(" OR ")})`;
    const labelStr = `(${labelArr.join(" OR ")})`;

    return { text: textStr, label: labelStr };
};

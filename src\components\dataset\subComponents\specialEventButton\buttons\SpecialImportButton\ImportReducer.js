import { combineReducers, createStore } from "redux";
import ImportAct from "./ImportAction";
import fileReducer from "../../../../../../reduxStore/file/fileReducer";

// config
import { stepConfig, stepStrConf } from "./config/stepConfig";

const initialState = {
    checkRes: false, // 檢查表格確認機制結果
    curStep: stepStrConf.step3,
    activeItem: stepConfig[stepStrConf.step3]
};

// Create a "reducer" function that determines what the new state
// should be when something happens in the app
function importReducer(state = initialState, action) {
    // Reducers usually look at the type of action that happened
    // to decide how to update the state
    switch (action.type) {
        case ImportAct.SET_CHECKRES:
            return { ...state, checkRes: action.payload };
        case ImportAct.SET_CURSTEP:
            return { ...state, curStep: action.payload };
        case ImportAct.INIT_CURSTEP:
            return { ...state, curStep: initialState.curStep };
        case ImportAct.SET_ACTIVEITEM:
            return { ...state, activeItem: action.payload };
        case ImportAct.INIT_ACTIVEITEM:
            return { ...state, activeItem: initialState.activeItem };
        default:
            // If the reducer doesn't care about this action type,
            // return the existing state unchanged
            return state;
    }
}

const cmbReducer = combineReducers({
    files: fileReducer, // 借用fileReducer，component: PickerContent會用到
    import: importReducer
});

const importStore = createStore(cmbReducer);

export default importStore;

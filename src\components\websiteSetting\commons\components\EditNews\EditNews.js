import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import PropTypes from "prop-types";

// semantic ui
import { Divider, Header } from "semantic-ui-react";

// scss
import "./EditNews.scss";

// components
import CustomButton from "./subComponents/CustomButton/CustomButton";
import CustomTable from "./subComponents/CustomTable/CustomTable";
import CustomPagination from "../../../../common/CustomPagination/CustomPagination";
import CustomModal from "./subComponents/CustomModal/CustomModal";
import NewsInfoArea from "./subComponents/NewsInfoArea/NewsInfoArea";

// config
import clsName from "./Utils/clsName";
import textConfig from "./Utils/textConfig";
import NewsAct from "./EditNewsAction";
import { setNewsInfo, setUpdateNewsInfo } from "./Utils/utils";
import getNewsTB from "./Utils/getNewsTB";

function EditNews({ websiteSubject }) {
    const newsDispatch = useDispatch();
    const { isEdited, tbBdData } = useSelector(state => state);

    // page control
    const [curPage, setCurPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [perPageNum, setPerPageNum] = useState(5);

    useEffect(() => {
        if (!websiteSubject || isEdited) return;
        getNewsTB(newsDispatch, websiteSubject);
    }, [websiteSubject, isEdited]);

    useEffect(() => {
        setTotalPages(Math.ceil(tbBdData.length / perPageNum));
    }, [tbBdData, perPageNum]);

    const handlePage = (evt, { activePage }) => {
        setCurPage(activePage);
    };

    const handleDDPage = (evt, data) => {
        setCurPage(data.value);
    };

    const handlePerPageNum = (evt, data) => {
        setCurPage(1);
        setPerPageNum(data.value);
    };

    //
    useEffect(() => {
        // 離開EditMode就把ModalCaller清空，避免下次進到EditMode有欄位記錄錯誤問題
        if (!isEdited) {
            newsDispatch({
                type: NewsAct.SET_MODALCALLER,
                payload: ""
            });

            // clear newsBriefInfo: & newsFullInfo & updateNewsInfo
            newsDispatch({
                type: NewsAct.SET_NEWSBRIEFINFO,
                payload: {}
            });
            setNewsInfo(newsDispatch, {});
            setUpdateNewsInfo(newsDispatch, {});
        }
    }, [isEdited]);

    return (
        <div className="EditNews">
            <div className="EditNews_header">
                <Header as="h1" style={{ margin: "0" }}>
                    {textConfig.Header_News}
                </Header>
            </div>
            <Divider />
            <div className="EditNews_main">
                {!isEdited ? (
                    <React.Fragment>
                        <div className="EditNews_main_header">
                            {/* 新增最新消息 */}
                            <CustomButton className={clsName.AddBtn} />
                        </div>
                        <div className="EditNews_main_table">
                            <CustomTable
                                tbBdData={tbBdData}
                                perPageNum={perPageNum}
                                currentPage={curPage}
                            />
                        </div>
                        <CustomPagination
                            currentPage={curPage}
                            totalPages={totalPages}
                            handlePage={handlePage}
                            handlePerPageNum={handlePerPageNum}
                            handleDDPage={handleDDPage}
                        />
                    </React.Fragment>
                ) : (
                    <div className="EditNews_footer">
                        <div className="EditNews_footer_header">
                            <CustomButton className={clsName.DeleteBtn} />
                            <CustomButton className={clsName.CancelBtn} />
                        </div>
                        {/* 顯示最新消息單筆資訊 */}
                        <NewsInfoArea className="EditNews_footer_NewsInfoArea" />
                    </div>
                )}
            </div>
            <Divider />
            <CustomModal />
        </div>
    );
}

EditNews.propTypes = {
    /** 網站最新消息 e.g. nmtl-web、tltc、... */
    websiteSubject: PropTypes.string
};

EditNews.defaultProps = {
    /** 網站最新消息 e.g. nmtl-web、tltc、... */
    websiteSubject: ""
};

export default EditNews;

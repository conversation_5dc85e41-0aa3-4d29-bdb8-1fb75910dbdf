import React, { useEffect, useState, useRef, useContext } from "react";

// scss
import "./CustomEditor.scss";

// plugins
import PropTypes from "prop-types";
import ReactQuill from "react-quill";
import htmlEditButton from "quill-html-edit-button";
import ResizeModule from "@botom/quill-resize-module";
import "react-quill/dist/quill.snow.css";
import { useDispatch, useSelector } from "react-redux";

//
import initToolbarOptions from "./customSetting";
import uploadImage from "./utils/uploadImage";
// import htmlToMarkDown from "../../../../../../../commons/htmlToMD";
import { convert2HtmlEntities } from "../../../../../../../commons/htmlEntities";
import { isEmpty } from "../../../../../../../commons";
import clsName from "../../Utils/clsName";
import { StoreContext } from "../../../../../../../store/StoreProvider";
import { setUpdateNewsInfo } from "../../Utils/utils";
import ImageFormat from "./utils/quillImage";

// 更換quill解析img tag的方式，保留"alt"、"style"... attribute
ReactQuill.Quill.register(ImageFormat, true);

// embedded ReactQuill 第三方套件
ReactQuill.Quill.register({
    "modules/htmlEditButton": htmlEditButton, // 顯示html樣式
    "modules/resize": ResizeModule // 調整圖片大小
});

function CustomEditor({ className, fusekiCol }) {
    // eslint-disable-next-line no-unused-vars
    const [globalState, _] = useContext(StoreContext);
    const { websiteSubject } = globalState.websiteSetting;

    const editorRef = useRef(null);
    const [toolBar, setToolBar] = useState({});
    const [content, setContent] = useState("");

    const newsDispatch = useDispatch();
    const { updateNewsInfo } = useSelector(state => state);

    useEffect(
        () => () => {
            // 離開 ReactQuill前，先initial toolBar，下次再重新render
            setToolBar(initToolbarOptions);
        },
        []
    );

    useEffect(() => {
        if (isEmpty(updateNewsInfo)) return;
        // toolbarOptions 加event handler
        const tmpToolBar = JSON.parse(JSON.stringify(initToolbarOptions));
        tmpToolBar.handlers = {
            image: () => uploadImage(editorRef, websiteSubject, "news")
        };
        setToolBar(tmpToolBar);

        // 設定內文
        let tmpValue = "";
        switch (className) {
            case clsName.ContentZH:
                tmpValue = updateNewsInfo?.contentZH || "";
                break;
            case clsName.ContentEN: {
                tmpValue = updateNewsInfo?.contentEN || "";
                break;
            }
            default:
                break;
        }

        if (tmpValue) {
            const pattern = /[\n]+/g;
            const subtitueNL = tmpValue.replace(pattern, "<br>");
            setContent(subtitueNL);
        }
    }, []);

    useEffect(() => {
        // 將目前content存到newsEvents
        const tmpNewsEvents = JSON.parse(JSON.stringify(updateNewsInfo));
        tmpNewsEvents[fusekiCol] = content;
        setUpdateNewsInfo(newsDispatch, tmpNewsEvents);
    }, [content]);
    return (
        <div className="CustomEditor">
            {/* ReactQuill 第一次render後就不再把custom handler的參數變化再帶到handler裡面，導致取值錯誤 */}
            {/* 暫時解法: toolBar 改成狀態控制，state change就重新render ReactQuill */}
            {!isEmpty(toolBar) && (
                <ReactQuill
                    id={`CustomEditorRQ-${className}`}
                    theme="snow"
                    value={convert2HtmlEntities(content)}
                    onChange={setContent}
                    modules={{
                        htmlEditButton: {
                            buttonHTML: "HTML",
                            prependSelector: "div#CustomEditorRQ"
                        },
                        toolbar: toolBar,
                        resize: {
                            locale: {},
                            toolbar: {
                                alignTools: false
                            }
                        }
                    }}
                    ref={editorRef}
                    style={{ minHeight: "200px" }}
                />
            )}
        </div>
    );
}

CustomEditor.propTypes = {
    /** column caller */
    className: PropTypes.string,
    /** fusekiCol real column Name */
    fusekiCol: PropTypes.string
};

CustomEditor.defaultProps = {
    /** column caller */
    className: "",
    /** fusekiCol real column Name */
    fusekiCol: ""
};

export default CustomEditor;

import React from "react";

// semantic ui
import { Container } from "semantic-ui-react";
import PropTypes from "prop-types";

// component
import PageBtn from "../CustomButton/PageBtn";
import fbConfig from "../../common/fbConfig";

const styleDescContainer = {
    display: "flex",
    justifyContent: "space-between"
};
function DescField({ data }) {
    return (
        <Container fluid style={styleDescContainer}>
            <Container fluid style={{ position: "relative" }}>
                <div
                    style={{
                        position: "absolute",
                        left: "0",
                        right: "0",
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        textOverflow: "ellipsis"
                    }}
                >
                    {data[fbConfig.desc]}
                </div>
            </Container>
            <PageBtn issueID={data[fbConfig.id]} />
        </Container>
    );
}

DescField.propTypes = {
    /** 該筆資料資訊 */
    data: PropTypes.shape({
        [fbConfig.id]: PropTypes.string,
        [fbConfig.email]: PropTypes.string,
        [fbConfig.desc]: PropTypes.string,
        [fbConfig.status]: PropTypes.string,
        [fbConfig.response]: PropTypes.string,
        [fbConfig.staff]: PropTypes.string,
        [fbConfig.name]: PropTypes.string,
        [fbConfig.file]: PropTypes.arrayOf(PropTypes.string),
        [fbConfig.history]: PropTypes.arrayOf(
            PropTypes.shape({
                time: PropTypes.number,
                status: PropTypes.string
            })
        )
    })
};

DescField.defaultProps = {
    /** 該筆資料資訊 */
    data: {}
};

export default DescField;

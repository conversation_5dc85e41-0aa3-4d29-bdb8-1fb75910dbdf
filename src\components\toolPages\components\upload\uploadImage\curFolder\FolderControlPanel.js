import React, { useContext } from "react";
import { useSelector, useDispatch } from "react-redux";
import { But<PERSON> } from "semantic-ui-react";
import axios from "axios";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";
import FileAct from "../../../../../../reduxStore/file/fileAction";

// API
import {
    fileServerAPI,
    fileServerMethod,
    fileServerApiRoute
} from "../../../../../../api/fileServer";
import { createHistoryEvent } from "../../../../../downloadData/components/history/common/common";
import UploadConfig from "../../uploadConfig";

const FolderControlPanel = () => {
    const dispatchRedux = useDispatch();
    const {
        files: {
            curFolderFiles,
            curFolderFilesUrl,
            currentFolder,
            headerActiveName,
            toolPagesActiveName
        }
    } = useSelector(state => state);

    const [state] = useContext(StoreContext);
    const { displayName } = state.user;
    const columns = [headerActiveName, toolPagesActiveName];
    const { role } = state.user;

    const handleDelBtn = () => {
        if (curFolderFiles.checked.length === 0) {
            return dispatchRedux({
                type: FileAct.PICKER_CUR_FOLDER_MESSAGE,
                payload: {
                    type: "warning",
                    title: "尚未選取檔案",
                    text: ""
                }
            });
        }

        const middlePath = fileServerApiRoute.readUploadImage;
        let actionType = "";

        const images = curFolderFiles.checked
            .map(f => {
                const { value, actiontype } = f;
                if (actiontype === UploadConfig.image) {
                    actionType = UploadConfig.image;
                    const urlSplit = value.split(middlePath);
                    // 取得 /read/upload 後面的字串,並去掉 420x420_(或 200x200_等字串)
                    return (
                        urlSplit.length > 1 &&
                        urlSplit[1].replace(/[0-9]{3}[xX×╳][0-9]{3}_/, "")
                    );
                }
                if (actiontype === UploadConfig.file) {
                    actionType = UploadConfig.file;
                    return value.slice(UploadConfig.FilePrePath.length);
                }
                return false;
            })
            .filter(url => url !== false);

        // 去掉重覆的
        const uniImages = [...new Set(images)];

        return axios({
            method: fileServerMethod.deleteFile,
            url: fileServerAPI.deleteFile.replace("[type]", actionType),
            headers: {
                "Access-Control-Allow-Origin": "*"
            },
            data: {
                files: uniImages
            }
        })
            .then(res => {
                if (res.status === 200 && res.data.status === "success") {
                    const filesRemain = curFolderFilesUrl.filter(cff => {
                        const { type, originalUrl } = cff;
                        let furl;
                        if (type === UploadConfig.image) {
                            // eslint-disable-next-line prefer-destructuring
                            furl = originalUrl.split(middlePath)[1];
                        } else if (type === UploadConfig.file) {
                            furl = originalUrl.slice(
                                UploadConfig.FilePrePath.length
                            );
                        }
                        return uniImages.indexOf(furl) < 0;
                    });

                    dispatchRedux({
                        type: FileAct.FOLDER_FILES_URL,
                        payload: filesRemain
                    });
                    // curFolderFiles.checked 清空
                    dispatchRedux({
                        type: FileAct.CLEAR_CUR_FOLDER_FILES_STATUS
                    });
                    createHistoryEvent(
                        displayName,
                        "刪除",
                        `${columns.join("/")}${currentFolder.path_ch}`
                    );
                    return dispatchRedux({
                        type: FileAct.PICKER_CUR_FOLDER_MESSAGE,
                        payload: {
                            type: "success",
                            title: "已刪除檔案",
                            text: ""
                        }
                    });
                }
                createHistoryEvent(
                    displayName,
                    "刪除失敗",
                    `${columns.join("/")}${currentFolder.path_ch}`
                );
                return dispatchRedux({
                    type: FileAct.PICKER_CUR_FOLDER_MESSAGE,
                    payload: {
                        type: "error",
                        title: "處理失敗，請稍後再試",
                        text: ""
                    }
                });
            })
            .catch(err => {
                dispatchRedux({
                    type: FileAct.PICKER_CUR_FOLDER_MESSAGE,
                    payload: {
                        type: "error",
                        title: "處理失敗，請稍後再試",
                        text: `error: ${err.message}`
                    }
                });
                createHistoryEvent(
                    displayName,
                    "刪除失敗",
                    `${columns.join("/")}${currentFolder.path_ch}`
                );
            });
    };

    return (
        <div className="control-panel-container">
            <div className="control-panel">
                <Button
                    className="control-panel-btn"
                    color="orange"
                    size="small"
                    disabled={
                        (curFolderFiles &&
                            curFolderFiles.checked &&
                            curFolderFiles.checked.length === 0) ||
                        role === "reader"
                    }
                    onClick={handleDelBtn}
                >
                    刪除勾選檔案
                </Button>
            </div>
        </div>
    );
};

export default FolderControlPanel;

import { getTradSimpleWordStr } from "twchar";
import escapeRegExpKeyword from "./convertSpecial<PERSON>har";
// convertSpecialChar
const LIMIT_SIZE = 5;
const settingSearchKeyword = keyword => {
    // 過濾特殊符號
    const words = escapeRegExpKeyword(keyword);

    // FIXME:: 暫定字串長度大於 5 時，只做前 5 個字的繁簡轉換
    if (keyword.length > LIMIT_SIZE) {
        const startWord = words.slice(0, LIMIT_SIZE);
        const endWord = words
            .slice(LIMIT_SIZE)
            .split("")
            .map(word => `[${word}]`) // 配合繁簡轉換需求
            .join("");

        // 繁簡轉換
        return `${getTradSimpleWordStr(startWord)}${endWord}`;
    }

    // 繁簡轉換
    return getTradSimpleWordStr(words);
};

export default settingSearchKeyword;

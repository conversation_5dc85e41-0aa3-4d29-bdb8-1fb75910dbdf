const uploadConfig = {};

uploadConfig.ImageFormName = "image";
uploadConfig.DbFormName = "db";

// 必須配合 file-server 的命名
const fileServerApi = {
    NONE: "",
    IMAGE: "image",
    FILE: "docs"
};

uploadConfig.none = fileServerApi.NONE;
uploadConfig.image = fileServerApi.IMAGE;
uploadConfig.file = fileServerApi.FILE;

// 「資料夾列表」與「上傳預覽」為不同的外觀與操作。
const DropdownTreeSelect = {
    LIST_ALL: "listAll",
    PREVIEW: "preview"
};

uploadConfig.DropDownListAll = DropdownTreeSelect.LIST_ALL;
uploadConfig.DropDownPreview = DropdownTreeSelect.PREVIEW;

// 抓取檔案列表與圖片列表
const ApiGetList = {
    FILES: "fileFolderPattern",
    IMAGES: "imageFolderPattern"
};

uploadConfig.ApiGetFiles = ApiGetList.FILES;
uploadConfig.ApiGetImages = ApiGetList.IMAGES;

// file prefix path in file server
uploadConfig.FilePrePath = "rf/upload/";

// image size
uploadConfig.ImageSize = "420x420";

export default uploadConfig;

import React, { useContext, useEffect } from "react";

// firebase
import firebase from "firebase/app";
import "firebase/auth";
import { Redirect } from "react-router";

// store
import { StoreContext } from "../../store/StoreProvider";
import act from "../../store/actions";
import { createHistoryEvent } from "../downloadData/components/history/common/common";

const SignOut = () => {
    const isLogin = JSON.parse(localStorage.getItem("isLogin"));

    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { displayName } = state.user;

    useEffect(() => {
        if (isLogin) {
            // SignOut firebase
            firebase
                .auth()
                .signOut()
                .then(() => {
                    // Sign-out successful.
                })
                .catch(error => {
                    // An error happened.
                    console.log(error);
                });
            // clean user data
            dispatch({ type: act.FIREBASE_LOGOUT_USER });
            // clean localStorage
            localStorage.removeItem("isLogin");
            // 登出成功
            createHistoryEvent(displayName, "登出");
        } else {
            // console.log('do nothing');
        }
    });

    return <Redirect to="/SignIn" />;
};

export default SignOut;

import React, { use<PERSON><PERSON>back, use<PERSON>ontext, useEffect, useState } from "react";

// ui
import {
    Table,
    Input,
    Divider,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>
} from "semantic-ui-react";

// api
import { getSheetHeader } from "../../../../../../../api/firebase/cloudFirestore";

// store
import { StoreContext } from "../../../../../../../store/StoreProvider";
import Act from "../../../../../../../store/actions";

// common
import { isEmpty } from "../../../../../../../commons";

// api
import Api from "../../../../../../../api/nmtl/Api";
import { getApiByField } from "../../../../../../../api/nmtl/ApiField";
import { createNmtlData, readNmtlData } from "../../../../../../../api/nmtl";
import getSingleByApi from "../../../../../../../api/nmtl/ApiSingle";
import getKeyBySingle from "../../../../../../../api/nmtl/ApiKey";

const CustomImportTab = () => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    // get dataset(mainSubject) and sheet
    const { mainSubject, sheet, uploaded } = state.data;
    const { key: sheetName, contentWritePath } = sheet.selected;
    const { dataset: datasetLabel } = mainSubject.selected;
    const { rawData } = uploaded;
    const [rowId, setRowId] = useState(0);
    const [sheetHeader, setSheetHeader] = useState(undefined);
    const [loading, setLoading] = useState(false);

    const showLimit = 10;

    const handleGetSheetTable = useCallback(async () => {
        const header = await getSheetHeader(sheetName);
        // set sheetHeader
        setSheetHeader(header);
    }, [sheetName]);

    const handleChangeRow = (event, { value }) => {
        setRowId(value);
        // console.log(value);
    };

    // get full api url
    // e.g. http://localhost:3000/basicInfo/1.0
    // useCallback 保存方法，避免 useEffect 喧染時被產生新的 element
    const getFullUrl = () => {
        if (contentWritePath) {
            // combine url and parameter
            return `${Api.getBaseUrl}/${contentWritePath}`;
        }
        return undefined;
    };

    const handleUploadData = async () => {
        // console.log("I am handleUploadData");
        setLoading(true);
        if (
            !isEmpty(datasetLabel) &&
            !isEmpty(sheetHeader) &&
            !isEmpty(rawData)
        ) {
            // headerlookupTable
            const headerLookupTable = {};
            sheetHeader &&
                sheetHeader.forEach(row => {
                    headerLookupTable[row.label] = row.id;
                });

            // 準備內容轉換表格，e.g. TestUser => PER123
            const headerIds = sheetHeader && sheetHeader.map(item => item.id);
            // 取得相關欄位的資料
            const apiList = headerIds
                .map(field => ({
                    field,
                    apiName: getApiByField(field)
                }))
                .filter(
                    item =>
                        // The getApiByField will return undefined when it not found from config(api:nmtl:ApiField.js)
                        item.apiName !== undefined
                );
            // unique apiName to avoids repeated queries
            const uniqueApiNameList = [
                ...new Set(apiList.map(item => item.apiName))
            ];
            // query all uniqueApiNameList
            const promises = uniqueApiNameList.map(apiName =>
                readNmtlData(Api[apiName])
            );
            // get results from promises
            const results = await Promise.allSettled(promises).then(res => res);
            // create cache result dictionary
            const apiResults = {};
            // To map the api and result in a dictionary as lookup table
            results.forEach((res, idx) => {
                const { status, value } = res;
                // if Promise.allSettled has succeeded or failed, it will return 'fulfilled' or 'rejected'
                if (status === "fulfilled" && value && !isEmpty(value.data)) {
                    apiResults[uniqueApiNameList[idx]] = value.data;
                }
            });
            const contentLookupTable = {};
            // callback result to apiList by apiResults
            apiList.forEach(item => {
                const { apiName } = item;
                // get result from apiResults(lookup table)
                const dataList = apiResults[apiName];
                if (!isEmpty(dataList)) {
                    dataList.forEach(dataItem => {
                        contentLookupTable[dataItem.label] = dataItem.id;
                    });
                }
            });

            // 將資料的 header 轉為 headerId，如果沒有匹配到的將會忽略
            const reversedHeaderData =
                rawData &&
                rawData.map(row => {
                    const reversedRow = {};
                    Object.keys(row).forEach(key => {
                        const newKey = headerLookupTable[key];
                        reversedRow[newKey] = row[key];
                    });
                    return reversedRow;
                });
            // console.log("reversedHeaderData: ", reversedHeaderData);

            // 檢查內容資料是否有未知的資料，如有則記錄下來
            const unkItem = [];
            reversedHeaderData &&
                reversedHeaderData.map(row => {
                    const reversedRow = {};
                    Object.keys(row).forEach(key => {
                        // 只針對有 ID的 header 做轉換
                        if (!isEmpty(getApiByField(key))) {
                            const rowCell = row[key].toString();
                            if (!isEmpty(rowCell)) {
                                if (isEmpty(contentLookupTable[rowCell])) {
                                    unkItem.push({
                                        key,
                                        value: row[key]
                                    });
                                }
                            }
                        }
                    });
                    return reversedRow;
                });
            // console.log("unkItem: ", unkItem);

            // 新增新的項目
            const unkResults = [];
            for (const item of unkItem) {
                const fieldKey = item.key;
                const fieldValue = item.value;
                const apiUrl = Api[getSingleByApi(getApiByField(fieldKey))];
                const itemKey = getKeyBySingle(
                    getSingleByApi(getApiByField(fieldKey))
                );
                if (apiUrl && itemKey) {
                    const newItem = {
                        graph: datasetLabel,
                        [itemKey]: fieldValue
                    };
                    unkResults.push(
                        await createNmtlData(
                            user,
                            apiUrl,
                            datasetLabel,
                            sheetName,
                            newItem
                        )
                    );
                }
            }
            // console.log(unkResults);

            /*
            let successedResult = 0;
            const unkItemResults = await Promise.allSettled(unkPromises).then(res => res);
            unkItemResults.forEach(res => {
                const { status, value } = res;
                // if Promise.allSettled has succeeded or failed, it will return 'fulfilled' or 'rejected'
                if (status === "fulfilled" && value) {
                    successedResult++;
                }
            });
            */
            // 新增資料
            if (unkResults.length === unkItem.length) {
                // 重新抓取資料
                const reloadPromises = uniqueApiNameList.map(apiName =>
                    readNmtlData(Api[apiName])
                );
                // get results from promises
                const reloadResults = await Promise.allSettled(
                    reloadPromises
                ).then(res => res);
                // create cache result dictionary
                const reloadApiResults = {};
                // To map the api and result in a dictionary as lookup table
                reloadResults.forEach((res, idx) => {
                    const { status, value } = res;
                    // if Promise.allSettled has succeeded or failed, it will return 'fulfilled' or 'rejected'
                    if (
                        status === "fulfilled" &&
                        value &&
                        !isEmpty(value.data)
                    ) {
                        reloadApiResults[uniqueApiNameList[idx]] = value.data;
                    }
                });
                const reloadedContentLookupTable = {};
                // callback result to apiList by apiResults
                apiList.forEach(item => {
                    const { apiName } = item;
                    // get result from apiResults(lookup table)
                    const dataList = reloadApiResults[apiName];
                    if (!isEmpty(dataList)) {
                        dataList.forEach(dataItem => {
                            reloadedContentLookupTable[dataItem.label] =
                                dataItem.id;
                        });
                    }
                });

                // 轉換相關欄位的內容資料
                const finalReversedData =
                    reversedHeaderData &&
                    reversedHeaderData.map(row => {
                        const reversedRow = {};
                        Object.keys(row).forEach(key => {
                            const rowCell = row[key];
                            reversedRow[key] =
                                reloadedContentLookupTable[rowCell] || rowCell;
                        });
                        return reversedRow;
                    });

                // 最終上傳完整的資料
                const apiUrl = getFullUrl();
                const finalResults = [];
                for (const data of finalReversedData) {
                    // add target dataset
                    data.graph = datasetLabel;
                    finalResults.push(
                        await createNmtlData(
                            user,
                            apiUrl,
                            datasetLabel,
                            sheetName,
                            data
                        )
                    );
                }
                // console.log(finalResults);
                // console.log(finalResults.length);
                // count
                let successCount = 0;
                let errorCount = 0;
                finalResults.forEach(finalResult => {
                    // console.log(finalResult);
                    if (finalResult === "OK") {
                        successCount += 1;
                    } else {
                        errorCount += 1;
                    }
                });
                const message = {
                    title: "Batch Upload",
                    success: successCount,
                    error: errorCount,
                    changeTabSignal: 1,
                    renderSignal: `batch-upload-${new Date().getTime()}`
                };
                // alert message
                dispatch({
                    type: Act.DATA_MESSAGE,
                    payload: message
                });
                // record result
                dispatch({
                    type: Act.DATA_CONTENT_UPLOADED_RECORD,
                    payload: message
                });
                // console.log(finalReversedData);
                setLoading(false);
            } else {
                // console.log("上傳檔案:新增項目:失敗");
                setLoading(false);
            }
        }
    };

    // init
    useEffect(() => {
        dispatch({ type: Act.DATA_CONTENT_UPLOADED_CLEAN });
    }, []);

    // get sheet
    useEffect(() => {
        handleGetSheetTable();
    }, [handleGetSheetTable]);

    // 首行永遠固定於左
    const fixFirstChild = {
        position: "sticky",
        left: "0",
        // zIndex: "1",
        backgroundColor: "#f9fafb",
        borderRight: "1px solid rgba(34,36,38,.1)",
        borderLeft: "1px solid rgba(34,36,38,.1)"
    };

    // custom style
    const customStyle = {
        overflowY: "hidden",
        minHeight: "150px"
    };

    const customInputStyle = {
        maxWidth: "50px",
        textAlign: "center"
    };

    return (
        <React.Fragment>
            <Dimmer active={loading} inverted>
                <Loader size="large" active={loading}>
                    匯入中....
                </Loader>
            </Dimmer>
            目前顯示第&nbsp;&nbsp;&nbsp;&nbsp;
            <Input
                size="mini"
                value={rowId}
                style={customInputStyle}
                onChange={handleChangeRow}
            />
            &nbsp;&nbsp;&nbsp;&nbsp;行資料至第&nbsp;&nbsp;&nbsp;
            {Number(rowId) + Number(showLimit)}
            &nbsp;&nbsp;&nbsp;行資料，預設最多顯示{showLimit}筆資料。
            <Divider />
            <div style={customStyle}>
                <Table celled size="small">
                    <Table.Header>
                        <Table.Row>
                            {sheetHeader &&
                                sheetHeader.map((row, headerIdx) => (
                                    <Table.HeaderCell
                                        key={`table-header-${headerIdx}`}
                                        singleLine
                                        style={
                                            headerIdx === 0 ? fixFirstChild : {}
                                        }
                                    >
                                        {row.label}
                                    </Table.HeaderCell>
                                ))}
                        </Table.Row>
                    </Table.Header>

                    <Table.Body>
                        {rawData &&
                            rawData
                                .slice(rowId, Number(rowId) + Number(showLimit))
                                .map((row, rowIdx) => (
                                    // eslint-disable-next-line react/no-array-index-key
                                    <Table.Row key={rowIdx}>
                                        {sheetHeader &&
                                            sheetHeader.map((shRow, shIdx) => (
                                                <Table.Cell
                                                    key={`table-cell-${rowIdx}-${shIdx}`}
                                                    singleLine
                                                    style={
                                                        shIdx === 0
                                                            ? fixFirstChild
                                                            : {}
                                                    }
                                                >
                                                    {row[shRow.label]}
                                                </Table.Cell>
                                            ))}
                                    </Table.Row>
                                ))}
                    </Table.Body>
                </Table>
            </div>
            <Divider />
            <Button
                disabled={isEmpty(rawData)}
                color="blue"
                onClick={handleUploadData}
            >
                確認
            </Button>
        </React.Fragment>
    );
};

export default CustomImportTab;

import React, { useContext } from "react";
import { Form, Checkbox } from "semantic-ui-react";

// general
import { StoreContext } from "../../../store/StoreProvider";
import Act from "../../../store/actions";

// configs
import NMTL_WEB_CONFIGS from "../nmtl-web/config";
const { IMAGE, TEXT } = NMTL_WEB_CONFIGS.MENU_ACTIVE_ITEM;

function WaterMarkSelect() {
  const [state, dispatch] = useContext(StoreContext);
  const {
    fusekiData: {
      vrMuseum: {
        logo: { tempData },
      },
    },
  } = state.websiteSetting;
  return (
    <div>
      <Form style={{ display: "flex", alignItems: "center" }}>
        <Checkbox
          radio
          label="圖片"
          checked={tempData.type === IMAGE}
          onClick={() =>
            dispatch({
              type: Act.SET_VRMUSEUM_LOGO_TEMP_DATA,
              payload: { ...tempData, type: IMAGE },
            })
          }
          style={{ marginRight: "3px" }}
        />
        <Checkbox
          radio
          label="文字"
          checked={tempData.type === TEXT}
          onClick={() =>
            dispatch({
              type: Act.SET_VRMUSEUM_LOGO_TEMP_DATA,
              payload: { ...tempData, type: TEXT },
            })
          }
        />
      </Form>
    </div>
  );
}

export default WaterMarkSelect;

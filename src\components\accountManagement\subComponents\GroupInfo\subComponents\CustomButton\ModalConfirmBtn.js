import React from "react";
import { <PERSON><PERSON> } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import { TypeName } from "../../../Utils/compoConfig";
import accMngAct from "../../../../../../reduxStore/accManage/accManageAction";
import { GROUPINFO } from "../../../../../../config/config-frontendSettings";
import { deleteDocument } from "../../../../../../api/firebase/storage";

function ModalConfirmBtn() {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { modalCaller, tableSelectPool, allGroupData, groupData } = state;

    const clickYes = () => {
        switch (modalCaller) {
            case TypeName.GroupDelete:
                {
                    let tmpAllGPData = JSON.parse(JSON.stringify(allGroupData));
                    tmpAllGPData = tmpAllGPData.filter(
                        el => tableSelectPool.groups.indexOf(el.id) === -1
                    );
                    dispatch({
                        type: accMngAct.SET_ALLGROUPDATA,
                        payload: tmpAllGPData
                    });
                    dispatch({
                        type: accMngAct.SET_TABLESELECTPOOL,
                        payload: { ...tableSelectPool, groups: [] }
                    });
                    // firebase刪除group資訊
                    tableSelectPool.groups.forEach(id => {
                        deleteDocument(GROUPINFO, id);
                    });
                }
                break;
            case TypeName.GroupMemberDelete:
                {
                    const tmpGPData = JSON.parse(JSON.stringify(groupData));
                    tmpGPData.members = tmpGPData.members.filter(
                        el => tableSelectPool.users.indexOf(el.uid) === -1
                    );
                    dispatch({
                        type: accMngAct.SET_GROUPDATA,
                        payload: tmpGPData
                    });
                    dispatch({
                        type: accMngAct.SET_TABLESELECTPOOL,
                        payload: { ...tableSelectPool, users: [] }
                    });
                }
                break;
            default:
                break;
        }
        dispatch({
            type: accMngAct.SET_MODALCALLER,
            payload: ""
        });
    };

    return (
        <Button onClick={clickYes} primary>
            確認
        </Button>
    );
}

export default ModalConfirmBtn;

import React, { useContext, useEffect } from 'react';

// css
import './websiteSetting.scss';

// components
import MenuBar from './components/MenuBar';
import SubjectDropdown from './commons/components/SubjectDropdown';
import EmptyView from '../../commons/components/empty/EmptyView';

// utils
import { getSingleLayerCollection, getMainSubject } from '../../api/firebase/cloudFirestore';
import { StoreContext } from '../../store/StoreProvider';
import Act from '../../store/actions';
import { isEmpty } from '../../commons';
import menuMapping from './commons/menuMapping';

function initialWebsiteSetting(dispatch) {
    dispatch({
        type: Act.SET_SELECTOPTION,
        payload: null,
    });
    dispatch({
        type: Act.SET_OPENMODAL,
        payload: false,
    });
    dispatch({
        type: Act.SET_MODALMESSAGE,
        payload: '',
    });
    dispatch({
        type: Act.SET_ISEDITEDDISABLE,
        payload: false,
    });
    dispatch({
        type: Act.SET_LISTDATA,
        payload: {},
    });
    dispatch({
        type: Act.SET_MSLISTDATA,
        payload: [],
    });
}

function WebsiteSetting() {
    const [state, dispatch] = useContext(StoreContext);
    const { menuActiveItem, originData, subMenu, websiteSubject } = state.websiteSetting;

    useEffect(() => {
        initialWebsiteSetting(dispatch);
        getSingleLayerCollection('frontend-settings')
            .then((result) => {
                dispatch({
                    type: Act.SET_ORIGINDATA,
                    payload: result,
                });
            })
            .catch((error) => {
                console.log(error);
            });
        getMainSubject()
            .then((result) => {
                dispatch({
                    type: Act.SET_MSLISTDATA,
                    payload: result,
                });
            })
            .catch((error) => {
                console.log(error);
            });
    }, []);

    useEffect(() => {
        dispatch({
            type: Act.SET_UPDATEDDATA,
            payload: originData,
        });
    }, [originData]);

    useEffect(() => {
        if (!websiteSubject) return;
        const findMenu = menuMapping.find((el) => el.subject === websiteSubject);
        if (!findMenu) {
            dispatch({
                type: Act.SET_WEBSETMENU,
                payload: [],
            });
            return;
        }

        dispatch({
            type: Act.SET_WEBSETMENU,
            payload: findMenu.menuItem,
        });
        dispatch({
            type: Act.SET_MENUACTIVEITEM,
            payload: findMenu.menuItem[0],
        });
    }, [websiteSubject]);

    return (
        <div className="WebsiteSetting">
            <div className="WebsiteSetting__topArea">
                <SubjectDropdown />
            </div>
            <div
                className="contentArea"
                style={{
                    maxHeight: `${menuActiveItem.key === 'PeakMonos' || menuActiveItem.key === 'Villages'
                            ? 'initial'
                            : '100%'
                        }`,
                }}
            >
                <EmptyView data={subMenu}>
                    <div className="leftArea">
                        <MenuBar />
                    </div>
                    <div className="rightArea">
                        {!isEmpty(menuActiveItem) && <menuActiveItem.component />}
                    </div>
                </EmptyView>
            </div>
        </div>
    );
}

export default WebsiteSetting;

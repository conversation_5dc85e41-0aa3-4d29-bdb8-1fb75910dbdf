import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Table } from "semantic-ui-react";
import { isEmpty } from "../../../../../../../commons";

function GroupDelCnt() {
    const state = useSelector(tmpState => tmpState.accMng);
    const { tableSelectPool, allGroupData } = state;
    const [delGroups, setDelGroups] = useState([]);
    const groupTableHeader = ["群組名稱", "群組人數"];

    useEffect(() => {
        const findGroups = allGroupData.filter(
            el => tableSelectPool.groups.indexOf(el.id) !== -1
        );
        setDelGroups(findGroups);
    }, []);

    return (
        <Table celled structured size="small">
            <Table.Header>
                <Table.Row>
                    {groupTableHeader.map(content => (
                        <Table.HeaderCell key={content}>
                            {content}
                        </Table.HeaderCell>
                    ))}
                </Table.Row>
            </Table.Header>
            <Table.Body>
                {!isEmpty(delGroups) &&
                    delGroups.map(el => (
                        <Table.Row key={el.id}>
                            <Table.Cell>{el.name}</Table.Cell>
                            <Table.Cell>{el.members.length}</Table.Cell>
                        </Table.Row>
                    ))}
            </Table.Body>
        </Table>
    );
}

export default GroupDelCnt;

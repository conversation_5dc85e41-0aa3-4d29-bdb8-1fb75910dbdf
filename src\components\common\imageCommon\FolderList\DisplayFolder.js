import React, { useContext } from "react";
import { List } from "semantic-ui-react";
import PropTypes from "prop-types";
import { useSelector } from "react-redux";
import { uuidv4 } from "../../../../commons/utility";
import { filterDataSet } from "../../../../commons/filterGroup";
import { StoreContext } from "../../../../store/StoreProvider";
import { isEmpty } from "../../../../commons";

// 遞迴把階層結構組件組裝起來
const RecursiveListItem = ({ data, onClickFolder, childrenKey, onToggle }) => {
    // const [collapse, setCollapse] = useState(true);
    // const collapseRef = React.useRef(true);
    const handleToggle = (e, props) => {
        // 阻止預設事件
        e.preventDefault();
        // 阻止捕捉事件,避免子層事件傳遞至父層
        e.stopPropagation();

        if (typeof onToggle === "function") {
            onToggle(e, data);
        }
    };

    // 是否展開子層資料夾
    const showChildren =
        data.collapse != null
            ? data.collapse &&
              Array.isArray(data.folders) &&
              data.folders.length > 0
            : Array.isArray(data.folders) && data.folders.length > 0;

    return (
        <List.Item
            as={data.folders && data.folders.length === 0 ? "a" : "div"}
            className="folder-list-item-1st"
            path={data.path}
            pathch={data.path_ch}
            foldername={data.folderName}
            foldernamech={data.folderName_ch}
            type={data.type}
            style={{
                marginTop: "4px",
                marginBottom: "4px"
            }}
            onClick={(e, props) => {
                // 阻止預設事件
                e.preventDefault();
                // 阻止捕捉事件,避免子層事件傳遞至父層
                e.stopPropagation();
                // file 沒有 folders 變數
                // 允許第一層及第N層都可以觸發 click 事件
                if (typeof onClickFolder === "function") {
                    onClickFolder(e, data);
                }
            }}
        >
            <List.Icon
                name="folder"
                style={{
                    color: data.active ? "#2185d0" : "#b8b8b8"
                }}
            />
            <List.Content>
                <List.Header
                    className="list-item-content-header"
                    style={{
                        color: data.active ? "#2185d0" : "rgba(0,0,0,.87)"
                    }}
                >
                    {data.folderName_ch}
                </List.Header>
                {showChildren && (
                    <List.List>
                        {data.folders.map(dt => (
                            <RecursiveListItem
                                key={uuidv4()}
                                data={dt}
                                onClickFolder={onClickFolder}
                                childrenKey={childrenKey}
                            />
                        ))}
                    </List.List>
                )}
            </List.Content>
            {data.collapse != null &&
                Array.isArray(data.folders) &&
                data.folders.length > 0 && (
                <List.Icon
                    name={showChildren ? "angle up" : "angle down"}
                    onClick={handleToggle}
                />
            )}
        </List.Item>
    );
};

RecursiveListItem.defaultProps = {
    data: {},
    onClickFolder: () => null,
    onToggle: () => null,
    childrenKey: "folders"
};

RecursiveListItem.propTypes = {
    data: PropTypes.objectOf(PropTypes.any),
    onClickFolder: PropTypes.func,
    onToggle: PropTypes.func,
    childrenKey: PropTypes.string
};

const isObject = data =>
    typeof data === "object" && !Array.isArray(data) && data !== null;

const safeList = list =>
    (Array.isArray(list) && list) || (isObject(list) && [list]) || [];

const DisplayFolder = props => {
    const { folderPattern, onClickFolder, childrenKey, onToggle } = props;

    const {
        files: { firstLayerFileName }
    } = useSelector(state => state);

    const [state] = useContext(StoreContext);
    const { groupInfo } = state.data;

    const safeFolderList = safeList(folderPattern);
    // folderPattern 結構1: [{ [childrenKey]: [] }]
    // folderPattern 結構2: { [childrenKey]: [] }
    return (
        <List relaxed size="medium" className="folder-list-root">
            {safeFolderList[0] &&
                Array.isArray(safeFolderList[0][childrenKey]) &&
                safeFolderList[0][childrenKey].length > 0 &&
                safeFolderList[0][childrenKey]
                    .filter(ms => {
                        if (!isEmpty(firstLayerFileName)) return true;
                        return filterDataSet(ms, groupInfo);
                    })
                    .map(ms => {
                        if (isEmpty(firstLayerFileName)) return ms;
                        const filterSubFolder = ms.folders.filter(msObj =>
                            filterDataSet(msObj, groupInfo)
                        );
                        return { ...ms, folders: filterSubFolder };
                    })
                    .map(f => (
                        <RecursiveListItem
                            key={uuidv4()}
                            data={f}
                            onClickFolder={onClickFolder}
                            childrenKey={childrenKey}
                            onToggle={onToggle}
                        />
                    ))}
        </List>
    );
};

DisplayFolder.defaultProps = {
    folderPattern: [],
    onClickFolder: () => null,
    childrenKey: "folders"
};

DisplayFolder.propTypes = {
    folderPattern: PropTypes.oneOfType([
        PropTypes.arrayOf(PropTypes.any),
        PropTypes.objectOf(PropTypes.any)
    ]),
    onClickFolder: PropTypes.func,
    childrenKey: PropTypes.string
};

export default DisplayFolder;

import textMsg from "../../../textMsg";
import Api from "../../../../../../api/nmtl/Api";
import { getClassType, getImgKeyData, formatURLObj } from "./saveDataUtils";
import initColumnDef, { imageColDef } from "./initColumnDef";
import { convertSimpleEntrySD } from "../../../../../common/sheetCrud/sheetCrudHelper";
import {
    createNmtlData,
    deleteNmtlData,
    updateNmtlData
} from "../../../../../../api/nmtl";
import { isEmpty } from "../../../../../../commons";

const saveURLEvent = (originObj, updateObj, subject, user) =>
    new Promise(async (resolve, reject) => {
        const { sheetName, graph } = textMsg;
        const apiStr = Api.getGeneric;
        const newsId = updateObj[initColumnDef.newsIdStr];
        const newsClassType = getClassType(newsId);
        if (!newsClassType) {
            resolve("No classType");
        }

        const oriIds = isEmpty(originObj)
            ? []
            : originObj[initColumnDef.hasURL].map(el => el.urlId);
        const updIds = updateObj?.[initColumnDef.hasURL].map(el => el.urlId);
        const keyData = getImgKeyData(imageColDef.urlId);

        updateObj[initColumnDef.hasURL].forEach(dstObj => {
            const dstURLID = dstObj[imageColDef.urlId];
            if (!dstURLID) {
                // 在updateObj發現沒有帶urlId，create
                const entryCol = {
                    graph,
                    classType: "URLEvent",
                    ...keyData
                };

                const { entryDst } = convertSimpleEntrySD(
                    formatURLObj({}, subject),
                    formatURLObj(dstObj, subject),
                    entryCol
                );

                // create hasNews link, e.g. nmtl:NEWS79 nmtl:hasURL nmtl:URLEVENT2701.
                const entry = {
                    classType: newsClassType,
                    graph,
                    srcId: newsId,
                    value: {
                        hasURL: [entryDst]
                    }
                };

                createNmtlData(user, apiStr, graph, sheetName, entry)
                    .then(res => resolve(res))
                    .catch(err => reject(err));
            } else if (oriIds.includes(dstURLID)) {
                // originObj也有發現的urlId，update
                const classType = getClassType(dstURLID);
                const srcObj = originObj[initColumnDef.hasURL].find(
                    el => el.urlId === dstURLID
                );
                const entryCol = {
                    graph,
                    classType,
                    ...keyData
                };

                const { entrySrc, entryDst } = convertSimpleEntrySD(
                    formatURLObj(srcObj, subject),
                    formatURLObj(dstObj, subject),
                    entryCol
                );

                if (JSON.stringify(entrySrc) !== JSON.stringify(entryDst)) {
                    updateNmtlData(
                        user,
                        apiStr,
                        graph,
                        sheetName,
                        entrySrc,
                        entryDst
                    )
                        .then(res => resolve(res))
                        .catch(err => reject(err));
                }
            }
        });

        if (!isEmpty(originObj)) {
            originObj[initColumnDef.hasURL].forEach(srcObj => {
                const srcURLID = srcObj[imageColDef.urlId];
                if (srcURLID && !updIds.includes(srcURLID)) {
                    // updateObj沒有發現的srcURLID，delete
                    const classType = getClassType(srcURLID);
                    const entryCol = {
                        graph,
                        classType,
                        ...keyData
                    };
                    const { entryDst } = convertSimpleEntrySD(
                        formatURLObj({}, subject),
                        formatURLObj(srcObj, subject),
                        entryCol
                    );
                    const entry = {
                        classType: newsClassType,
                        graph,
                        srcId: newsId,
                        value: {
                            hasURL: [entryDst]
                        }
                    };

                    deleteNmtlData(user, apiStr, graph, sheetName, entry)
                        .then(() => resolve("OK"))
                        .catch(err => reject(err));
                }
            });
        }
        resolve("OK");
    });

export default saveURLEvent;

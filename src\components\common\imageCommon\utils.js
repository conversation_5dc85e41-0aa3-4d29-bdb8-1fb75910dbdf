const utils = {};

utils.safeStr = str => str || "";

utils.stringList = (str, sep = ",") =>
    (Array.isArray(str) && str.map(item => item.toString()).join(sep)) ||
    (str && str.toString()) ||
    "";

utils.prettyStrConcat = (strings, sep = " ") => {
    const stringList = Array.isArray(strings) ? strings : [strings];
    const safeStringList = stringList.map(item => utils.stringList(item));
    return safeStringList.join(utils.safeStr(sep));
};

export default utils;

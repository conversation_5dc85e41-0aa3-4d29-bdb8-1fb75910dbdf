import React from "react";
import "../../EditVillagesDetail.scss";
import { modalFormConfig } from "../config";
import GenericFormArea from "./GenericFormArea";

const ModalFormArea = ({
    updateFct,
    data,
    updatedData,
    optionLists,
    editingPchId
}) => (
    <GenericFormArea
        updateFct={updateFct}
        updatedData={updatedData}
        data={data}
        config={modalFormConfig}
        additionalComponents={null}
        optionLists={optionLists}
        editingPchId={editingPchId}
    />
);

export default ModalFormArea;

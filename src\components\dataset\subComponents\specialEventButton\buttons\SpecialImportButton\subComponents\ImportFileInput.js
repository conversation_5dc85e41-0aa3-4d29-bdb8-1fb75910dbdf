import React, { useContext, useEffect, useState } from "react";
import { useDropzone } from "react-dropzone";

// utils
import { rejectFile } from "../utils/rejectFile";
import acceptFile from "../utils/acceptFile";

// config
import { StoreContext } from "../../../../../../../store/StoreProvider";
import { contentTypeToClassType } from "../../../../../../common/sheetCrud/sheetCrudHelper";

const thumbsContainer = {
    display: "flex",
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 16
};

const thumb = {
    display: "inline-flex",
    marginBottom: 8,
    marginRight: 8,
    width: "100%",
    height: "100%",
    padding: 4,
    boxSizing: "border-box"
};

const thumbInner = {
    display: "flex",
    minWidth: 0,
    overflow: "hidden"
};

const warningMsgStyle = {
    margin: "10px",
    fontSize: "1.5rem",
    color: "red"
};

const dropZoneStyle = {
    height: "50vh",
    width: "100%",
    display: "flex",
    alignItems: "center",
    justifyContent: "center"
};

function ImportFileInput() {
    /** 取上層資料
     *  user: displayName, email,
     *  data: graph, sheetName
     * */
    const [oldState] = useContext(StoreContext);
    const { sheet, mainSubject } = oldState.data;
    const { key: graph } = mainSubject.selected;
    const { key: sheetName, contentWritePath, hasTab } = sheet.selected;

    const [warningMsg, setWarningMsg] = useState("");
    const [msg, setMsg] = useState("");
    const [newShtName, setNewShtName] = useState("");

    useEffect(() => {
        if (!sheetName) return;

        if (!hasTab && contentWritePath) {
            setNewShtName(contentTypeToClassType[contentWritePath]);
        } else {
            // 案、件層級
            const tmpCwPath = contentWritePath || hasTab[1]?.contentWritePath;
            setNewShtName(contentTypeToClassType[tmpCwPath]);
        }
    }, [sheetName, contentWritePath, hasTab]);

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        multiple: false, // 不能一次丟多個檔案
        // accepted MIME type，只接受副檔名: ".xlsx", ".xls"
        accept: [
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-excel"
        ],
        maxFiles: 1, // 上傳檔案數量總數最多一個
        // onDrop: async acceptedFiles => {
        //     if (!newShtName) return;
        //     // 按件層級header判斷，沒有就照原本架構取值
        //     const tabHeader = hasTab?.[1]?.tabClass;
        //
        //     const fbHeader = !tabHeader
        //         ? await getSheetHeader(newShtName)
        //         : await getSheetTabHeader("PublicationInfo", tabHeader);
        //
        //     // fixme: 改成根據不同sheet取得不同的checkList data
        //     const langList = await getListByApi(Api.getLanguage);
        //     const copyRightList = await getListByApi(Api.getCopyrightStatus);
        //     const literGenList = await getListByApi(Api.getLiteraryGenre);
        //     const perOrgListApi = Api.getDsPerOrgInfolist.replace(
        //         "{ds}",
        //         graph
        //     );
        //     const perOrgList = await getListByApi(perOrgListApi);
        //
        //     const checkData = {
        //         fbHeader,
        //         langList,
        //         copyRightList,
        //         literGenList,
        //         perOrgList
        //     };
        //
        //     const res = await checkAccepted(acceptedFiles, checkData);
        //
        //     // if pass === checkRes.success, ready send to nmtl-api
        //     const pass = res.map(el => el.value.pass)[0];
        //     if (pass === checkRes.success) {
        //         let tmpMsg = res.map(el => el.value.res)[0];
        //         tmpMsg +=
        //             "，格式正確，開始匯入。您可以關閉此視窗，資料匯入完成後將寄發通知信至您的信箱。";
        //         setMsg(tmpMsg);
        //
        //         // api import
        //         const apiStr = Api.importData
        //             .replace("{graph}", graph)
        //             .replace("{sheetName}", newShtName);
        //
        //         const formData = new FormData();
        //
        //         // ※formData 要放的是 file 物件不是字串
        //         acceptedFiles.forEach(file => {
        //             formData.append("file", file);
        //         });
        //         formData.append("userName", displayName);
        //         formData.append("userEmail", email);
        //
        //         // start send to api
        //         axios.post(apiStr, formData, {
        //             headers: { "Content-Type": "multipart/form-data" }
        //         });
        //     } else {
        //         let tmpMsg = res.map(el => el.value.res)[0];
        //         tmpMsg +=
        //             "格式錯誤，停止匯入。您可以關閉此視窗，資料匯入完成後將寄發通知信至您的信箱。";
        //         setMsg(tmpMsg);
        //     }
        // },
        onDrop: acceptedFiles => {
            const parmas = {
                acceptedFiles,
                newShtName,
                hasTab,
                graph,
                user: oldState.user,
                setMsg
            };
            acceptFile(parmas);
        },
        onDropRejected: allRejectFile => {
            setWarningMsg(rejectFile(allRejectFile));
        }
    });

    // fixme: markdown text to html not working
    const thumbs = () => (
        <div style={thumb}>
            <div style={thumbInner}>{msg}</div>
        </div>
    );

    return (
        <>
            <div {...getRootProps()} className="dropzone" style={dropZoneStyle}>
                <input {...getInputProps()} />
                {isDragActive ? (
                    <div>
                        <p>拖曳匯入檔案至此....</p>
                        <p>只接受副檔名: .xlsx, .xls</p>
                    </div>
                ) : (
                    <div>
                        <p>拖曳匯入檔案至此或點擊此區以選取匯入檔案</p>
                        <p>只接受副檔名: .xlsx, .xls</p>
                    </div>
                )}
            </div>
            {warningMsg.length > 0 && (
                <div style={warningMsgStyle}>{warningMsg}</div>
            )}
            {msg && <aside style={thumbsContainer}>{thumbs()}</aside>}
        </>
    );
}

export default ImportFileInput;

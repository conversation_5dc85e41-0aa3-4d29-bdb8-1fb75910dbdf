import Api from '../../api/nmtl/Api';

const datasetSheet = {
    BasicInfo: {
        api: 'basicInfo/1.0',
        getClassList: Api.getPersonDsList,
        searchApi: Api.getBasicInfoSearchApi,
        searchApi2: Api.getBasicInfoSearchApi,
        // getTable: 'backend/authority/basicinfo/2.0',
        getTable2: 'backend/dl_basicinfo/2.0',
        getTable3: 'backend/dl_basicinfo/3.0',
        getAuthority: 'backend/authority/basicinfo/2.0',
        getAuthorityGraph: 'backend/authoritygraph/basicinfo/2.0',
    },
    // 外譯房
    Person: {
        api: 'person/1.0',
        // getClassList: 'backend/person/list/2.3',
        // searchApi: 'backend/dl_person/search/3.0',
        // searchApi2: 'backend/dl_person/search/3.0',
        // getTable2: 'backend/dl_person/2.1',
        // getAuthority: 'backend/authority/person/2.0',
        // getInformationTable: 'backend/person/information/2.2',
    },
    Education: {
        api: 'education/1.0',
        getClassList: Api.getEducationDsList,
        searchApi: Api.getEducationSearchApi,
        searchApi2: Api.getEducationSearchApi,
        // getTable: 'dl_education/1.1',
        getTable2: 'backend/dl_education/2.1',
        getAuthority: 'backend/authority/education/2.0',
    },
    Organization: {
        api: 'organization/1.0',
        getClassList: Api.getOrganizationDsList,
        searchApi: Api.getOrganizationSearchApi,
        searchApi2: Api.getOrganizationSearchApi,
        // getTable: 'dl_organization/1.1',
        getTable2: 'backend/dl_organization/2.1',
        getAuthority: 'backend/authority/organization/2.0',
    },
    Specialty: {
        api: 'specialty/1.0',
        getClassList: Api.getSpecialtyDsList,
        searchApi: Api.getSpecialtySearchApi,
        searchApi2: Api.getSpecialtySearchApi,
        // getTable: 'dl_specialty/1.1',
        getTable2: 'backend/dl_specialty/2.1',
        getAuthority: 'backend/authority/specialty/2.0',
    },
    Relationship: {
        api: 'relationship/1.0',
        getClassList: Api.getRelationshipDsList,
        searchApi: Api.getRelationshipSearchApi,
        searchApi2: Api.getRelationshipSearchApi,
        // getTable: 'dl_relationship/1.1',
        getTable2: 'backend/dl_relationship/2.2',
        getAuthority: 'backend/authority/relationship/2.0',
    },
    Event: {
        api: 'event/1.0',
        getClassList: Api.getEventDsList,
        searchApi: Api.getEventSearchApi,
        searchApi2: Api.getEventSearchApi,
        // getTable: 'dl_event/1.1',
        getTable2: 'backend/dl_event/2.0',
        getAuthority: 'backend/authority/event/2.0',
    },
    Foundation: {
        api: 'foundation/1.0',
        getClassList: Api.getFoundationDsList,
        searchApi: Api.getFoundationSearchApi,
        searchApi2: Api.getFoundationSearchApi,
        // getTable: 'dl_foundation/1.1',
        getTable2: 'backend/dl_foundation/2.1',
        getAuthority: 'backend/authority/foundation/2.0',
    },
    DerivateWork: {
        api: 'derivatework/1.0',
        getClassList: Api.getDerivateworkDsList,
        searchApi: Api.getDerivateWorkSearchApi,
        searchApi2: Api.getDerivateWorkSearchApi,
        // getTable: 'dl_derivatework/1.1',
        getTable2: 'backend/dl_derivatework/2.0',
        getAuthority: 'backend/authority/derivatework/2.0',
    },
    // 外譯房
    PublicationInfo: {
        api: 'publicationInfo/1.0',
        // tabApi: {
        //     peicesInfo: 'backend/dl_publicationInfo/peicesInfo/count/2.0',
        //     caseInfo: 'backend/dl_publicationInfo/caseInfo/count/2.0',
        // },
        // hasTab: {
        //     peicesInfo: {
        //         contentClassList: 'backend/publicationInfo/peicesInfo/list/2.4',
        //         contentReadPath: 'backend/dl_publicationInfo/peicesInfo/2.2',
        //         contentWritePath: 'publicationInfo/1.0',
        //         contentSearchPath2: 'backend/dl_publicationInfo/peicesInfo/search/3.0',
        //         api: 'publication/1.0',
        //         contentSearchPath: 'backend/dl_publicationInfo/peicesInfo/search/3.0',
        //         contentDownloadPath: 'backend/dl_publicationInfo/peicesInfo/figure/1.0',
        //     },
        //     caseInfo: {
        //         contentSearchPath: 'backend/dl_publicationInfo/caseInfo/search/3.0',
        //         contentWritePath: 'publicationInfo/1.0',
        //         contentSearchPath2: 'backend/dl_publicationInfo/caseInfo/search/3.0',
        //         contentClassList: 'backend/publicationInfo/caseInfo/list/2.2',
        //         contentDownloadPath: 'backend/dl_publicationInfo/caseInfo/2.3',
        //         api: 'publication/1.0',
        //         contentReadPath: 'backend/dl_publicationInfo/caseInfo/2.3',
        //     },
        // },
    },
    Publication: {
        api: 'publication/1.0',
        getClassList: Api.getPublicationDsList,
        searchApi: Api.getPublicationSearchApi,
        searchApi2: Api.getPublicationSearchApi,
        // getTable: 'dl_publication/1.3',
        getTable2: 'backend/dl_publication/2.0',
        getAuthority: 'backend/authority/publication/2.0',
    },
    Article: {
        api: 'article/1.0',
        getClassList: Api.getArticleDsList,
        searchApi: Api.getArticleSearchApi,
        searchApi2: Api.getArticleSearchApi,
        // getTable: 'dl_article/1.2',
        getTable2: 'backend/dl_article/2.0',
        getAuthority: 'backend/authority/article/2.0',
    },
    Award: {
        api: 'award/1.0',
        getClassList: Api.getAwardDsList,
        searchApi: Api.getAwardSearchApi,
        searchApi2: Api.getAwardSearchApi,
        // getTable: 'dl_award/1.1',
        getTable2: 'backend/dl_award/2.1',
        getAuthority: 'backend/authority/award/2.0',
    },
    Project: {
        api: 'project/1.0',
        getClassList: Api.getProjectDsList,
        searchApi: Api.getProjectSearchApi,
        searchApi2: Api.getProjectSearchApi,
        // getTable: 'dl_project/1.1',
        getTable2: 'backend/dl_project/2.0',
        getAuthority: 'backend/authority/project/2.0',
    },
    Location: {
        api: 'location/1.0',
        getClassList: Api.getLocationDsList,
        searchApi: Api.getLocationSearchApi,
        searchApi2: Api.getLocationSearchApi,
        // getTable: 'backend/authority/location/2.0',
        getTable2: 'backend/dl_location/2.0',
        getAuthority: 'backend/authority/location/2.0',
        getAuthorityGraph: 'backend/authoritygraph/location/2.0',
    },
    ReplaceOrg: {
        api: 'replacedOrganization/1.0',
        getClassList: Api.getReplacedorgDsList,
        searchApi: Api.getReplaceOrgSearchApi,
        searchApi2: Api.getReplaceOrgSearchApi,
        // getTable: 'dl_replacedOrg/1.1',
        getTable2: 'backend/dl_replacedOrg/2.2',
        getAuthority: 'backend/authority/replacedOrganization/2.0',
    },
    OrganizationInfo: {
        api: 'organizationInfo/1.0',
        getClassList: Api.getOrganizationinfoDsList,
        searchApi: Api.getOrganizationInfoSearchApi,
        searchApi2: Api.getOrganizationInfoSearchApi,
        // getTable: 'dl_organizationInfo/1.1',
        getTable2: 'backend/dl_organizationInfo/2.0',
        getAuthority: 'backend/authority/organizationInfo/2.0',
    },
    Collectible: {
        api: 'collectible/1.0',
        getClassList: Api.getCollectibleDsList,
        searchApi: Api.getCollectibleSearchApi,
        searchApi2: Api.getCollectibleSearchApi,
        // getTable: 'dl_collectible/1.1',
        getTable2: 'backend/dl_collectible/2.1',
        getAuthority: 'backend/authority/collectible/2.0',
    },
    Nanzi: {
        api: 'nanzi/1.0',
        getClassList: Api.getNanziDsList,
        searchApi: Api.getNanziSearchApi,
        searchApi2: Api.getNanziSearchApi,
        // getTable: 'backend/dl_nanzi/2.0',
        getTable2: 'backend/dl_nanzi/2.0',
        getAuthority: 'backend/authority/nanzi/2.0',
    },
    TlvmPeriod: {
        api: 'tlvmperiod/1.0',
        getClassList: Api.getTlvmperiodDsList,
        searchApi: Api.getTlvmPeriodSearchApi,
        searchApi2: Api.getTlvmPeriodSearchApi,
        // getTable: 'dl_tlvmperiod/1.1',
        getTable2: 'backend/dl_tlvmperiod/2.0',
        getAuthority: 'backend/authority/tlvmperiod/2.0',
    },
};

export default datasetSheet;

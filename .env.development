PORT=3000

NODE_ENV=production

NODE_OPTIONS="--max-old-space-size=8192"

REACT_APP_ALLOWED_ORIGINS=http://***************

#REACT_APP_ELASTIC_NODE_URL=http://nmtl_log_api:3000
REACT_APP_ELASTIC_NODE_URL=https://counter.daoyidh.com

# REACT_APP_NMTL_API_NODE=http://localhost:3000
REACT_APP_NMTL_API_NODE=https://api2.daoyidh.com/nmtl2

REACT_APP_NMTL_API_LANG="zh-tw"

#REACT_APP_FILE_SERVER_URL=https://fs-root.daoyidh.com
REACT_APP_FILE_SERVER_URL=https://fs2.daoyidh.com
#REACT_APP_FILE_SERVER_URL=http://localhost:9000

ALLOW_TEST_USER_ONLY=false

# REACT_APP_WEB_MODE:正式站(production)or測試站(development)or本地端(local)
REACT_APP_WEB_MODE=local

# 是否要印出 console.log 的內容
REACT_APP_DEBUG=true

REACT_APP_ATAI_URL=https://api2.daoyidh.com/genaipdf

REACT_APP_ATAI_PDF_URL=https://fs2.daoyidh.com/static/file/upload/atai-backend2

REACT_APP_VILLAGES_TIDBITS_FILE_SERVER_URL=tidbits2

REACT_APP_HISTORY_LOG_FIREBASE_DOC_NAME=nmtl2-history-events
REACT_APP_HISTORY_LOG_FILESERVER_PATH=nmtl

REACT_APP_DOMAIN_SUFFIX=.daoyidh.com

import Swal from "sweetalert2";
import queryString from "query-string";

const timerDefault = 5000;
const positionDefault = "top";
const iconType = ["success", "warning", "error", "info", "question"];

const swalMixCreator = types => {
    const swalMix = {};
    types.forEach(t => {
        swalMix[t] = Swal.mixin({
            icon: t,
            position: positionDefault,
            timer: timerDefault
            // toast: true,
        });
    });
    return swalMix;
};

const swalMix = swalMixCreator(iconType);

// verify file name
const verifyFileName = (file, acceptableExt) => {
    const extReg = `^(image|application)\\/(${acceptableExt.join("|")})$`;
    // const nameReg = /[\d\w-()]+\.[a-z]{3,6}/;
    // FIXME: pdf 可能有中文，如何判斷字是否不合法？
    const nameReg = /(.*?)+\.[a-zA-Z]{3,6}/;
    return file.type.match(extReg) && file.name.match(nameReg);
};

const acceptCharacter = {
    char: "A-Z,a-z,0-9,-_()",
    ch: "大小寫英文字母, 數字, 及 _-()"
};

const uuidv4 = () =>
    "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, c => {
        // eslint-disable-next-line no-bitwise
        const r = (Math.random() * 16) | 0;
        // eslint-disable-next-line no-bitwise,eqeqeq
        const v = c == "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });

const generateUID = () => {
    // I generate the UID from two parts here
    // to ensure the random number provide enough bits.
    // eslint-disable-next-line no-bitwise
    let firstPart = (Math.random() * 46656) | 0;
    // eslint-disable-next-line no-bitwise
    let secondPart = (Math.random() * 46656) | 0;
    firstPart = `000${firstPart.toString(36)}`.slice(-3);
    secondPart = `000${secondPart.toString(36)}`.slice(-3);
    return firstPart + secondPart;
};

const customSubjectOption = () => [
    {
        classId: "testSubject",
        enable: "1",
        id: "testSubject",
        label: "測試主題",
        seq: 0,
        type: "ms"
    },
    // fixme: 共通修改選項移除後，"nmtl-web"改成"nmtl"
    {
        classId: "nmtl-web",
        enable: "1",
        id: "nmtl-web",
        label: "文學好臺誌",
        seq: 0,
        type: "ms"
    }
];

// 取得檔案名稱
const getFileName = url => {
    // firebase URL
    if (url.indexOf("firebasestorage") >= 0) {
        let s1Parse = Object.keys(queryString.parse(url))[0];
        [s1Parse] = s1Parse.split("?");
        return s1Parse.split("/")[10];
    }
    // daoyi URL
    return url.split("/").pop();
};

const noZHTag = str => str.slice(-3) !== "@zh";

const checkURLPattern = str => {
    // url regular expression
    const regex = /\bhttps?:\/\/\S+/;
    return str.match(regex);
};

export {
    swalMix,
    verifyFileName,
    acceptCharacter,
    uuidv4,
    generateUID,
    customSubjectOption,
    getFileName,
    noZHTag,
    checkURLPattern
};

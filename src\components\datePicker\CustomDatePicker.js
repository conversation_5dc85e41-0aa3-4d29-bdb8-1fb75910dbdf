import React from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import "./customDatePicker.scss";

const CustomDatePicker = ({
    startDate,
    endDate,
    minDate,
    maxDate,
    onChange
}) => {
    return (
        <div className="datePickerContainer">
            <div className="datePickerWrapper">
                <DatePicker
                    selected={new Date(startDate)}
                    onChange={date => {
                        onChange("startDate", date);
                    }}
                    selectsStart
                    startDate={new Date(startDate)}
                    endDate={new Date(endDate)}
                    minDate={new Date(minDate)}
                    maxDate={new Date(maxDate)}
                    placeholderText="Select start date"
                    className="datePicker"
                />
            </div>
            <div className="dash"></div>
            <div className="datePickerWrapper">
                <DatePicker
                    selected={new Date(endDate)}
                    onChange={date => {
                        onChange("endDate", date);
                    }}
                    selectsEnd
                    startDate={new Date(startDate)}
                    endDate={new Date(endDate)}
                    minDate={new Date(startDate) || minDate}
                    maxDate={new Date(maxDate)}
                    placeholderText="Select end date"
                    className="datePicker"
                />
            </div>
        </div>
    );
};

export default CustomDatePicker;

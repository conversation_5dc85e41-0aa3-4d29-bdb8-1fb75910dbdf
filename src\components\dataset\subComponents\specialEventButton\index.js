import React, { useContext, useMemo } from "react";
// custom button

import MergeButton from "../eventButton/buttons/MergeButton";
import DeleteButton from "../eventButton/buttons/DeleteButton";

// FIXME: disable import temporary.

// store
import { StoreContext } from "../../../../store/StoreProvider";

// config
import role from "../../../../App-role";
import SpecialCreateButton from "./buttons/SpecialCreateButton";
import SpecialImportButton from "./buttons/SpecialImportButton";
import SpecialDownloadButton from "./buttons/SpecialDownloadButton";

// FIXME: when table value changed, this component is changing as well.
// Why useMemo is ineffective?
const SpecialEventButton = () => {
    // eslint-disable-next-line no-unused-vars
    const [state] = useContext(StoreContext);
    const { user } = state;

    const safeRole = user?.role || role.anonymous;

    // admin
    const admin = (
        <React.Fragment>
            {/* FIXME: disable import temporary. */}
            <SpecialCreateButton />
            <MergeButton />
            <DeleteButton />
            <SpecialImportButton />
            <SpecialDownloadButton />
        </React.Fragment>
    );
    // developer
    const developer = (
        <React.Fragment>
            {/* FIXME: disable import temporary. */}
            <SpecialCreateButton />
            <MergeButton />
            <DeleteButton />
            <SpecialImportButton />
            <SpecialDownloadButton />
        </React.Fragment>
    );
    // editor
    const editor = (
        <React.Fragment>
            {/* FIXME: disable import temporary. */}
            <SpecialCreateButton />
            <MergeButton />
            <DeleteButton />
            <SpecialImportButton />

            <SpecialDownloadButton />
        </React.Fragment>
    );
    // reader
    const reader = (
        <React.Fragment>
            <SpecialDownloadButton />
        </React.Fragment>
    );
    // anonymous
    const anonymous = (
        <React.Fragment>
            <SpecialDownloadButton />
        </React.Fragment>
    );
    //
    const buttons = {
        admin,
        developer,
        editor,
        reader,
        anonymous
    };

    const ButtonGroup = useMemo(() => () => buttons[safeRole], [safeRole]);
    return <ButtonGroup />;
};

export default SpecialEventButton;

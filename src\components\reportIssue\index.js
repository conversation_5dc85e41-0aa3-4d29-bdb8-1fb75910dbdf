import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";

// semantic ui
import { <PERSON><PERSON>, Grid } from "semantic-ui-react";

// scss
import "./reportIssue.scss";

// components
import SubjectDropdown from "./components/CustomDropdown/SubjectDropdown";
import CustomTab from "./components/CustomTab";
import SaveBtn from "./components/CustomButton/SaveBtn";

// utils
import { saveAllData, checkDataChange, getAllData } from "./common/utils";
import { isEmpty } from "../../commons";

// config
import textConfig from "./common/textConfig";
import useCusContext from "../common/hooks/useCusContext";
import ResultModal from "../common/CustomModal/ResultModal";

const styleFirstGridCol = {
    display: "flex",
    justifyContent: "flex-end"
};

function ReportIssue() {
    const dispatch = useDispatch();
    const { rpSubject, newAllData, allData } = useSelector(
        state => state.report
    );

    const [state] = useCusContext();
    const { displayName } = state.user;

    const [saveBtnDisable, setSaveBtnDisable] = useState(true);
    const [loading, setLoading] = useState(false);

    // Result modal
    const [openModal, setOpenModal] = useState(false);
    const [modalMessage, setModalMessage] = useState("");

    useEffect(() => {
        if (!rpSubject) return;
        getAllData(rpSubject, dispatch);
    }, [rpSubject]);

    useEffect(() => {
        // 檢查SaveBtn disabled or not
        if (isEmpty(newAllData) || isEmpty(allData)) return;
        const check = checkDataChange(allData, newAllData);
        setSaveBtnDisable(check);
    }, [newAllData, allData]);

    const saveCallBack = msg => {
        setLoading(false);
        setOpenModal(true);
        setModalMessage(msg);
        getAllData(rpSubject, dispatch);
    };

    const handleSave = () => {
        setLoading(true);
        saveAllData(allData, newAllData, displayName)
            .then(res => {
                const msg =
                    res.status === "OK"
                        ? textConfig.SuccessMessage.UPDATEDATA
                        : textConfig.ErrorMessage.OTHER;
                saveCallBack(msg);
            })
            .catch(err => {
                const msg = `${textConfig.ErrorMessage.UPDATEDATA}\n${err}`;
                saveCallBack(msg);
            });
    };

    const closeResModal = () => {
        setOpenModal(false);
        setSaveBtnDisable(true);
    };

    return (
        <div className="ReportIssue">
            <div className="ReportIssue__topArea">
                <Header as="h2">
                    <Header.Content>
                        <SubjectDropdown />
                    </Header.Content>
                </Header>
            </div>
            <div className="ReportIssue__contentArea">
                <Grid>
                    <Grid.Row>
                        <Grid.Column style={styleFirstGridCol}>
                            <SaveBtn
                                loading={loading}
                                style={{ margin: "0" }}
                                label={textConfig.label.update}
                                disabled={saveBtnDisable}
                                handleClick={handleSave}
                            />
                        </Grid.Column>
                    </Grid.Row>
                </Grid>
                <CustomTab />
            </div>
            <ResultModal
                openModal={openModal}
                modalMessage={modalMessage}
                onClose={closeResModal}
                onClick={closeResModal}
            />
        </div>
    );
}

export default ReportIssue;

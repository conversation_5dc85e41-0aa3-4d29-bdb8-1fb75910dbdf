const Act = {
    // Test
    TEST_USER_DETAIL: "TEST_USER_DETAIL",
    // Firebase user
    FIREBASE_LOGIN_USER: "FIREBASE_LOGIN_USER",
    FIREBASE_LOGOUT_USER: "FIREBASE_LOGOUT_USER",
    // Firebase group
    FIREBASE_GROUP_INFO_BY_USER: "FIREBASE_GROUP_INFO_BY_USER",
    // USER ROLE
    FIREBASE_USER_ROLE_CHANGED: "FIREBASE_USER_ROLE_CHANGED",
    FIREBASE_USER_ROLE_CHANGED_CLEAN: "FIREBASE_USER_ROLE_CHANGED_CLEAN",
    FIREBASE_USER_REMOVE: "FIREBASE_USER_REMOVE",
    FIREBASE_USER_ROLE_UPDATE_RENDER_SIGNAL:
        "FIREBASE_USER_ROLE_UPDATE_RENDER_SIGNAL",
    // sidebar MainSubject
    DATA_MAINSUBJECT: "DATA_MAINSUBJECT",
    DATA_MAINSUBJECT_CLEAN: "DATA_MAINSUBJECT_CLEAN",
    // sidebar Sheet
    DATA_SHEET: "DATA_SHEET",
    DATA_TAB_COUNT_SHEET: "DATA_TAB_COUNT_SHEET",
    DATA_TAB_COUNT_SHEET_CLEAN: "DATA_TAB_COUNT_SHEET_CLEAN",
    DATA_TAB_KEY: "DATA_TAB_KEY",
    DATA_TAB_KEY_CLEAN: "DATA_TAB_KEY_CLEAN",
    DATA_SHEET_CLEAN: "DATA_SHEET_CLEAN",
    DATA_SHEET_HEADER: "DATA_SHEET_HEADER",
    DATA_SHEET_HEADER_FIELD: "DATA_SHEET_HEADER_FIELD",
    DATA_SHEET_HEADER_FIELD_ADD: "DATA_SHEET_HEADER_FIELD_ADD",
    DATA_SHEET_HEADER_FIELD_REMOVE: "DATA_SHEET_HEADER_FIELD_REMOVE",
    DATA_SHEET_HEADER_FIELD_UPDATE: "DATA_SHEET_HEADER_FIELD_UPDATE",
    DATA_SHEET_ACTIVATE_HEADER: "DATA_SHEET_ACTIVATE_HEADER",
    DATA_SHEET_ACTIVATE_HEADER_ADD: "DATA_SHEET_ACTIVATE_HEADER_ADD",
    DATA_SHEET_ACTIVATE_HEADER_REMOVE: "DATA_SHEET_ACTIVATE_HEADER_REMOVE",
    DATA_SHEET_ACTIVATE_HEADER_CLEAN: "DATA_SHEET_ACTIVATE_HEADER_CLEAN",
    // pagination
    DATA_PAGINATION_ACTIVE_PAGE: "DATA_PAGINATION_ACTIVE_PAGE",
    DATA_PAGINATION_ACTIVE_PAGE_INIT: "DATA_PAGINATION_ACTIVE_PAGE_INIT",
    DATA_PAGINATION_TOTAL_PAGE: "DATA_PAGINATION_TOTAL_PAGE",
    DATA_PAGINATION_PAGE_INIT: "DATA_PAGINATION_PAGE_INIT",
    DATA_PAGINATION_PAGENUM: "DATA_PAGINATION_PAGENUM",
    DATA_PAGINATION_TOTAL_COUNT: "DATA_PAGINATION_TOTAL_COUNT",
    // content
    DATA_CONTENT_LOADING: "DATA_CONTENT_LOADING",
    DATA_CONTENT_NO_LOADING: "DATA_CONTENT_NO_LOADING",
    DATA_CONTENT_ROWS: "DATA_CONTENT_ROWS",
    DATA_CONTENT_ROWS_CLEAN: "DATA_CONTENT_ROWS_CLEAN",
    DATA_CONTENT_CURRENT_BOOK_LANGUAGE: "DATA_CONTENT_CURRENT_BOOK_LANGUAGE",
    DATA_CONTENT_SHOW_ONLY_UNINTEGRATED_DATA:
        "DATA_CONTENT_SHOW_ONLY_UNINTEGRATED_DATA",
    // content create
    DATA_CONTENT_ROW_CREATED: "DATA_CONTENT_ROW_CREATED",
    DATA_CONTENT_ROW_CREATED_CLEAN: "DATA_CONTENT_ROW_CREATED_CLEAN",
    // content changed
    DATA_CONTENT_ROW_CHANGED: "DATA_ROW_CHANGED",
    DATA_CONTENT_ROW_NO_CHANGED: "DATA_CONTENT_ROW_NO_CHANGED",
    DATA_CONTENT_ROW_CHANGED_CLEAN: "DATA_CONTENT_ROW_CHANGED_CLEAN",
    DATA_CONTENT_ROW_CHANGED_ONE_CLEAN: "DATA_CONTENT_ROW_CHANGED_ONE_CLEAN",
    // content checked
    DATA_CONTENT_ROW_CHECKED: "DATA_CONTENT_ROW_CHECKED",
    DATA_CONTENT_ROW_CHECKED_ALL: "DATA_CONTENT_ROW_CHECKED_ALL",
    DATA_CONTENT_ROW_NO_CHECKED: "DATA_CONTENT_ROW_NO_CHECKED",
    DATA_CONTENT_ROW_CHECKED_CLEAN: "DATA_CONTENT_ROW_CHECKED_CLEAN",
    // content uploaded
    DATA_CONTENT_UPLOADED: "DATA_CONTENT_UPLOADED",
    DATA_CONTENT_UPLOADED_CLEAN: "DATA_CONTENT_UPLOADED_CLEAN",
    DATA_CONTENT_UPLOADED_RECORD: "DATA_CONTENT_UPLOADED_RECORD",
    DATA_CONTENT_UPLOADED_RECORD_CLEAN: "DATA_CONTENT_UPLOADED_RECORD_CLEAN",
    // groupInfo
    DATA_GROUPINFO: "DATA_GROUPINFO",
    // content: image editor
    IMAGE_EDITOR_DISPLAY: "IMAGE_EDITOR_DISPLAY",
    IMAGE_EDITOR_CURRENT_DATA: "IMAGE_EDITOR_CURRENT_DATA",
    // message
    DATA_MESSAGE: "DATA_MESSAGE",
    DATA_MESSAGE_CLEAN: "DATA_MESSAGE_CLEAN",
    DATA_INFO_MESSAGE: "DATA_INFO_MESSAGE",
    DATA_INFO_MESSAGE_CLEAN: "DATA_INFO_MESSAGE_CLEAN",
    // search
    DATA_SEARCH_KEYWORD: "DATA_SEARCH_KEYWORD",
    DATA_SEARCH_KEYWORD_CLEAN: "DATA_SEARCH_KEYWORD_CLEAN",
    DATA_SEARCH_SEARCH_COLUMN: "DATA_SEARCH_SEARCH_COLUMN",
    DATA_SEARCH_DATASET: "DATA_SEARCH_DATASET",
    // history
    HISTORY_LOG: "HISTORY_LOG",
    HISTORY_LOG_CLEAN: "HISTORY_LOG_CLEAN",
    // upload
    IMG_PICKER_INITIAL_STATE: "IMG_PICKER_INITIAL_STATE",
    UPLOAD_TMP_IMAGES: "UPLOAD_TMP_IMAGES",
    UPLOAD_IMAGE: "UPLOAD_IMAGE",
    UPLOAD_FILE: "UPLOAD_FILE",
    SET_FORM_DATA: "SET_FORM_DATA",
    SET_FOLDER_SETTINGS: "SET_FOLDER_SETTINGS",
    SELECT_FOLDER: "SELECT_FOLDER",
    FOLDER_FILES_URL: "FOLDER_FILES_URL",
    CUR_FOLDER_FILES_STATUS: "CUR_FOLDER_FILES_STATUS",
    CLEAR_CUR_FOLDER_FILES_STATUS: "CLEAR_CUR_FOLDER_FILES_STATUS",
    DROP_FOLDER_FILES_STATUS: "DROP_FOLDER_FILES_STATUS",
    CLEAR_DROP_FOLDER_FILES_STATUS: "CLEAR_DROP_FOLDER_FILES_STATUS",
    SET_FILE_SERVER_URL: "SET_FILE_SERVER_URL",
    UPLOAD_LOADING: "UPLOAD_LOADING",
    UPLOAD_CLEAR_CACHE: "UPLOAD_CLEAR_CACHE",
    // database
    INITIAL_DB_UPLOAD_STATE: "INITIAL_DB_UPLOAD_STATE",
    UPLOAD_DB_FILE: "UPLOAD_DB_FILE",
    DATABASE_MSG: "DATABASE_MSG",
    ACCEPTABLE_MINE_TYPE: "ACCEPTABLE_MINE_TYPE",
    SAVE_UPLOAD_DB_FILE: "SAVE_UPLOAD_DB_FILE",
    DOWNLOAD_DB_FILE: "DOWNLOAD_DB_FILE",
    // common
    SET_HEADERACTIVENAME: "SET_HEADERACTIVENAME",
    SET_SYSTEMDATAACTIVEITEM: "SET_SYSTEMDATAACTIVEITEM",
    SET_TOOLPAGESACTIVENAME: "SET_TOOLPAGESACTIVENAME",
    SET_DOWNLOADDATAACTIVENAME: "SET_DOWNLOADDATAACTIVENAME",
    // example table
    EXAMPLE_TABLE_SELECTED_SHEET: "EXAMPLE_TABLE_SELECTED_SHEET",
    // websiteSetting
    SET_MENUACTIVEITEM: "SET_MENUACTIVEITEM",
    SET_ORIGINDATA: "SET_ORIGINDATA",
    SET_SELECTOPTION: "SET_SELECTOPTION",
    SET_UPDATEDDATA: "SET_UPDATEDDATA",
    SET_REALTIMEDATA: "SET_REALTIMEDATA",
    SET_OPENMODAL: "SET_OPENMODAL",
    SET_MODALMESSAGE: "SET_MODALMESSAGE",
    SET_ISEDITEDDISABLE: "SET_ISEDITEDDISABLE",
    SET_LISTDATA: "SET_LISTDATA",
    SET_MSLISTDATA: "SET_MSLISTDATA",
    SET_WEBSITESUBJECT: "SET_WEBSITESUBJECT",
    SET_WEBSETMENU: "SET_WEBSETMENU",
    FRONTEDIT_TLTC: "FRONTEDIT_TLTC",
    // for VrMuseum
    SET_VRMUSEUM_LOGO: "SET_VRMUSEUM_LOGO",
    SET_VRMUSEUM_LOGO_ORI_DATA: "SET_VRMUSEUM_LOGO_ORI_DATA",
    SET_VRMUSEUM_LOGO_TEMP_DATA: "SET_VRMUSEUM_LOGO_TEMP_DATA",
    // sorted
    DATA_SORTED: "DATA_SORTED",
    DATA_SORTED_CLEAN: "DATA_SORTED_CLEAN",
    DATA_SORTED_IDS: "DATA_SORTED_IDS",
    DATA_IS_FILL_ALL_AUTHOR_OTHERNAME: "DATA_IS_FILL_ALL_AUTHOR_OTHERNAME",
    DATA_IS_FILL_ALL_TRANSLATOR_OTHERNAME:
        "DATA_IS_FILL_ALL_TRANSLATOR_OTHERNAME",
    DATA_SET_IS_FILL_ALL_OTHERNAME_MODAL_OPEN:
        "DATA_SET_IS_FILL_ALL_OTHERNAME_MODAL_OPEN",
    DATA_SET_AUTHOR_OTHERNAME_FOR_DRAG_TABLE:
        "DATA_SET_AUTHOR_OTHERNAME_FOR_DRAG_TABLE",
    DATA_SET_TRANSLATOR_OTHERNAME_FOR_DRAG_TABLE:
        "DATA_SET_TRANSLATOR_OTHERNAME_FOR_DRAG_TABLE",
    DATA_SET_CLONE_CREATE_STATE: "DATA_SET_CLONE_CREATE_STATE",
    DATA_SET_CLONE_CREATE_STATE_FOR_UPDATE:
        "DATA_SET_CLONE_CREATE_STATE_FOR_UPDATE",
    DATA_SET_IS_DRAGGING: "DATA_SET_IS_DRAGGING",
    DATA_SET_IS_SEARCHING_OVER: "DATA_SET_IS_SEARCHING_OVER",
    DATA_SET_IS_AUTHOR_IN_DRAGGINGMODE: "DATA_SET_IS_AUTHOR_IN_DRAGGINGMODE",
    DATA_SET_IS_TRANSLATOR_IN_DRAGGINGMODE:
        "DATA_SET_IS_TRANSLATOR_IN_DRAGGINGMODE",
    DATA_SET_UPDATE_CLONE_LOCAL_CREATE_STATE:
        "DATA_SET_UPDATE_CLONE_LOCAL_CREATE_STATE",
    DATA_CLEAR_CLONE_CREATE_STATE: "DATA_CLEAR_CLONE_CREATE_STATE",
    NOTIFY_DROPDOWN_LIST_UPDATE: "NOTIFY_DROPDOWN_LIST_UPDATE"
};

export default Act;

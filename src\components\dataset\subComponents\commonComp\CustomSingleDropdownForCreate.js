import React, { useContext, useState, useMemo } from "react";

// store
import { createFilter } from "react-select";
import CreatableSelect from "react-select/creatable";
import { StoreContext } from "../../../../store/StoreProvider";
import { getMenuPlacement } from "../../datasetConfig";

// custom ui
import MenuList from "./MenuList";

// common
import {
    MAX_OPTION,
    CREATED_PREFIX,
    forCreateDropdown,
    graph4CreateInstance
} from "../../../common/sheetCrud/sheetCrudHelper";

// api
import Act from "../../../../store/actions";
import { getApiByAllField } from "../../../../api/nmtl/ApiField";
import { createNmtlData } from "../../../../api/nmtl";
import Api from "../../../../api/nmtl/Api";
import { getReservedNewId } from "../../../common/sheetCrud/utils";
import getKeyBySingle from "../../../../api/nmtl/ApiKey";
import getSingleByApi from "../../../../api/nmtl/ApiSingle";
import { isEmpty } from "../../../../commons";

const CustomSingleDropdownForCreate = ({
    cellId,
    rowId,
    // default 0
    idx = 0,
    defaultValue,
    createState,
    setCallback,
    isCreateNew = false
}) => {
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { sheet, mainSubject } = state.data;
    const { dataset } = mainSubject.selected;
    const { key: sheetName } = sheet.selected;
    const { headerFields } = sheet;
    const [inputValue, setInputValue] = useState(defaultValue);
    // set true when input and default vale are diff
    const [isDiffValue, setDiffValue] = useState(false);

    // change input value
    const handleChange = selectValue => {
        const sltVal =
            selectValue === null
                ? { [idx]: { id: "", label: "", value: "" } }
                : { [idx]: selectValue.id };
        const cellValue = { ...createState.value, ...sltVal };

        // console.log(selectValue, sltVal, cellValue, createState);
        // change input background color when value is diff
        if (createState.value?.id === selectValue?.id) {
            setDiffValue(false);
            // dispatch
            dispatch({
                type: Act.DATA_CONTENT_ROW_NO_CHANGED,
                payload: {
                    rowId,
                    cellId,
                    idx
                }
            });
        } else {
            // update diff state
            setDiffValue(true);
            // dispatch
            dispatch({
                type: Act.DATA_CONTENT_ROW_CHANGED,
                payload: {
                    rowId,
                    cellId,
                    // Object { 0: "EthnicAmis" }
                    cellValue
                }
            });
        }

        // 最後再 setCallback 去 change CreateState
        setCallback(cellId, rowId, idx, {
            isOption: true,
            value: cellValue
        });
    };

    const handleInputChange = value => {
        if (!value) {
            return;
        }
        setInputValue(value);
        // 不用更新 createState
        // setCallback(cellId, rowId, idx, { isOption: true, input: inputValue });
    };

    const addToOptions = ({ newSrcId, newApi, label }) => {
        const newOpt = {
            id: newSrcId,
            label,
            value: newSrcId
        };

        // 如果此欄位為多重 type，增加到該 type
        // hasPublisher: ORG, PER
        if (newApi && Object.keys(headerFields).indexOf(newApi)) {
            headerFields[newApi].push(newOpt);
        }

        setCallback(cellId, rowId, idx, {
            isOption: true,
            input: null,
            value: { [idx]: label },
            options: createState.options.concat(newOpt)
        });
    };

    const getDatasetByClass = newClass => {
        // if newClass is Person, return "authority"
        if (newClass in graph4CreateInstance) {
            return graph4CreateInstance[newClass];
        }
        return dataset;
    };

    const getCreateNmtlItemResult = async (item, newClass, newApi) => {
        // console.log(item, newClass, newApi);
        if (isEmpty(item) || isEmpty(dataset) || isEmpty(sheetName)) {
            return { newSrcId: null };
        }
        const apiUrl = Api.getGeneric;
        const itemKey = getKeyBySingle(getSingleByApi(newApi));

        const createGraph = getDatasetByClass(newClass);
        console.log("createGraph", createGraph);

        // 先 reserved id
        const newSrcId = await getReservedNewId(itemKey);
        if (apiUrl && itemKey) {
            const newItem = {
                graph: createGraph,
                classType: newClass || itemKey,
                srcId: newSrcId || "",
                value: {
                    label: item
                }
            };

            const createResult = await createNmtlData(
                user,
                apiUrl,
                dataset,
                sheetName,
                newItem
            ).then(res => res === "OK");

            return { newSrcId, createResult };
        }
        return { newSrcId: null };
    };

    const handleCreate = async inputVal => {
        // 在 Component 裡頭 create，CreateButton 也有自己的 create.
        if (isCreateNew) {
            // 如果沒有任何需要 create 的，表示此為選擇已存在的 instance
            // 將此 instance 加入表格
            // suffix feature
            // apiArr: Array()
            const apiArr = getApiByAllField(cellId);
            if (apiArr.length > 1) {
                console.error("Not implemented!!", apiArr);
                return;
            }

            // 假設這個欄位只有對應一種類別
            const newApi = apiArr[0];
            // 取得 cellId 對應的 class
            const newClass = getKeyBySingle(getSingleByApi(newApi));

            console.log("newClass", newClass);

            if (!newClass) {
                return;
            }

            const newValue = inputVal;

            const { newSrcId } = await getCreateNmtlItemResult(
                newValue,
                newClass,
                newApi
            );

            if (newSrcId) {
                addToOptions({ newSrcId, newApi, label: newValue, cellId });

                handleChange({ id: newSrcId });
                // update diff state
                setDiffValue(true);

                dispatch({
                    type: Act.DATA_MESSAGE,
                    payload: {
                        title: `${newValue} 已創建完成`,
                        error: 0
                        // renderSignal 會讓 ContentView 重新渲染，所有的值會 refresh
                        // renderSignal: `update-${new Date().getTime()}`
                    }
                });
            }
        } else {
            // CreateButton
            const newValue = `${CREATED_PREFIX}_${inputVal}`;
            const newOpt = {
                id: newValue,
                label: inputVal,
                value: newValue
            };

            setCallback(cellId, rowId, idx, {
                isOption: true,
                input: null,
                value: { [idx]: newValue },
                options: createState.options.concat(newOpt)
            });
        }
    };

    const customStyles = {
        container: styles => ({
            ...styles,
            margin: "-9px",
            minWidth: "100%",
            // minWidth: "360px",
            width: "max-content"
        }),
        control: (styles, { selectProps: { controlColor } }) => ({
            ...styles,
            borderStyle: "none",
            borderRadius: "unset",
            backgroundColor: controlColor
        })
    };

    const customPlacement = getMenuPlacement(rowId);
    const customControlBgColor = isDiffValue ? "#f9c09a66" : "";

    return useMemo(
        () => (
            <CreatableSelect
                placeholder="請輸入..."
                isClearable
                styles={customStyles}
                isDisabled={createState.isLoading}
                isLoading={createState.isLoading}
                options={
                    inputValue
                        ? createState.options
                            .filter(o => o.label.includes(inputValue))
                            .slice(0, MAX_OPTION)
                        : createState.options.slice(0, MAX_OPTION)
                }
                value={
                    createState.value
                        ? createState.options
                            .filter(o => o.id === createState.value[idx])
                            .slice(0, MAX_OPTION)
                        : null
                }
                onChange={handleChange}
                onInputChange={handleInputChange}
                onCreateOption={handleCreate}
                noOptionsMessage={() => null}
                components={{
                    // 關閉下拉選單按鈕，剩下 input 輸入框
                    DropdownIndicator: () => null,
                    IndicatorSeparator: () => null
                }}
                menuPlacement={customPlacement}
                controlColor={customControlBgColor}
                // [fixed] select lag issue: https://github.com/JedWatson/react-select/issues/3128 by M1K3Yio commented on 19 Oct 2018
                filterOption={createFilter({ ignoreAccents: false })}
            />
        ),
        [
            cellId,
            createState.value,
            createState.input,
            createState.isLoading,
            inputValue
        ]
    );
};

export default CustomSingleDropdownForCreate;

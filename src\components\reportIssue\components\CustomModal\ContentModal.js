import React from "react";
import PropTypes from "prop-types";
import { useSelector } from "react-redux";

// semantic ui
import { <PERSON><PERSON>, <PERSON><PERSON>, Header, Container } from "semantic-ui-react";

// config
import textConfig from "../../common/textConfig";

// components
import ContentTable from "../CustomTable/ContentTable";

function ContentModal({ openModal, onClose }) {
    const { cntData } = useSelector(state => state.report);
    return (
        <Modal size="small" open={openModal}>
            <Modal.Header>
                <Container
                    style={{ display: "flex", justifyContent: "space-between" }}
                >
                    <Header as="h2" style={{ margin: "0" }}>
                        {textConfig.label.desc}
                    </Header>
                    <Header as="h4" style={{ margin: "auto 0" }}>
                        編號: {cntData.issueID}
                    </Header>
                </Container>
            </Modal.Header>
            <Modal.Content>
                <ContentTable />
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={onClose}>{textConfig.label.close}</Button>
            </Modal.Actions>
        </Modal>
    );
}

ContentModal.propTypes = {
    /** 顯示儲存資料後結果的視窗 */
    openModal: PropTypes.bool,
    /** 關閉視窗動作 */
    onClose: PropTypes.func
};

ContentModal.defaultProps = {
    /** 顯示儲存資料後結果的視窗 */
    openModal: false,
    /** 關閉視窗動作 */
    onClose: () => {}
};

export default ContentModal;

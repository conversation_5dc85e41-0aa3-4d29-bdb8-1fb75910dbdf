import React, { useContext } from "react";
import { Modal, Table, Header } from "semantic-ui-react";
import { isEmpty } from "../../../../../../commons";
import { StoreContext } from "../../../../../../store/StoreProvider";
import { PEICES_INFO } from "../../../../../common/sheetCrud/sheetCrudHelper";

function BasicContent() {
    const [state] = useContext(StoreContext);
    const { content, sheet } = state.data;
    const { header } = sheet;
    const { checked, rows } = content;
    const { tabClass } = sheet.tabKey;

    const deleteValue = ({ row, check, sh }) => {
        if (tabClass === PEICES_INFO) {
            const [rIdx, cIdx] = check.split("-");

            if (!rIdx || !cIdx) return "";

            if (Object.hasOwn(row[rIdx], sh)) {
                return row[rIdx][sh] || "";
            }

            return row[rIdx]?.transList[cIdx][sh] || "";
        }

        return row[check][sh];
    };

    return (
        <Modal.Content image scrolling>
            <Modal.Description>
                <Header size="medium">刪除資料</Header>
                <Table celled compact selectable>
                    <Table.Header>
                        <Table.Row>
                            {/*  handle table title(sheet) */}
                            {!isEmpty(rows) &&
                                !isEmpty(checked) &&
                                header &&
                                header.map(sh => (
                                    <Table.HeaderCell singleLine key={sh.id}>
                                        {sh.label}
                                    </Table.HeaderCell>
                                ))}
                        </Table.Row>
                    </Table.Header>
                    <Table.Body>
                        {!isEmpty(rows) && !isEmpty(checked) ? (
                            checked &&
                            checked.map((ck, rowIdx) => (
                                <Table.Row
                                    error
                                    key={`delete-button-row-${rowIdx}`}
                                >
                                    {header &&
                                        header.map((sh, shIdx) => {
                                            const cell = deleteValue({
                                                row: rows,
                                                check: ck.rowId,
                                                sh: sh.id
                                            });

                                            return (
                                                <Table.Cell
                                                    key={`delete-button-row-${rowIdx}-${shIdx}-${cell}`}
                                                >
                                                    {Object.values(
                                                        cell || ""
                                                    ).join("\n") || ""}
                                                </Table.Cell>
                                            );
                                        })}
                                </Table.Row>
                            ))
                        ) : (
                            <Table.Row>
                                <Table.Cell warning>
                                    <p>No Data to Delete</p>
                                </Table.Cell>
                            </Table.Row>
                        )}
                    </Table.Body>
                </Table>
            </Modal.Description>
        </Modal.Content>
    );
}

export default BasicContent;

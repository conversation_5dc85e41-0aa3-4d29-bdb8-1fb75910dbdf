import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, Modal } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import VillagesAct from "../../../VillagesAction";

const DeleteModal = ({ onClick, message }) => {
    const dispatch = useDispatch();
    const { isDelModalOpen } = useSelector(state => state);
    const onOpen = () => {
        dispatch({
            type: VillagesAct.SET_IS_DEL_MODAL_OPEN,
            payload: !isDelModalOpen
        });
    };

    return (
        <Modal onClose={onOpen} onOpen={onOpen} open={isDelModalOpen}>
            <Modal.Content image>
                <Modal.Description>
                    <Header>{message}</Header>
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={onClick} negative>
                    是
                </Button>
                <Button onClick={onOpen}>否</Button>
            </Modal.Actions>
        </Modal>
    );
};

export default DeleteModal;

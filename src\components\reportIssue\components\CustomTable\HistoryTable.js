import React from "react";
import { useSelector } from "react-redux";

// semantic ui
import { Table } from "semantic-ui-react";

// config
import { hisConfig } from "../../common/colConfig";

const headerName = [
    { text: "時間", key: hisConfig.time, uiSetting: { collapsing: true } },
    {
        text: "進度",
        key: hisConfig.status,
        uiSetting: { collapsing: true, singleLine: true }
    }
];

function HistoryTable() {
    const { hisData } = useSelector(state => state.report);

    const getTbBodyCell = (findColObj, key, data) => {
        const { collapsing, singleLine, textAlign } =
            findColObj?.uiSetting || {};

        let showVal = data[key];
        // time number to YYYY-MM-DD hh:mm:ss format
        if (key === hisConfig.time) {
            const date = new Date(data[key]);
            const [yyyyMMDD, tmpTime] = date.toISOString().split("T");
            const [hhMMSS] = tmpTime.split(".");
            showVal = `${yyyyMMDD} ${hhMMSS}`;
        }

        return (
            <Table.Cell
                key={`${key}`}
                collapsing={collapsing}
                singleLine={singleLine}
                textAlign={textAlign}
                style={{ whiteSpace: "pre" }}
            >
                {showVal}
            </Table.Cell>
        );
    };

    return (
        <Table celled padded>
            <Table.Header>
                <Table.Row>
                    {headerName.map(el => (
                        <Table.HeaderCell key={el.key} singleLine>
                            {el.text}
                        </Table.HeaderCell>
                    ))}
                </Table.Row>
            </Table.Header>

            <Table.Body>
                {hisData.history.map(data => (
                    <Table.Row key={data[hisConfig.time]}>
                        {headerName.map(tmpObj => {
                            const { key } = tmpObj;
                            if (data[key]) {
                                return getTbBodyCell(tmpObj, key, data);
                            }
                            return <Table.Cell key={`${key}`} />;
                        })}
                    </Table.Row>
                ))}
            </Table.Body>
        </Table>
    );
}

export default HistoryTable;

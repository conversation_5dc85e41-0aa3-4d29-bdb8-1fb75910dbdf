import React, { useEffect, useState, useContext } from "react";

// scss
import "./CustomInput.scss";

// plugins
import { Input } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";

//
import textConfig from "../../Utils/textConfig";
import clsName from "../../Utils/clsName";
import { isEmpty } from "../../../../../../../commons";
import { setUpdateNewsInfo, uploadFile } from "../../Utils/utils";
import { StoreContext } from "../../../../../../../store/StoreProvider";
import initColumnDef from "../../Utils/initColumnDef";

function CustomInput({ className, fusekiCol }) {
    const [globalState] = useContext(StoreContext);
    const { websiteSubject } = globalState.websiteSetting;

    const newsDispatch = useDispatch();
    const { updateNewsInfo } = useSelector(state => state);

    const [endIcon, setEndIcon] = useState(null);
    const [plholder, setPlholder] = useState("");
    const [type, setType] = useState("text");
    const [labelPos, setLabelPos] = useState("left");
    const [value, setValue] = useState("");

    useEffect(() => {
        switch (className) {
            case clsName.WebCacheEN:
            case clsName.WebCacheZH:
            case clsName.ExternalLinkEN:
            case clsName.ExternalLinkZH: {
                if (
                    [clsName.WebCacheEN, clsName.WebCacheZH].includes(className)
                ) {
                    setPlholder(textConfig.PlaceHolder_Webcache);
                } else {
                    setPlholder(textConfig.PlaceHolder_Link);
                }
                setEndIcon("linkify");
                setLabelPos("right");
                break;
            }
            case clsName.Attachment:
                setPlholder(textConfig.Header_Attachment);
                setType("file");
                break;
            case clsName.SearchInput:
                setPlholder(textConfig.PlaceHolder_Search);
                setEndIcon("search");
                setLabelPos("right");
                break;
            case clsName.PublisherName:
                setPlholder(textConfig.Header_PublisherName);
                break;
            case clsName.Provenance:
                setPlholder(textConfig.Header_Provenance);
                break;
            case clsName.HeadlineEN:
                setPlholder(textConfig.Header_TitleEN);
                break;
            case clsName.HeadlineZH:
                setPlholder(textConfig.Header_TitleZH);
                break;
            case clsName.EndPublishDate:
            case clsName.PublishDate:
                setPlholder("YYYY-MM-DD");
                setType("date");
                break;
            default:
                break;
        }
    }, []);

    useEffect(() => {
        if (isEmpty(updateNewsInfo)) return;
        setValue(updateNewsInfo[fusekiCol] || "");
    }, [updateNewsInfo]);

    const handleChange = async (evt, data) => {
        const tmpInfo = JSON.parse(JSON.stringify(updateNewsInfo));
        setValue(data.value);
        switch (className) {
            case clsName.PublishDate:
                tmpInfo.hasStartDate = data.value;
                break;
            case clsName.EndPublishDate:
                tmpInfo.hasEndDate = data.value;
                break;
            case clsName.Attachment:
                tmpInfo.allfileAvailableAt = await uploadFile(
                    evt.currentTarget,
                    websiteSubject,
                    tmpInfo[initColumnDef.fileAvailableAt],
                    "news"
                );
                break;
            case clsName.PublisherName:
                tmpInfo.newsCreator = data.value;
                break;
            case clsName.Provenance:
                tmpInfo.newsSource = data.value;
                break;
            case clsName.HeadlineEN:
                tmpInfo.titleEN = data.value;
                break;
            case clsName.HeadlineZH:
                tmpInfo.titleZH = data.value;
                break;
            case clsName.ExternalLinkZH:
                tmpInfo.externalLinkZH = data.value;
                break;
            case clsName.ExternalLinkEN:
                tmpInfo.externalLinkEN = data.value;
                break;
            case clsName.WebCacheZH:
            case clsName.WebCacheEN:
                tmpInfo[fusekiCol] = data.value;
                break;
            default:
                break;
        }
        // 寫回到updateNewsInfo
        setUpdateNewsInfo(newsDispatch, tmpInfo);
    };
    return (
        <Input
            className="NEWS_CustomInput"
            label={endIcon && { icon: endIcon }}
            labelPosition={labelPos}
            placeholder={plholder}
            onChange={handleChange}
            type={type}
            fluid
            value={value}
            accept=".pdf,.doc,.odt,.docx"
            multiple
        />
    );
}

export default CustomInput;

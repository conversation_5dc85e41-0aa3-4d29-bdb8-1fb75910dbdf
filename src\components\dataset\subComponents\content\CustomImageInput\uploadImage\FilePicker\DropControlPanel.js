import { Button } from "semantic-ui-react";
import React, { useContext, useState } from "react";

// store
import { StoreContext } from "../../../../../../../store/StoreProvider";

import { handleSaveBtn, handleDelBtn } from "./dropControlPanelHelper";

const DropControlPanel = () => {
    const [state, dispatch] = useContext(StoreContext);
    const { upload, data } = state;
    const { uploadImages, dropFolderFiles } = upload;
    const { imageEditorData } = data;
    const { currentData } = imageEditorData;
    const { defaultValue } = currentData;
    // eslint-disable-next-line no-unused-vars
    const [diffValue, setDiffValue] = useState(false);
    const [originalValue] = useState(defaultValue);

    const buttonList = {
        save: {
            name: "save",
            label: "儲存挑選的圖片",
            show: true,
            disabled:
                uploadImages.length === 0 || dropFolderFiles.checked.length === 0,
            onClick: () =>
                handleSaveBtn(state, dispatch, originalValue, setDiffValue)
        },
        delete: {
            name: "delete",
            label: "刪除勾選的圖片",
            show: false,
            disabled: true,
            onClick: () => handleDelBtn(state, dispatch)
        }
    };

    return (
        <div className="control-panel-container">
            <div className="control-panel">
                {Object.values(buttonList)
                    .filter(btn => btn.show)
                    .map((btn, idx) => (
                        <Button
                            key={idx.toString()}
                            className="control-panel-btn"
                            color="blue"
                            size="small"
                            disabled={btn.disabled}
                            onClick={() => btn.onClick()}
                        >
                            {btn.label}
                        </Button>
                    ))}
            </div>
        </div>
    );
};

export default DropControlPanel;

import React, { useContext, useEffect, useMemo, useState } from "react";
import { Form, Label } from "semantic-ui-react";
import Select, { createFilter } from "react-select";
import { StoreContext } from "../../../../../../../store/StoreProvider";
import { createConfig } from "../../../createConfig";
import { MAX_OPTION } from "../../../../../../common/sheetCrud/sheetCrudHelper";
import { readNmtlData } from "../../../../../../../api/nmtl";
import Api from "../../../../../../../api/nmtl/Api";
import { isEmpty } from "../../../../../../../commons";
import { updateObjectValue } from "../../../helper";
import Act from "../../../../../../../store/actions";

const CustomDoubleDropdownForAcc = ({
    cellId,
    createState,
    setCallback,
    defaultValue,
    subTitle,
    linkToHeader,
    itemAt,
    menuName,
    rValue,
    options,
    cloneLocalCreateState,
    setCloneLocalCreateStateFct,
    isInDraggingMode,
    curPage
}) => {
    const [state, dispatch] = useContext(StoreContext);
    const { dataset } = state.data.mainSubject.selected;
    const [firstInput, setFirstInput] = useState([]);
    const [secondInput, setSecondInput] = useState([]);
    const [newOption, setNewOption] = useState(null);
    const [allOption, setAllOption] = useState(null);

    useEffect(() => {
        if (firstInput.includes(null) || isEmpty(cloneLocalCreateState)) return;
        const otherNameArr =
            cloneLocalCreateState[
                linkToHeader === "hasAuthor" ? "authorName" : "translatorName"
            ];

        if (!isEmpty(otherNameArr)) {
            const zhValue =
                otherNameArr
                    .find(el => el.includes("@zh"))
                    ?.replace("@zh", "") || null;

            const enValue =
                otherNameArr
                    .find(el => el.includes("@en"))
                    ?.replace("@en", "") || null;

            const splitZhValue = zhValue?.split("、") || [];
            const splitEnValue = enValue?.split("、") || [];

            setFirstInput(splitZhValue);
            setSecondInput(splitEnValue);
        }
    }, [cloneLocalCreateState]);

    function reorderNames(ids, string, tmpOptions) {
        const langTag = string.match(/@zh|@en/);
        const lang = langTag ? langTag[0] : "";
        const names = string.replace(/@zh|@en/, "").split("、");

        const optionsMap = {};
        tmpOptions.forEach(option => {
            if (!optionsMap[option.id]) {
                optionsMap[option.id] = [];
            }
            optionsMap[option.id].push(option.label);
        });

        const namesToIds = {};
        // eslint-disable-next-line no-restricted-syntax
        for (const id of ids) {
            if (optionsMap[id]) {
                // eslint-disable-next-line no-restricted-syntax
                for (const label of optionsMap[id]) {
                    namesToIds[label] = id;
                }
            }
        }

        const sortedNames = ids.flatMap(id =>
            names.filter(name => namesToIds[name] === id)
        );

        const result = sortedNames.join("、") + lang;

        return result;
    }

    // 確認目前createState上的authorName或者translatorName是否與目前的其他名稱長度對應
    const isEqual = inputData => {
        if (!inputData) return false;
        const filteredArr1 = cloneLocalCreateState[linkToHeader].filter(
            item => item !== null
        );

        return filteredArr1.length === inputData.split("、").length;
    };

    const handleZhChange = value => {
        // const filterOptions = options.filter()
        const tmpValue = JSON.parse(JSON.stringify(firstInput)) || [];
        const foundValue = newOption.find(
            el => firstInput?.indexOf(el.label) > -1
        );
        if (foundValue) {
            const indexAt = firstInput.indexOf(foundValue.label);
            // tmpValue.splice(indexAt, 1);
            tmpValue[indexAt] = value ? value.label : null;
        } else {
            tmpValue.push(value.label);
        }

        const checkData = tmpValue.filter(el => el);
        const filterEnData = secondInput.filter(el => el !== null);

        setFirstInput(tmpValue);

        const zhData = !isEmpty(checkData)
            ? `${checkData.join("、")}@zh`
            : null;

        const enData = !isEmpty(filterEnData)
            ? `${filterEnData.join("、")}@en`
            : null;

        const namesInString1 = zhData
            .split("、")
            .map(name => name.replace("@zh", ""));

        const validNames = namesInString1.filter(name => {
            const option = options.find(opt => opt.label === name);
            return (
                option &&
                cloneLocalCreateState[linkToHeader].includes(option.id)
            );
        });

        const newZhData = `${validNames.join("、")}@zh`;

        if (!isEqual(newZhData)) {
            dispatch({
                type:
                    linkToHeader === "hasAuthor"
                        ? Act.DATA_IS_FILL_ALL_AUTHOR_OTHERNAME
                        : Act.DATA_IS_FILL_ALL_TRANSLATOR_OTHERNAME,
                payload: false
            });
            return;
        }

        const zhResult = reorderNames(
            cloneLocalCreateState[linkToHeader],
            newZhData,
            allOption
        );
        const enResult = reorderNames(
            cloneLocalCreateState[linkToHeader],
            enData,
            allOption
        );

        // setCallback(cellId, [zhResult, enData], menuName);
        const tmpObj = updateObjectValue(cloneLocalCreateState, cellId, [
            zhResult,
            enResult
        ]);

        dispatch({
            type:
                linkToHeader === "hasAuthor"
                    ? Act.DATA_IS_FILL_ALL_AUTHOR_OTHERNAME
                    : Act.DATA_IS_FILL_ALL_TRANSLATOR_OTHERNAME,
            payload: true
        });

        // 讓CustomDragTableView可以更新rowData
        dispatch({
            type: Act.DATA_SET_CLONE_CREATE_STATE,
            payload: tmpObj
        });

        setCloneLocalCreateStateFct(tmpObj);
    };
    //
    const handleLangChange = value => {
        const tmpValue = JSON.parse(JSON.stringify(secondInput)) || [];
        const foundValue = newOption.find(
            el => secondInput.indexOf(el.label) > -1
        );

        if (foundValue) {
            const indexAt = secondInput.indexOf(foundValue.label);
            // tmpValue.splice(indexAt, 1);

            tmpValue[indexAt] = value ? value.label : null;
        } else {
            tmpValue.push(value.label);
        }

        // 確保裡面都是有值才串成字串
        const checkData = tmpValue.filter(el => el);
        const filterZhData = firstInput.filter(el => el !== null);

        const zhData = !isEmpty(filterZhData)
            ? `${filterZhData.join("、")}@zh`
            : null;

        const enData = !isEmpty(checkData)
            ? `${checkData.join("、")}@en`
            : null;

        const namesInString1 = enData
            .split("、")
            .map(name => name.replace("@en", ""));

        const validNames = namesInString1.filter(name => {
            const option = options.find(opt => opt.label === name);
            return (
                option &&
                cloneLocalCreateState[linkToHeader].includes(option.id)
            );
        });

        const newEnData = `${validNames.join("、")}@en`;

        setSecondInput(tmpValue);

        if (!isEqual(newEnData)) {
            dispatch({
                type:
                    linkToHeader === "hasAuthor"
                        ? Act.DATA_IS_FILL_ALL_AUTHOR_OTHERNAME
                        : Act.DATA_IS_FILL_ALL_TRANSLATOR_OTHERNAME,
                payload: false
            });
            return;
        }
        const zhResult = reorderNames(
            cloneLocalCreateState[linkToHeader],
            zhData,
            allOption
        );

        const enResult = reorderNames(
            cloneLocalCreateState[linkToHeader],
            newEnData,
            allOption
        );
        // setCallback(cellId, [zhData, enResult], menuName);
        const tmpObj = updateObjectValue(cloneLocalCreateState, cellId, [
            zhResult,
            enResult
        ]);

        dispatch({
            type:
                linkToHeader === "hasAuthor"
                    ? Act.DATA_IS_FILL_ALL_AUTHOR_OTHERNAME
                    : Act.DATA_IS_FILL_ALL_TRANSLATOR_OTHERNAME,
            payload: true
        });

        // 讓CustomDragTableView可以更新rowData
        dispatch({
            type: Act.DATA_SET_CLONE_CREATE_STATE,
            payload: tmpObj
        });
        setCloneLocalCreateStateFct(tmpObj);
    };

    const getOption = async (ds, ids) => {
        if (ids === "create") {
            setNewOption([]);
            return;
        }
        const res = await readNmtlData(
            Api.getOtherNameList.replace("{ds}", ds).replace("{ids}", ids)
        );

        const opt = res.data.map(el => ({
            id: el.srcId,
            label: el.label,
            value: el.label
        }));

        setNewOption(opt);
    };

    const getAllOption = async (ds, ids) => {
        if (!ids) return;
        const filteredIds = ids.filter(i => i !== null);

        const res = await readNmtlData(
            Api.getOtherNameList
                .replace("{ds}", ds)
                .replace("{ids}", filteredIds)
        );

        // eslint-disable-next-line consistent-return
        const opt = res.data.map(el => ({
            id: el.srcId,
            label: el.label,
            value: el.label
        }));

        setAllOption(opt);
    };

    useEffect(() => {
        if (
            !cloneLocalCreateState ||
            isEmpty(cloneLocalCreateState[linkToHeader])
        ) {
            return;
        }

        getOption(dataset, cloneLocalCreateState[linkToHeader][itemAt]);
        getAllOption(dataset, cloneLocalCreateState[linkToHeader]);
    }, [itemAt, createState, cloneLocalCreateState]);

    const customStyles = {
        control: styles => ({
            ...styles,
            // boxShadow: "none",
            borderRadius: "4px",
            border: "solid 0.5px #e0e1e2"
        })
    };

    return useMemo(
        () => (
            <Form>
                <Form.Field>
                    <Label basic> {`中文版網站${subTitle || "中文"}`}</Label>
                    <div style={{ margin: "1rem 0" }}>
                        <Select
                            // isClearable
                            placeholder={createConfig.dropdownHint}
                            styles={customStyles}
                            options={
                                newOption
                                    ? newOption
                                        .filter(el => el.label !== firstInput)
                                        .slice(0, MAX_OPTION)
                                    : null
                            }
                            value={
                                firstInput && newOption
                                    ? newOption
                                        .filter(
                                            el =>
                                                firstInput.indexOf(el.label) >
                                                  -1
                                        )
                                        .slice(0, MAX_OPTION)
                                    : null
                            }
                            onChange={handleZhChange}
                            filterOption={createFilter({
                                ignoreAccents: false
                            })}
                            isDisabled={!isInDraggingMode}
                        />
                    </div>
                </Form.Field>
                <Form.Field>
                    <Label basic>{`外文版網站${subTitle || "外文"}`}</Label>
                    {/* <Divider /> */}
                    <div style={{ margin: "1rem 0" }}>
                        <Select
                            // isClearable
                            placeholder={createConfig.dropdownHint}
                            styles={customStyles}
                            options={
                                newOption
                                    ? newOption
                                        .filter(
                                            el => el.label !== secondInput
                                        )
                                        .slice(0, MAX_OPTION)
                                    : null
                            }
                            value={
                                secondInput && newOption
                                    ? newOption
                                        .filter(
                                            el =>
                                                secondInput.indexOf(
                                                    el.label
                                                ) > -1
                                        )
                                        .slice(0, MAX_OPTION)
                                    : null
                            }
                            onChange={handleLangChange}
                            filterOption={createFilter({
                                ignoreAccents: false
                            })}
                            isDisabled={!isInDraggingMode}
                        />
                    </div>
                </Form.Field>
            </Form>
        ),
        [
            cellId,
            firstInput,
            secondInput,
            createState,
            newOption,
            cloneLocalCreateState,
            allOption,
            curPage
        ]
    );
};

export default CustomDoubleDropdownForAcc;

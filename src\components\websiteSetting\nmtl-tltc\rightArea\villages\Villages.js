import React, {
    use<PERSON><PERSON>back,
    useContext,
    useEffect,
    useMemo,
    useState
} from "react";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";

// css
import "./Villages.scss";

// components
import { Button, Form, Icon, Table } from "semantic-ui-react";
import LanguageSelect from "../../../components/LanguageSelect";
import SaveButton from "../../../commons/components/SaveButton";
import EditVillages from "./components/EditVillages";
import CustomCheckBox from "./components/subComponents/CustomCheckBox/CustomCheckBox";
import SpecialAllCheckBox from "./components/subComponents/CustomCheckBox/SpecialAllCheckBox";
import VillagesCustomPagination from "./components/VillagesCustomPagination";

// store & api
import Api from "../../../../../api/nmtl/Api";
import { readNmtlData } from "../../../../../api/nmtl";
import VillagesAct from "./VillagesAction";

// utils
import { tableVillagesHeaderConfig } from "./config";
import { getReservedNewId } from "../../../../common/sheetCrud/utils";

// icons
import iconEdit from "../../../../../images/icon_edit.svg";
import CustomHeaderInformation from "../../../../dataset/subComponents/headerInformation";
import { isEmpty } from "../../../../../commons";
import useGetSubjectOPs from "../../../../common/hooks/useGetSubjectOPs";
import { StoreContext } from "../../../../../store/StoreProvider";
import { createHistoryEvent } from "../../../../downloadData/components/history/common/common";

const sortedMethod = { ASC: "ascending", DESC: "descending" };

function Villages() {
    const dispatch = useDispatch();
    const [globalState] = useContext(StoreContext);
    const [stateContext] = useContext(StoreContext);
    const { villagesIsEdited, content: ct } = useSelector(state => state);
    const { checked } = ct;
    const { user } = stateContext;
    const { displayName } = user;
    const { websiteSubject, menuActiveItem } = globalState.websiteSetting;
    const { headerActiveName } = stateContext.common;
    const { groupInfo } = stateContext.data;
    const dropOptions = useGetSubjectOPs(groupInfo);
    const [language, setLanguage] = useState("");
    // 介紹
    const [srcVillagesData, setSrcVillagesData] = useState({});
    const [villagesData, setVillagesData] = useState({});

    // 駐村列表
    const [villagesListInfo, setVillagesListInfo] = useState([]);
    // 是否點擊過刪除
    const [isDeleteVil, setIsDeleteVil] = useState(false);
    // page control
    const [curPage, setCurPage] = useState(1);
    const [perPageNum, setPerPageNum] = useState(5);

    const [sortMethod, setSortMethod] = useState(sortedMethod.DESC);

    const sheetSelectedValue = dropOptions.find(it => it.key === websiteSubject)
        ?.text;
    const columns = [headerActiveName, sheetSelectedValue, menuActiveItem.name];

    // 用 useMemo 控制, get all checked id
    const activeCheckedIds = useMemo(
        () => checked?.map(item => item.rowId) || [],
        [checked, curPage]
    );

    useEffect(() => {
        dispatch({
            type: VillagesAct.DATA_CONTENT_ROW_CHECKED_CLEAN
        });
    }, [curPage]);

    const getData = () => {
        // 待改動API
        const apiStr = Api.getTltcvillagesPageData;
        readNmtlData(apiStr).then(res => {
            if (res?.data) {
                setSrcVillagesData(res?.data[0] || {});
                setVillagesData(res?.data[0] || {});
            }
        });
    };

    useEffect(() => {
        getData();
    }, []);

    useEffect(() => {
        const fetchData = async () => {
            const apiStr = Api.getVillagesList;
            await axios.get(apiStr).then(res => {
                dispatch({
                    type: VillagesAct.SET_VILLAGE_LIST,
                    payload: res?.data?.data
                });
            });
        };
        fetchData();
    }, [villagesIsEdited]);

    useEffect(() => {
        const fetchData = async () => {
            const apiStr = Api.getVillagesListInfo;
            await axios.get(apiStr).then(res => {
                const tmpVillagesListInfo = res?.data?.data.sort(
                    (a, b) => a.lastModified - b.lastModified
                );
                setVillagesListInfo(tmpVillagesListInfo);
                dispatch({
                    type: VillagesAct.DATA_SET_ROWS,
                    payload: tmpVillagesListInfo
                });
            });
        };
        fetchData();
    }, [isDeleteVil, villagesIsEdited]);

    const checkBoxCell = idx => (
        <Table.Cell textAlign="center" collapsing>
            {/* key 必須是唯一的如果重複將會被視為相同 element
                     也會造成不進行任何更動，也就是數值不會改變的 issue
                     怎麼設定都可以就是不可以相同 */}
            <CustomCheckBox
                key={`$content-checkbox-${idx}-${activeCheckedIds.indexOf(
                    idx
                ) !== -1}`}
                rowId={idx}
                isChecked={activeCheckedIds.indexOf(`${idx}`) !== -1}
            />
        </Table.Cell>
    );

    const handleAdd = async () => {
        const getD = async () => {
            const vilId = await getReservedNewId("VillageEvent");
            return vilId;
        };
        const vilId = await getD();

        dispatch({
            type: VillagesAct.SET_EDITING_VILLAGEID,
            payload: vilId
        });
        dispatch({
            type: VillagesAct.SET_IS_EDITED_VILLAGES,
            payload: true
        });
    };

    const handleDelete = async () => {
        const curPageData = villagesListInfo.slice(
            (curPage - 1) * perPageNum,
            curPage * perPageNum
        );
        const chooseIds = activeCheckedIds.map(i => curPageData[i]?.id);
        const leftData = villagesListInfo
            .filter(item => !chooseIds.includes(item.id))
            .map(i => i.id);
        const ogVilId = villagesListInfo.map(i => i.id);

        const entrySrc = {
            graph: "settings",
            classType: "FrontEdit",
            srcId: "CARDtltc",
            value: {
                hasVillageEvent: [...ogVilId]
            }
        };
        const entryDst = {
            graph: "settings",
            classType: "FrontEdit",
            srcId: "CARDtltc",
            value: {
                hasVillageEvent: [...leftData]
            }
        };

        await axios.put(Api.getGeneric, { entrySrc, entryDst });

        const historyMsg = `${JSON.stringify(entryDst)}`;

        // 建立歷史紀錄
        createHistoryEvent(
            displayName,
            "刪除",
            `${columns.join("/")}：${historyMsg}`
        );

        setIsDeleteVil(prev => !prev);
    };

    const handleEdit = el => {
        dispatch({
            type: VillagesAct.SET_IS_EDITED_VILLAGES,
            payload: true
        });
        dispatch({
            type: VillagesAct.SET_EDITING_VILLAGEID,
            payload: el.id
        });
    };

    const handleSort = item => {
        if (item.header !== "最後修改時間") return;

        setSortMethod(prev =>
            prev === sortedMethod.ASC ? sortedMethod.DESC : sortedMethod.ASC
        );

        const tmpSortMethod =
            sortMethod === sortedMethod.ASC
                ? sortedMethod.DESC
                : sortedMethod.ASC;

        villagesListInfo.sort((a, b) =>
            tmpSortMethod !== sortedMethod.ASC
                ? a.lastModified - b.lastModified
                : b.lastModified - a.lastModified
        );
    };

    // 用useCallback減少component UpdateText re-render問題
    const handleChange = useCallback(
        evt => {
            if (language === "zh") {
                setVillagesData({
                    ...villagesData,
                    villageDescZH: evt.target.value
                });
            } else {
                setVillagesData({
                    ...villagesData,
                    villageDescEN: evt.target.value
                });
            }
        },
        [language, villagesData]
    );

    function unixTimeToYYYYMMDD(unixTimeMs) {
        if (isEmpty(unixTimeMs)) return;
        const date = new Date(+unixTimeMs);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");

        // eslint-disable-next-line consistent-return
        return `${year}-${month}-${day}`;
    }

    return (
        <>
            {!villagesIsEdited ? (
                <div className="Villages">
                    <div className="topArea">
                        <h1>譯者駐村簡介</h1>
                        <LanguageSelect
                            language={language}
                            setLanguage={setLanguage}
                        />
                    </div>
                    <div className="textArea">
                        <Form style={{ height: "100%", width: "100%" }}>
                            <textarea
                                style={{ resize: "none", height: "100%" }}
                                // disabled={isEditedDisable}
                                value={
                                    language === "zh"
                                        ? villagesData.villageDescZH
                                        : villagesData.villageDescEN
                                }
                                onChange={handleChange}
                            />
                        </Form>
                    </div>
                    <div className="btnArea">
                        <SaveButton
                            srcData={srcVillagesData}
                            dstData={villagesData}
                            closeCallBack={getData}
                        />
                    </div>
                    <div className="topline">
                        <h1>活動列表</h1>
                        <div>
                            <Button
                                onClick={handleAdd}
                                color="green"
                                style={{ marginRight: "1rem" }}
                            >
                                新增
                            </Button>
                            <Button onClick={handleDelete} color="red">
                                刪除
                            </Button>
                        </div>
                    </div>
                    <div className="bookArea">
                        <Table celled structured size="small" selectable>
                            <Table.Header>
                                <Table.Row>
                                    <Table.HeaderCell
                                        key="checkbox"
                                        width={2}
                                        textAlign="center"
                                    >
                                        <SpecialAllCheckBox
                                            isChecked={
                                                activeCheckedIds.length > 0 &&
                                                activeCheckedIds.length ===
                                                    checked.length
                                            }
                                        />
                                    </Table.HeaderCell>
                                    {tableVillagesHeaderConfig.map(i => (
                                        <Table.HeaderCell
                                            width={i.row}
                                            key={i.header}
                                            onClick={() => handleSort(i)}
                                            textAlign={i.display}
                                        >
                                            {i.header}

                                            {i.header === "最後修改時間" && (
                                                <>
                                                    <CustomHeaderInformation
                                                        id={i.header}
                                                    />
                                                    <Icon
                                                        name={
                                                            sortMethod ===
                                                            sortedMethod.ASC
                                                                ? "sort up"
                                                                : "sort down"
                                                        }
                                                        link
                                                    />
                                                </>
                                            )}
                                        </Table.HeaderCell>
                                    ))}
                                </Table.Row>
                            </Table.Header>
                            <Table.Body>
                                {villagesListInfo &&
                                    villagesListInfo
                                        .slice(
                                            (curPage - 1) * perPageNum,
                                            curPage * perPageNum
                                        )
                                        .map((i, ctIdx) => (
                                            <Table.Row key={i.id}>
                                                {checkBoxCell(ctIdx)}

                                                <Table.Cell>
                                                    {i.label}
                                                </Table.Cell>
                                                <Table.Cell>
                                                    {unixTimeToYYYYMMDD(
                                                        i.lastModified
                                                    )}
                                                </Table.Cell>
                                                <Table.Cell
                                                    textAlign="center"
                                                    verticalAlign="middle"
                                                >
                                                    <Button
                                                        style={{
                                                            backgroundColor:
                                                                "initial",
                                                            margin: "0",
                                                            padding: "0"
                                                        }}
                                                        onClick={() => {
                                                            handleEdit(i);
                                                        }}
                                                    >
                                                        <img
                                                            src={iconEdit}
                                                            alt="Edit"
                                                        />
                                                    </Button>
                                                </Table.Cell>
                                            </Table.Row>
                                        ))}
                            </Table.Body>
                        </Table>
                    </div>
                    <VillagesCustomPagination
                        data={villagesListInfo}
                        id="editVillages"
                        setCurPage={setCurPage}
                        setPerPageNum={setPerPageNum}
                        curPage={curPage}
                        perPageNum={perPageNum}
                    />
                </div>
            ) : (
                <EditVillages />
            )}
        </>
    );
}

export default Villages;

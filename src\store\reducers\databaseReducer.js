import Act from '../actions';

const initState = {
    dbFiles: [],
    UPLOAD_DB_FILE_CONFIG: {
        maxCount: 1,
        ACCEPTABLE_MINE_TYPE: [
            'application/gz',
            'application/gzip',
            'application/trig',
            'application/x-gzip',
        ],
        ACCEPTABLE_EXT: ['gz', 'gzip', 'trig', 'x-gzip'],
    },
    dbFileUpload: {
        isUploading: false,
    },
    dbFileDownload: {
        isDownloading: false,
    },
    databaseMsg: {
        type: '',
        title: '',
        text: '',
    },
};

const databaseReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.INITIAL_DB_UPLOAD_STATE:
            return {
                ...state,
                dbFiles: initState.files,
                dbFileUpload: initState.dbFileUpload,
            };
        case Act.UPLOAD_DB_FILE:
            return { ...state, dbFiles: action.payload };
        case Act.SAVE_UPLOAD_DB_FILE:
            return {
                ...state,
                dbFileUpload: {
                    ...state.dbFileUpload,
                    isUploading: action.payload,
                },
            };
        case Act.DOWNLOAD_DB_FILE:
            return {
                ...state,
                dbFileDownload: {
                    ...state.dbFileDownload,
                    isDownloading: action.payload,
                },
            };
        case Act.DATABASE_MSG:
            return { ...state, databaseMsg: action.payload };
        default:
            return state;
    }
};

export default databaseReducer;

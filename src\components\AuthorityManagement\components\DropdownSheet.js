import React, { useContext, useEffect, useState } from 'react';

// ui
import { Dropdown, Loader } from 'semantic-ui-react';

// cloud
import { getAuthoritySheets } from '../../../api/firebase/cloudFirestore';

// store
import { StoreContext } from '../../../store/StoreProvider';
import Act from '../../../store/actions';
import { sheetMapping } from '../../common/sheetCrud/utils';
import { filterSheet } from '../../../commons/filterGroup';
import { isEmpty } from '../../../commons';

const DropdownSheet = () => {
  const [state, dispatch] = useContext(StoreContext);
  const { sheet, groupInfo } = state.data;
  const { value: keepSheetValue } = sheet.selected;

  const [sheets, setSheets] = useState(undefined);
  const [filterSheets, setFilterSheets] = useState(undefined);
  const [error, setError] = useState(undefined);

  const handleGetSheets = async () => {
    const sheetsData = await getAuthoritySheets();
    if (!sheetsData.error) {
      // set to sheets
      setSheets(sheetMapping(sheetsData));
    } else {
      setError(sheetsData.error);
    }
  };

  useEffect(() => {
    handleGetSheets();
  }, []);

  useEffect(() => {
    if (!sheets || isEmpty(groupInfo)) return;
    // [新增] filterSheet功能 - 20230202
    const tmpFilter = sheets.filter((tmpSheet) => filterSheet(tmpSheet, groupInfo));
    setFilterSheets(tmpFilter);
  }, [sheets, groupInfo]);

  const handleClick = (event, { value }) => {
    // get selected item
    const item = sheets.filter((it) => it.value === value)[0];
    // set sheet
    if (item) {
      // 判斷 sheet 中需不需要分頁
      if (item?.hasTab) {
        const tab = Object.values(item?.hasTab).sort((itemA, itemB) => itemA.order - itemB.order);

        dispatch({
          type: Act.DATA_SHEET,
          payload: {
            ...item,
            hasTab: tab,
          },
        });
      } else {
        dispatch({
          type: Act.DATA_SHEET,
          payload: item,
        });
      }
    }
    // refresh all parameter
    // refresh pagination when sheet changed
    dispatch({ type: Act.DATA_PAGINATION_ACTIVE_PAGE_INIT });
    // refresh changed when sheet changed
    dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });
    // refresh checked when sheet changed
    dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });
    // refresh created when sheet changed
    dispatch({ type: Act.DATA_CONTENT_ROW_CREATED_CLEAN });
    // refresh created when sheet changed
    dispatch({ type: Act.DATA_SEARCH_KEYWORD_CLEAN });
    // clean content when sheet changed
    dispatch({ type: Act.DATA_SORTED_CLEAN });
    dispatch({ type: Act.DATA_TAB_COUNT_SHEET_CLEAN });
    dispatch({ type: Act.DATA_TAB_KEY_CLEAN });
    // clean content
    dispatch({ type: Act.DATA_CONTENT_ROWS_CLEAN });
  };

  if (error) {
    return <span>{error}</span>;
  }

  if (filterSheets) {
    return (
      <Dropdown
        fluid
        search
        selection
        value={keepSheetValue}
        options={filterSheets.map((s) => ({
          key: s.key,
          value: s.value,
          text: s.text,
        }))}
        onChange={handleClick}
        placeholder="權威表單"
        className="DropdownAuthoritySheet"
      />
    );
  }

  return <Loader active inline="centered" />;
};

export default DropdownSheet;

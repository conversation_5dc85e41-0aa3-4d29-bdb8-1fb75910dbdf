import React, { useContext, useEffect, useMemo, useState } from 'react';

// store
import { components, createFilter } from 'react-select';
import CreatableSelect from 'react-select/creatable';
import { WindowedMenuList } from 'react-windowed-select';
import { StoreContext } from '../../../../../store/StoreProvider';
import { isEmpty } from '../../../../../commons';
import Act from '../../../../../store/actions';
import Api from '../../../../../api/nmtl/Api';
import getKeyBySingle from '../../../../../api/nmtl/ApiKey';
import getSingleByApi from '../../../../../api/nmtl/ApiSingle';
import { getReservedNewId, specialConvertToOption } from '../../../../common/sheetCrud/utils';
import { createNmtlData } from '../../../../../api/nmtl';
import { isCorrectSuffix } from '../../../../../api/nmtl/ApiField';
import { getMenuPlacement } from '../../../datasetConfig';
// import { MAX_OPTION } from "../../../../common/sheetCrud/sheetCrudHelper";
// import MenuList from "../MenuList";
// import { findSameIdList } from "../../CreateComp/helper";
// import { sheetCreateView } from "../../CreateComp/createConfig";
import ExistedModal from './ExistedModal';
import DropdownEditModal from './DropdownEditModal';
import { findSameIdList } from '../../CreateComp/helper';

// custom ui

// common

// api
// 可以新增

// 關閉偵測同一id的彈跳視窗
const showExistedModal = true;
const CustomMultiDropdownCreatable = ({
    cellId,
    rowId,
    // idx = 0,
    createState,
    setCallback,
    defaultValue,
    // isShowId,
    menuName,
}) => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { mainSubject, sheet } = state.data;
    const { headerFields } = sheet;
    const { dataset } = mainSubject.selected;
    const { key: sheetName } = sheet.selected;
    const [inputValue, setInputValue] = useState(defaultValue);
    const [option, setOption] = useState(null);
    const [equalValue, setEqualValue] = useState(null);
    const [open, setOpen] = useState(false);
    const [editId, setEditId] = useState(false);
    const [editModalOpen, setEditModalOpen] = useState(false);

    // console.log(cellId, defaultValue, menuName, inputValue);
    // set true when input and default vale are diff
    // const [isDiffValue, setDiffValue] = useState(false);

    const MultiValueLabel = (props) => (
        <div
            onMouseDown={(e) => {
                e.stopPropagation();
                // 儲存目前編輯chip的id
                setEditId(props.data.id);
                setEditModalOpen(true);
            }}
        >
            <components.MultiValueLabel {...props}>{props.children}</components.MultiValueLabel>
        </div>
    );

    const handleChange = (selectValue) => {
        // selectValue: [{ id: "PER96766", label: "施仁思@PER", value: "PER96766" }...]
        const cellValue = isEmpty(selectValue) ? [] : selectValue;
        // extract and combine id, e.g. ORG1/ORG2
        const selectCombinedIds = cellValue.map((item) => item.id).sort();

        // keep input value when it changed
        if (cellValue.length === 0) {
            // clear
            setCallback(cellId, null, menuName);
        } else {
            // const tmpValue =
            //     createState && createState[cellId]
            //         ? [...createState[cellId], ...selectCombinedIds]
            //         : [...selectCombinedIds];
            setCallback(cellId, selectCombinedIds, menuName);
        }
    };

    const handleInputChange = (input) => {
        if (!input) {
            return;
        }
        // setCallback(cellId, rowId, idx, { isOption: true, input: inputValue });
        setInputValue(input);
    };

    const newOptions = useMemo(() => {
        if (!headerFields) {
            return [];
        }
        return specialConvertToOption(cellId, headerFields) || [];
    }, [cellId, headerFields]);

    const getCreateNmtlItemResult = async (item, newClass, newApi) => {
        if (isEmpty(item) || isEmpty(dataset) || isEmpty(sheetName)) {
            return { newSrcId: null };
        }

        const apiUrl = Api.getGeneric;
        const itemKey = getKeyBySingle(getSingleByApi(newApi));

        // 先 reserved id
        const newSrcId = await getReservedNewId(itemKey);
        if (!newSrcId) {
            dispatch({
                type: Act.DATA_INFO_MESSAGE,
                payload: {
                    title: `${cellId} 欄位請填入正確後綴，如：@PER, @ORG 或 @EVT`,
                    error: 1,
                    renderSignal: `update-${new Date().getTime()}`,
                },
            });
            return { newSrcId: null };
        }

        if (apiUrl && itemKey) {
            const newItem = {
                graph: dataset,
                classType: newClass || itemKey,
                srcId: newSrcId || '',
                value: {
                    label: [item, item.replace('@zh', '@en')],
                },
            };

            const createResult = await createNmtlData(
                user,
                apiUrl,
                dataset,
                sheetName,
                newItem,
            ).then((res) => res === 'OK');

            return { newSrcId, createResult };
        }
        return { newSrcId: null };
    };

    const handleCreate = async (input) => {
        // // 移除@zh、@PER、@en等
        function removeAfterFirstAt(s) {
            const atPosition = s.indexOf('@');
            if (atPosition === -1) {
                return s;
            }
            return s.slice(0, atPosition);
        }

        const sameList = await findSameIdList(
            removeAfterFirstAt(input),
            sheetName,
            Api.getSameListWithGraphForPerOrg,
            // sheetCreateView[sheetName].apiPath
        );

        if (!isEmpty(sameList)) {
            setEqualValue(sameList);
            setOpen(true);
            return;
        }
        // suffix feature
        const { isSuffix, newValue, newClass, newApi } = isCorrectSuffix(cellId, input);

        if (!isSuffix) {
            dispatch({
                type: Act.DATA_INFO_MESSAGE,
                payload: {
                    title: `${cellId} 欄位請填入正確後綴，如：@PER, @ORG 或 @EVT`,
                    error: 1,
                    renderSignal: `update-${new Date().getTime()}`,
                },
            });
            return;
        }

        const { newSrcId } = await getCreateNmtlItemResult(newValue, newClass, newApi);

        // setCallback(cellId, rowId, idx, { isOption: true, isLoading: true });
        if (newSrcId) {
            const newOptZh = {
                id: newSrcId,
                label: input,
                value: newSrcId,
                graph: dataset,
            };
            const newOptEn = {
                id: newSrcId,
                label: input.replace('@zh', '@en'),
                value: newSrcId,
                graph: dataset,
            };
            // 如果此欄位為多重 type，增加到該 type
            // hasPublisher: ORG, PER
            if (Object.keys(headerFields).indexOf(newApi) > -1) {
                headerFields[newApi].push(newOptZh);
                headerFields[newApi].push(newOptEn);
            }

            const tmpValue =
                createState && createState[cellId]
                    ? [...createState[cellId], newOptZh.id]
                    : [newOptZh.id];

            const tmpNewOpts = [newOptZh, newOptEn];
            setOption(tmpNewOpts);
            setCallback(cellId, tmpValue, menuName);
        }
    };

    const customStyles = {
        container: (styles) => ({
            ...styles,
            margin: '-9px',
            minWidth: '100%',
            width: '300px',
        }),
        control: (styles, { selectProps: { controlColor } }) => ({
            ...styles,
            borderStyle: 'none',
            borderRadius: 'unset',
            backgroundColor: controlColor,
        }),
    };

    const customPlacement = getMenuPlacement(rowId);
    // const customControlBgColor = isDiffValue ? "#f9c09a66" : "";

    useEffect(() => {
        if (!option) return;
        newOptions.push(...option);
    }, [option]);

    const removeBrackets = (option) => option.replace(/\s*\([^)]*\)\s*/g, '');

    return useMemo(() => {
        const seenLabels = new Set();
        const filteredOptions =
            createState && createState[cellId]
                ? newOptions
                      .filter(
                          (o) =>
                              createState &&
                              createState[cellId]?.indexOf(o.id) > -1 &&
                              o.graph === 'tltc',
                      )
                      .map((option) => ({
                          ...option,
                          label: removeBrackets(option.label),
                      }))
                      .filter((option) => {
                        if (seenLabels.has(option.label)) {
                            return false;
                        }
                          seenLabels.add(option.label);
                        return true;
                      })
                : null;

        return (
            <>
                <CreatableSelect
                    isMulti
                    isClearable
                    styles={customStyles}
                    options={newOptions.filter((o) => o.graph === 'tltc')}
                    value={filteredOptions}
                    onChange={handleChange}
                    onInputChange={handleInputChange}
                    onCreateOption={handleCreate}
                    components={{
                        MenuList: WindowedMenuList,
                        MultiValueLabel,
                    }}
                    menuPlacement={customPlacement}
                    filterOption={createFilter({ ignoreAccents: false })}
                />
                {/* 關閉偵測同一id的彈跳視窗 */}
                {showExistedModal && (
                    <ExistedModal
                        equalValue={equalValue}
                        open={open}
                        setOpen={setOpen}
                        cellId={cellId}
                        newOptions={newOptions}
                        setCallback={setCallback}
                        menuName={menuName}
                        createState={createState}
                        headerFields={headerFields}
                    />
                )}
                <DropdownEditModal
                    id={editId}
                    onClick={() => {
                        setEditModalOpen((prev) => !prev);
                    }}
                    open={editModalOpen}
                    options={newOptions.filter((o) => o.graph === 'tltc')}
                    cellId={cellId}
                />
            </>
        );
    }, [cellId, createState, inputValue, newOptions, menuName, open, editModalOpen]);
};

export default CustomMultiDropdownCreatable;

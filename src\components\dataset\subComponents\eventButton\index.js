import React, { useContext, useMemo } from "react";

// custom button
import UpdateButton from "./buttons/UpdateButton";
import DeleteButton from "./buttons/DeleteButton";
import CreateButton from "./buttons/CreateButton";
import HeaderButton from "./buttons/HeaderButton";
import DownloadButton from "./buttons/DownloadButton";
import MergeButton from "./buttons/MergeButton";
import AuthorityCopyButton from "./authorityButtons/CopyButton";
import AuthorityCreateButton from "./authorityButtons/CreateButton";
import AuthorityMergeButton from "./authorityButtons/MergeButton";
import AuthorityUpdateButton from "./authorityButtons/UpdateButton";
// FIXME: disable import temporary.
// import ImportButton from "./buttons/ImportButton";

// store
import { StoreContext } from "../../../../store/StoreProvider";

// config
import role from "../../../../App-role";

// FIXME: when table value chagned, this component is changing as well.
// Why useMemo is ineffective?
const EventButton = () => {
    // eslint-disable-next-line no-unused-vars
    const [state] = useContext(StoreContext);
    const { user } = state;
    const { mainSubject, sheet } = state.data;
    const { dataset } = mainSubject.selected;
    const { key: sheetName } = sheet.selected;

    const safeRole = user?.role || role.anonymous;
    const isAuthority = dataset === "authority" && sheetName === "BasicInfo";

    // admin
    const admin = isAuthority ? (
        <React.Fragment>
            <DownloadButton />
            <DeleteButton />
            <AuthorityUpdateButton />
            <AuthorityMergeButton />
            <AuthorityCopyButton />
            <AuthorityCreateButton />
            <HeaderButton />
        </React.Fragment>
    ) : (
        <React.Fragment>
            {/* FIXME: disable import temporary. */}
            {/* <ImportButton /> */}
            <DownloadButton />
            <DeleteButton />
            <UpdateButton />
            <MergeButton />
            <CreateButton />
            <HeaderButton />
        </React.Fragment>
    );

    // developer
    const developer = isAuthority ? (
        <React.Fragment>
            <DownloadButton />
            <DeleteButton />
            <AuthorityUpdateButton />
            <AuthorityMergeButton />
            <AuthorityCopyButton />
            <AuthorityCreateButton />
            <HeaderButton />
        </React.Fragment>
    ) : (
        <React.Fragment>
            {/* FIXME: disable import temporary. */}
            {/* <ImportButton /> */}
            <DownloadButton />
            <DeleteButton />
            <UpdateButton />
            <MergeButton />
            <CreateButton />
            <HeaderButton />
        </React.Fragment>
    );

    // editor
    const editor = (
        <React.Fragment>
            {/* FIXME: disable import temporary. */}
            {/* <ImportButton /> */}
            <DownloadButton />
            <DeleteButton />
            <UpdateButton />
            <MergeButton />
            <CreateButton />
            <HeaderButton />
        </React.Fragment>
    );

    // reader
    const reader = (
        <React.Fragment>
            <HeaderButton />
        </React.Fragment>
    );

    // anonymous
    const anonymous = (
        <React.Fragment>
            <HeaderButton />
        </React.Fragment>
    );

    // 根據角色返回不同的按鈕組
    const buttons = {
        admin,
        developer,
        editor,
        reader,
        anonymous
    };

    // 使用 useMemo 來記憶按鈕組結果
    const ButtonGroup = useMemo(() => buttons[safeRole], [safeRole, dataset, sheetName]);

    return <div>{ButtonGroup}</div>;
};

export default EventButton;

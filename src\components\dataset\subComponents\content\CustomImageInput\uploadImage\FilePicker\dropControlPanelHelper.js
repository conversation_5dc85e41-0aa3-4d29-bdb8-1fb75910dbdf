import axios from "axios";
import { useDispatch } from "react-redux";
import Act from "../../../../../../../store/actions";
import FileAct from "../../../../../../../reduxStore/file/fileAction";
import {
    // baseUrl,
    fileServerAPI,
    fileServerMethod
} from "../../../../../../../api/fileServer";
import uploadConfig from "../../../../../../toolPages/components/upload/uploadConfig";

export const handleSaveBtn = (state, dispatch, originalValue, setDiffValue) => {
    const dispatchRedux = useDispatch();
    const { upload, data } = state;
    const {
        uploadImages,
        imageFormData,
        currentFolder,
        dropFolderFiles
    } = upload;
    const { imageEditorData } = data;
    const { currentData } = imageEditorData;
    // eslint-disable-next-line no-unused-vars
    const { rowId, cellId, defaultValue, setImgSrcValue } = currentData;
    if (uploadImages.length === 0) {
        return dispatchRedux({
            type: FileAct.PICKER_DROP_MESSAGE,
            payload: {
                type: "warning",
                title: "尚未選取檔案"
            }
        });
    }
    if (currentFolder.path.length === 0) {
        return dispatchRedux({
            type: FileAct.PICKER_DROP_MESSAGE,
            payload: {
                type: "warning",
                title: "尚未選取要上傳的資料夾"
            }
        });
    }
    if (dropFolderFiles.checked.length === 0) {
        return dispatchRedux({
            type: FileAct.PICKER_DROP_MESSAGE,
            payload: {
                type: "warning",
                title: "尚未勾選檔案"
            }
        });
    }

    // 只能挑選一張圖片, 製作 formData
    const formData = new FormData();
    const singleImage = uploadImages.filter(
        i => i.url === dropFolderFiles.checked[0].value
    );
    formData.append(uploadConfig.ImageFormName, singleImage);

    return axios({
        method: fileServerMethod.uploadImage,
        url: `${fileServerAPI.uploadImage}/${currentFolder.path}`,
        headers: {
            "Access-Control-Allow-Origin": "*"
        },
        data: imageFormData
    })
        .then(res => {
            if (res.status === 200 && res.data.status === "success") {
                dispatch({
                    type: Act.UPLOAD_IMAGE,
                    payload: Object.assign([])
                });
                // 儲存圖片 url 至 global state 該筆資料
                // const imgUploadSrc = `${fileServerAPI.readUploadImage}${
                //     currentFolder.path
                // }/${singleImage[0].name}`;
                const imgUploadSrc = `${fileServerAPI.readUploadImage}${currentFolder.path}/${singleImage[0].name}`;

                // update diff state
                setDiffValue(true);
                // change img src in table
                setImgSrcValue(imgUploadSrc);
                // set selected image to currentValue
                dispatch({
                    type: Act.IMAGE_EDITOR_CURRENT_DATA,
                    payload: {
                        rowId,
                        cellId,
                        defaultValue: originalValue,
                        currentValue: imgUploadSrc,
                        setImgSrcValue
                    }
                });
                // add changed data to data.content.changed
                dispatch({
                    type: Act.DATA_CONTENT_ROW_CHANGED,
                    payload: {
                        rowId,
                        cellId,
                        cellData: imgUploadSrc
                    }
                });

                return dispatchRedux({
                    type: FileAct.PICKER_DROP_MESSAGE,
                    payload: {
                        type: "success",
                        title: "檔案上傳成功"
                    }
                });
            }
            return dispatchRedux({
                type: FileAct.PICKER_DROP_MESSAGE,
                payload: {
                    type: "error",
                    title: "檔案上傳失敗，請稍後再試"
                }
            });
        })
        .catch(err =>
            dispatchRedux({
                type: FileAct.PICKER_DROP_MESSAGE,
                payload: {
                    type: "error",
                    title: "檔案上傳失敗，請稍後再試",
                    text: `error: ${err.message}`
                }
            })
        );
};

// eslint-disable-next-line no-unused-vars
export const handleDelBtn = (state, dispatch) => {
    const dispatchRedux = useDispatch();
    const { upload } = state;
    const { uploadImages, dropFolderFiles } = upload;

    const imgUrlChecked = dropFolderFiles.checked.map(dff => dff.value);
    const fileterImgs = uploadImages.filter(
        i => !imgUrlChecked.includes(i.url)
    );
    dispatch({
        type: Act.UPLOAD_IMAGE,
        payload: fileterImgs
    });
    // alert message
    dispatchRedux({
        type: FileAct.PICKER_DROP_MESSAGE,
        payload: {
            type: "success",
            title: "已清除檔案"
        }
    });
};

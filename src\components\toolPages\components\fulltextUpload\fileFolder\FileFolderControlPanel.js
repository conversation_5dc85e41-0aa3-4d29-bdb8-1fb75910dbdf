import { Button } from "semantic-ui-react";
import React, { useContext } from "react";
import axios from "axios";

// store
import { useDispatch } from "react-redux";
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

// API
import {
    fileServerAPI,
    fileServerMethod,
    // eslint-disable-next-line import/named
    fileServerApiRoute
} from "../../../../../api/fileServer";
import { createHistoryEvent } from "../../../../downloadData/components/history/common/common";
import uploadConfig from "../../upload/uploadConfig";
import FileAct from "../../../../../reduxStore/file/fileAction";

const FileFolderControlPanel = () => {
    const dispatchRedux = useDispatch();
    const [state, dispatch] = useContext(StoreContext);
    const { upload } = state;
    const { curFolderFiles, curFolderFilesUrl, currentFolder } = upload;
    const { headerActiveName, toolPagesActiveName } = state.common;
    const { displayName } = state.user;
    const columns = [headerActiveName, toolPagesActiveName];
    const { role } = state.user;

    const handleDelBtn = () => {
        if (curFolderFiles.checked.length === 0) {
            return dispatchRedux({
                type: FileAct.PICKER_CUR_FOLDER_MESSAGE,
                payload: {
                    type: "warning",
                    title: "尚未選取檔案",
                    text: ""
                }
            });
        }

        const middlePath = fileServerApiRoute.readUploadImage;
        const images = curFolderFiles.checked
            .map(f => {
                const { value, actiontype } = f;
                if (actiontype === uploadConfig.image) {
                    const urlSplit = value.split(middlePath);
                    // 取得 /read/upload 後面的字串,並去掉 420x420_(或 200x200_等字串)
                    return (
                        urlSplit.length > 1 &&
                        urlSplit[1].replace(/[0-9]{3}[xX×╳][0-9]{3}_/, "")
                    );
                }
                if (actiontype === uploadConfig.file) {
                    return value;
                }
                return false;
            })
            .filter(url => url !== false);

        return axios({
            method: fileServerMethod.deleteImage,
            url: fileServerAPI.deleteImage,
            headers: {
                "Access-Control-Allow-Origin": "*"
            },
            data: {
                images
            }
        })
            .then(res => {
                if (res.status === 200 && res.data.status === "success") {
                    // 從 curFolderFilesUrl 刪除已處理的資料
                    // const filesRemain = curFolderFilesUrl.filter(
                    //     cff => !imgUrlChecked.includes(cff.url)
                    // );
                    const filesRemain = curFolderFilesUrl.filter(cff => {
                        const { type, originalUrl, imgFileName } = cff;
                        let furl;
                        if (type === uploadConfig.image) {
                            // eslint-disable-next-line prefer-destructuring
                            furl = originalUrl.split(middlePath)[1];
                        } else if (type === uploadConfig.file) {
                            furl = imgFileName;
                        }
                        return images.indexOf(furl) < 0;
                    });

                    dispatch({
                        type: Act.FOLDER_FILES_URL,
                        payload: Object.assign([], filesRemain)
                    });
                    // curFolderFiles.checked 清空
                    dispatch({
                        type: Act.CUR_FOLDER_FILES_STATUS,
                        payload: { original: [], checked: [] }
                    });
                    createHistoryEvent(
                        displayName,
                        "刪除",
                        `${columns.join("/")}${currentFolder.path_ch}`
                    );
                    return dispatchRedux({
                        type: FileAct.PICKER_CUR_FOLDER_MESSAGE,
                        payload: {
                            type: "success",
                            title: "已刪除檔案",
                            text: ""
                        }
                    });
                }
                createHistoryEvent(
                    displayName,
                    "刪除失敗",
                    `${columns.join("/")}${currentFolder.path_ch}`
                );
                return dispatchRedux({
                    type: FileAct.PICKER_CUR_FOLDER_MESSAGE,
                    payload: {
                        type: "error",
                        title: "處理失敗，請稍後再試",
                        text: ""
                    }
                });
            })
            .catch(err => {
                dispatchRedux({
                    type: FileAct.PICKER_CUR_FOLDER_MESSAGE,
                    payload: {
                        type: "error",
                        title: "處理失敗，請稍後再試",
                        text: `error: ${err.message}`
                    }
                });
                createHistoryEvent(
                    displayName,
                    "刪除失敗",
                    `${columns.join("/")}${currentFolder.path_ch}`
                );
            });
    };

    return (
        <div className="control-panel-container">
            <div className="control-panel">
                <Button
                    className="control-panel-btn"
                    color="orange"
                    size="small"
                    disabled={
                        (curFolderFiles &&
                            curFolderFiles.checked &&
                            curFolderFiles.checked.length === 0) ||
                        role === "reader"
                    }
                    onClick={handleDelBtn}
                >
                    刪除勾選檔案
                </Button>
            </div>
        </div>
    );
};

export default FileFolderControlPanel;

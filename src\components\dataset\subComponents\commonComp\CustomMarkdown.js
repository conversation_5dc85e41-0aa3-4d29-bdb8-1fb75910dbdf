import React, { useContext, useState, useMemo, useEffect } from 'react';

// ui
import { Input, Modal, Button } from 'semantic-ui-react';

// store
import { StoreContext } from '../../../../store/StoreProvider';
import Act from '../../../../store/actions';

// components
import CustomMarkdownEditor from '../../../common/CustomMarkdownEditor/CustomMarkdownEditor';

import htmlToMarkDown from '../../../../commons/htmlToMD';
import { openImageUpload } from '../../datasetConfig';
import { getLangTag } from '../../../common/sheetCrud/utils';

const CustomMarkdown = ({
  rowId,
  cellId,
  idx,
  defaultValue,
  createState,
  setCallback,
  useEditor = false,
  readMode = false,
  isDiffValue = false,
  ...rest
}) => {
  const [state, dispatch] = useContext(StoreContext);
  // get dataset(mainSubject) and sheet
  const { sheet, mainSubject } = state.data;
  const [open, setOpen] = useState(false);
  const [curValue, setCurValue] = useState(htmlToMarkDown(defaultValue));
  const [selectYes, setSelectYes] = useState(false);

  const setValue = (value) => {
    const cellValue = { ...createState, ...{ [idx]: value } };

    if (value !== defaultValue) {
      // dispatch
      dispatch({
        type: Act.DATA_CONTENT_ROW_CHANGED,
        payload: {
          rowId,
          cellId,
          idx,
          cellValue,
        },
      });
    } else {
      // 如果有值又沒有改變，不做修正
      if (value && value.length > 0) {
        const unchangedValue = {
          ...createState,
          ...{ [idx]: defaultValue },
        };
        dispatch({
          type: Act.DATA_CONTENT_ROW_NO_CHANGED,
          payload: {
            rowId,
            cellId,
            idx,
            cellValue: unchangedValue,
          },
        });
        return;
      }

      // dispatch
      dispatch({
        type: Act.DATA_CONTENT_ROW_NO_CHANGED,
        payload: {
          rowId,
          cellId,
          idx,
        },
      });
    }

    // keep input value when it changed
    setCallback(cellId, rowId, idx, cellValue);
  };

  // change input value
  const handleInputChange = (event, { value }) => {
    setValue(value);
  };

  const handleAddClick = () => {
    // -1 為新增 item
    // FIXME: 原本 newState 以新 item {value: ""} 傳入，但不知為何 createState 的值沒有改變
    const newState = Object.assign({}, createState);
    newState[Object.keys(createState).length + 1] = '';
    setCallback(cellId, rowId, -1, Object.assign({}, newState));
  };

  const handleEditorInputChange = (value) => {
    const mkValue = htmlToMarkDown(value);
    setCurValue(mkValue);
  };

  const handleOnMouseDown = () => {
    // 修改點選Cell之後，markdown設定值沒有正確寫到CustomMarkdownEditor裡面問題
    // const { value } = target;
    // setCurValue(value);
    setOpen(true);
  };

  const handleOnCancel = () => {
    // setValue(curValue);
    setOpen(false);
  };

  const handleConfirm = () => {
    setOpen(false);
    setSelectYes(true);
  };

  // 按下確認後再設定curValue
  useEffect(() => {
    if (!selectYes) return;
    setValue(curValue);
  }, [curValue, selectYes]);

  useEffect(() => {
    if (open) return;
    setSelectYes(false);
  }, [open]);

  return useMemo(
    () => (
      <React.Fragment>
        <Input
          {...rest}
          // keep input value
          label={
            !readMode && {
              basic: true,
              icon: 'add',
              content: getLangTag(createState[idx]),
              size: 'mini',
              onClick: handleAddClick,
            }
          }
          labelPosition="right"
          value={createState[idx]}
          // this is not an error, it just used to change the input background color
          error={isDiffValue}
          // onChange event
          onChange={handleInputChange}
          onMouseDown={handleOnMouseDown}
        />
        <Modal
          onClose={() => setOpen(false)}
          onOpen={() => setOpen(true)}
          open={useEditor && open}
          closeOnDimmerClick={false}
        >
          <Modal.Content style={{ pointerEvents: readMode && 'none' }}>
            <CustomMarkdownEditor
              curValue={createState[idx]}
              setValue={handleEditorInputChange}
              // 只有作家簡介、作品簡介、tlvm-台灣文學史-描述，才可以把圖片上傳功能打開
              onImgVideo={openImageUpload(mainSubject, sheet, cellId)}
              mainSubject={mainSubject}
            />
          </Modal.Content>
          <Modal.Actions>
            <Button color="black" onClick={handleOnCancel}>
              取消
            </Button>
            {!readMode && (
              <Button color="green" onClick={handleConfirm}>
                確認
              </Button>
            )}
          </Modal.Actions>
        </Modal>
      </React.Fragment>
    ),
    [cellId, createState, open],
  );
};

export default CustomMarkdown;

import React, { useContext, useEffect, useState, useCallback, useMemo, useRef } from 'react';
import { useSelector } from 'react-redux';
import { sortedByStroke } from 'twchar';

// store
import { Container, Table, Header, Loader, Form } from 'semantic-ui-react';
import { StoreContext } from '../../../../store/StoreProvider';
import Act from '../../../../store/actions';

// url
import Api from '../../../../api/nmtl/Api';
import { readNmtlData, getUnintegratedData } from '../../../../api/nmtl';

// cloud
import { getSheetHeader } from '../../../../api/firebase/cloudFirestore';

// custom element
import TableCellValue from '../commonComp/TableCellValue';

// common
import { isEmpty } from '../../../../commons';
import arrayMerge from '../../../../commons/arrayMerge';

// api function
import { getApiByAllField, addSuffix } from '../../../../api/nmtl/ApiField';
// import ContextMenu from "./CustomContextMenu";
import ImagePicker from './CustomImageInput/ImagePicker';
import FullTextFilePicker from './CustomFileUpload/FullTextFilePicker';
import {
  allConvertToCreateState,
  isNotShownInTable,
  assignSubValues,
} from '../../../common/sheetCrud/utils';
import './contentView.css';
import uploadConfig from '../../../toolPages/components/upload/uploadConfig';
import { filterColumn } from '../../../../commons/filterGroup';
import CustomCheckBox from './CustomCheckBox/CustomCheckBox';
import { escapeRegExpKeyword } from '../../../../commons/escapeRegExp';
import { basicInfoShowHeaders } from './ContentConfig';
import { SHEET_TYPES, GRAPH_TYPES } from '../../../common/sheetCrud/config';
import { singleValueHeaders } from '../../../../constants';
// import settingSearchKeyword from "../../../../commons/settingSearchKeyword";
// import { getTextAndLabel } from "../../../../commons/transferKeyword";

const ContentView = () => {
  // eslint-disable-next-line no-unused-vars
  const [state, dispatch] = useContext(StoreContext);

  // get dataset(mainSubject) and sheet
  const {
    mainSubject,
    sheet,
    content: ct,
    pagination,
    message,
    search,
    groupInfo,
    // imageEditor
  } = state.data;

  // extract parameter for sidebar selected item
  const { dataset } = mainSubject.selected;
  const {
    key: sheetName,
    contentReadPath,
    contentSearchPath,
    // 表格的主要抓取 class
    contentClassList,
    // for authority only
    authority,
    authorityContentReadPath,
  } = sheet.selected;
  const { checked, showOnlyUnintegratedData } = ct;
  const { header, activeHeader, activeHeaderCount, headerFields, activeHeaderAllChecked } = sheet;
  const { activePage, pageNum } = pagination;
  const { renderSignal } = message;
  const { keyword, searchDataset } = search;

  const {
    files: {
      imageEditor: { displayModal },
    },
  } = useSelector((stateRedux) => stateRedux);
  // const { displayModal } = imageEditor; // 控制是否顯示 imagePicker

  const [createState, setCreateState] = useState(null);
  // get content data
  const [content, setContent] = useState(undefined);
  // handle error
  const [error, setError] = useState(undefined);
  // get sheet header
  const [sheetHeader, setSheetHeader] = useState(undefined);
  // is content loading
  const [isLoading, setIsLoading] = useState(false);

  const fetchCounterRef = useRef(0);

  // get all checked id
  const activeCheckedIds = checked ? Object.keys(checked) : [];
  // get all active id
  const activeHeaderIds = useMemo(() => activeHeader[sheetName]?.map((item) => item.id), [
    activeHeader,
    sheetName,
  ]);

  const isAuthority = dataset === 'authority' && sheetName === 'BasicInfo';

  useEffect(() => {
    if (Object.keys(headerFields).length === 0) {
      return;
    }
    if (!content || content.length === 0) {
      return;
    }

    setCreateState(allConvertToCreateState(content, header, headerFields));
  }, [headerFields, content, header]);

  const setCallback = (cellId, rowId, idx, jsonVal) =>
    setCreateState((preState) => {
      // srcId 不能 call setCallback，否則會變 object
      if (cellId === 'srcId') {
        return preState;
      }

      const newRow = preState[rowId];

      // 如果 createState 沒有 default 值，在此給值
      if (Object.keys(newRow).indexOf(cellId) < 0) {
        newRow[cellId] = {};
      }
      if (idx === -1) {
        // 新增 item
        newRow[cellId] = jsonVal;
      } else if (Object.keys(jsonVal).indexOf('isOption') > -1) {
        // options, multi-options 必須帶一個固定值來判斷
        if (Object.keys(jsonVal).indexOf('value') > -1) {
          newRow[cellId].value = jsonVal.value;
        }
        // 有些特殊值需要改
        if (Object.keys(jsonVal).indexOf('input') > -1) {
          newRow[cellId].input = jsonVal.input;
        }
        if (Object.keys(jsonVal).indexOf('isLoading') > -1) {
          newRow[cellId].isLoading = jsonVal.isLoading;
        }
        if (Object.keys(jsonVal).indexOf('options') > -1) {
          newRow[cellId].options = jsonVal.options;
        }
      } else {
        // richInfo
        newRow[cellId] = JSON.parse(JSON.stringify(jsonVal));
      }
      return [...preState.slice(0, rowId), newRow, ...preState.slice(rowId + 1)];
    });

  // get sheet header for table title
  const handleGetSheetHeader = useCallback(async () => {
    if (sheetName) {
      // 取得資料
      let sortheader = await getSheetHeader(sheetName);

      // set data
      if (sortheader.error) {
        setError(sortheader.error);
        return;
      }

      // [新增] filterColumn功能 - 20230202
      sortheader = sortheader.filter((obj) => filterColumn(sheetName, obj, groupInfo));

      // handle header
      setSheetHeader(sortheader);
      // dispatch header
      dispatch({
        type: Act.DATA_SHEET_HEADER,
        payload: sortheader,
      });
      // dispatch header default activate
      if (isEmpty(activeHeader[sheetName])) {
        dispatch({
          type: Act.DATA_SHEET_ACTIVATE_HEADER,
          payload: {
            [sheetName]: activeHeaderAllChecked[sheetName]
              ? sortheader
              : isAuthority
                ? sortheader.filter((el) => basicInfoShowHeaders.includes(el.id))
                : sortheader.slice(0, activeHeaderCount),
          },
        });
      }
    }
  }, [dispatch, sheetName, activeHeader, activeHeaderCount, activeHeaderAllChecked, groupInfo]);

  // get sheet header fields
  // todo fix: this function sometimes repeats the query(fetch) between reducer and useEffect
  const handleGetSheetHeaderFields = useCallback(async () => {
    // get all header ids
    const headerIds = header && header.map((item) => item.id);
    // get all fields from already exist list
    const fields = Object.keys(headerFields);
    // create Api and Field mapping table for easy to find
    const apiList = headerIds
      .map((field) => ({
        field,
        apiName: getApiByAllField(field),
      }))
      .filter(
        (item) =>
          // The getApiByAllField will return undefined when it not found from config(api:nmtl:ApiField.js)
          item.apiName.length > 0 &&
          // filter repeat field if it does exist
          fields.indexOf(item.field) === -1,
      );
    // unique apiName to avoids repeated queries
    const uniqueApiNameList = [...new Set(apiList.map((item) => item.apiName).flat())];
    // get each uniqueApi promise by fetch
    const promises = uniqueApiNameList.map((apiName) =>
      // 部份 API 有參數，如：getMSWorks
      readNmtlData(Api[apiName].replace('{ds}', dataset)),
    );
    // get results from promises
    const results = await Promise.allSettled(promises).then((res) => res);
    // create catch result dictionary
    const apiResults = {};
    // To map the api and result in a dictionary as lookup table
    results.forEach((res, idx) => {
      const { status, value } = res;
      // if Promise.allSettled has succeeded or failed, it will return 'fulfilled' or 'rejected'
      if (status === 'fulfilled' && value && value.data) {
        const apiName = uniqueApiNameList[idx];
        // return data: { id, label } => { id, label, value (id) }
        apiResults[apiName] = value.data.map((d) => ({
          id: d.id,
          label: `${d.label}${addSuffix(apiName)}`,
          value: d.id,
        }));
      }
    });

    // store all results
    if (!isEmpty(apiResults)) {
      dispatch({
        type: Act.DATA_SHEET_HEADER_FIELD,
        payload: apiResults,
      });
    }
  }, [dispatch, header, headerFields]);

  const sortedByNumber = (results, key) =>
    results.sort((a, b) => {
      const numA = parseInt(a[key].replace(/\D/g, ''), 10);
      const numB = parseInt(b[key].replace(/\D/g, ''), 10);
      return numA - numB;
    });

  // get full api url
  const getSortedIds = useCallback(async () => {
    if (dataset && contentClassList) {
      // combine url and parameter
      const personListUrl = showOnlyUnintegratedData
        ? Api.getAuthorityNeedProcessPersonIds
        : Api.getPersonDsAllIdList;
      const personListApi = personListUrl.replace('{ds}', searchDataset || dataset);
      const contentClassApi = contentClassList.replace('{ds}', searchDataset || dataset);

      const qryStr = isAuthority ? personListApi : contentClassApi;

      const classList = await readNmtlData(qryStr);

      // 權威檔要按id排序
      return isAuthority
        ? sortedByNumber(classList.data, 'id')
        : sortedByStroke(classList.data, 'label');
    }
    return undefined;
  }, [dataset, contentClassList, searchDataset, showOnlyUnintegratedData]);

  // get full api url
  // e.g. http://localhost:3000/dl_award/1.0?dataset=abo&limit=10&offset=10&ids=
  // useCallback 保存方法，避免 useEffect 喧染時被產生新的 element
  const getReadUrl = useCallback(
    (searchIds) => {
      // authority的ContentReadPath單獨處理
      if (dataset === GRAPH_TYPES.AUTHORITY && authority === '1') {
        // 增加表單判斷，避免影響原本使用方式
        if (sheetName === SHEET_TYPES.BASIC_INFO) {
          // authority BasicInfo不讀取contentReadPath，改成authorityContentReadPath
          if (authorityContentReadPath) {
            return authorityContentReadPath.replace('{ids}', searchIds);
          }
        }
      }

      if (dataset && contentReadPath && activePage) {
        // combine url and parameter
        const urlPath = `${Api.getBaseUrl}/${contentReadPath}`;
        const parameter = `ds=${dataset}&limit=-1&offset=0&ids=${searchIds}`;
        return `${urlPath}?${parameter}`;
      }
      return undefined;
    },
    [dataset, contentReadPath, activePage, sheetName, authorityContentReadPath],
  );

  const searchNmtlData = useCallback(async () => {
    if (!keyword || (!dataset && !searchDataset)) {
      setIsLoading(false);
      return { data: [], total: null, error: 'Not ready' };
    }

    let searchApi = '';

    if (showOnlyUnintegratedData && isAuthority) {
      searchApi = Api.getAuthorityNeedProcessSearchPersonIds;
    } else if (isAuthority) {
      searchApi = Api.getAuthoritySearchPersonIds;
    } else {
      searchApi = contentSearchPath;
    }

    if (!searchApi) {
      setIsLoading(false);
      return { data: [], total: null, error: 'Not ready' };
    }

    const searchUrl = searchApi
      .replace('{ds}', searchDataset || dataset)
      .replace('{keyword}', escapeRegExpKeyword(keyword));

    return readNmtlData(searchUrl);
  }, [dataset, contentSearchPath, keyword, searchDataset, showOnlyUnintegratedData]);

  const fetchSubValue = useCallback(
    async (data) =>
      // hasSource, hasURL, hasFormat, hasReplaced...
      assignSubValues(data, dataset),
    [dataset, activePage],
  );

  const readNmtlSearchData = async () => {
    let searchData = [];
    let searchIds = '';
    let searchTotal = -1;
    let mergedUnintegratedData = [];
    const mergedFinalData = [];

    // search
    if (keyword && contentSearchPath) {
      // fetch data
      const { data } = await searchNmtlData();
      searchData = sortedByNumber(data, 'id');
      searchTotal = searchData.length;

      // 沒有結果
      if (searchTotal === 0) {
        return { data: null, total: null, error: '沒有搜尋結果' };
      }
    } else {
      // id, label 全抓，Read sorted ids
      searchData = await getSortedIds();
    }

    // 取出不重複的id
    const uniValue = [...new Set(searchData?.map((item) => item.id))];

    if (uniValue.length === 0) {
      return { data: null, total: null, error: '沒有搜尋結果' };
    }

    // 分頁
    const startIndex = (activePage - 1) * 10;
    searchIds = uniValue.slice(startIndex, startIndex + 10).join(',');

    // 讀取數據
    const readApiUrl = getReadUrl(searchIds);

    if (!readApiUrl) {
      return { data: null, total: null, error: 'Not ready' };
    }

    // fetch data
    const { data: readData, error: readError } = await readNmtlData(readApiUrl);

    if (!readData) {
      return { data: null, total: null, error: readError };
    }

    // 合併數據
    const mergedData = arrayMerge.sheet(readData);

    if (!mergedData) {
      return { data: null, total: null, error: 'arrayMerge' };
    }

    // 權威檔找出未融合資料
    if (isAuthority) {
      // 取得未融合資料
      const unintegratedData = await getUnintegratedData(searchIds);

      if (unintegratedData.length > 0) {
        mergedUnintegratedData = arrayMerge.unintegratedMergeSheet(unintegratedData);
      }
    }

    // 權威檔找到同 id 插入到下一項
    if (mergedData.length > 0 && mergedUnintegratedData.length > 0) {
      mergedData.forEach((dataItem) => {
        mergedFinalData.push(dataItem);

        const matchingUnintegratedData = mergedUnintegratedData.filter(
          (item) => item.srcId === dataItem.srcId,
        );

        mergedFinalData.push(...matchingUnintegratedData);
      });
    } else {
      mergedFinalData.push(...mergedData);
    }

    // 獲取子屬性 hasSource, hasURL, hasFormat, hasReplaced...
    const finalData = await fetchSubValue(mergedFinalData);

    if (!finalData) {
      return { data: null, total: null, error: 'fetchSubValue' };
    }

    return {
      data: finalData,
      total: searchTotal > -1 ? searchTotal : uniValue.length,
      error: null,
    };
  };

  // handle fetch data from api
  // useCallback 保存方法，避免 useEffect 喧染時被產生新的 element
  const handleGetContent = useCallback(async () => {
    fetchCounterRef.current += 1;
    const currentFetchId = fetchCounterRef.current;

    setIsLoading(true);

    // fetch data
    const { data: contentData, total, error: dataError } = await readNmtlSearchData();

    // 只要更新最新的請求
    if (currentFetchId === fetchCounterRef.current) {
      // set data
      if (!dataError) {
        // set content for table view
        setContent(contentData);
        // reset error if user reload
        setError(undefined);
        dispatch({
          type: Act.DATA_CONTENT_ROWS,
          payload: contentData,
        });
      }
      // set error if data not exist
      else {
        setError(dataError);
      }

      // set pagination
      if (total >= 0) {
        dispatch({
          type: Act.DATA_PAGINATION_TOTAL_PAGE,
          payload: Math.ceil(total / pageNum),
        });
        dispatch({
          type: Act.DATA_PAGINATION_TOTAL_COUNT,
          payload: total,
        });
      }

      setIsLoading(false);
    }
  }, [dispatch, keyword, contentSearchPath, getReadUrl, showOnlyUnintegratedData, searchDataset]);

  // init
  useEffect(() => {
    // refresh changed when sheet changed
    dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });
    // refresh checked when sheet changed
    dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });
    // refresh created when sheet changed
    dispatch({ type: Act.DATA_CONTENT_ROW_CREATED_CLEAN });
  }, [dispatch]);

  // 1. get sheet header
  useEffect(() => {
    handleGetSheetHeader();
  }, [sheetName, groupInfo]);

  // 2. get sheet header fields
  useEffect(() => {
    handleGetSheetHeaderFields();
  }, [dataset, header]);

  // 3. get content
  useEffect(() => {
    handleGetContent();
  }, [
    handleGetContent,
    dataset,
    contentReadPath,
    renderSignal,
    showOnlyUnintegratedData,
    searchDataset,
  ]);

  useEffect(() => {
    // refresh pagination when sheet changed
    dispatch({ type: Act.DATA_PAGINATION_ACTIVE_PAGE_INIT });
    // refresh changed when sheet changed
    dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });
    // refresh checked when sheet changed
    dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });
    // refresh created when sheet changed
    dispatch({ type: Act.DATA_CONTENT_ROW_CREATED_CLEAN });
  }, [showOnlyUnintegratedData]);

  // 首行永遠固定於左
  const fixFirstChild = {
    position: 'sticky',
    left: '0',
    // zIndex: "1",
    // backgroundColor: "#f9fafb",
    borderRight: '1px solid rgba(34,36,38,.1)',
    resize: 'horizontal',
    backgroundColor: 'inherit',
    border: 'none',
  };
  const columnResize = {
    resize: 'horizontal',
    backgroundColor: 'inherit',
    border: 'none',
  };
  const tableHeader = () => {
    if (!sheetHeader || error || isLoading) {
      return null;
    }

    const res = [];
    // for checkbox
    res.push(<Table.HeaderCell key="checkbox" />);
    // for contextMenu
    // handle table title(sheet)
    sheetHeader.forEach((sh, idx) => {
      if (isNotShownInTable(sh.id)) {
        return;
      }

      if (activeHeaderIds && activeHeaderIds.indexOf(sh.id) !== -1) {
        res.push(
          // for checkbox
          <Table.HeaderCell key={sh.id}>
            <Form readOnly>
              <Form.TextArea
                readOnly
                rows={2}
                style={idx === 0 ? fixFirstChild : columnResize}
                value={`${sh.label}\n${sh.id}`}
              />
            </Form>
          </Table.HeaderCell>,
        );
      }
    });

    return res;
  };

  const getStyle = (unintegrated, cellHeader, hasMultipleValues, isAuthority) => {
    const cellStyle = {};

    if (isAuthority && unintegrated && cellHeader !== 'introduction') {
      cellStyle.pointerEvents = 'none';

      return cellStyle;
    }

    if (
      isAuthority &&
      !unintegrated &&
      hasMultipleValues &&
      singleValueHeaders.includes(cellHeader)
    ) {
      cellStyle.backgroundImage = `
                linear-gradient(to right, #FFB5B5 1px, transparent 1px), 
                linear-gradient(to bottom, #FFB5B5 1px, transparent 1px)
            `;
      cellStyle.backgroundSize = '10px 10px';

      return cellStyle;
    }

    return cellStyle;
  };

  const tableCell = (ctIdx, unintegrated) => {
    if (!sheetHeader || !activeHeaderIds) {
      return null;
    }

    return activeHeaderIds.map((actHeader, shIdx) => {
      if (isNotShownInTable(actHeader)) {
        return null;
      }
      if (isEmpty(ct.rows)) {
        return null;
      }
      if (!createState) {
        return null;
      }

      // defaultValue: Object { 0: "PER2", 1: "PER201", … }
      const defaultValue = ct.rows[ctIdx][actHeader] ? ct.rows[ctIdx][actHeader] : null;

      const hasMultipleValues =
        defaultValue && typeof defaultValue === 'object' && Object.keys(defaultValue).length > 1;

      // defaultValue為初始值，createState為畫面上的值
      return (
        <Table.Cell
          key={`${sheetName}-cell-${ctIdx}-${shIdx}-${activePage}`}
          className={`${sheetName}-cell-${ctIdx}-${shIdx}-${activePage}`}
          style={getStyle(unintegrated, actHeader, hasMultipleValues, dataset === 'authority')}
        >
          <TableCellValue
            className={`${sheetName}-cell-${ctIdx}-${shIdx}-${activePage}-tableCell`}
            actHeader={actHeader}
            defaultValue={defaultValue}
            ctIdx={ctIdx}
            shIdx={shIdx}
            activePage={activePage}
            createState={createState ? createState[ctIdx][actHeader] : null}
            setCallback={setCallback}
            unintegrated={unintegrated}
          />
        </Table.Cell>
      );
    });
  };

  const tableRowCell = (value) => (
    <Table.Row>
      <Table.Cell textAlign="center" verticalAlign="middle" style={{ height: '300px' }}>
        {value}
      </Table.Cell>
    </Table.Row>
  );

  const tableBody = () => {
    if (!dataset || !sheetName) {
      return tableRowCell(<Header>請選擇 資料集 和 表單。</Header>);
    }
    if (isLoading) {
      return tableRowCell(
        <Loader active inline="centered" size="large">
          Loading...
        </Loader>,
      );
    }
    if (error) {
      return tableRowCell(<Header>{error}</Header>);
    }
    if (!sheetHeader || isEmpty(content)) {
      return null;
    }

    return content.map((rd, ctIdx) => (
      <Table.Row
        key={`${sheetName}-content-${activePage}-${ctIdx}`}
        style={{
          backgroundColor: isAuthority && rd.unintegrated && '#E0E0E0',
        }}
      >
        <Table.Cell collapsing>
          {/* key 必須是唯一的如果重複將會被視為相同 element
                                                     也會造成不進行任何更動，也就是數值不會改變的 issue
                                                     怎麼設定都可以就是不可以相同 */}
          <CustomCheckBox
            key={`${sheetName}-content-checkbox-${activePage}-${ctIdx}-${activeCheckedIds.indexOf(
              ctIdx,
            ) !== -1}`}
            rowId={ctIdx}
            isChecked={activeCheckedIds.indexOf(ctIdx) !== -1}
          />
        </Table.Cell>
        {/* <Table.Cell */}
        {/*    collapsing */}
        {/*    style={{ */}
        {/*        backgroundColor: "rgb(249, 250, 251)" */}
        {/*    }} */}
        {/* > */}
        {/*    <ContextMenu */}
        {/*        rowId={ctIdx} */}
        {/*        rowData={rd} */}
        {/*        isDisabled={!isEmpty(changed)} */}
        {/*    /> */}
        {/* </Table.Cell> */}
        {tableCell(ctIdx, rd.unintegrated)}
      </Table.Row>
    ));
  };

  return useMemo(
    () => (
      <React.Fragment>
        <Container style={{ width: '100%' }}>
          <div
            style={
              sheetHeader && !isEmpty(content) && !error
                ? {
                  overflowY: isAuthority ? 'auto' : 'hidden',
                  minHeight: '500px',
                  maxHeight: isAuthority && '65vh',
                }
                : {}
            }
          >
            <Table className="freeze-head-and-col" celled selectable size="small">
              <Table.Header style={isAuthority ? { position: 'sticky', zIndex: 10, top: 0 } : null}>
                <Table.Row>{tableHeader()}</Table.Row>
              </Table.Header>
              <Table.Body>{tableBody()}</Table.Body>
            </Table>
          </div>
        </Container>
        {/* <pre>
                    {JSON.stringify(content, null, 2)}
                </pre>
                 */}
        {/* modal: image picker */}
        {displayModal === uploadConfig.image ? <ImagePicker /> : null}
        {displayModal === uploadConfig.file ? <FullTextFilePicker /> : null}
      </React.Fragment>
    ),
    [activeHeaderIds, sheetHeader, isLoading, displayModal, createState],
  );
};

export default ContentView;

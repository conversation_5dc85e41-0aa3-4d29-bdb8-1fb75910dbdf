import Act from "../actions";

const initState = {
    tmpImages: [], // 上傳 images 的暫存區
    // images: images uploaded or dropped (not save to db yet)
    // images: [{url:""},{url:""}]
    uploadImages: [],
    files: [],
    imageFormData: null,
    // folderPattern:跟 API 取得的資料夾結構
    folderPattern: null,
    // UPLOAD_CONFIG: 跟 API 取得的檔案上傳設定
    UPLOAD_CONFIG: {
        maxSize: "50000000",
        maxCount: 20
    },
    // ACCEPTABLE_EXTENSIONS: 跟 API 取得的可接受的延伸檔名
    ACCEPTABLE_EXTENSIONS: [],
    // currentFolder: 目前鎖定的資料夾
    currentFolder: {
        path: "",
        path_ch: "",
        folderName: "",
        folderName_ch: ""
    },
    // curFolderFilesUrl: 目前(drop area 或 current folder)顯示的圖片 [{url:""},{url:""}]
    curFolderFilesUrl: [],
    // curFolderFiles: 目前選定的資料夾的圖片, checked 代表 "已勾選"
    // curFolderFiles: { original: [], checked: [] },
    // dropFolderFiles: 目前 drop area 的圖片, checked 代表 "已勾選"
    dropFolderFiles: { original: [], checked: [] },
    // fileServerUrl: file server base url
    fileServerUrl: "",
    loading: {
        state: false, // e.g. true/false
        message: "" // e.g. loading, uploading
    }
};

const uploadReducer = (state = initState, action) => {
    switch (action.type) {
        // case Act.IMG_PICKER_INITIAL_STATE:
        //     return {
        //         ...state,
        //         tmpImages: [],
        //         images: [],
        //         dropFolderFiles: initState.dropFolderFiles
        //     };
        case Act.UPLOAD_TMP_IMAGES:
            return {
                ...state,
                tmpImages: action.payload
            };
        case Act.UPLOAD_IMAGE:
            return { ...state, uploadImages: action.payload };
        case Act.UPLOAD_FILE:
            return { ...state, files: action.payload };
        case Act.SET_FORM_DATA:
            return { ...state, imageFormData: action.payload };
        case Act.SET_FOLDER_SETTINGS:
            return { ...state, ...action.payload };
        case Act.SELECT_FOLDER:
            return {
                ...state,
                currentFolder: action.payload
            };
        case Act.FOLDER_FILES_URL:
            return {
                ...state,
                curFolderFilesUrl: action.payload
            };
        case Act.CUR_FOLDER_FILES_STATUS:
            return {
                ...state,
                curFolderFiles: JSON.parse(JSON.stringify(action.payload))
            };
        // case Act.CLEAR_CUR_FOLDER_FILES_STATUS:
        //     return {
        //         ...state,
        //         curFolderFiles: initState.curFolderFiles
        //     };
        case Act.DROP_FOLDER_FILES_STATUS:
            return {
                ...state,
                dropFolderFiles: action.payload
            };
        // case Act.CLEAR_DROP_FOLDER_FILES_STATUS:
        //     return {
        //         ...state,
        //         dropFolderFiles: initState.dropFolderFiles
        //     };
        // case Act.SET_FILE_SERVER_URL:
        //     return {
        //         ...state,
        //         fileServerUrl: action.payload
        //     };
        case Act.UPLOAD_LOADING:
            return {
                ...state,
                loading: action.payload
            };
        // case Act.UPLOAD_CLEAR_CACHE:
        //     return initState;
        default:
            return state;
    }
};

export default uploadReducer;

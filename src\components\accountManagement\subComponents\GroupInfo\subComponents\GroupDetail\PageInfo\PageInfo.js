import React, { useEffect, useState } from "react";

// plugins
import { useDispatch, useSelector } from "react-redux";
import { Form, Accordion, Checkbox, Menu } from "semantic-ui-react";

//
import routes from "../../../../../../../App-route";
import menus from "../../../../../../../App-header";
import { isEmpty } from "../../../../../../../commons";
import accMngAct from "../../../../../../../reduxStore/accManage/accManageAction";
import textConfig from "../../../../Utils/textConifg";

function PageInfo() {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { groupData } = state;
    const [routeInfo, setRouteInfo] = useState([]);

    const clkSubChB = (path, name) => {
        const tmpGpData = JSON.parse(JSON.stringify(groupData));
        const findPath = tmpGpData.page.find(el => el.mainPath === path);
        if (findPath) {
            const findSubMenu = findPath.subMenu.find(
                el => el.subMenuName === name
            );
            if (findSubMenu) {
                findSubMenu.viewable = !findSubMenu.viewable;
            }

            // check all subMenu viewable status
            let tmpCk = true;
            findPath.subMenu.forEach(el => {
                if (!el.viewable) {
                    tmpCk = false;
                }
            });
            findPath.mainPathView = tmpCk;
        }
        dispatch({
            type: accMngAct.SET_GROUPDATA,
            payload: tmpGpData
        });
    };

    const CheckBoxForm = ({ path, childMenu }) => (
        <Form>
            <Form.Group grouped>
                {childMenu.map(({ name }) => (
                    <Form.Checkbox
                        key={name}
                        label={name}
                        value={name}
                        checked={
                            groupData.page
                                .find(el => el.mainPath === path)
                                .subMenu.find(el => el.subMenuName === name)
                                .viewable
                        }
                        onClick={() => clkSubChB(path, name)}
                    />
                ))}
            </Form.Group>
        </Form>
    );

    const clickAccTitle = el => {
        const tmpRouteInfo = JSON.parse(JSON.stringify(routeInfo));
        const findObj = tmpRouteInfo.find(rtItem => rtItem.id === el.id);
        if (findObj) {
            findObj.active = !findObj.active;
        }
        setRouteInfo(tmpRouteInfo);
    };

    const clickTitleCheckBox = (evt, tmpObj) => {
        evt.stopPropagation();
        const { path } = tmpObj;
        const tmpGroupData = JSON.parse(JSON.stringify(groupData));
        const { page } = tmpGroupData;
        const findPageObj = page.find(({ mainPath }) => mainPath === path);
        if (findPageObj) {
            findPageObj.mainPathView = !findPageObj.mainPathView;
            findPageObj.subMenu = findPageObj.subMenu.map(el => ({
                ...el,
                viewable: findPageObj.mainPathView
            }));
        }
        dispatch({
            type: accMngAct.SET_GROUPDATA,
            payload: tmpGroupData
        });
    };

    const CheckBoxTitle = ({ path }) => {
        const menuObj = menus.menuLeft.find(item => item.path === path);
        return (
            <Checkbox
                label={menuObj.name}
                onClick={evt => clickTitleCheckBox(evt, menuObj)}
                checked={
                    groupData.page.find(item => item.mainPath === path)
                        ?.mainPathView
                }
                indeterminate={
                    groupData.page
                        .find(item => item.mainPath === path)
                        ?.subMenu?.filter(el => el.viewable === true)
                        ?.length !==
                        groupData.page.find(item => item.mainPath === path)
                            ?.subMenu?.length &&
                    groupData.page
                        .find(item => item.mainPath === path)
                        ?.subMenu?.filter(el => el.viewable === true)?.length >
                        0
                }
            />
        );
    };

    const initGroupDataPage = (controlRouteList, groupPageData) => {
        let tmpObj = [];
        if (isEmpty(groupPageData)) {
            // initial groupData.page
            tmpObj = controlRouteList.map(el => ({
                mainPath: el.path,
                subMenu: el.childMenu?.map(childEl => ({
                    subMenuName: childEl.name,
                    viewable: false
                })),
                mainPathView: false
            }));
        } else {
            // initial groupData.page with existed group.page data
            tmpObj = controlRouteList.map(el => {
                const findPageObj = groupPageData.find(
                    pgObj => pgObj.mainPath === el.path
                );
                return {
                    mainPath: el.path,
                    subMenu: el.childMenu?.map(childEl => {
                        const findChildObj = findPageObj?.subMenu?.find(
                            subPgObj => subPgObj.subMenuName === childEl.name
                        );
                        return {
                            subMenuName: childEl.name,
                            viewable: findChildObj?.viewable || false
                        };
                    }),
                    mainPathView: findPageObj?.mainPathView || false
                };
            });
        }

        const tmpGroupData = JSON.parse(JSON.stringify(groupData));
        tmpGroupData.page = tmpObj;
        dispatch({
            type: accMngAct.SET_GROUPDATA,
            payload: tmpGroupData
        });
    };

    useEffect(() => {
        // 存在firebase_group-information內的page才顯示
        const mainPaths = groupData?.page?.map(({ mainPath }) => mainPath);

        // PageInfo頁面要顯示的資料
        const controlRouteList = routes
            .filter(({ accPageSelect, path }) => {
                // 編輯時，需顯示所有page
                if (groupData.name === "") {
                    return accPageSelect;
                }

                // 否則過濾 path 是否在 newX.x 中的 mainPath
                return accPageSelect && mainPaths.includes(path);
            })
            .map(el => ({ ...el, active: false }));
        initGroupDataPage(controlRouteList, groupData.page);
        setRouteInfo(controlRouteList);
    }, []);

    return (
        <Accordion as={Menu} vertical fluid>
            {routeInfo.map((el, idx) => (
                <Menu.Item key={el.id}>
                    <Accordion.Title
                        active={el.active}
                        content={CheckBoxTitle(el)}
                        index={idx}
                        onClick={() => clickAccTitle(el)}
                    />
                    <Accordion.Content
                        active={el.active}
                        content={
                            !isEmpty(el.childMenu)
                                ? CheckBoxForm(el)
                                : textConfig.NO_SUBMENU
                        }
                    />
                </Menu.Item>
            ))}
        </Accordion>
    );
}

export default PageInfo;

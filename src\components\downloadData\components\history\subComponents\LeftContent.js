import React from 'react';
import {Table} from "semantic-ui-react";
import {isEmpty} from "../../../../../commons";

const LeftContent = ({ entry }) => {

    return (
        /*
        <pre>
            {
                JSON.stringify(entry, null, 2)
            }
        </pre>
         */
        !isEmpty(entry) ?
            <Table celled selectable  compact='very'>
                <Table.Body>
                    {
                        Object.keys(entry).map((key, idx)=> {
                            return (
                                <Table.Row key={`src-${idx}-${key}`}>
                                    <Table.Cell>{key}</Table.Cell>
                                    <Table.Cell>{entry[key]}</Table.Cell>
                                </Table.Row>
                            )
                        })
                    }
                </Table.Body>
            </Table>
            :
            null
    );
};

export default LeftContent;
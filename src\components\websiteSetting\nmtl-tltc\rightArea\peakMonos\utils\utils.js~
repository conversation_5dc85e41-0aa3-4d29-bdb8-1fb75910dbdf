/* eslint-disable no-unused-vars */
import axios from "axios";
import textMsg from "../../../../commons/textMsg";
import Api from "../../../../../../api/nmtl/Api";
import { convertSimpleEntrySD } from "../../../../../common/sheetCrud/sheetCrudHelper";
import { isEmpty } from "../../../../../../commons";
import { createNmtlData, updateNmtlData } from "../../../../../../api/nmtl";
import { fileServerAPI } from "../../../../../../api/fileServer";
import uploadConfig from "../../../../../toolPages/components/upload/uploadConfig";
import { getFileName } from "../../../../commons/components/EditNews/Utils/saveDataUtils";
import { createHistoryEvent } from "../../../../../downloadData/components/history/common/common";

const initColumnDef = {
    introduction: "introduction",
    fileAvailableAt: "fileAvailableAt",
    peakId: "peakId",
    hasPeakYear: "hasPeakYear"
};
const graph = "tltc";
const columns = ["網頁設定選項", "外譯房", "臺灣文學獎"];

const areObjectsEqual = (obj1, obj2) => {
    // Get the keys of the objects
    const keys1 = Object.keys(obj1);
    const keys2 = Object.keys(obj2);

    // Check if the number of keys is the same
    if (keys1.length !== keys2.length) {
        return false;
    }

    // Check if the values of each key are the same
    // eslint-disable-next-line no-restricted-syntax
    for (const key of keys1) {
        if (obj1[key] !== obj2[key]) {
            return false;
        }
    }

    // If all checks passed, the objects are equal
    return true;
};

const reorder = (list, startIndex, endIndex) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);

    return result;
};

const getKeyData = (keyIdName, data) =>
    // 設定keyData
    ({
        // 要傳給api "srcId"欄位
        keyIdName,
        // 要傳給api "value"欄位的資料內容
        // dbPropName: 要傳給api "value"欄位的 database property name
        // prop: 從apiStr抓到資料內容的欄位名稱
        // lang: 要帶給api的語系名稱 (optional)
        dataValue: Object.keys(data).map(key =>
            // if (["ZH", "EN"].includes(key.slice(-2).toUpperCase())) {
            //     return {
            //         dbPropName: key.slice(0, -2),
            //         prop: initColumnDef[key],
            //         lang: key.slice(-2).toLowerCase()
            //     };
            // }
            ({ dbPropName: key, prop: data[key] })
        )
    });

const savePeakBrief = (originObj, updateObj, subject, user, classType) =>
    new Promise(async (resolve, reject) => {
        const { sheetName } = textMsg;
        const apiStr = Api.getGeneric;

        if (!classType) {
            resolve("No classType");
        }

        const keyData = getKeyData("peakId", initColumnDef);
        const entryCol = {
            graph,
            classType,
            ...keyData
        };

        const { entrySrc, entryDst } = convertSimpleEntrySD(
            originObj,
            updateObj,
            entryCol
        );

        delete entrySrc?.value?.[initColumnDef.peakId];

        // // create
        if (isEmpty(entrySrc) && !isEmpty(entryDst)) {
            const peaksId = entryDst?.value?.[initColumnDef.peakId];
            const firstRes = await createNmtlData(
                user,
                apiStr,
                graph,
                sheetName,
                entryDst
            );

            if (firstRes === "OK") {
                delete entryDst?.value?.[initColumnDef.peakId];
                createNmtlData(user, apiStr, graph, sheetName, entryDst)
                    .then(res => resolve(res))
                    .catch(err => reject(err));

                const historyMsg = `${JSON.stringify(entryDst)}`;

                // 建立歷史紀錄
                createHistoryEvent(
                    user.displayName,
                    "創建",
                    `${columns.join("/")}：${historyMsg}`
                );
            }
        } else {
            // update
            updateNmtlData(user, apiStr, graph, sheetName, entrySrc, entryDst)
                .then(res => resolve(res))
                .catch(err => reject(err));

            const historyMsg = `${JSON.stringify(
                entrySrc
            )}\n變動後：\n${JSON.stringify(entryDst)}`;

            // 建立歷史紀錄
            createHistoryEvent(
                user.displayName,
                "更新",
                `${columns.join("/")}：${historyMsg}`
            );
        }
    });

const savePeakData = async (
    dispatch,
    peakInfo,
    newPeakInfo,
    user,
    subject,
    classType
) => {
    const copySrc = JSON.parse(JSON.stringify(peakInfo));
    const copyDst = JSON.parse(JSON.stringify(newPeakInfo));
    const collectRes = [];

    const peakRes = await savePeakBrief(
        copySrc,
        copyDst,
        subject,
        user,
        classType
    );
    collectRes.push(peakRes);
};

const savePeakChapterData = (
    originObj,
    updateObj,
    subject,
    user,
    classType,
    peakID
) =>
    new Promise(async (resolve, reject) => {
        const { sheetName } = textMsg;
        const apiStr = Api.getGeneric;

        const initColumnTemp = {
            pchId: "pchId",
            order: "order",
            title: "title"
        };

        if (!classType) {
            resolve("No classType");
        }

        const keyData = getKeyData("pchId", initColumnTemp);
        const entryCol = {
            graph,
            classType,
            ...keyData
        };

        const { entrySrc, entryDst } = convertSimpleEntrySD(
            originObj,
            updateObj,
            entryCol
        );

        // // remove newsIdStr property
        delete entrySrc?.value?.[initColumnTemp.pchId];

        // // create
        if (isEmpty(entrySrc) && !isEmpty(entryDst)) {
            const pchId = entryDst?.value?.[initColumnTemp.pchId];
            const tmpEntry = {
                classType: "PeakMono",
                graph,
                srcId: peakID,
                value: {
                    hasPeakChapter: pchId
                }
            };
            const firstRes = await createNmtlData(
                user,
                apiStr,
                graph,
                sheetName,
                tmpEntry
            );
            // create new News instance
            if (firstRes === "OK") {
                delete entryDst?.value?.[initColumnTemp.pchId];
                createNmtlData(user, apiStr, graph, sheetName, entryDst)
                    .then(res => resolve(res))
                    .catch(err => reject(err));

                const historyMsg = `${JSON.stringify(entryDst)}`;

                // 建立歷史紀錄
                createHistoryEvent(
                    user.displayName,
                    "創建",
                    `${columns.join("/")}：${historyMsg}`
                );
            }
        } else {
            delete entryDst?.value?.[initColumnTemp.pchId];
            // update
            updateNmtlData(user, apiStr, graph, sheetName, entrySrc, entryDst)
                .then(res => resolve(res))
                .catch(err => reject(err));

            const historyMsg = `${JSON.stringify(entryDst)}`;

            // 建立歷史紀錄
            createHistoryEvent(
                user.displayName,
                "創建",
                `${columns.join("/")}：${historyMsg}`
            );
        }
    });

const savePeakChapter = async (
    dispatch,
    peakChapter,
    newPeakChapter,
    user,
    subject,
    classType,
    peakID
) => {
    const copySrc = JSON.parse(JSON.stringify(peakChapter));
    const copyDst = JSON.parse(JSON.stringify(newPeakChapter));
    const isSameSrcAndDst = areObjectsEqual(copySrc, copyDst);
    if (isSameSrcAndDst) return;

    const collectRes = [];
    const peakRes = await savePeakChapterData(
        copySrc,
        copyDst,
        subject,
        user,
        classType,
        peakID
    );
    collectRes.push(peakRes);
};

// Delete peak chapter
const deletePeakChapterData = (peakId, pchId, subject, user) =>
    new Promise(async (resolve, reject) => {
        const { sheetName } = textMsg;
        const apiStr = Api.getGeneric;
        const tmpEntry = {
            classType: "PeakMono",
            graph,
            srcId: peakId,
            value: {
                hasPeakChapter: pchId
            }
        };
        const tmpEntryDst = {
            classType: "Publication",
            graph,
            srcId: pchId,
            value: {
                hasPeakChapter: []
            }
        };
        updateNmtlData(user, apiStr, graph, sheetName, tmpEntry, tmpEntryDst)
            .then(res => resolve(res))
            .catch(err => reject(err));
    });

const deletePeakChapter = async (dispatch, peakId, pchId, user, subject) => {
    const collectRes = [];
    const peakRes = await deletePeakChapterData(peakId, pchId, subject, user);
    collectRes.push(peakRes);
};

const crudPeakChapterContentWriter = (user, srcId, perId, type) =>
    new Promise(async (resolve, reject) => {
        const { sheetName } = textMsg;
        const apiStr = Api.getGeneric;
        if (type === "save") {
            const entry = {
                classType: "Publication",
                graph,
                srcId,
                value: {
                    hasDescribedTarget: [perId]
                }
            };
            createNmtlData(user, apiStr, graph, sheetName, entry)
                .then(res => resolve(res))
                .catch(err => reject(err));
        }
        if (type === "delete") {
            const entry = {
                classType: "Publication",
                graph,
                srcId,
                value: {
                    hasDescribedTarget: perId
                }
            };
            const entryDst = {
                classType: "Publication",
                graph,
                srcId: perId,
                value: {
                    hasDescribedTarget: []
                }
            };
            updateNmtlData(user, apiStr, graph, sheetName, entry, entryDst)
                .then(res => resolve(res))
                .catch(err => reject(err));
        }
    });

const arrayDifference = (arrayOne, arrayTwo) =>
    arrayOne.filter(item1 => !arrayTwo.some(item2 => item2.value === item1));

const handleFileError = (reqUrl, formData) =>
    new Promise((resolve, reject) => {
        axios({
            method: "PUT",
            url: reqUrl,
            headers: {
                "Access-Control-Allow-Origin": "*"
            },
            data: formData,
            responseType: "json" // important
        })
            .then(res => {
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });

const uploadFile = (targetEl, subject, curAllUpload, fsFolderName, type) => {
    const allUpload = [];
    if (targetEl.files) {
        // eslint-disable-next-line no-restricted-syntax
        for (const file of targetEl.files) {
            const commonUrl = fileServerAPI.uploadFile.replace(
                "[type]",
                file.type.indexOf("image") > -1
                    ? uploadConfig.image
                    : uploadConfig.file
            );

            const reqUrl = `${commonUrl}/${subject}/${fsFolderName}`;
            const formData = new FormData();

            // 更改檔案名稱
            // const modifiedFile = new File(
            //     [file],
            //     `${file.lastModified}_${file.name}`,
            //     { type: file.type }
            // );

            formData.append("image", file);

            const path = `${
                file.type.indexOf("image") > -1
                    ? fileServerAPI.readUploadImage
                    : fileServerAPI.readFile
            }/${subject}/${fsFolderName}/`;
            allUpload.push({
                promiseFun: handleFileError(reqUrl, formData),
                path
            });
        }
    }

    return new Promise((resolve, reject) => {
        Promise.all(allUpload.map(el => el.promiseFun))
            .then(allResult => {
                const allURL = allResult
                    .filter(obj => obj.data.message)
                    .map((tmpObj, idx) => {
                        const tmpFileName = getFileName(
                            tmpObj.data.images[0].imgUrl
                        );
                        return `${allUpload[idx].path}${tmpFileName}`;
                    });
                resolve([...allURL]);
            })
            .catch(error => reject(error));
    });
};

export {
    savePeakData,
    reorder,
    savePeakChapter,
    deletePeakChapter,
    crudPeakChapterContentWriter,
    arrayDifference,
    uploadFile,
    areObjectsEqual
};

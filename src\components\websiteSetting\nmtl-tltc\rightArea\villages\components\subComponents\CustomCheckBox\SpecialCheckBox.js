import React, { useContext, useState, useEffect } from "react";

// ui
import { Checkbox } from "semantic-ui-react";

// store
import VillagesAct from "../../../VillagesAction";
import { StoreContext } from "../../../../../../../../store/StoreProvider";

const SpecialCheckBox = ({ rowId, cellId, isChecked, onClick, ...rest }) => {
    const [, dispatch] = useContext(StoreContext);

    // handle checkbox
    const [checked, setChecked] = useState(isChecked);
    // handle checkbox for delete

    useEffect(() => {
        setChecked(isChecked);
    }, [isChecked]);

    const handleCheckbox = () => {
        // checkbox state
        setChecked(!checked);

        // record rowId if checked === true
        if (!checked) {
            // record checkbox for delete
            dispatch({
                type: VillagesAct.DATA_CONTENT_ROW_CHECKED,
                payload: { rowId: `${rowId}-${cellId}` }
            });
        } else {
            // cancel record checkbox for delete
            dispatch({
                type: VillagesAct.DATA_CONTENT_ROW_NO_CHECKED,
                payload: { rowId: `${rowId}-${cellId}` }
            });
        }
    };

    return <Checkbox {...rest} checked={checked} onClick={handleCheckbox} />;
};

export default SpecialCheckBox;

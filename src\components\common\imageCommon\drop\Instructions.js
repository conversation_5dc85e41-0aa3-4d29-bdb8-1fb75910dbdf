import { Message } from "semantic-ui-react";
import React from "react";
import utils from "../utils";

const { prettyStrConcat } = utils;
const Instructions = props => {
    const {
        maxSize,
        fileCountLimit,
        extensions,
        minDpi,
        acceptCharacter
    } = props;

    return (
        <Message info>
            <Message.Header>圖片上傳規範：</Message.Header>
            <p>
                {prettyStrConcat([
                    "單一檔案大小限制:",
                    maxSize / 1000000,
                    "MB"
                ])}
            </p>
            <p>{prettyStrConcat(["單次上傳檔案數量限制:", fileCountLimit])}</p>
            <p>{prettyStrConcat(["可接受檔案格式:", extensions])}</p>
            <p>{prettyStrConcat(["像素最低限制:", minDpi])}</p>
            <p>{prettyStrConcat(["檔名可接受字元:", acceptCharacter])}</p>
        </Message>
    );
};

export default Instructions;

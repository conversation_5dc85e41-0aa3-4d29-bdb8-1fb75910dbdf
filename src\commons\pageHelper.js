/**
 * 由資料清單及 每頁比數, 計算總頁碼
 * @param data {*[]}
 * @param countPerPage {number}
 * @returns {number}
 */
export const getTotalPage = (data = [], countPerPage) => {
    const searchLen = Array.isArray(data) ? data.length : 0;
    const remainder = searchLen % countPerPage;
    const quotient = Math.floor(searchLen / countPerPage);
    let totalP = 1;
    if (remainder > 0) {
        totalP = quotient < 1 ? 1 : quotient + 1;
    } else {
        totalP = quotient < 1 ? 1 : quotient;
    }
    return totalP;
};
/**
 * 依據 page(目前頁碼)及 countPerPage(每頁數量)取得 limit, offset
 * @param page {number}
 * @param countPerPage {number}
 * @returns {{offset: number, limit: number}}
 */
export const getLimitOffset = (page, countPerPage) => ({
    limit: page * countPerPage,
    offset: (page - 1) * countPerPage,
});
/**
 * 依據 limit, offset 取得區間的 data
 * @param data {*[]}
 * @param limit {number}
 * @param offset {number}
 * @returns {*|*[]}
 */
export const getDataByLimitOffset = (data, limit, offset) =>
    (Array.isArray(data) &&
        data.filter((dt, idx) => idx >= offset && idx < limit)) ||
    [];

// filter curPage data by limit and offset

/**
 * 透過 activePage 及 pageSize 取得當前頁碼的資料
 * @param data {*[]}
 * @param activePage {number} 當前頁碼, 從 1 開始
 * @param pageSize {number} 每頁資料筆數
 * @returns {*}
 */
export const getDataByActivePage = (data, activePage, pageSize) => {
    const _offset = activePage <= 1 ? 0 : (activePage - 1) * pageSize;
    const end = _offset + pageSize;
    return data.slice(_offset, end);
};

/**
 * errCode: react-dropzone套件定義的error類別
 * */
export const dropErrMsg = errCode => {
    switch (errCode) {
        case "file-invalid-type":
            return "File type must be one of .xlsx, .xls";
        case "too-many-files":
            return "Too many files";
        default:
            return errCode;
    }
};

export const rejectFile = allRejectFile => {
    let tmpWarningMsg = "";
    const allErrCode = allRejectFile
        .filter(fileEl => fileEl.errors.length !== 0)
        .reduce((acc, nextFile) => {
            const tmpAllErrCode = nextFile.errors.map(err => err.code);
            tmpAllErrCode.forEach(errCode => {
                acc[errCode] = errCode;
            });
            return acc;
        }, {});

    Object.keys(allErrCode).forEach((errCode, idx) => {
        if (Object.keys(allErrCode).length === 1) {
            tmpWarningMsg += `${dropErrMsg(errCode)}.\n`;
        } else {
            tmpWarningMsg += `${idx + 1}. ${dropErrMsg(errCode)}.\n`;
        }
    });

    return tmpWarningMsg;
};

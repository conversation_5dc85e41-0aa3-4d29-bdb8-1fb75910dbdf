import React, { useState, useMemo } from "react";
import { useDispatch } from "react-redux";

// ui
import { Image, Label, Icon } from "semantic-ui-react";
import ImagePicker from "../../content/CustomImageInput/ImagePicker";
// store
import noImage from "../../../../../images/No_image_200x200.png";
import uploadConfig from "../../../../toolPages/components/upload/uploadConfig";
import FileAct from "../../../../../reduxStore/file/fileAction";
import { getImgFolderPattern } from "../../../../common/imageCommon/FolderList/folderListHelper";

const noImgPlaceholderUrl =
    "https://dummyimage.com/200x200/fff/000&text=No_image";

const CustomImageInput = ({ cellId, createState, setCallback, menuName }) => {
    const dispatchRedux = useDispatch();

    const [open, setOpen] = useState(false);
    // const [inputValue, setInputValue] = useState(defaultValue);
    const [isHover, setIsHover] = useState({ zIndex: "auto", scale: 1 });

    const style = {
        // display: "inline-block",
        // position: "absolute",
        zIndex: isHover.zIndex,
        transform: `scale(${isHover.scale})`,
        borderRadius: "unset"
    };

    const setValue = value => {
        setCallback(cellId, value, menuName);
    };

    const onValueChange = newValue => {
        setValue(newValue);
    };

    const handleCellClick = () => {
        if (!createState || !createState[cellId]) {
            setCallback(cellId, "", menuName);
            dispatchRedux({
                type: FileAct.SET_DEFAULT_VALUE,
                // payload: ""
                payload: ""
            });
            dispatchRedux({
                type: FileAct.SELECT_FILE,
                payload: ""
            });
        } else {
            dispatchRedux({
                type: FileAct.SET_DEFAULT_VALUE,
                // payload: ""
                payload: createState[cellId] || ""
            });
            dispatchRedux({
                type: FileAct.SELECT_FILE,
                payload: ""
            });
        }

        getImgFolderPattern(dispatchRedux, uploadConfig.ApiGetImages);
        setOpen(true);
    };

    const onMouseEnter = () => {
        // 沒有圖片，不需要做放大效果
        if (!createState || !createState[cellId]) {
            return;
        }
        setIsHover({ zIndex: "100", scale: 10 });
    };
    const onMouseLeave = () => {
        setIsHover({ zIndex: "auto", scale: 1 });
    };

    let imageName = "";
    if (createState && createState[cellId]) {
        const lastIdx = createState[cellId]?.lastIndexOf("/");
        imageName = createState[cellId]?.slice(lastIdx + 1);

        // 因為從 ImagePicker 取得的 image 有帶 size: 600x600_filename.jpg
        // 需要把 600x600 拿掉
        const sizeMatch = imageName.match(
            /^(?<width>\d+)[xX×╳](?<height>\d+)_/
        );

        if (sizeMatch && sizeMatch.length === 3) {
            // 420x420_0001-2-1667697481055.jpg
            // Array(3) [ "420x420_", "420", "420" ]
            imageName = imageName.replace(sizeMatch[0], "");
        }
    }

    // let imageName = "";
    // if (createState && createState[idx] && createState[idx].length > 0) {
    //     const lastIdx = createState[idx].lastIndexOf("/");
    //     imageName = createState[idx].slice(lastIdx + 1);
    //
    //     // 因為從 ImagePicker 取得的 image 有帶 size: 600x600_filename.jpg
    //     // 需要把 600x600 拿掉
    //     const sizeMatch = imageName.match(
    //         /^(?<width>\d+)[xX×╳](?<height>\d+)_/
    //     );
    //
    //     if (sizeMatch && sizeMatch.length === 3) {
    //         // 420x420_0001-2-1667697481055.jpg
    //         // Array(3) [ "420x420_", "420", "420" ]
    //         imageName = imageName.replace(sizeMatch[0], "");
    //     }
    // }

    // imageURL_hasURL
    // Object { 0: "" }
    // console.log(cellId, createState);
    return useMemo(
        () => (
            <div>
                <Label image basic>
                    <Image
                        src={
                            (createState && createState[cellId]) ||
                            noImage ||
                            noImgPlaceholderUrl
                        }
                        size="tiny"
                        alt={imageName}
                        style={style}
                        onClick={handleCellClick}
                        onMouseEnter={onMouseEnter}
                        onMouseLeave={onMouseLeave}
                    />
                    {imageName}
                    <Label.Detail
                        as={Icon}
                        name="add"
                        onClick={handleCellClick}
                    />
                </Label>
                <ImagePicker
                    open={open}
                    setOpen={setOpen}
                    defaultValue={(createState && createState[cellId]) || ""}
                    onValueChange={onValueChange}
                />
            </div>
        ),
        [cellId, createState, open, isHover, menuName]
    );
};

export default CustomImageInput;

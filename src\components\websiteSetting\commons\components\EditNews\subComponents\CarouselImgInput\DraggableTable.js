import React, { useContext } from "react";

// plugins
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { Form, Icon, Image, Ref, Table, TextArea } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";

//
import RemoveBtn from "../CustomButton/RemoveBtn";
import { setUpdateNewsInfo, uploadImg } from "../../Utils/utils";
import { StoreContext } from "../../../../../../../store/StoreProvider";
import initColumnDef, { imageColDef } from "../../Utils/initColumnDef";

function DraggableTable({ carImgArr }) {
    const [globalState] = useContext(StoreContext);
    const { websiteSubject } = globalState.websiteSetting;

    const newsDispatch = useDispatch();
    const { updateNewsInfo } = useSelector(state => state);

    const tableHeader = [
        { key: "col1", value: "" },
        { key: "col2", value: "輪播圖片" },
        { key: "col3", value: "搭配描述" },
        { key: "col4", value: "" }
    ];

    const onDragEnd = result => {
        const startIndex = result.source.index;
        const endIndex = result.destination.index;

        // 找updateNewsInfo的carousel
        const tmpNewsEvents = JSON.parse(JSON.stringify(updateNewsInfo));
        const tmpCarImgArr = tmpNewsEvents[initColumnDef.hasURL];
        const tmpObj = tmpCarImgArr[startIndex];
        tmpCarImgArr[startIndex] = tmpCarImgArr[endIndex];
        tmpCarImgArr[endIndex] = tmpObj;

        // image order change
        tmpCarImgArr[startIndex][imageColDef.order] = (
            startIndex + 1
        ).toString();
        tmpCarImgArr[endIndex][imageColDef.order] = (endIndex + 1).toString();

        setUpdateNewsInfo(newsDispatch, tmpNewsEvents);
    };

    const handleUpload = async index => {
        const url = await uploadImg(websiteSubject);

        const tmpNewsEvents = JSON.parse(JSON.stringify(updateNewsInfo));
        tmpNewsEvents[initColumnDef.hasURL][index].imgUrl = url;

        setUpdateNewsInfo(newsDispatch, tmpNewsEvents);
    };

    const editText = (value, index) => {
        const tmpNewsEvents = JSON.parse(JSON.stringify(updateNewsInfo));
        tmpNewsEvents[initColumnDef.hasURL][index].imgText = value;

        setUpdateNewsInfo(newsDispatch, tmpNewsEvents);
    };

    return (
        <DragDropContext onDragEnd={onDragEnd}>
            <Table selectable structured>
                <Table.Header>
                    <Table.Row>
                        {tableHeader.map(colObj => (
                            <Table.HeaderCell key={colObj.key}>
                                {colObj.value}
                            </Table.HeaderCell>
                        ))}
                    </Table.Row>
                </Table.Header>
                <Droppable droppableId="droppableId">
                    {provided => (
                        <Ref
                            innerRef={provided.innerRef}
                            {...provided.droppableProps}
                        >
                            <Table.Body>
                                {carImgArr.map((el, index) => {
                                    const { imgText, imgUrl } = el;
                                    return (
                                        <Draggable
                                            draggableId={`draggable_${index}`}
                                            index={index}
                                            key={index}
                                        >
                                            {dragRow => (
                                                <Ref
                                                    innerRef={dragRow.innerRef}
                                                >
                                                    <Table.Row
                                                        key={imgUrl}
                                                        {...dragRow.draggableProps}
                                                        {...dragRow.dragHandleProps}
                                                    >
                                                        <Table.Cell width={1}>
                                                            <Icon name="sidebar" />
                                                        </Table.Cell>
                                                        <Table.Cell width={7}>
                                                            <Image
                                                                style={{
                                                                    width:
                                                                        "100%",
                                                                    cursor:
                                                                        "pointer"
                                                                }}
                                                                src={imgUrl}
                                                                onClick={() =>
                                                                    handleUpload(
                                                                        index
                                                                    )
                                                                }
                                                            />
                                                        </Table.Cell>
                                                        <Table.Cell width={7}>
                                                            <Form className="textForm">
                                                                <TextArea
                                                                    rows={8}
                                                                    value={
                                                                        imgText
                                                                    }
                                                                    onInput={(
                                                                        evt,
                                                                        data
                                                                    ) => {
                                                                        editText(
                                                                            data.value,
                                                                            index
                                                                        );
                                                                    }}
                                                                />
                                                            </Form>
                                                        </Table.Cell>
                                                        <Table.Cell width={1}>
                                                            <RemoveBtn
                                                                className="carouselRM"
                                                                index={index}
                                                            />
                                                        </Table.Cell>
                                                    </Table.Row>
                                                </Ref>
                                            )}
                                        </Draggable>
                                    );
                                })}
                                {provided.placeholder}
                            </Table.Body>
                        </Ref>
                    )}
                </Droppable>
            </Table>
        </DragDropContext>
    );
}

export default DraggableTable;

import { splitTag } from "../config/config";

const checkLitGenre = (cell, propLabel, literGenList) => {
    let tmpResStr = "";

    const allList = literGenList.map(el => el.label);

    if (typeof cell.value === "string") {
        const check = cell.value
            .split(splitTag)
            .some(val => !allList.includes(val));

        if (check) {
            tmpResStr += `${cell.address}, [${
                cell.value
            }], 欄位:${propLabel}，只能填寫下列文學分類選項。\n [${allList.join(
                "、"
            )}]`;
        }
    }

    return tmpResStr;
};

export default checkLitGenre;

import React, { useEffect, useState } from "react";

// ui
import { Checkbox } from "semantic-ui-react";

// store
import { useDispatch } from "react-redux";
import VillagesAct from "../../../VillagesAction";

const CustomCheckBox = ({ rowId, isChecked, ...rest }) => {
    const dispatch = useDispatch();
    // handle checkbox
    const [checked, setChecked] = useState(isChecked);

    useEffect(() => {
        setChecked(isChecked);
    }, [isChecked]);

    // handle checkbox for delete
    const handleCheckbox = () => {
        // checkbox state
        setChecked(!checked);

        // record rowId if checked === true
        if (!checked) {
            // record checkbox for delete
            dispatch({
                type: VillagesAct.DATA_CONTENT_ROW_CHECKED,
                payload: { rowId: `${rowId}` }
            });
        } else {
            // cancel record checkbox for delete
            dispatch({
                type: VillagesAct.DATA_CONTENT_ROW_NO_CHECKED,
                // payload: { rowId }
                payload: { rowId: `${rowId}` }
            });
        }
    };

    return <Checkbox {...rest} checked={checked} onClick={handleCheckbox} />;
};

export default CustomCheckBox;

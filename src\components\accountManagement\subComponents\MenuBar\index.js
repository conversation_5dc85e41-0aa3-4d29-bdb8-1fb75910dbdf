import React, { useEffect } from 'react';

// semantic-ui
import { Menu } from 'semantic-ui-react';

// redux
import { useSelector, useDispatch } from 'react-redux';

//
import accMngAct from '../../../../reduxStore/accManage/accManageAction';
import textConfig from '../Utils/textConifg';
import menuItem from './menuItem';

function MenuBar() {
    // eslint-disable-next-line no-shadow
    const state = useSelector((tmpState) => tmpState.accMng);
    const { activeItemACC } = state;
    const dispatch = useDispatch();

    useEffect(() => {
        dispatch({
            type: accMngAct.SET_ACTIVEITEMACC,
            payload: menuItem[0],
        });
    }, []);

    const handleClick = (el) => {
        dispatch({
            type: accMngAct.SET_ACTIVEITEMACC,
            payload: el,
        });
        if (el.name === textConfig.GROUPINFO_TITLE) {
            dispatch({
                type: accMngAct.SET_GROUPINFOINIT,
            });
        }
    };

    return (
        <Menu pointing vertical style={{ width: '100%' }} className="menuBar">
            {/* 清單顯示 */}
            {menuItem.map((el) => {
                const { key, name } = el;
                return (
                    <Menu.Item
                        key={key}
                        name={name}
                        active={activeItemACC.name === name}
                        onClick={() => handleClick(el)}
                    />
                );
            })}
        </Menu>
    );
}

export default MenuBar;

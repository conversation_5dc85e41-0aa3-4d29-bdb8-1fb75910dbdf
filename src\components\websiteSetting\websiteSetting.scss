* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

@mixin center {
    display: flex;
    justify-content: center;
    align-items: center;
}

@mixin rightComponentMain {
    width: 90%;
    //border: 1px red solid;
    height: 90%;
    padding: 20px;
}

@mixin btnArea {
    display: flex;
    justify-content: flex-end;
}

@mixin textLanguageSelect {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 5px;
}

.WebsiteSetting {
    //width: 100%;
    height: 85vh;
    //border: 1px red solid;
    padding: 10px;
    display: flex;
    //flex-wrap: wrap;
    flex-direction: column;
    &__topArea {
        width: 100%;
        margin-bottom: 1rem;
    }

    .contentArea {
        display: flex;
        flex-wrap: nowrap;
        flex: 1 1 auto;
        max-height: 100%;
        .leftArea {
            margin-right: 5px;
            width: 15%;
        }
        .rightArea {
            border: 1px black solid;
            width: 85%;
            margin-bottom: 1rem;
            @include center;
            .MainIntroduction {
                @include rightComponentMain;
                .topArea {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin: 5px;
                }
                .updateArea {
                    height: 30%;
                    margin-bottom: 5px;
                }
                .textArea {
                    margin-bottom: 5px;
                    height: 40%;
                }
                .btnArea {
                    @include btnArea;
                }
            }
            .MainCarousel {
                @include rightComponentMain;
                .topArea {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                    margin: 5px;
                }
                .listArea {
                    padding: 10px;
                    border: 1px black solid;
                    overflow-y: scroll;
                    height: 90%;
                    margin-bottom: 10px;
                    .contentWithImageAndSelector {
                        width: 30%;
                        display: flex;
                        flex-direction: column;
                    }
                }
                .btnArea {
                    @include btnArea;
                }
            }
            .MainCardImage,
            .CardImageUpdater {
                @include rightComponentMain;
                //width: 50%;
                justify-self: start;
                .Selector {
                    margin-bottom: 5px;
                }
                .DropFileRegion {
                    margin-bottom: 5px;
                    height: 80%;
                    img {
                        height: 100%;
                    }
                }
                .btnArea {
                    @include btnArea;
                    width: 50%;
                }
            }
            .LinkingPage {
                @include rightComponentMain;
                .topArea {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .bottomArea {
                    height: 80%;
                    margin: 10px auto;
                    overflow-y: scroll;
                }
                .btnArea {
                    @include btnArea;
                }
            }
            .PrivacyPage,
            .ReferencePage,
            .RegionLiterature {
                @include rightComponentMain;
                .topArea {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                }
                .bottomArea {
                    padding-top: 10px;
                    height: 90%;
                    .updateArea {
                        margin-bottom: 5px;
                        height: 100%;
                    }
                    .btnArea {
                        @include btnArea;
                    }
                }
            }
            .SubjectLiterature,
            .CityLiterature {
                @include rightComponentMain;
                overflow-y: scroll;
                .Selector {
                    margin-bottom: 5px;
                }
                .updateArea {
                    margin-bottom: 5px;
                    height: 80%;
                    .updateAreaTop {
                        @include textLanguageSelect;
                    }
                    .updateAreaButtom {
                        //height: 35%;
                        margin-bottom: 1rem;
                    }
                }
                .btnArea {
                    @include btnArea;
                }
            }
            .VrMuseum {
                width: 90%;
                height: 90%;
                padding: 20px;
                overflow-y: scroll;
                border: 1px black solid;
                .Selector {
                    margin-bottom: 5px;
                }
                .textUpdate {
                    height: 50%;
                    .updateAreaTop {
                        @include textLanguageSelect;
                    }
                    .updateArea {
                        height: 80%;
                    }
                }
                .watermark,
                .pictureUpdate {
                    height: 50%;
                    margin-bottom: 5px;
                    .updateAreaTop {
                        @include textLanguageSelect;
                    }
                    .updateArea {
                        height: 80%;
                        .watermarkImg {
                            width: 30%;
                            height: 100%;
                            display: flex;
                            flex-direction: column;
                            .cancelBtn {
                                height: 20%;
                                align-self: flex-end;
                                background-color: inherit;
                                margin: 0;
                                padding: 0;
                            }
                        }
                    }
                }
                .btnArea {
                    @include btnArea;
                }
            }
            .LiteralProducer {
                @include rightComponentMain;
                .topArea {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .bottomArea {
                    height: 80%;
                    .updateArea {
                        margin-bottom: 5px;
                        height: 100%;
                    }
                }
                .btnArea {
                    @include btnArea;
                }
            }
            .LiteralData {
                @include rightComponentMain;
                .topArea {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;
                }
                .listArea {
                    margin-top: 10px;
                    padding: 10px;
                    border: 1px black solid;
                    overflow-y: scroll;
                    height: 90%;
                    margin-bottom: 10px;
                }
                .btnArea {
                    @include btnArea;
                }
            }

            .ManualPage {
                @include rightComponentMain;
                .topArea {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    height: 10%;
                }
                .bottomArea {
                    padding-top: 10px;
                    height: 90%;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    .updateArea {
                        display: flex;
                        flex-direction: column;
                        margin-bottom: 5px;
                        height: 80%;
                        h3 {
                            margin-bottom: 0;
                        }
                    }
                    .btnArea {
                        @include btnArea;
                        height: 10%;
                    }
                }
            }

            .DeveloperPage {
                @include rightComponentMain;
                .topArea {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                .bottomArea {
                    padding-top: 10px;
                    height: 100%;
                    .updateArea {
                        margin-bottom: 5px;
                        height: 80%;
                    }
                    .btnArea {
                        @include btnArea;
                    }
                }
            }
        }
    }
}

#CustomEditorCtyLt,
#CustomEditorSubLt {
    .ql-container.ql-snow {
        min-height: 350px;
        height: 350px;
    }
}

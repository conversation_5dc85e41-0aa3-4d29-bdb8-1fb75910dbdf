import Act from "../actions";
import {
    tltcReducer,
    tltcInitState
} from "../../components/websiteSetting/nmtl-tltc/tltcReducer";

const initState = {
    menuActiveItem: {},
    originData: [], // 第一次載入的原始資料
    selectOption: null, // 有下拉選單頁的選擇項目
    updatedData: [], // 紀錄更改後變動資料
    realtimeData: [], // 及時抓資料庫當下資料
    openModal: false, // DB資料更動時，跳出提示視窗
    modalMessage: "", // Modal顯示資料
    isEditedDisable: false, // LinkingPage、MainCarousel頁面有編輯模式
    listData: {}, // 用在MainCarousel，上傳image時需要知道點選哪個priority的element
    msListData: [], // 從firestore的"setting/dataset/mainSubject"抓到的資料給SubjectLiterature、VrMuseum dropdown使用
    // fixme: ↑---------------舊的變數，後續架後改變再搭配替換，目前只用在nmtl-web-----------------↑
    websiteSubject: "", // 選擇設定網站主題ID
    subMenu: [], // 根據不同選擇主題的子選單
    ...tltcInitState, // 單獨tltc要更改的變數另存
    // for Fuseki data
    fusekiData: {
        vrMuseum: {
            logo: {
                oriData: {}, // 第一次載入的原始資料
                tempData: {
                    // 紀錄更改後變動資料
                    text: "",
                    imageUrl: "",
                    type: "image" // 設定浮水印上傳圖片或是文字
                }
            }
        }
    }
};

const websiteSettingReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.SET_MENUACTIVEITEM:
            return { ...state, menuActiveItem: action.payload };
        case Act.SET_ORIGINDATA:
            return { ...state, originData: action.payload };
        case Act.SET_SELECTOPTION:
            return { ...state, selectOption: action.payload };
        case Act.SET_UPDATEDDATA:
            return { ...state, updatedData: action.payload };
        case Act.SET_REALTIMEDATA:
            return { ...state, realtimeData: action.payload };
        case Act.SET_OPENMODAL:
            return { ...state, openModal: action.payload };
        case Act.SET_MODALMESSAGE:
            return { ...state, modalMessage: action.payload };
        case Act.SET_ISEDITEDDISABLE:
            return { ...state, isEditedDisable: action.payload };
        case Act.SET_LISTDATA:
            return { ...state, listData: action.payload };
        case Act.SET_MSLISTDATA:
            return { ...state, msListData: action.payload };
        case Act.SET_WEBSITESUBJECT:
            return { ...state, websiteSubject: action.payload };
        case Act.SET_WEBSETMENU:
            return { ...state, subMenu: action.payload };
        case Act.FRONTEDIT_TLTC:
            return tltcReducer(state, action);
        // for VrMuseum
        case Act.SET_VRMUSEUM_LOGO:
            state.fusekiData.vrMuseum.logo = action.payload;
            return { ...state };
        case Act.SET_VRMUSEUM_LOGO_ORI_DATA:
            state.fusekiData.vrMuseum.logo.oriData = action.payload;
            return { ...state };
        case Act.SET_VRMUSEUM_LOGO_TEMP_DATA:
            state.fusekiData.vrMuseum.logo.tempData = action.payload;
            return { ...state };
        default:
            return state;
    }
};

export default websiteSettingReducer;

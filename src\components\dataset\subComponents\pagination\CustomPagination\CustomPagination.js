import React, { useContext } from "react";

import { Icon, Pagination } from "semantic-ui-react";
import PgDD from "./PgDD";
import PerPgNumDd from "./PerPgNumDD";
import InputPgDD from "./InputPgDD";
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

// scss
import "./Pagination.scss";

function CustomPagination() {
    const [state, dispatch] = useContext(StoreContext);
    const { pagination, mainSubject, sheet } = state.data;
    const { totalPage, activePage: currentPage } = pagination;
    const { dataset } = mainSubject.selected;
    const { key: sheetName } = sheet.selected;

    const handlePage = (evt, { activePage }) => {
        dispatch({
            type: Act.DATA_PAGINATION_ACTIVE_PAGE,
            payload: activePage
        });

        dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });
        // refresh checked when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });
        // refresh created when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CREATED_CLEAN });
    };

    if (!totalPage) return null;
    return (
        <div className="Pagination">
            <div className="chooseRange">{/* <PerPgNumDd /> */}</div>
            <Pagination
                activePage={currentPage}
                ellipsisItem={{
                    content: <Icon name="ellipsis horizontal" />,
                    icon: true
                }}
                firstItem={{
                    content: <Icon name="angle double left" />,
                    icon: true
                }}
                lastItem={{
                    content: <Icon name="angle double right" />,
                    icon: true
                }}
                prevItem={{ content: <Icon name="angle left" />, icon: true }}
                nextItem={{ content: <Icon name="angle right" />, icon: true }}
                totalPages={totalPage}
                onPageChange={handlePage}
            />
            <div className="selectPage">
                {dataset === "authority" && sheetName === "BasicInfo" ? (
                    <InputPgDD />
                ) : (
                    <PgDD />
                )}
            </div>
        </div>
    );
}

export default CustomPagination;

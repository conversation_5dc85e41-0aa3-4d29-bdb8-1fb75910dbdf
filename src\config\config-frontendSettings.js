const frontendSettingsConfig = {};

// frontendSettingsConfig.QRCODE_PATH = "/qrcode";
// QRCode website url
// frontendSettingsConfig.QRCODE_URL = "https://hkbdb-qrcode.daoyidh.com";
//
// frontendSettingsConfig.API_COL = "api";
// frontendSettingsConfig.API_DOC = "api-config";

// NMTL
frontendSettingsConfig.NMTLWEB_COL = "frontend-settings";
frontendSettingsConfig.GROUPINFO = "group-information";
// frontendSettingsConfig.KEY_STATUS = "status";
// frontendSettingsConfig.KEY_KEYWORD = "keyword";

// frontendSettingsConfig.STATUS_UNKNOWN = "Unknown";
// frontendSettingsConfig.STATUS_ACTIVE = "Active";
//
// frontendSettingsConfig.STATUS_TIMEOUT = 3000;
// frontendSettingsConfig.SERVER_QRCODE_TIMEOUT = 60000;

// Hotkey
// frontendSettingsConfig.HOTKEY_COL = "hotkey";
// if (process.env.REACT_APP_WEB_MODE === "production") {
//   frontendSettingsConfig.HOTKEY_DOC = "keyword";
// } else {
//   frontendSettingsConfig.HOTKEY_DOC = "keyword2";
// }
// frontendSettingsConfig.HOTKEY_MAX = 10;

module.exports = frontendSettingsConfig;

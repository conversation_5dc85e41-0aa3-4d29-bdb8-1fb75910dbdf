import React, { useContext, useEffect, useState } from "react";

// components
import Selector from "../../components/Selector";
import LanguageSelect from "../../components/LanguageSelect";
import UpdateText from "../../components/UpdateText";
import SaveButton from "../../components/SaveButton";

// general
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

function DeveloperPage() {
    const [state, dispatch] = useContext(StoreContext);
    const { originData, menuActiveItem } = state.websiteSetting;
    const [dropDown, setDropDown] = useState({});
    const [language, setLanguage] = useState("zh");

    useEffect(() => {
        // 下拉選單選項清空
        dispatch({
            type: Act.SET_SELECTOPTION,
            payload: ""
        });
    }, []);

    useEffect(() => {
        if (originData.length !== 0) {
            const tmpOriginData = JSON.parse(JSON.stringify(originData));
            const tmpLiteralData = tmpOriginData.find(
                element => element.id === "LiteralData"
            );
            const tmpDeveloperPage = tmpOriginData.find(
                element => element.id === menuActiveItem.key
            );
            // 把文學數據的title換成LiteralData的設定值
            const keys = Object.keys(tmpDeveloperPage).filter(
                key => key !== "id"
            );
            keys.forEach(key => {
                // 在DeveloperPage，firestore儲存欄位都會以'literary'為開頭
                // 在firestore的DeveloperPage collection每個document的literalId欄位對應LiteralData collection的priority欄位
                // literalId 只有跟文學數據有關的才有此欄位
                if (key.indexOf("literary") >= 0) {
                    const literalDataValues = Object.values(tmpLiteralData.zh);
                    const findValue = literalDataValues.find(
                        element =>
                            element.priority === tmpDeveloperPage[key].literalId
                    );
                    if (findValue) {
                        tmpDeveloperPage[key].name = findValue.title;
                    }
                }
            });
            setDropDown(tmpDeveloperPage);
            dispatch({
                type: Act.SET_UPDATEDDATA,
                payload: tmpOriginData
            });
        }
    }, [originData]);

    return (
        <div className="DeveloperPage">
            <div className="topArea">
                <Selector dropDown={dropDown} />
                <LanguageSelect language={language} setLanguage={setLanguage} />
            </div>
            <div className="bottomArea">
                <div className="updateArea">
                    <UpdateText
                        dropDown={dropDown}
                        language={language}
                        option={{
                            column: "description"
                        }}
                    />
                </div>
                <div className="btnArea">
                    <SaveButton language={language} />
                </div>
            </div>
        </div>
    );
}

export default DeveloperPage;

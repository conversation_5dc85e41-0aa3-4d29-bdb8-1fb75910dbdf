import React, { useEffect, useRef, useState } from 'react';
import TopicStats from './components/TopicStats';
import Panel from './components/Panel';
import './statsDashboard.scss';
import { statsDataOrigin } from './constants/statsDashboardConfig';
import { processData, getCurrentTime, getStatsCount } from './utils/utils';
import { getMainSubject } from '../../../../api/firebase/cloudFirestore';
import { getEarliestDate, getPageView } from '../../../../api/firebase/realtimeDatabase';

const StatsDashboard = () => {
    const [statsData, setStatsData] = useState(JSON.parse(JSON.stringify(statsDataOrigin)));
    const [mainSubjectData, setMainSubjectData] = useState([]);
    const pageViewDateRef = useRef({ startDate: null, endDate: null });
    const [earliestDate, setEarliestDate] = useState('');
    const [latestDate, setLatestDate] = useState(
        new Date(new Date().setDate(new Date().getDate() - 1)).toISOString().slice(0, 10),
    );

    const updateStatsData = (topicIdx, statsIdx, stat, detailStatsArr) => {
        setStatsData((prevStatsData) => {
            const newStatsData = [...prevStatsData];
            newStatsData[topicIdx].stats[statsIdx] = {
                ...newStatsData[topicIdx].stats[statsIdx],
                stat,
                detailStatsArr,
            };
            newStatsData[topicIdx].lastUpdatedTime.time = getCurrentTime();
            return newStatsData;
        });
    };

    const getAllWebStats = async (item, statsIdx, topicIdx) => {
        if (!item.url) return;

        updateStatsData(topicIdx, statsIdx, '', []);

        try {
            const { stat, detailStatsArr } = await processData(item.id, item.url, mainSubjectData);

            updateStatsData(topicIdx, statsIdx, stat, detailStatsArr);
        } catch (e) {
            console.error('Failed to fetch data:', e);
            updateStatsData(topicIdx, statsIdx, '未取得數據', []);
        }
    };

    const updateAllWebStats = () => {
        const allWebStatsIdx = statsData.findIndex((el) => el.id === 'allWebStats');

        statsData[allWebStatsIdx].stats.forEach((statItem, statsIdx) => {
            getAllWebStats(statItem, statsIdx, 0);
        });
    };

    const updatePageViewData = async () => {
        const pageViewIdx = statsData.findIndex((el) => el.id === 'pageView');

        if (pageViewIdx === -1) return;

        const { startDate, endDate } = statsData[pageViewIdx].datePicker;

        if (
            pageViewDateRef.current.startDate !== startDate ||
            pageViewDateRef.current.endDate !== endDate
        ) {
            pageViewDateRef.current = {
                startDate,
                endDate,
            };

            const pageViewData = await getPageView(
                statsData[pageViewIdx].stats,
                startDate,
                endDate,
            );

            setStatsData((prevStatsData) => {
                const newStatsData = [...prevStatsData];
                newStatsData[pageViewIdx].stats = pageViewData;
                return newStatsData;
            });
        }
    };

    const updateStatsCount = async () => {
        const copystatsDataOrg = JSON.parse(JSON.stringify(statsDataOrigin));
        const topicIdx = copystatsDataOrg.findIndex((el) => el.id === 'datasetStats');

        setStatsData((prevStatsData) => {
            const newStatsData = [...prevStatsData];
            newStatsData[topicIdx].stats = copystatsDataOrg[topicIdx].stats;
            newStatsData[topicIdx].select.selected = '';
            return newStatsData;
        });

        const statsCountData = await getStatsCount(copystatsDataOrg, topicIdx);

        if (!statsCountData) return;

        setStatsData((prevStatsData) => {
            const newStatsData = [...prevStatsData];
            newStatsData[topicIdx].stats = statsCountData;
            newStatsData[topicIdx].select.selected = 'tww';
            newStatsData[topicIdx].lastUpdatedTime.time = getCurrentTime();
            return newStatsData;
        });
    };

    const handleUpdateData = (id) => {
        switch (id) {
            case 'allWebStats':
                updateAllWebStats();
                break;
            case 'datasetStats':
                updateStatsCount();
                break;
            default:
                break;
        }
    };

    useEffect(() => {
        const getMainSubjectData = async () => {
            try {
                const response = await getMainSubject();
                setMainSubjectData(response);
            } catch (e) {
                console.error('Failed to fetch main subject data:', e);
            }
        };

        getMainSubjectData();
    }, []);

    useEffect(() => {
        if (mainSubjectData.length > 0) {
            updateAllWebStats();
            updateStatsCount();
        }
    }, [mainSubjectData]);

    useEffect(() => {
        const pageViewIdx = statsData.findIndex((el) => el.id === 'pageView');
        if (
            statsData[pageViewIdx].datePicker.startDate &&
            statsData[pageViewIdx].datePicker.endDate
        ) {
            updatePageViewData();
        }
    }, [statsData]);

    useEffect(() => {
        const updateInitDate = async () => {
            const pageViewIdx = statsData.findIndex((el) => el.id === 'pageView');

            try {
                const earliestDateData = await getEarliestDate();
                const newEarliestDate = new Date(earliestDateData);
                newEarliestDate.setDate(newEarliestDate.getDate() + 1);
                const formattedEarliestDate = newEarliestDate.toISOString().split('T')[0];

                setEarliestDate(formattedEarliestDate);

                setStatsData((prevStatsData) => {
                    const newStatsData = [...prevStatsData];
                    newStatsData[pageViewIdx].datePicker.startDate = formattedEarliestDate;
                    newStatsData[pageViewIdx].datePicker.endDate = new Date(Date.now() - 86400000)
                        .toISOString()
                        .slice(0, 10);
                    return newStatsData;
                });
            } catch (e) {
                console.error('Error initializing data:', e);
            }
        };

        updateInitDate();
    }, []);

    return (
        <div className="statsDashboard">
            {statsData.map(
                ({
                    id,
                    topicTitle,
                    statsCount,
                    lastUpdatedTime,
                    select,
                    datePicker,
                    detailStats,
                    stats,
                }) => (
                    <TopicStats
                        key={id}
                        topicId={id}
                        topicTitle={topicTitle}
                        statsCount={statsCount}
                        lastUpdatedTime={lastUpdatedTime}
                        select={select}
                        datePicker={datePicker}
                        detailStats={detailStats}
                        stats={stats}
                        handleUpdateData={() => handleUpdateData(id)}
                        setStatsData={setStatsData}
                        earliestDate={earliestDate}
                        latestDate={latestDate}
                    />
                ),
            )}
            <Panel statsData={statsData} />
        </div>
    );
};

export default StatsDashboard;

import React, { useContext } from "react";
import { useSelector, useDispatch } from "react-redux";

// component
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Modal } from "semantic-ui-react";
import DropAlertMsg from "../../../../../common/imageCommon/drop/DropAlertMsg";
import Drop from "../../../../../common/imageCommon/drop/Drop";
// store
import { StoreContext } from "../../../../../../store/StoreProvider";
import helpers from "./filePickerHelper";
import FileAct from "../../../../../../reduxStore/file/fileAction";

const style = {
    modalTrigger: {
        width: "100px",
        height: "100px",
        margin: "0 8px 8px 0",
        padding: "4px",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        border: "2px dashed #737373",
        textAlign: "center",
        cursor: "pointer"
    }
};

// FilePickerModal可用於嵌入在 Gallery 中的 firstChild
const FilePickerModal = ({ onClose, type }) => {
    const dispatchRedux = useDispatch();
    const {
        files: { folderPattern },
        files
    } = useSelector(state => state);
    const { pickConfig, currentFolder, loading } = files;

    const [state] = useContext(StoreContext);
    const { role } = state.user;

    // local state
    const [open, setOpen] = React.useState(false);

    const triggerOnClick = () => {
        if (currentFolder.path.length === 0) {
            dispatchRedux({
                type: FileAct.PICKER_CUR_FOLDER_MESSAGE,
                payload: {
                    type: "warning",
                    title: "尚未選取要上傳的資料夾"
                }
            });
        } else {
            setOpen(true);
        }
    };

    const trigger = (
        <div
            style={style.modalTrigger}
            onClick={() => {
                if (role !== "reader") {
                    triggerOnClick();
                }
            }}
        >
            <Icon name="plus" />
        </div>
    );

    const handleClose = () => {
        setOpen(false);
        // 關閉的時候,清空上傳資料夾
        dispatchRedux({
            type: FileAct.UPLOAD_IMAGE,
            payload: []
        });
        dispatchRedux({
            type: FileAct.CLEAN_PICKER_CUR_FOLDER_MESSAGE
        });
        dispatchRedux({
            type: FileAct.CLEAN_PICKER_DROP_MESSAGE
        });
        if (onClose) onClose();
    };

    return (
        <Modal
            size="large"
            open={open}
            onClose={() => handleClose()}
            trigger={trigger}
        >
            <Dimmer active={loading.state} inverted>
                <Loader size="large">{loading.message}</Loader>
            </Dimmer>
            {/* <Modal.Header>挑選圖片</Modal.Header> */}
            <Modal.Content>
                <Drop
                    type={type}
                    config={{ ...pickConfig.uploadPage, withCheckbox: false }}
                    DropAlertMsg={DropAlertMsg}
                    handleDropCallback={helpers.handleDropCallback(
                        files,
                        dispatchRedux,
                        folderPattern
                    )}
                />
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={() => handleClose()}>完成</Button>
            </Modal.Actions>
        </Modal>
    );
};
export default FilePickerModal;

import React from "react";
import PropTypes from "prop-types";

// semantic ui
import { Button } from "semantic-ui-react";

function SaveBtn(props) {
    const { handleClick, label, loading, disabled } = props;
    const { style } = props;
    return (
        <Button
            style={style}
            primary
            onClick={handleClick}
            loading={loading}
            disabled={disabled}
        >
            {label}
        </Button>
    );
}

SaveBtn.propTypes = {
    /** onClick callback */
    handleClick: PropTypes.func,
    /** 儲存按鈕顯示名稱 */
    label: PropTypes.string,
    /** loading */
    loading: PropTypes.bool,
    /** disabled */
    disabled: PropTypes.bool,
    /** style */
    style: PropTypes.objectOf(PropTypes.string)
};

SaveBtn.defaultProps = {
    /** onClick callback */
    handleClick: () => {},
    /** 儲存按鈕顯示名稱 */
    label: "",
    /** loading */
    loading: false,
    /** disabled */
    disabled: false,
    /** style */
    style: {}
};

export default SaveBtn;

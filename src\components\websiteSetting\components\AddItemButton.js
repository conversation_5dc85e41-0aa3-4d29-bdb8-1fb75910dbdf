import React, { useContext } from "react";
import { <PERSON><PERSON>, I<PERSON> } from "semantic-ui-react";
import { StoreContext } from "../../../store/StoreProvider";
import { sortByPriority, returnEmptyKeyName, returnDefaultValue } from "../commons";
import Act from "../../../store/actions";

function AddItemButton({ setAllData }) {
    const [state, dispatch] = useContext(StoreContext);
    const { updatedData, menuActiveItem, selectOption } = state.websiteSetting;

    const AddItem = () => {
        const tmpAllData = JSON.parse(JSON.stringify(updatedData));
        const tmpObj = tmpAllData.find(element => element.id === menuActiveItem.key);
        let sortValue = [];
        if (selectOption) {
            // 處理LinkingPage
            const keys = Object.keys(tmpObj[selectOption]).filter(element => element !== "name");
            keys.forEach(element => sortValue.push(tmpObj[selectOption][element]));
            sortValue = sortByPriority(sortValue);
        } else {
            // 處理MainCarousel
            const keys = Object.keys(tmpObj).filter(element => element !== "id");
            const tmpObjValue = [];
            keys.forEach(element => tmpObjValue.push(tmpObj[element]));
            sortValue = sortByPriority(tmpObjValue);
        }
        // console.log("sortValue ", sortValue);
        const defaultValue = returnDefaultValue(menuActiveItem.key);
        if (sortValue.length > 0) {
            defaultValue.priority = sortValue[sortValue.length - 1].priority + 1;
        }
        let defaultKeyName = "";
        if (selectOption) {
            // 處理LinkingPage
            defaultKeyName = returnEmptyKeyName(selectOption) + defaultValue.priority;
            tmpObj[selectOption][defaultKeyName] = defaultValue;
        } else {
            // 處理MainCarousel
            defaultKeyName = returnEmptyKeyName("carousel") + defaultValue.priority;
            tmpObj[defaultKeyName] = defaultValue;
        }
        // console.log("tmpObj ", tmpObj);
        // setAllData讓畫面即時更新
        let tmpData = [];
        if (menuActiveItem.key === "MainCarousel") {
            Object.keys(tmpObj).filter(key => key !== "id")
                .forEach(element => {
                    tmpData.push(tmpObj[element]);
                });
        } else if (menuActiveItem.key === "LinkingPage") {
            Object.keys(tmpObj[selectOption]).filter(key => key !== "name")
                .forEach(element => {
                    tmpData.push(tmpObj[selectOption][element]);
                });
        }
        // console.log("tmpData ", tmpData);
        setAllData(sortByPriority(tmpData));

        // 設定updatedData
        const tmpObjIndex = tmpAllData.findIndex(element => element.id === menuActiveItem.key);
        tmpAllData[tmpObjIndex] = tmpObj;
        // console.log("tmpAllData ", tmpAllData);
        dispatch({
            type: Act.SET_UPDATEDDATA,
            payload: tmpAllData
        });
    };

    return (
        <Button icon circular style={{ background: "inherit", fontSize: "large" }}
            onClick={AddItem}
        >
            <Icon name='add circle'/>
        </Button>
    );
}

export default AddItemButton;
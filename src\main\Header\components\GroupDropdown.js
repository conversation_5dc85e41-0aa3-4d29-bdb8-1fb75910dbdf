import React, { useContext, useEffect, useState } from "react";
import { Dropdown, Menu } from "semantic-ui-react";
import { StoreContext } from "../../../store/StoreProvider";
import { isEmpty } from "../../../commons";
import Act from "../../../store/actions";

function GroupDropdown() {
    const [state, dispatch] = useContext(StoreContext);
    const { userAllGroupInfo } = state.user;
    const { groupInfo } = state.data;
    const [dpOptions, setDpOptions] = useState([]);
    const [optionIdx, setOptionIdx] = useState(0);

    useEffect(() => {
        if (isEmpty(userAllGroupInfo)) return;

        // 如果更新userAllGroupInfo已經先選定任意group，rerender需要指定相同group
        let initGPElementIdx = 0;
        if (!isEmpty(groupInfo)) {
            const findIdx = userAllGroupInfo.findIndex(
                el => el.id === groupInfo.id
            );
            if (findIdx > -1) {
                initGPElementIdx = findIdx;
            }
        }
        setOptionIdx(initGPElementIdx);

        const tmpOptions = userAllGroupInfo.map(gpInfo => {
            const { name, id } = gpInfo;
            return {
                key: id,
                text: name,
                value: id
            };
        });
        setDpOptions(tmpOptions);

        dispatch({
            type: Act.DATA_GROUPINFO,
            payload: userAllGroupInfo[initGPElementIdx]
        });
    }, [userAllGroupInfo, groupInfo]);

    const handleChange = data => {
        const findGP = userAllGroupInfo.find(el => el.id === data.value);
        if (findGP) {
            dispatch({
                type: Act.DATA_GROUPINFO,
                payload: findGP
            });
        }
    };

    return isEmpty(dpOptions) ? (
        <Menu.Item style={{ color: "red" }}>
            尚未加入群組，請通知管理員
        </Menu.Item>
    ) : (
        <Dropdown
            style={{
                minWidth: "initial",
                borderColor: "black",
                borderRadius: "0"
            }}
            selection
            pointing
            // 外譯房群組不顯示在nmtl後臺中
            options={dpOptions.filter(el => el.text !== "外譯房")}
            value={dpOptions[optionIdx].value}
            onChange={(evt, data) => handleChange(data)}
        />
    );
}

export default GroupDropdown;

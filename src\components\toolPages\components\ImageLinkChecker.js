/* eslint-disable no-nested-ternary */
/* eslint-disable no-await-in-loop */
/* eslint-disable no-restricted-syntax */
import React, { useEffect, useMemo, useState } from 'react';
import axios from 'axios';
import * as XLSX from 'xlsx';
import {
    Container,
    Header,
    Dropdown,
    Button,
    Table,
    Segment,
    Icon,
    Message,
    Label,
    Divider,
    Loader,
} from 'semantic-ui-react';
import Api from '../../../api/nmtl/Api';

const datasetOptions = [{ key: 'tlvm', text: '臺灣文學虛擬博物館', value: 'tlvm' }];
const formOptions = [{ key: 'event', text: '事件', value: 'event' }];

const isYouTubeUrl = (url) => /^(https?:\/\/)?((www|m)\.)?(youtube\.com|youtu\.be)\//i.test(url);

const normalizeYouTubeUrl = (url) => {
    try {
        const u = new URL(url);
        const host = u.hostname.replace(/^www\.|^m\./i, '');

        // youtu.be/VIDEO_ID
        if (host === 'youtu.be') {
            const id = u.pathname.split('/')[1];
            if (id) return `https://www.youtube.com/watch?v=${id}`;
        }

        if (host === 'youtube.com') {
            // /embed/VIDEO_ID
            if (u.pathname.startsWith('/embed/')) {
                const id = u.pathname.split('/')[2];
                if (id) return `https://www.youtube.com/watch?v=${id}`;
            }
            // /shorts/VIDEO_ID
            if (u.pathname.startsWith('/shorts/')) {
                const id = u.pathname.split('/')[2];
                if (id) return `https://www.youtube.com/watch?v=${id}`;
            }
            // /watch?v=VIDEO_ID
            const v = u.searchParams.get('v');
            if (v) return `https://www.youtube.com/watch?v=${v}`;
        }
    } catch (e) {
        console.log('normalizeYouTubeUrl error: ', e);
    }
    return url;
};

const checkImage = (url, timeout = 8000) =>
    new Promise((resolve) => {
        const img = new Image();
        let done = false;
        const timer = setTimeout(() => {
            if (done) return;
            done = true;
            img.src = '';
            resolve(false);
        }, timeout);

        img.onload = () => {
            if (done) return;
            done = true;
            clearTimeout(timer);
            resolve(true);
        };
        img.onerror = () => {
            if (done) return;
            done = true;
            clearTimeout(timer);
            resolve(false);
        };

        const bust = `${url.includes('?') ? '&' : '?'}cacheBust=${Date.now()}`;
        img.src = url + bust;
    });

const checkYouTube = async (url) => {
    try {
        const normalized = normalizeYouTubeUrl(url);
        await axios.get('https://www.youtube.com/oembed', {
            params: { url: normalized, format: 'json' },
            timeout: 8000,
        });
        return true;
    } catch (e) {
        return false;
    }
};

const pMap = async (items, mapper, concurrency = 10, onProgress) => {
    const ret = [];
    let i = 0;
    let running = 0;
    return new Promise((resolve) => {
        const next = () => {
            if (i >= items.length && running === 0) return resolve(ret);
            while (running < concurrency && i < items.length) {
                const curIndex = i++;
                running += 1;
                Promise.resolve(mapper(items[curIndex], curIndex))
                    .then((val) => {
                        ret[curIndex] = val;
                        if (onProgress) onProgress(curIndex + 1, items.length);
                    })
                    .catch(() => {
                        ret[curIndex] = undefined;
                    })
                    .finally(() => {
                        running -= 1;
                        next();
                    });
            }
        };
        next();
    });
};

const LinkChecker = () => {
    const [dataset, setDataset] = useState('');
    const [form, setForm] = useState('');
    const [links, setLinks] = useState([]);
    const [checkedLinks, setCheckedLinks] = useState([]);
    const [loading, setLoading] = useState(false);
    const [checking, setChecking] = useState(false);
    const [progress, setProgress] = useState({ done: 0, total: 0 });
    const [filter, setFilter] = useState('all');

    const validCount = checkedLinks.filter((r) => r.valid).length;
    const invalidCount = checkedLinks.filter((r) => r.valid === false).length;
    const mainBtnText = loading ? '載入中' : checking ? '檢查中' : '檢查';

    const filteredRows = useMemo(() => {
        const data = checkedLinks.length > 0 ? checkedLinks : links;
        if (filter === 'valid') return data.filter((r) => r.valid === true);
        if (filter === 'invalid') return data.filter((r) => r.valid === false);
        return data;
    }, [checkedLinks, links, filter]);

    const handleCheck = async () => {
        if (!links.length) return;
        setChecking(true);
        setProgress({ done: 0, total: links.length });

        const results = await pMap(
            links,
            async (link) => {
                const ok = isYouTubeUrl(link.link)
                    ? await checkYouTube(link.link)
                    : await checkImage(link.link);
                return { ...link, valid: ok };
            },
            10,
            (done, total) => setProgress({ done, total }),
        );

        setCheckedLinks(results);
        setChecking(false);
    };

    const handleDownloadExcel = () => {
        const rows =
            filteredRows.length > 0 ? filteredRows : checkedLinks.length > 0 ? checkedLinks : links;
        if (!rows.length || !dataset || !form) return;

        const data = rows.map((r) => ({
            事件ID: r.id,
            連結: r.link,
            狀態: r.valid === true ? '有效' : r.valid === false ? '失效' : '尚未檢查',
        }));

        const wb = XLSX.utils.book_new();
        const ws = XLSX.utils.json_to_sheet(data, { skipHeader: false });

        const colWidths = Object.keys(data[0]).map((key) => ({
            wch:
                Math.max(
                    key.length,
                    ...data.map((row) => (row[key] ? String(row[key]).length : 0)),
                ) + 2,
        }));
        ws['!cols'] = colWidths;

        XLSX.utils.book_append_sheet(wb, ws, '檢查結果');

        const date = new Date();
        const y = date.getFullYear();
        const m = String(date.getMonth() + 1).padStart(2, '0');
        const d = String(date.getDate()).padStart(2, '0');
        const filename = `link-check-${dataset}-${form}-${y}${m}${d}.xlsx`;
        XLSX.writeFile(wb, filename);
    };

    useEffect(() => {
        if (!dataset || !form) {
            setLinks([]);
            setCheckedLinks([]);
            return;
        }

        const getLinks = async () => {
            try {
                setLoading(true);
                const res = await axios.get(Api.getEventImageLinkList(dataset));

                if (!res?.data?.data || res.data.data.length === 0) return;

                const data = res.data.data
                    .filter((d) => d && d.id && d.link)
                    .map((d) => ({ id: String(d.id), link: String(d.link) }));

                setLinks(data);
            } catch (e) {
                console.log('getLinks error: ', e);
            } finally {
                setLoading(false);
            }
        };

        getLinks();
    }, [dataset, form]);

    return (
        <Container style={{ padding: 24 }}>
            <Header as="h2" content="圖片連結檢查" />

            <Divider />

            <Segment>
                <Header as="h4" content="選擇來源" />
                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 12 }}>
                    <Dropdown
                        placeholder="選擇資料集"
                        fluid
                        selection
                        options={datasetOptions}
                        value={dataset || undefined}
                        disabled={checking || loading}
                        onChange={(_, data) => setDataset(data.value)}
                    />
                    <Dropdown
                        placeholder="選擇表單"
                        fluid
                        selection
                        options={formOptions}
                        value={form || undefined}
                        disabled={checking || loading}
                        onChange={(_, data) => setForm(data.value)}
                    />
                </div>

                <Divider />

                <div
                    style={{
                        display: 'flex',
                        gap: 12,
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        flexWrap: 'wrap',
                    }}
                >
                    <div>
                        {checking && (
                            <Label basic>
                                進度：{progress.done}/{progress.total}
                            </Label>
                        )}
                    </div>
                    <div style={{ display: 'flex', gap: 8 }}>
                        <Button
                            primary
                            icon
                            labelPosition="left"
                            onClick={handleCheck}
                            disabled={!links.length || loading || checking}
                        >
                            <Icon
                                name={loading || checking ? 'spinner' : 'play'}
                                loading={loading || checking}
                            />
                            {mainBtnText}
                        </Button>

                        <Button
                            color="teal"
                            icon
                            labelPosition="left"
                            onClick={handleDownloadExcel}
                            disabled={
                                (!links.length && !checkedLinks.length) || loading || checking
                            }
                        >
                            <Icon name="download" />
                            下載
                        </Button>
                    </div>
                </div>

                {(!dataset || !form) && (
                    <Message info style={{ marginTop: 12 }}>
                        <Message.Header>請先選擇「資料集」與「表單」</Message.Header>
                        <p>選好後會載入對應的 URL 清單</p>
                    </Message>
                )}
            </Segment>

            {links.length > 0 && (
                <Segment>
                    {loading || checking ? (
                        <Segment
                            style={{
                                display: 'flex',
                                justifyContent: 'center',
                                alignItems: 'center',
                                minHeight: 300,
                            }}
                        >
                            <Loader active inline="centered" />
                        </Segment>
                    ) : (
                        <>
                            <Header as="h4">檢查結果</Header>

                            <div
                                style={{
                                    display: 'flex',
                                    gap: 12,
                                    alignItems: 'center',
                                    justifyContent: 'space-between',
                                    flexWrap: 'wrap',
                                }}
                            >
                                <div>
                                    <Label color="blue" basic>
                                        目前清單：{links.length} 筆 URL
                                    </Label>
                                    {checkedLinks.length > 0 && (
                                        <Label color="green" basic>
                                            有效：{validCount}
                                        </Label>
                                    )}
                                    {checkedLinks.length > 0 && (
                                        <Label color="red" basic>
                                            失效：{invalidCount}
                                        </Label>
                                    )}
                                </div>
                                <Dropdown
                                    selection
                                    options={[
                                        { key: 'all', text: '全部', value: 'all' },
                                        { key: 'valid', text: '有效', value: 'valid' },
                                        { key: 'invalid', text: '失效', value: 'invalid' },
                                    ]}
                                    value={filter}
                                    onChange={(_, data) => setFilter(data.value)}
                                />
                            </div>

                            <Table celled striped selectable compact>
                                <Table.Header>
                                    <Table.Row>
                                        <Table.HeaderCell width={2}>事件ID</Table.HeaderCell>
                                        <Table.HeaderCell width={12}>連結</Table.HeaderCell>
                                        <Table.HeaderCell width={2}>狀態</Table.HeaderCell>
                                    </Table.Row>
                                </Table.Header>
                                <Table.Body>
                                    {(filteredRows.length > 0
                                        ? filteredRows
                                        : checkedLinks.length > 0
                                        ? checkedLinks
                                        : links
                                    ).map((r) => (
                                        <Table.Row
                                            key={r.id}
                                            positive={r.valid === true}
                                            negative={r.valid === false}
                                        >
                                            <Table.Cell>
                                                <p>{r.id}</p>
                                            </Table.Cell>
                                            <Table.Cell style={{ wordBreak: 'break-all' }}>
                                                <a href={r.link} target="_blank" rel="noreferrer">
                                                    {r.link}
                                                </a>
                                            </Table.Cell>
                                            <Table.Cell>
                                                {r.valid === true && (
                                                    <>
                                                        <Icon name="check circle" color="green" />{' '}
                                                        有效
                                                    </>
                                                )}
                                                {r.valid === false && (
                                                    <>
                                                        <Icon name="times circle" color="red" />{' '}
                                                        失效
                                                    </>
                                                )}
                                                {r.valid === undefined && (
                                                    <>
                                                        <Icon name="hourglass half" /> 尚未檢查
                                                    </>
                                                )}
                                            </Table.Cell>
                                        </Table.Row>
                                    ))}
                                </Table.Body>
                            </Table>
                        </>
                    )}
                </Segment>
            )}
        </Container>
    );
};

export default LinkChecker;

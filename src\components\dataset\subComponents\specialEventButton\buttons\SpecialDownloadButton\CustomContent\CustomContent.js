import { Divider, Form, Grid } from "semantic-ui-react";
import React from "react";
import { TABLE_KEY } from "../downloadConfig";

const CustomContent = ({ curMenu, record, setRecord }) => {
    const handleCheckAll = (value, tbKey, infoKey) => {
        const copyRecord = JSON.parse(JSON.stringify(record));
        const tbArr = copyRecord[curMenu][tbKey];
        const infoArr = copyRecord[curMenu][infoKey];

        copyRecord[curMenu][tbKey] = tbArr.map(el => ({
            ...el,
            checked: value.checked ? value.checked : !value.checked
        }));
        copyRecord[curMenu][infoKey] = infoArr.map(el => ({
            ...el,
            checked: value.checked
        }));

        setRecord(copyRecord);
    };

    const handleCheck = (value, tbKey) => {
        const copyRecord = JSON.parse(JSON.stringify(record));
        const tmpArr = copyRecord[curMenu][tbKey];
        const findVal = tmpArr.find(el => el.id === value.id);
        if (findVal) {
            findVal.checked = !findVal.checked;
        }
        setRecord(copyRecord);
    };

    return (
        <Form>
            <Form.Group grouped widths="equal">
                <Grid>
                    <Grid.Row columns={5}>
                        <Grid.Column>
                            <Form.Checkbox
                                label="全選"
                                value="all"
                                onClick={(e, value) =>
                                    handleCheckAll(
                                        value,
                                        [TABLE_KEY.tableArr],
                                        [TABLE_KEY.infoArr]
                                    )
                                }
                            />
                        </Grid.Column>
                        {record &&
                            record[curMenu] &&
                            record[curMenu].tableArr.map(dataItem => {
                                const { id, label, checked } = dataItem;
                                return (
                                    <Grid.Column key={`${label}-${id}`}>
                                        <Form.Checkbox
                                            id={id}
                                            label={label}
                                            value={label}
                                            onClick={(e, value) =>
                                                handleCheck(value, [
                                                    TABLE_KEY.tableArr
                                                ])
                                            }
                                            checked={checked}
                                        />
                                    </Grid.Column>
                                );
                            })}
                    </Grid.Row>
                    <Divider />
                    <Grid.Row columns={5}>
                        {record &&
                            record[curMenu] &&
                            record[curMenu].infoArr.map(dataItem => {
                                const { id, label, checked } = dataItem;
                                return (
                                    <Grid.Column key={`${label}-${id}`}>
                                        <Form.Checkbox
                                            id={id}
                                            label={label}
                                            value={label}
                                            onClick={(e, value) =>
                                                handleCheck(value, [
                                                    TABLE_KEY.infoArr
                                                ])
                                            }
                                            checked={checked}
                                        />
                                    </Grid.Column>
                                );
                            })}
                    </Grid.Row>
                </Grid>
            </Form.Group>
        </Form>
    );
};

export default CustomContent;

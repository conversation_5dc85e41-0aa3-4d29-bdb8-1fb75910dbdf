import React, { useContext, useEffect, useState } from "react";

// plugins
import { useDispatch, useSelector } from "react-redux";
import PropTypes from "prop-types";
import { <PERSON>ton, Icon } from "semantic-ui-react";

// config
import clsName from "../../Utils/clsName";
import { isEmpty } from "../../../../../../../commons";
import { delNews } from "../../Utils/utils";
import NewsAct from "../../EditNewsAction";
import { StoreContext } from "../../../../../../../store/StoreProvider";
import getFullNewsInfo from "../../Utils/getFullNewsInfo";
import { createHistoryEvent } from "../../../../../../downloadData/components/history/common/common";
import useGetSubjectOPs from "../../../../../../common/hooks/useGetSubjectOPs";

function EditBtn({ selectItem, className, action, id }) {
    const newsDispatch = useDispatch();
    const { modalCaller, modalSelect, updateNewsInfo } = useSelector(
        state => state
    );

    const [oldState] = useContext(StoreContext);
    const { websiteSubject, menuActiveItem } = oldState.websiteSetting;

    const { displayName } = oldState.user;
    const { headerActiveName } = oldState.common;

    const { groupInfo } = oldState.data;
    const dropOptions = useGetSubjectOPs(groupInfo);

    const sheetSelectedValue = dropOptions.find(it => it.key === websiteSubject)
        ?.text;
    const columns = [headerActiveName, sheetSelectedValue, menuActiveItem.name];

    const [iconStr, setIconStr] = useState("");
    const [color, setColor] = useState("black");

    useEffect(() => {
        switch (className) {
            case clsName.EditBtn:
                setIconStr("edit");
                break;
            case clsName.RemoveBtn:
                setIconStr("trash alternate");
                setColor("red");
                break;
            default:
                break;
        }
    }, []);

    // 點選RemoveBtn跳出Modal後，點選"確認"的後續動作
    useEffect(() => {
        if (modalSelect === null || isEmpty(updateNewsInfo)) return;
        if (
            modalCaller === clsName.RemoveBtn &&
            className === clsName.RemoveBtn &&
            id === updateNewsInfo.newsIdStr
        ) {
            if (modalSelect) {
                delNews(
                    newsDispatch,
                    updateNewsInfo,
                    updateNewsInfo,
                    oldState.user,
                    websiteSubject
                );

                const historyMsg = `${JSON.stringify(updateNewsInfo)}`;

                // 建立歷史紀錄
                createHistoryEvent(
                    displayName,
                    "刪除",
                    `${columns.join("/")}：${historyMsg}`
                );
            }
        }
    }, [modalCaller, modalSelect, updateNewsInfo]);

    return (
        <Button
            icon
            inverted
            style={{
                fontSize: "large",
                color
            }}
            onClick={evt => {
                evt.stopPropagation();
                getFullNewsInfo(selectItem, newsDispatch);
                if (className === clsName.EditBtn) {
                    action(selectItem);
                } else if (className === clsName.RemoveBtn) {
                    newsDispatch({
                        type: NewsAct.SET_NEWSBRIEFINFO,
                        payload: selectItem
                    });

                    action(newsDispatch, className);
                }
            }}
        >
            <Icon name={iconStr} />
        </Button>
    );
}

EditBtn.propTypes = {
    /** component caller */
    className: PropTypes.string,
    /** select item data */
    selectItem: PropTypes.shape({
        newsIdStr: PropTypes.string,
        newsType: PropTypes.string,
        hasStartDate: PropTypes.string,
        title: PropTypes.string,
        newsCreator: PropTypes.string,
        status: PropTypes.string
    }),
    /** button action callback */
    action: PropTypes.func,
    /** News ID */
    id: PropTypes.string
};

EditBtn.defaultProps = {
    /** component caller */
    className: "",
    /** select item data */
    selectItem: {},
    /** button action callback */
    action: () => {},
    /** News ID */
    id: ""
};

export default EditBtn;

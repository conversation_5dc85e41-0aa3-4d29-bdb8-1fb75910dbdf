import React, { useState, useMemo, useEffect } from "react";

// ui
import { Input } from "semantic-ui-react";
import useDebounce from "../../../../common/hooks/useDebounce";

// store
const CustomSingleInput = ({
    cellId,
    defaultValue,
    createState,
    disabled = false,
    setCallback,
    menuName
    // ...rest
}) => {
    // input value
    const [inputValue, setInputValue] = useState(defaultValue || "");
    const debInputValue = useDebounce(inputValue, 100); // debounce value

    // searchbar change
    const handleChange = value => {
        if (disabled) {
            return;
        }

        setInputValue(value);
    };
    //
    useEffect(() => {
        if (!debInputValue || debInputValue === "") {
            setCallback(cellId, null, menuName);
            return;
        }

        // if (!debInputValue) return;

        setCallback(cellId, debInputValue, menuName);
    }, [debInputValue]);

    return useMemo(
        () => (
            <Input
                fluid
                value={inputValue || ""}
                // onChange event
                onChange={(e, data) => handleChange(data.value)}
            />
        ),
        [cellId, inputValue, createState]
    );
};

export default CustomSingleInput;

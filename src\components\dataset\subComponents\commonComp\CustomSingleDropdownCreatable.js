import React, { useContext, useState, useMemo, useEffect, useRef } from 'react';

// store
import { createFilter } from 'react-select';
import CreatableSelect from 'react-select/creatable';
import { StoreContext } from '../../../../store/StoreProvider';
import { getMenuPlacement } from '../../datasetConfig';

// custom ui
import MenuList from './MenuList';

// common
import {
  MAX_OPTION,
  CREATED_PREFIX,
  notShowingExistedModalHeader,
  graph4CreateInstance,
} from '../../../common/sheetCrud/sheetCrudHelper';

// api
import Act from '../../../../store/actions';
import {
  checkDataInGraph,
  existedDataInOtherGraph,
  getApiByAllField,
  returnClasstype,
} from '../../../../api/nmtl/ApiField';
import { createNmtlData } from '../../../../api/nmtl';
import Api from '../../../../api/nmtl/Api';
import { getReservedNewId } from '../../../common/sheetCrud/utils';
import getKeyBySingle from '../../../../api/nmtl/ApiKey';
import getSingleByApi from '../../../../api/nmtl/ApiSingle';
import { isEmpty } from '../../../../commons';
import ExistedModalForOldForm from './ExistedModalForOldForm';

const CustomSingleDropdownCreatable = ({
  cellId,
  rowId,
  // default 0
  idx = 0,
  defaultValue,
  createState,
  setCallback,
  isCreateNew = false,
  isDiffValue = false,
}) => {
  const [state, dispatch] = useContext(StoreContext);
  const { user } = state;
  const { sheet, mainSubject } = state.data;
  const { dataset } = mainSubject.selected;
  const { key: sheetName } = sheet.selected;
  const { headerFields } = sheet;

  const [equalValue, setEqualValue] = useState(null);
  const [open, setOpen] = useState(false);
  const [inputIds, setInputIds] = useState([]);
  const [inputValue, setInputValue] = useState(defaultValue);
  const notShowingExistedModal = notShowingExistedModalHeader.includes(cellId);

  const hasCreatedDataInlocalStorage = useRef(false);

  // change input value
  const handleChange = async (selectValue) => {
    const sltVal = selectValue === null ? { [idx]: null } : { [idx]: selectValue.id };
    const cellValue = { ...createState.value, ...sltVal };
    // 如果selectValue已存在其他資料集，跳modal讓使用者選擇要搬動的資料
    if (!isEmpty(selectValue)) {
      const cellValueArr = [];
      cellValueArr.push(selectValue);

      const selectCombinedIds = cellValueArr.map((item) => item.id).sort();
      setInputIds(selectCombinedIds);

      const isInGraph = !isEmpty(cellValueArr)
        ? await checkDataInGraph(cellValueArr?.at(-1).id, dataset)
        : true;
      const tmpClasstype =
        !isEmpty(cellValueArr) && returnClasstype(cellId, cellValueArr.at(-1)?.label);

      if (!isInGraph && !isEmpty(selectValue) && !notShowingExistedModal) {
        const sameList = await existedDataInOtherGraph(
          tmpClasstype,
          cellValueArr.at(-1).label,
          cellId,
        );

        setEqualValue(sameList);
        setOpen(true);
        return;
      }
    }

    // change input background color when value is diff
    if (createState.value?.[idx] === selectValue?.id && !hasCreatedDataInlocalStorage.current) {
      dispatch({
        type: Act.DATA_CONTENT_ROW_NO_CHANGED,
        payload: {
          rowId,
          cellId,
          idx,
        },
      });
    } else {
      dispatch({
        type: Act.DATA_CONTENT_ROW_CHANGED,
        payload: {
          rowId,
          cellId,
          cellValue,
        },
      });
    }

    // 最後再 setCallback 去 change CreateState
    setCallback(cellId, rowId, idx, {
      isOption: true,
      value: cellValue,
    });
  };

  const handleInputChange = (value) => {
    if (!value) {
      return;
    }
    setInputValue(value);
    // 不用更新 createState
    // setCallback(cellId, rowId, idx, { isOption: true, input: inputValue });
  };

  const addToOptions = ({ newSrcId, newApi, label }) => {
    const newOpt = {
      id: newSrcId,
      label,
      value: newSrcId,
    };

    // 如果此欄位為多重 type，增加到該 type
    // hasPublisher: ORG, PER
    if (newApi && Object.keys(headerFields).indexOf(newApi)) {
      headerFields[newApi].push(newOpt);
    }

    // 更新目前的總列表
    if (!createState.options) {
      // eslint-disable-next-line no-param-reassign
      createState.options = [];
    }
    createState.options.push(newOpt);

    setCallback(cellId, rowId, idx, {
      isOption: true,
      input: null,
      // value: { [idx]: label },
      value: { [idx]: newSrcId },
      // options: createState.options.concat([newOpt])
      options: createState.options,
    });
  };

  const getDatasetByClass = (newClass) => {
    // if newClass is Person, return "authority"
    if (newClass in graph4CreateInstance) {
      return graph4CreateInstance[newClass];
    }
    return dataset;
  };

  const getCreateNmtlItemResult = async (item, newClass, newApi) => {
    if (isEmpty(item) || isEmpty(dataset) || isEmpty(sheetName)) {
      return { newSrcId: null };
    }
    const apiUrl = Api.getGeneric;
    const itemKey = getKeyBySingle(getSingleByApi(newApi));

    const createGraph = getDatasetByClass(newClass);

    // 先 reserved id
    const newSrcId = await getReservedNewId(itemKey);
    if (apiUrl && itemKey) {
      const newItem = {
        graph: createGraph,
        classType: newClass || itemKey,
        srcId: newSrcId || '',
        value: {
          label: item,
        },
      };

      const createResult = await createNmtlData(user, apiUrl, dataset, sheetName, newItem).then(
        (res) => res === 'OK',
      );

      return { newSrcId, createResult };
    }
    return { newSrcId: null };
  };

  const handleCreate = async (inputVal) => {
    // 在 Component 裡頭 create，CreateButton 也有自己的 create.
    if (isCreateNew) {
      // 如果沒有任何需要 create 的，表示此為選擇已存在的 instance
      // 將此 instance 加入表格
      // suffix feature
      // apiArr: Array()
      const apiArr = getApiByAllField(cellId);
      if (apiArr.length > 1) {
        console.error('Not implemented!!', apiArr);
        return;
      }

      // 假設這個欄位只有對應一種類別
      const newApi = apiArr[0];
      // 取得 cellId 對應的 class
      const newClass = getKeyBySingle(getSingleByApi(newApi));

      if (!newClass) {
        return;
      }

      const newValue = inputVal;

      const { newSrcId } = await getCreateNmtlItemResult(newValue, newClass, newApi);

      if (newSrcId) {
        addToOptions({ newSrcId, newApi, label: newValue, cellId });

        dispatch({
          type: Act.DATA_MESSAGE,
          payload: {
            title: `${newValue} 已創建完成`,
            error: 0,
            // renderSignal 會讓 ContentView 重新渲染，所有的值會 refresh
            // renderSignal: `update-${new Date().getTime()}`
          },
        });

        // 創建完把值放進 localStorage，等待 refresh 完 呼叫handleChange
        if (dataset !== 'authority') {
          localStorage.setItem(
            `singleDropdownCreate_${rowId}_${cellId}_${idx}`,
            JSON.stringify({ cellId, rowId, idx, srcId: newSrcId, label: newValue }),
          );
        }
      }
    } else {
      // CreateButton
      const newValue = `${CREATED_PREFIX}_${inputVal}`;
      const newOpt = {
        id: newValue,
        label: inputVal,
        value: newValue,
      };

      setCallback(cellId, rowId, idx, {
        isOption: true,
        input: null,
        value: { [idx]: newValue },
        options: createState.options.concat(newOpt),
      });
    }
  };

  const handleModalClose = () => {
    if (hasCreatedDataInlocalStorage.current) {
      localStorage.removeItem(`singleDropdownCreate_${rowId}_${cellId}_${idx}`);
      hasCreatedDataInlocalStorage.current = false;
    }
  };

  const customStyles = {
    container: (styles) => ({
      ...styles,
      margin: '-9px',
      minWidth: '100%',
    }),
    control: (styles, { selectProps: { controlColor } }) => ({
      ...styles,
      borderStyle: 'none',
      borderRadius: 'unset',
      backgroundColor: controlColor,
    }),
  };

  const customPlacement = getMenuPlacement(rowId);
  const customControlBgColor = isDiffValue ? '#f9c09a66' : '';

  // 從localStorage拿出創建完的值，呼叫handleChange
  useEffect(() => {
    const createdData = JSON.parse(
      localStorage.getItem(`singleDropdownCreate_${rowId}_${cellId}_${idx}`),
    );

    if (!isEmpty(createdData)) {
      hasCreatedDataInlocalStorage.current = true;

      const changeVal = {
        id: createdData.srcId,
        label: createdData.label,
        value: createdData.srcId,
      };

      handleChange(changeVal);
    }
  }, []);

  return useMemo(
    () => (
      <>
        <CreatableSelect
          isClearable
          styles={customStyles}
          isDisabled={createState.isLoading}
          isLoading={createState.isLoading}
          options={
            inputValue
              ? createState.options.filter((o) => o.label.includes(inputValue)).slice(0, MAX_OPTION)
              : createState.options.slice(0, MAX_OPTION)
          }
          value={
            createState.value
              ? createState.options
                  .filter((o) => o.id === createState.value[idx])
                  .slice(0, MAX_OPTION)
              : null
          }
          onChange={handleChange}
          onInputChange={handleInputChange}
          onCreateOption={handleCreate}
          components={{ MenuList }}
          menuPlacement={customPlacement}
          controlColor={customControlBgColor}
          // [fixed] select lag issue: https://github.com/JedWatson/react-select/issues/3128 by M1K3Yio commented on 19 Oct 2018
          filterOption={createFilter({ ignoreAccents: false })}
        />
        <ExistedModalForOldForm
          equalValue={equalValue}
          open={open}
          setOpen={setOpen}
          inputIds={inputIds}
          cellId={cellId}
          rowId={rowId}
          idx={idx}
          setCallback={setCallback}
          handleClose={handleModalClose}
        />
      </>
    ),
    [
      cellId,
      createState.value,
      createState.input,
      createState.isLoading,
      inputValue,
      open,
      inputIds,
    ],
  );
};

export default CustomSingleDropdownCreatable;

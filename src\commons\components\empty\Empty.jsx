import React from "react";
import PropTypes from "prop-types";
import classNames from "classnames";
import emptyIcon from "./image/emptyIcon.svg";

import "./empty.scss";

const Empty = ({ className, description, emptySrc, imageStyle }) => (
    <div className={classNames("antd-empty", className)}>
        <div className="ant-empty-image">
            <img
                src={emptySrc || emptyIcon}
                alt={description}
                style={imageStyle}
            />
            {description && (
                <div className="ant-empty-description">{description}</div>
            )}
        </div>
    </div>
);

Empty.defaultProps = {
    className: undefined,
    emptySrc: undefined,
    description: "No Data",
    imageStyle: {}
};

Empty.propTypes = {
    /** Css名稱 */
    className: PropTypes.string,
    /** 空白時圖案的Src */
    emptySrc: PropTypes.string,
    /** 描述 */
    description: PropTypes.node,
    /** 圖案的style */
    imageStyle: PropTypes.objectOf(PropTypes.any)
};

export default Empty;

import React, { useCallback, useContext, useEffect, useState } from 'react';

// ui
import { Message, Segment, Header } from 'semantic-ui-react';

// custom
import { sortedByStroke } from 'twchar';
import CustomDownloadAllPageButton from './CustomDownloadAllPageButton';

// store
import Api from '../../../../../../../../api/nmtl/Api';
import { StoreContext } from '../../../../../../../../store/StoreProvider';

// api
import { readNmtlData } from '../../../../../../../../api/nmtl';

const CustomAllTab = () => {
    const [isLoading, setIsLoading] = useState(false);
    const [batchSize, setBatchSize] = useState(0);
    const [error, setError] = useState(undefined);
    const excelFileLimit = 10000;

    const [state] = useContext(StoreContext);
    // get dataset(mainSubject) and sheet
    const { mainSubject, sheet, search } = state.data;
    // extract parameter for sidebar selected item
    const { dataset: datasetLabel } = mainSubject.selected;
    const { contentClassList } = sheet.selected;
    const { keyword } = search;

    // get full api url
    const getSortedIdsLength = async () => {
        if (datasetLabel && contentClassList) {
            // combine url and parameter
            const qryStr = contentClassList.replace('{ds}', datasetLabel);

            const classList = await readNmtlData(qryStr);
            const resData = sortedByStroke(classList.data, 'label');
            return { total: resData.length, error: null };
        }
        return { total: 0, error: null };
    };

    // get count
    const handleGetCount = useCallback(async () => {
        // show loading
        setIsLoading(true);
        const { total, error: errorMsg } = await getSortedIdsLength();
        if (!errorMsg) {
            setBatchSize(total);
        } else {
            setError(errorMsg);
        }

        // disable loading
        setIsLoading(false);
    }, [keyword]);

    useEffect(() => {
        handleGetCount();
    }, []);

    const customStyle = {
        minHeight: '50px',
    };

    return (
        <React.Fragment>
            <Message warning>
                <Message.Header>檔案資訊</Message.Header>
                <p>
                    下載的檔案內容將會包含所有的項目！如果檔案內容項目超過10,000筆資料，則該檔案將會被劃分為多份檔案，而每個檔案最多為10,000筆資料
                </p>
            </Message>
            {isLoading ? (
                <Segment style={customStyle}>正在載入中...</Segment>
            ) : error ? (
                <Header>{`無法載入: ${error}`}</Header>
            ) : (
                <CustomDownloadAllPageButton range={batchSize} limit={excelFileLimit} />
            )}
        </React.Fragment>
    );
};

export default CustomAllTab;

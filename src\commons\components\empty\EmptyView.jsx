import React from "react";
import PropTypes from "prop-types";
import classNames from "classnames";
import Empty from "./Empty";

import "./emptyView.scss";

import { isEmpty } from "../../index";

const EmptyView = ({ children, data, className }) => (
    <React.Fragment>
        {isEmpty(data) ? (
            <Empty className={classNames("emptyView", className)} />
        ) : (
            children
        )}
    </React.Fragment>
);

EmptyView.defaultProps = {
    data: null,
    children: null,
    className: null
};

EmptyView.propTypes = {
    data: PropTypes.oneOfType([
        PropTypes.arrayOf(PropTypes.any),
        PropTypes.objectOf(PropTypes.any)
    ]),
    children: PropTypes.oneOfType([
        PropTypes.arrayOf(PropTypes.any),
        PropTypes.objectOf(PropTypes.any)
    ]),
    className: PropTypes.string
};

export default EmptyView;

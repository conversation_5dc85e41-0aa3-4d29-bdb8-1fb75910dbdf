// ./UploadTab.jsx
import React, { useState } from 'react';
import { Grid, Segment } from 'semantic-ui-react';

// components
import FolderMenu from './uploadImage/FolderMenu';
import CurrentFolder from './uploadImage/curFolder';
import FilePicker from './uploadImage/FilePicker';
import CurrentImage from './CurrentImage';

// helper
import uploadConfig from '../../../../toolPages/components/upload/uploadConfig';

const UploadImage = ({ defaultValue, currentValue }) => {
    const [pickMethod, setPickMethod] = useState('store'); // ENUM("store", "upload")

    const selectMode = {
        store: {
            name: 'store',
            label: '從圖片庫中挑選',
            onClick: () => setPickMethod('store'),
            leftComp: <FolderMenu />,
            rightComp: <CurrentFolder type={uploadConfig.image} />,
        },
        upload: {
            name: 'upload',
            label: '上傳圖片',
            onClick: () => setPickMethod('upload'),
            leftComp: <FolderMenu />,
            rightComp: <FilePicker type={uploadConfig.image} />,
        },
    };

    return (
        <>
            {/* 目前圖片預覽（依 store 狀態） */}
            <CurrentImage defaultValue={defaultValue} currentValue={currentValue} />

            {/* 若需要顯示切換按鈕，可解開下列 UI
      <Button.Group style={{ marginBottom: 12 }}>
        {Object.values(selectMode).map((btn) => (
          <Button
            key={btn.name}
            positive={pickMethod === btn.name}
            onClick={btn.onClick}
          >
            {btn.label}
          </Button>
        ))}
      </Button.Group>
      */}

            <Grid textAlign="center" celled stackable>
                <Grid.Row>
                    <Grid.Column width={3}>
                        <Segment basic compact>
                            {/* 左側：資料夾選單 */}
                            {pickMethod in selectMode && selectMode[pickMethod].leftComp}
                        </Segment>
                    </Grid.Column>
                    <Grid.Column width={13}>
                        {/* 右側：依模式切換 */}
                        {pickMethod in selectMode && selectMode[pickMethod].rightComp}
                    </Grid.Column>
                </Grid.Row>
            </Grid>
        </>
    );
};

export default UploadImage;

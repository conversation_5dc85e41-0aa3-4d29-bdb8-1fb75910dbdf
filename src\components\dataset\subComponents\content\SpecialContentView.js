import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';
import { Table, Icon, Header, Loader, Image, Container } from 'semantic-ui-react';
import { sortedByStroke } from 'twchar';
import { useHistory } from 'react-router-dom';
import { getSheetHeader, getSheetTabHeader } from '../../../../api/firebase/cloudFirestore';
import Act from '../../../../store/actions';
import { isEmpty } from '../../../../commons';
import { StoreContext } from '../../../../store/StoreProvider';
import {
    handleSorting,
    isNotShownInTable,
    multiConvertToCreateState,
    readContent,
    timeStampToDate,
} from '../../../common/sheetCrud/utils';
import { readNmtlData } from '../../../../api/nmtl';
import Api from '../../../../api/nmtl/Api';
import CustomHeaderInformation from '../headerInformation';
import CustomCheckBox from './CustomCheckBox/CustomCheckBox';
import { addSuffix, getApiByAllField } from '../../../../api/nmtl/ApiField';
import { uuidv4 } from '../../../../commons/utility';
import editIcon from '../../../../images/<EMAIL>';
import { LAST_MODIFIED, PEICES_INFO, textArray } from '../../../common/sheetCrud/sheetCrudHelper';
import {
    EDIT_ROW,
    editIconStyle,
    sortedMethod,
    sortedStyle,
    widthStyle,
    hiddenHeightStyle,
} from './ContentConfig';
import SpecialCheckBox from './CustomCheckBox/SpecialCheckBox';
import SpecialAllCheckBox from './CustomCheckBox/SpecialAllCheckBox';
import { escapeRegExpKeyword } from '../../../../commons/escapeRegExp';
import settingSearchKeyword from '../../../../commons/settingSearchKeyword';
import { getTextAndLabel } from '../../../../commons/transferKeyword';

const SpecialContentView = ({ curState }) => {
    const [state, dispatch] = useContext(StoreContext);
    // get dataset(mainSubject) and sheet
    const {
        mainSubject,
        sheet,
        content: ct,
        pagination,
        message,
        search,
        groupInfo,
        sorted,
        // imageEditor
    } = state.data;

    // extract parameter for sidebar selected item
    const { dataset } = mainSubject.selected;
    const {
        key: sheetName,
        contentReadPath,
        contentClassList,
        displayHeader,
        oriHeader,
    } = curState;
    const { checked } = ct;
    const {
        header,
        activeHeader,
        activeHeaderCount,
        headerFields,
        activeHeaderAllChecked,
        tabKey,
    } = sheet;

    const { activePage, pageNum } = pagination;
    const { renderSignal } = message;
    const { keyword, searchColumn: searchCol } = search;
    const { column: sortCol, direction } = sorted;
    const { tabClass } = tabKey;

    const [createState, setCreateState] = useState(null);
    // get content data
    const [content, setContent] = useState(undefined);
    // handle error
    const [error, setError] = useState(undefined);
    // get sheet header
    const [sheetHeader, setSheetHeader] = useState(undefined);
    // is content loading
    const [isLoading, setIsLoading] = useState(false);

    // 用 useMemo 控制, get all checked id
    const activeCheckedIds = useMemo(() => checked?.map((item) => item.rowId) || [], [checked]);

    // get all active id
    const activeHeaderIds = useMemo(() => activeHeader[sheetName]?.map((item) => item.id), [
        activeHeader,
        sheetName,
    ]);

    const history = useHistory();

    const contentSearchPath =
        process.env.REACT_APP_WEB_MODE === 'production'
            ? curState?.contentSearchPath
            : curState?.contentSearchPath2;

    const handleGetSheetHeaderFields = useCallback(async () => {
        // get all header ids
        const headerIds = header && header.map((item) => item.id);
        // get all fields from already exist list
        const fields = Object.keys(headerFields);
        // create Api and Field mapping table for easy to find

        const apiList = headerIds
            .map((field) => ({
                field,
                apiName: getApiByAllField(field),
            }))
            .filter(
                (item) =>
                    // The getApiByAllField will return undefined when it not found from config(api:nmtl:ApiField.js)
                    item.apiName.length > 0 &&
                    // filter repeat field if it does exist
                    fields.indexOf(item.field) === -1,
            );
        // unique apiName to avoids repeated queries
        const uniqueApiNameList = [...new Set(apiList.map((item) => item.apiName).flat())];
        // 針對只抓外譯房資料更改api
        const replaceApiNameList = {
            getPersonlist: 'getScvPersonList',
            getLocationList: 'getScvLocationList',
            getOrganizationList: 'getScvOrganizationList',
        };
        const newUniqueApiNameList = uniqueApiNameList.reduce((acc, item) => {
            // eslint-disable-next-line no-prototype-builtins
            if (replaceApiNameList.hasOwnProperty(item)) {
                acc.push(replaceApiNameList[item]);
            } else {
                acc.push(item);
            }
            return acc;
        }, []);
        // get each uniqueApi promise by fetch
        const promises = newUniqueApiNameList.map((apiName) =>
            // 部份 API 有參數，如：getMSWorks
            readNmtlData(Api[apiName].replace('{ds}', dataset)),
        );
        // get results from promises
        const results = await Promise.allSettled(promises).then((res) => res);
        // create catch result dictionary
        const apiResults = {};
        // To map the api and result in a dictionary as lookup table
        results.forEach((res, idx) => {
            const { status, value } = res;
            // if Promise.allSettled has succeeded or failed, it will return 'fulfilled' or 'rejected'
            if (status === 'fulfilled' && value && value.data) {
                const apiName = uniqueApiNameList[idx];
                // return data: { id, label } => { id, label, value (id) }
                apiResults[apiName] = value.data.map((d) => ({
                    id: d.id,
                    label: `${d.label}${addSuffix(apiName)}`,
                    value: d.id,
                    graph: d.Graph && d.Graph,
                }));
            }
        });

        // store all results
        if (!isEmpty(apiResults)) {
            dispatch({
                type: Act.DATA_SHEET_HEADER_FIELD,
                payload: apiResults,
            });
        }
    }, [dispatch, header, headerFields]);

    const getSortedIds = useCallback(async () => {
        if (dataset && contentClassList) {
            const qryStr = contentClassList.replace('{ds}', dataset);

            const classList = await readNmtlData(qryStr);
            return sortedByStroke(classList.data, 'label');
        }
        return undefined;
    }, [dataset, contentClassList]);

    const getReadUrl = useCallback(
        (searchIds) => {
            if (dataset && contentReadPath && activePage) {
                // combine url and parameter
                const urlPath = `${Api.getBaseUrl}/${contentReadPath}`;
                const parameter = `ds=${dataset}&limit=-1&offset=0&ids=${searchIds}`;
                return `${urlPath}?${parameter}`;
            }
            return undefined;
        },
        [dataset, contentReadPath, activePage],
    );

    const formattedKeyword = (v) => {
        const escapeKeyword = escapeRegExpKeyword(v);
        const tradSimpleWord = settingSearchKeyword(escapeKeyword);
        const { textArr, labelArr } = getTextAndLabel(tradSimpleWord);

        return { textArr: textArr || '', labelArr: labelArr || '' };
    };

    const handleSearch = (totalArg) => {
        const searchApiUrl = contentSearchPath
            .replace('{ds}', dataset)
            .replace('keyword={keyword}', totalArg);

        if (!searchApiUrl) {
            setIsLoading(false);
            return { data: [], total: null, error: 'Not ready' };
        }

        return readNmtlData(searchApiUrl);
    };

    const getSearchData = useCallback(async () => {
        if (!dataset || !contentSearchPath) return { data: [] };

        /**
         * textArr: (*賴和* OR *赖和*)
         * labelArr: (賴和 OR 赖和)
         */
        const { textArr, labelArr } = formattedKeyword(keyword);

        /**
         * Generates the search argument string for a specific column.
         * @param {string} column - The column to generate the search argument for.
         * @param {boolean} includeValues - Whether to include search values in the argument.
         * @returns {string} The formatted search argument string.
         */
        const generateArgs = (column, includeValues = false) => {
            const formattedColumn = textArray.includes(column) ? `${column}_text` : column;

            const searchValues = textArray.includes(column)
                ? `(${textArr.join(' OR ')})`
                : `(${labelArr.join(' OR ')})`;

            return includeValues
                ? `search_${formattedColumn}=${searchValues}`
                : `search_${formattedColumn}=`;
        };

        // 濾出需要帶進 API 裡的參數
        const allColumn = ['all'].concat(activeHeaderIds);
        const argList = allColumn.filter(
            (i) => ![EDIT_ROW.id, LAST_MODIFIED, searchCol].includes(i),
        );

        // 考慮到繁簡體轉換，各個欄位需要帶的參數不同，sparql無法一次符合多個需求，搜尋「全部」時，各別打API
        if (searchCol === 'all') {
            const searchArg = generateArgs(searchCol);
            const promises = argList.map((col) => {
                const otherArg = argList.map((el) => generateArgs(el, el === col)).join('&');

                return handleSearch(`${searchArg}&${otherArg}`);
            });

            const results = await Promise.allSettled(promises);
            const data = results
                .filter((i) => i.status === 'fulfilled' && !isEmpty(i.value?.data))
                .flatMap((item) => item.value.data);

            return { data };
        }

        const searchArg = generateArgs(searchCol, true);
        const otherArg = argList.map((el) => generateArgs(el)).join('&');

        const result = await handleSearch(`${searchArg}&${otherArg}`);

        return { data: result?.data || [] };
    }, [dataset, contentSearchPath, keyword, searchCol]);

    const readNmtlSearchData = async () => {
        let searchData = [];
        let searchIds = [];

        // search
        if (keyword && contentSearchPath) {
            // fetch data
            const { data } = await getSearchData();
            searchData = data;
        } else {
            // id, label 全抓，Read sorted ids
            searchData = await getSortedIds();
        }

        // 取出不重複的id
        let uniValue = [...new Set(searchData?.map((item) => item.srcId))];

        // 沒有結果
        if (uniValue.length === 0) {
            return { data: null, total: null, error: '沒有搜尋結果' };
        }

        /**
         * sortCol: "sort_srcId" || "hasAuthor" ....
         * direction: ASC || DESC
         * uniValue: [PUB234, PUB456....]
         */
        if (sortCol && direction && searchData) {
            uniValue = handleSorting(tabClass, sortCol, direction, searchData, uniValue);
        }

        dispatch({
            type: Act.DATA_SORTED_IDS,
            payload: uniValue,
        });

        // searchData 為全部搜尋到的結果
        if (uniValue) {
            // 根據 active page 決定要取的 searchIds
            searchIds = uniValue.slice(
                (activePage - 1) * pageNum,
                (activePage - 1) * pageNum + pageNum,
            );
        }

        // Read
        const readApiUrl = getReadUrl(searchIds.join(','));
        if (!readApiUrl) {
            setIsLoading(false);
            return { data: null, total: null, error: 'Not ready' };
        }

        // 讀取內容
        const { mergedData: data, error: readError } = await readContent({
            tabClass,
            searchIds,
            readApiUrl,
            // readTabUrl: `${Api.getBaseUrl}/${contentReadTabPath}?limit=-1&offset=0&ds=${dataset}&ids={ids}`
        });

        if (!data) {
            setIsLoading(false);
            return { data: null, total: null, error: readError };
        }

        return {
            data,
            total: uniValue.length,
            error: readError,
        };
    };

    const handleGetSheetHeader = useCallback(async () => {
        if (sheetName) {
            // hasTab 存在時，使用 getSheetTabHeader
            const totalHeader = curState.tabClass
                ? await getSheetTabHeader(sheetName, curState.tabClass)
                : await getSheetHeader(sheetName);

            // specialTable 指定顯示的 header

            const sortheader = displayHeader
                ?.map((item) => totalHeader.find((el) => el.id === item))
                .sort((itemA, itemB) => itemA.seq - itemB.seq);

            // set data
            if (sortheader.error) {
                setError(sortheader.error);
                return;
            }

            // [新增] filterColumn功能 - 20230202
            // sortheader = sortheader.filter(obj =>
            //     filterColumn(sheetName, obj, groupInfo)
            // );

            // 加上編輯按鈕
            sortheader.push(EDIT_ROW);

            // handle header
            setSheetHeader(sortheader);
            // dispatch header
            dispatch({
                type: Act.DATA_SHEET_HEADER,
                payload: totalHeader,
            });

            // dispatch header default activate
            if (isEmpty(activeHeader[sheetName]) || curState.tabClass) {
                dispatch({
                    type: Act.DATA_SHEET_ACTIVATE_HEADER,
                    payload: {
                        [sheetName]: sortheader,
                    },
                });
            }
        }
    }, [
        tabClass,
        displayHeader,
        dispatch,
        sheetName,
        activeHeader,
        activeHeaderCount,
        activeHeaderAllChecked,
        groupInfo,
    ]);

    const handleGetContent = useCallback(async () => {
        // fetch data if sidebar mainSubject and sheet have been selected
        // set isLoading to true
        setIsLoading(true);

        const { data: contentData, total, error: dataError } = await readNmtlSearchData();

        // set data
        if (!dataError) {
            // set content for table view
            setContent(contentData);
            // reset error if user reload
            setError(undefined);
            dispatch({
                type: Act.DATA_CONTENT_ROWS,
                payload: contentData,
            });
        }
        // set error if data not exist
        else {
            setError(dataError);
        }

        // set pagination
        if (total >= 0) {
            dispatch({
                type: Act.DATA_PAGINATION_TOTAL_PAGE,
                payload: Math.ceil(total / pageNum),
            });
            dispatch({
                type: Act.DATA_PAGINATION_TOTAL_COUNT,
                payload: total,
            });
        }
        // set isLoading to false
        setIsLoading(false);
    }, [
        curState,
        dispatch,
        keyword,
        searchCol,
        contentSearchPath,
        getReadUrl,
        sortCol,
        direction,
        pageNum,
    ]);

    const handleSort = (clickColumn) => {
        // 帶有 sort 才能排序
        if (!clickColumn?.sort) return;
        if (sortCol !== clickColumn.id) {
            dispatch({
                type: Act.DATA_SORTED,
                payload: { column: clickColumn.id, direction: sortedMethod.ASC },
            });
            return;
        }

        dispatch({
            type: Act.DATA_SORTED,
            payload: {
                column: clickColumn.id,
                direction: direction === sortedMethod.ASC ? sortedMethod.DESC : sortedMethod.ASC,
            },
        });
    };

    useEffect(() => {
        // specialTable 不會用到下拉選單，直接顯示資料
        // if (Object.keys(headerFields).length === 0) {
        //     return;
        // }
        if (!content || content.length === 0) {
            return;
        }

        setCreateState(multiConvertToCreateState(content, header, headerFields));
    }, [headerFields, content, header]);

    useEffect(() => {
        // refresh changed when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });
        // refresh checked when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });
        // refresh created when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CREATED_CLEAN });
    }, [dispatch, curState]);

    // 1. get sheet header
    useEffect(() => {
        // console.log("sheetHeader", sheetHeader);
        handleGetSheetHeader();
    }, [displayHeader, sheetName, groupInfo]);

    // 2. get sheet header fields
    useEffect(() => {
        handleGetSheetHeaderFields();
    }, [dataset, header]);

    // 3. get content
    useEffect(() => {
        handleGetContent();
    }, [handleGetContent, dataset, contentReadPath, renderSignal]);

    const headerTool = (propObj) => (
        <React.Fragment>
            {propObj?.infoIcon ? <CustomHeaderInformation id={propObj.id} /> : ''}
            {propObj?.sort && propObj.id !== sortCol ? <Icon name="sort" link /> : ''}
        </React.Fragment>
    );

    const tableHeader = () => {
        if (!sheetHeader || !activeHeader || error || isLoading) {
            return null;
        }

        if (!activeHeader[sheetName]) {
            return null;
        }

        const res = [];

        res.push(
            <Table.HeaderCell key="checkbox">
                <SpecialAllCheckBox
                    isChecked={
                        activeCheckedIds.length > 0 && activeCheckedIds.length === checked.length
                    }
                />
            </Table.HeaderCell>,
        );

        activeHeader[sheetName].forEach((sh) => {
            if (isNotShownInTable(sh.id)) {
                return;
            }

            res.push(
                // for checkbox
                <Table.HeaderCell
                    padded="true"
                    singleLine
                    key={sh.id}
                    sorted={sortCol === sh.id ? direction : null}
                    onClick={() => handleSort(sh)}
                    style={sh?.sort ? {} : sortedStyle}
                >
                    {sh.label}
                    {headerTool(sh)}
                </Table.HeaderCell>,
            );
        });

        return res;
    };

    const displayValue = (cell) => {
        const valueType = typeof cell;

        switch (valueType) {
            case 'object':
                return Object.values(cell).map((value) => (
                    <div key={uuidv4()}>
                        {value}
                        <br />
                    </div>
                ));

            default:
                return cell;
        }
    };

    const checkBoxCell = (idx) => (
        <Table.Cell collapsing>
            {/* key 必須是唯一的如果重複將會被視為相同 element
                     也會造成不進行任何更動，也就是數值不會改變的 issue
                     怎麼設定都可以就是不可以相同 */}
            <CustomCheckBox
                key={`${sheetName}-content-checkbox-${activePage}-${idx}-${activeCheckedIds.indexOf(
                    idx,
                ) !== -1}`}
                rowId={idx}
                isChecked={activeCheckedIds.indexOf(`${idx}`) !== -1}
            />
        </Table.Cell>
    );

    const tabCheckBoxCell = (data, idx, cellIdx) => (
        <Table.Cell collapsing>
            {/* key 必須是唯一的如果重複將會被視為相同 element
                     也會造成不進行任何更動，也就是數值不會改變的 issue
                     怎麼設定都可以就是不可以相同 */}
            <SpecialCheckBox
                key={`${sheetName}-content-checkbox-${activePage}-${idx}-${cellIdx}`}
                rowId={idx}
                cellId={cellIdx}
                isChecked={activeCheckedIds.indexOf(`${idx}-${cellIdx}`) !== -1}
            />
        </Table.Cell>
    );

    const getBookLang = (input) => {
        const atIndex = input.indexOf('@');
        if (atIndex !== -1) {
            return input.substring(0, atIndex);
        }
        return input;
    };

    const handleToPage = (ctIdx, rdIdx, contentArray) => {
        if (contentArray) {
            const curBookLang = `${getBookLang(
                contentArray[ctIdx].transList[rdIdx].translationBookName[0],
            )}_${contentArray[ctIdx].transList[rdIdx].srcId}`;

            dispatch({
                type: Act.DATA_CONTENT_CURRENT_BOOK_LANGUAGE,
                payload: curBookLang,
            });
        }

        let id;
        if (curState.tabClass === PEICES_INFO) {
            id = content[ctIdx].isTranslationBookOf;
        } else {
            id = content[ctIdx].srcId;
        }
        history.push(`/Dataset/Information/${id}`, { id });
    };

    const EditCell = (ctIdx, rdIdx, content) => (
        <Table.Cell
            key={`${sheetName}-cell-${ctIdx}-${rdIdx}-${activePage}`}
            textAlign="center"
            verticalAlign="middle"
            collapsing
        >
            <Image
                src={editIcon}
                style={editIconStyle}
                avater="true"
                centered
                onClick={() => handleToPage(ctIdx, rdIdx, content)}
            />
            {/* <EditModal /> */}
        </Table.Cell>
    );

    const tabTableBody = () => {
        const transHeader = activeHeaderIds.filter(
            (el) => oriHeader && !oriHeader.includes(el) && el !== EDIT_ROW.id,
        );

        return (
            content &&
            content.map((rd, ctIdx) =>
                rd.transList.map((rdObj, rdIdx) => (
                    <Table.Row key={`${sheetName}-content-${activePage}-${ctIdx}-${rdIdx}`}>
                        {tabCheckBoxCell(rd, ctIdx, rdIdx)}
                        {/* 外譯房 著作 件層級  */}
                        {rdIdx === 0 &&
                            oriHeader.map((key) => (
                                <Table.Cell
                                    style={widthStyle}
                                    key={`${sheetName}-content-${activePage}-${ctIdx}-${rdIdx}-${key}`}
                                    rowSpan={rd.transList.length || 1}
                                    className={`${sheetName}-content-${activePage}-${ctIdx}-${rdIdx}-${key}`}
                                >
                                    {key === 'hasAuthor' ? (
                                        <div
                                            style={{
                                                overflowY: 'auto',
                                                maxHeight: '120px',
                                            }}
                                        >
                                            {displayValue(rd[key])}
                                        </div>
                                    ) : (
                                        displayValue(rd[key])
                                    )}
                                </Table.Cell>
                            ))}

                        {transHeader.map((key) => {
                            const cellValue =
                                key === LAST_MODIFIED ? timeStampToDate(rdObj[key]) : rdObj[key];
                            if (key === 'translationBookName') {
                                return (
                                    <Table.Cell
                                        key={`${sheetName}-content-${activePage}-${ctIdx}-${key}`}
                                    >
                                        <div>{displayValue(cellValue)}</div>
                                    </Table.Cell>
                                );
                            }
                            return (
                                <Table.Cell
                                    style={widthStyle}
                                    key={`${sheetName}-content-${activePage}-${ctIdx}-${key}`}
                                >
                                    {displayValue(cellValue)}
                                </Table.Cell>
                            );
                        })}
                        {EditCell(ctIdx, rdIdx, content)}
                    </Table.Row>
                )),
            )
        );
    };

    const defaultTableBody = () => {
        if (!sheetHeader || !activeHeaderIds) {
            return null;
        }
        //

        return content.map((rd, ctIdx) => (
            <Table.Row key={`${sheetName}-content-${activePage}-${ctIdx}`}>
                {checkBoxCell(ctIdx)}
                {activeHeaderIds.map((actHeader, shIdx) => {
                    if (isNotShownInTable(actHeader)) {
                        return null;
                    }
                    if (isEmpty(ct.rows)) {
                        return null;
                    }
                    if (!createState) {
                        return null;
                    }

                    // cellValue: Object { 0: "PER2", 1: "PER201", … } and reformat timeStamp
                    const cellValue = (actHeader === LAST_MODIFIED
                        ? timeStampToDate(ct.rows[ctIdx][actHeader])
                        : ct.rows[ctIdx][actHeader]) || { 0: '' };

                    // editCell
                    if (actHeader === EDIT_ROW.id) {
                        return EditCell(ctIdx, shIdx);
                    }
                    /* export 外譯房 著作 案層級 shIdx === 2 */
                    /*
                    if (shIdx === 2) {
                        return (
                            <Table.Cell
                                key={`${sheetName}-cell-${ctIdx}-${shIdx}-${activePage}`}
                                className={`${sheetName}-cell-${ctIdx}-${shIdx}-${activePage}`}
                            >
                                <div style={hiddenHeightStyle}>{displayValue(cellValue)}</div>
                            </Table.Cell>
                        );
                    } */
                    return (
                        <Table.Cell
                            style={widthStyle}
                            key={`${sheetName}-cell-${ctIdx}-${shIdx}-${activePage}`}
                            className={`${sheetName}-cell-${ctIdx}-${shIdx}-${activePage}`}
                        >
                            {shIdx === 2 ? (
                                <div style={hiddenHeightStyle}>{displayValue(cellValue)}</div>
                            ) : (
                                displayValue(cellValue)
                            )}
                            {/* {displayValue(cellValue)} */}
                        </Table.Cell>
                    );
                })}
            </Table.Row>
        ));
    };

    const tableBodyView = () => {
        if (isLoading || error) {
            return (
                <Table.Row>
                    <Table.Cell
                        textAlign="center"
                        verticalAlign="middle"
                        style={{ height: '300px' }}
                    >
                        {isLoading && (
                            <Loader active inline="centered" size="large">
                                Loading...
                            </Loader>
                        )}
                        {error && <Header>{error}</Header>}
                    </Table.Cell>
                </Table.Row>
            );
        }

        // content or header is empty
        if (!sheetHeader || isEmpty(content)) {
            return null;
        }

        // specialTab
        if (tabClass === PEICES_INFO) {
            return tabTableBody();
        }

        // default table view
        return defaultTableBody();
    };

    // const nb = 2;
    return useMemo(
        () => (
            <React.Fragment>
                <Container style={{ width: '100%' }}>
                    <div
                        style={
                            sheetHeader && !isEmpty(content) && !error
                                ? {
                                    overflowY: 'hidden',
                                    minHeight: '500px',
                                }
                                : {}
                        }
                    >
                        <Table celled size="small" sortable>
                            <Table.Header>
                                <Table.Row>{tableHeader()}</Table.Row>
                            </Table.Header>

                            <Table.Body>{tableBodyView()}</Table.Body>
                        </Table>
                    </div>
                </Container>
            </React.Fragment>
        ),
        [activeCheckedIds, activeHeaderIds, sheetHeader, isLoading, createState],
    );
};

export default SpecialContentView;

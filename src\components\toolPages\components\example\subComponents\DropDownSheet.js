import React, { useContext, useEffect, useState } from "react";

// ui
import { Dropdown } from "semantic-ui-react";

// cloud
import { getSheets } from "../../../../../api/firebase/cloudFirestore";

// store
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

// common
import { isEmpty } from "../../../../../commons";
import { sheetMapping } from "../../../../common/sheetCrud/utils";
import { filterSheet } from "../../../../../commons/filterGroup";

const DropDownSheet = () => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { groupInfo } = state.data;
    const [sheets, setSheets] = useState([]);
    const [error, setError] = useState(undefined);

    const handleGetSheets = async () => {
        const sheetsData = await getSheets();
        if (!sheetsData.error) {
            const sheetsMap = sheetMapping(sheetsData).filter(tmpSheet =>
                filterSheet(tmpSheet, groupInfo)
            );
            // set to sheets
            setSheets(sheetsMap);
        } else {
            setError(sheetsData.error);
        }
    };

    useEffect(() => {
        if (isEmpty(groupInfo)) return;
        handleGetSheets();
    }, [groupInfo]);

    const handleClick = (event, { value }) => {
        // get selected item
        const item = sheets.filter(it => it.value === value)[0];
        // set sheet
        if (item) {
            dispatch({
                type: Act.EXAMPLE_TABLE_SELECTED_SHEET,
                payload: item
            });
        }
    };

    if (error) {
        return <span>{error}</span>;
    }

    return (
        <Dropdown
            loading={isEmpty(sheets)}
            search
            selection
            options={sheets}
            onChange={handleClick}
            placeholder="表單"
        />
    );
};

export default DropDownSheet;

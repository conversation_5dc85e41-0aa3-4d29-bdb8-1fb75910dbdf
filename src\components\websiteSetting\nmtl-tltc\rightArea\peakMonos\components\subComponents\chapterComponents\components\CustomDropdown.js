import React, { useEffect, useState } from "react";
import { Grid } from "semantic-ui-react";
import { components, createFilter } from "react-select";
import { WindowedMenuList } from "react-windowed-select";
import { useDispatch, useSelector } from "react-redux";
import CreatableSelect from "react-select/creatable";
import CustomEditModal from "./CustomEditModal";
import PeakAct from "../../../../PeakMonosAction";
import { isEmpty } from "../../../../../../../../../commons";
import "../../../EditCate.scss";

const CustomDropdown = ({
    type,
    rowName,
    value,
    optionList,
    updatedData,
    subRowName,
    classType,
    debouncedUpdateFct
}) => {
    const dispatch = useDispatch();
    const {
        isEditDropdownChip,
        editingDropdownIsSaving,
        editingDropdownCreateValue,
        editingDropdownClassType
    } = useSelector(state => state);

    const [localValue, setLocalValue] = useState([]);
    const [options, setOptions] = useState([]);

    const onChangeFct = e => {
        const inputIds = [...new Set(e.map(i => i.value))];
        const v = optionList.filter(i => inputIds.includes(i.value));

        setLocalValue(v);
        debouncedUpdateFct(updatedData, type, inputIds);
    };

    useEffect(() => {
        if (isEmpty(value)) return;
        if (!isEmpty(localValue)) return;
        const v = optionList.filter(i => value.includes(i.value));
        setLocalValue(v);
    }, [optionList]);

    useEffect(() => {
        if (editingDropdownIsSaving && !isEmpty(localValue)) {
            const tmpValue = localValue.map(i => i.value);
            const v = optionList.filter(i => tmpValue.includes(i.value));
            setLocalValue(v);
        }
    }, [optionList]);

    useEffect(() => {
        if (editingDropdownClassType !== classType || !editingDropdownIsSaving)
            return;
        const v = [...localValue, ...editingDropdownCreateValue];
        const inputIds = [...new Set(v.map(i => i.value))];

        setLocalValue(v);
        debouncedUpdateFct(updatedData, type, inputIds);
    }, [editingDropdownCreateValue]);

    useEffect(() => {
        setOptions(optionList);
    }, [optionList]);

    const customStyles = {
        container: styles => ({
            ...styles,
            minWidth: "360px",
            width: "100%",
            border: "1px solid rgba(34, 36, 38, 0.15)"
        }),
        control: (styles, { selectProps: { controlColor } }) => ({
            ...styles,
            borderStyle: "none",
            borderRadius: "unset",
            backgroundColor: controlColor
        })
    };

    const MultiValueLabel = props => (
        <div
            onMouseDown={e => {
                e.stopPropagation();
                // 儲存目前編輯chip的id
                dispatch({
                    type: PeakAct.SET_EDITDROPDOWNCHIPID,
                    payload: props.data.value
                });

                // 儲存目前編輯chip的options
                dispatch({
                    type: PeakAct.SET_EDITDROPDOWNOPTIONLIST,
                    payload: optionList
                });

                // 紀錄是否在編輯或新增狀態
                dispatch({
                    type: PeakAct.SET_ISEDITDROPDOWNCHIP,
                    payload: true
                });

                // 儲存狀態為編輯或新增
                dispatch({
                    type: PeakAct.SET_EDITDROPDOWNTYPE,
                    payload: "update"
                });

                // 儲存目前編輯chip的classType
                dispatch({
                    type: PeakAct.SET_EDITDROPDOWNCLASSTYPE,
                    payload: classType
                });

                // 確認目前尚未點擊Modal儲存按鈕
                dispatch({
                    type: PeakAct.SET_EDITDROPDOWNISSAVING,
                    payload: false
                });
            }}
        >
            <components.MultiValueLabel {...props}>
                {props.children}
            </components.MultiValueLabel>
        </div>
    );

    const handleCreate = inputValue => {
        // 清空目前編輯的chip Id
        dispatch({
            type: PeakAct.SET_EDITDROPDOWNCHIPID,
            payload: ""
        });

        // 儲存剛剛create輸入的值
        dispatch({
            type: PeakAct.SET_EDITDROPDOWNADDVALUE,
            payload: inputValue
        });

        // 儲存目前編輯chip的options
        dispatch({
            type: PeakAct.SET_EDITDROPDOWNOPTIONLIST,
            payload: optionList
        });

        // 紀錄是否在編輯或新增狀態
        dispatch({
            type: PeakAct.SET_ISEDITDROPDOWNCHIP,
            payload: true
        });

        // 儲存狀態為編輯或新增
        dispatch({
            type: PeakAct.SET_EDITDROPDOWNTYPE,
            payload: "create"
        });

        // 儲存目前編輯chip的classType
        dispatch({
            type: PeakAct.SET_EDITDROPDOWNCLASSTYPE,
            payload: classType
        });

        // 確認目前尚未點擊Modal儲存按鈕
        dispatch({
            type: PeakAct.SET_EDITDROPDOWNISSAVING,
            payload: false
        });
    };

    return (
        <Grid.Row>
            <Grid.Column
                width={3}
                style={{
                    backgroundColor: "#e0e1e2"
                }}
            >
                <div className="topArea__left">
                    <span>{rowName}</span>
                    {subRowName && (
                        <div className="topArea__left--subRowName">
                            {subRowName.includes("、") ? (
                                <>
                                    <span>{subRowName.split("、")[0]}</span>
                                    <span>{subRowName.split("、")[1]}</span>
                                </>
                            ) : (
                                <span>{subRowName}</span>
                            )}
                        </div>
                    )}
                </div>
            </Grid.Column>
            <Grid.Column width={13} style={{ display: "flex" }}>
                <div className="topArea__right">
                    <div className="topArea__right--box">
                        <CreatableSelect
                            components={{
                                MenuList: WindowedMenuList,
                                MultiValueLabel
                            }}
                            isMulti
                            options={options}
                            value={localValue}
                            onChange={onChangeFct}
                            styles={customStyles}
                            filterOption={createFilter({
                                ignoreAccents: false
                            })}
                            onCreateOption={handleCreate}
                        />
                    </div>
                </div>
                <CustomEditModal
                    onClick={() => {
                        dispatch({
                            type: PeakAct.SET_ISEDITDROPDOWNCHIP,
                            payload: !isEditDropdownChip
                        });
                    }}
                    key={rowName}
                />
            </Grid.Column>
        </Grid.Row>
    );
};

export default CustomDropdown;

// components
import CustomRadio from "../CustomRadio/CustomRadio";
import CustomInput from "../CustomInput/CustomInput";
import CustomDropdown from "../CustomDropdown/CustomDropdown";
import CarouselImgInput from "../CarouselImgInput/CarouselImgInput";
import CustomEditor from "../CustomEditor/CustomEditor";
import AttachmentInput from "../CustomInput/AttachmentInput";

// config
import textConfig from "../../Utils/textConfig";
import clsName from "../../Utils/clsName";
import initColumnDef from "../../Utils/initColumnDef";

/** 參數說明
 * title: 顯示在左邊欄位的描述
 * gridCol2Width(optional): 控制欄位寬度
 * components: 搭配使用元件
 * compProps(optional): 搭配使用元件會用到屬性
 * => {
 *     label: 用在className
 *     fusekiCol: 用在填寫fuseki對應欄位名稱
 * }
 * */
const NewsInfoData = [
    {
        title: textConfig.Header_Class,
        components: CustomDropdown,
        compProps: {
            label: clsName.Classification,
            fusekiCol: initColumnDef.newsType
        }
    },
    {
        title: textConfig.Header_Status,
        components: CustomRadio,
        compProps: {
            label: clsName.StatusRadio,
            fusekiCol: initColumnDef.status
        }
    },
    {
        title: textConfig.Header_PublisherName,
        components: CustomInput,
        compProps: {
            label: clsName.PublisherName,
            fusekiCol: initColumnDef.newsCreator
        }
    },
    {
        title: textConfig.Header_Provenance,
        components: CustomInput,
        compProps: {
            label: clsName.Provenance,
            fusekiCol: initColumnDef.newsSource
        }
    },
    {
        title: textConfig.Header_Time,
        components: CustomInput,
        compProps: {
            label: clsName.PublishDate,
            fusekiCol: initColumnDef.hasStartDate
        }
    },
    {
        title: textConfig.End_Time,
        components: CustomInput,
        compProps: {
            label: clsName.EndPublishDate,
            fusekiCol: initColumnDef.hasEndDate
        }
    },
    {
        title: textConfig.Header_Top,
        components: CustomRadio,
        compProps: {
            label: clsName.TopRadio,
            fusekiCol: initColumnDef.top
        }
    },
    {
        title: textConfig.Header_TitleZH,
        gridCol2Width: 13,
        components: CustomInput,
        compProps: {
            label: clsName.HeadlineZH,
            fusekiCol: initColumnDef.titleZH
        }
    },
    {
        title: textConfig.Header_TitleEN,
        gridCol2Width: 13,
        components: CustomInput,
        compProps: {
            label: clsName.HeadlineEN,
            fusekiCol: initColumnDef.titleEN
        }
    },
    {
        title: textConfig.Header_LinkSelection,
        gridCol2Width: 13,
        components: CustomRadio,
        compProps: {
            label: clsName.linkSelect
        }
    },
    {
        isLink: [
            {
                title: textConfig.Header_ExternalLinkZH,
                gridCol2Width: 13,
                components: CustomInput,
                compProps: {
                    label: clsName.ExternalLinkZH,
                    fusekiCol: initColumnDef.externalLinksZH
                }
            },
            {
                title: textConfig.Header_ExternalLinkEN,
                gridCol2Width: 13,
                components: CustomInput,
                compProps: {
                    label: clsName.ExternalLinkEN,
                    fusekiCol: initColumnDef.externalLinksEN
                }
            },
            {
                title: textConfig.Header_WebCacheZH,
                gridCol2Width: 13,
                components: CustomInput,
                compProps: {
                    label: clsName.WebCacheZH,
                    fusekiCol: initColumnDef.webcacheZH
                }
            },
            {
                title: textConfig.Header_WebCacheEN,
                gridCol2Width: 13,
                components: CustomInput,
                compProps: {
                    label: clsName.WebCacheEN,
                    fusekiCol: initColumnDef.webcacheEN
                }
            }
        ],
        notLink: [
            {
                title: textConfig.Header_Attachment,
                components: AttachmentInput,
                gridCol2Width: 13,
                compProps: {
                    fusekiCol: initColumnDef.fileAvailableAt
                }
            },
            {
                title: textConfig.Header_Carousel,
                components: CarouselImgInput,
                gridCol2Width: 13,
                compProps: {
                    fusekiCol: initColumnDef.hasURL
                }
            },
            {
                title: textConfig.Header_ContentZH,
                components: CustomEditor,
                gridCol2Width: 13,
                compProps: {
                    label: clsName.ContentZH,
                    fusekiCol: initColumnDef.contentZH
                }
            },
            {
                title: textConfig.Header_ContentEN,
                components: CustomEditor,
                gridCol2Width: 13,
                compProps: {
                    label: clsName.ContentEN,
                    fusekiCol: initColumnDef.contentEN
                }
            }
        ]
    }
];
export default NewsInfoData;

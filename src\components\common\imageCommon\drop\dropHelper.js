import FileAct from "../../../../reduxStore/file/fileAction";
// options
import options from "../options";

const { SELECT_MODE } = options;

const handleDrop = ({ acceptedFiles, handleDropCallback }) => {
    // 若有帶 handleDropCallback, 則阻斷後續的作業
    if (!handleDropCallback) return;
    handleDropCallback(acceptedFiles);
};

const handleDropReject = (rejectFiles, config, dispatch) => {
    const alertMsgTitle = `${rejectFiles.length} 個檔案 不合規範，請重新上傳`;
    return dispatch({
        type: FileAct.PICKER_DROP_MESSAGE,
        payload: {
            type: "warning",
            title: alertMsgTitle
        }
    });
};

const handleChange = (e, file, dropFolderFiles, dispatchRedux, config) => {
    const curFolderFilesChange = Object.assign({}, dropFolderFiles);
    // single select mode:
    if (config.selectMode === SELECT_MODE.single.name) {
        curFolderFilesChange.checked = Object.assign([]);
        if (file.checked) {
            curFolderFilesChange.checked.push(file);

            return dispatchRedux({
                type: FileAct.DROP_FOLDER_FILES_STATUS,
                payload: curFolderFilesChange
            });
        }
        return dispatchRedux({
            type: FileAct.DROP_FOLDER_FILES_STATUS,
            payload: curFolderFilesChange
        });
    }
    // multiple select mode:
    if (config.selectMode === SELECT_MODE.multiple.name) {
        if (file.checked) {
            curFolderFilesChange.checked.push(file);
            return dispatchRedux({
                type: FileAct.DROP_FOLDER_FILES_STATUS,
                payload: curFolderFilesChange
            });
        }
        curFolderFilesChange.checked = curFolderFilesChange.checked.filter(
            cff => cff["data-img-url"] !== file["data-img-url"]
        );
        return dispatchRedux({
            type: FileAct.DROP_FOLDER_FILES_STATUS,
            payload: curFolderFilesChange
        });
    }
    return null;
};

export { handleDrop, handleChange, handleDropReject };

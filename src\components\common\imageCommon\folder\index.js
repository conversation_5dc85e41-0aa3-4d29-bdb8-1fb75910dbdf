import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Form, Grid, Input, Segment } from "semantic-ui-react";

// component
import Gallery from "../gallery";
import FileList from "../FileList/FileList";

// store
import FileAct from "../../../../reduxStore/file/fileAction";

import { handleGalleryChange } from "../gallery/galleryHelper";
import uploadConfig from "../../../toolPages/components/upload/uploadConfig";
import folderConfig from "./folderConfig";

import utils from "../utils";
import CurFolderAlert from "./CurFolderAlert";
import useDebounce from "../../hooks/useDebounce";

const { prettyStrConcat, safeStr } = utils;
const { radioValue } = folderConfig;

const CurrentFolder = props => {
    const {
        type,
        FolderControlPanel,
        pickConfig,
        galleryFirstChild,
        mode
    } = props;

    const dispatch = useDispatch();
    const {
        files: { currentFolder, curFolderFilesUrl, curFolderFiles }
    } = useSelector(state => state);

    const [keyword, setKeyword] = useState("");
    const dehValue = useDebounce(keyword, 800);

    const [ckValue, setCkValue] = useState(radioValue.all);
    const [filterCriteria, setFilterCriteria] = useState({}); // 帶給Gallery的篩選條件

    const radioList = [
        { name: "全部", value: radioValue.all },
        { name: "已連結", value: radioValue.link },
        { name: "未連結", value: radioValue.noLink }
    ];

    useEffect(() => {
        const tmpObj = {
            keyword: dehValue,
            radio: ckValue
        };
        setFilterCriteria(tmpObj);
    }, [dehValue, ckValue]);

    const onKWChange = (evt, data) => {
        setKeyword(data.value);
    };

    const onRadioChange = (evt, data) => {
        setCkValue(data.value);
    };

    // eslint-disable-next-line no-unused-vars
    const handleImgClick = imgData => {
        // todo: 顯示原圖
        // eslint-disable-next-line no-console
        // console.log("handleImgClick imgData", imgData);
    };

    return (
        <>
            <h2>資料夾</h2>

            <h3>目前資料夾：{currentFolder.path_ch}</h3>
            <Segment placeholder style={{ margin: "0" }}>
                <Grid className="current-folder-container">
                    <Grid.Row>
                        <Grid.Column textAlign="center">
                            <Grid.Row
                                style={{
                                    display: "grid",
                                    gridTemplateColumns: "repeat(3, 1fr)"
                                }}
                            >
                                <Grid.Column>
                                    <Grid.Row
                                        style={{
                                            display: "flex",
                                            marginBottom: "0.5rem"
                                        }}
                                    >
                                        <Input
                                            onChange={onKWChange}
                                            placeholder="搜尋框(搜尋檔名)"
                                        />
                                    </Grid.Row>
                                    <Grid.Row>
                                        <h4 style={{ textAlign: "start" }}>
                                            篩選條件
                                        </h4>
                                        <Form>
                                            <Form.Group inline>
                                                {radioList.map(el => {
                                                    const { name, value } = el;
                                                    return (
                                                        <Form.Radio
                                                            key={value}
                                                            label={name}
                                                            value={value}
                                                            checked={
                                                                value ===
                                                                ckValue
                                                            }
                                                            onChange={
                                                                onRadioChange
                                                            }
                                                        />
                                                    );
                                                })}
                                            </Form.Group>
                                        </Form>
                                    </Grid.Row>
                                </Grid.Column>
                                <Grid.Column>
                                    <Grid.Row>
                                        <h4>資料夾顯示區</h4>
                                    </Grid.Row>
                                    <Grid.Row>
                                        <h5>
                                            {prettyStrConcat([
                                                "數量：",
                                                safeStr(
                                                    curFolderFilesUrl &&
                                                        curFolderFilesUrl.length
                                                ),
                                                "個"
                                            ])}
                                        </h5>
                                    </Grid.Row>
                                </Grid.Column>
                            </Grid.Row>

                            {/* control panel: custom control panel */}
                            <FolderControlPanel />

                            {/* alert message */}
                            <CurFolderAlert />
                            {/* display images with lazy loading */}
                            <aside
                                className="thumbsContainer"
                                style={{
                                    textAlign: "left",
                                    height: "auto"
                                }}
                            >
                                {uploadConfig.image === type ? (
                                    <Gallery
                                        images={curFolderFilesUrl}
                                        withCheckbox={pickConfig.withCheckbox}
                                        selectMode={pickConfig.selectMode}
                                        onChange={(e, data) => {
                                            handleGalleryChange({
                                                file: data,
                                                curFolderFiles,
                                                dispatch,
                                                selectMode:
                                                    pickConfig.selectMode
                                            });
                                        }}
                                        onImageClick={handleImgClick}
                                        firstChild={galleryFirstChild}
                                        currentFolder={currentFolder}
                                        filterCriteria={filterCriteria}
                                    />
                                ) : (
                                    <FileList
                                        files={curFolderFilesUrl}
                                        onChange={selectedFiles => {
                                            dispatch({
                                                type:
                                                    FileAct.CUR_FOLDER_FILES_STATUS,
                                                payload: selectedFiles
                                            });
                                        }}
                                        firstChild={galleryFirstChild}
                                        ctlType={uploadConfig.DropDownListAll}
                                        mode={mode}
                                    />
                                )}
                            </aside>
                        </Grid.Column>
                    </Grid.Row>
                </Grid>
            </Segment>
        </>
    );
};

export default CurrentFolder;

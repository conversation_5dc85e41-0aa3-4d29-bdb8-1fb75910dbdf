import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import { Button, Input, Modal } from "semantic-ui-react";
import { isEmpty } from "../../../../../../../../../commons";
import Api from "../../../../../../../../../api/nmtl/Api";
import PeakAct from "../../../../PeakMonosAction";
import { getReservedNewId } from "../../../../../../../../common/sheetCrud/utils";
import "../../../EditCate.scss";

const CustomEditModal = ({ onClick }) => {
    const dispatch = useDispatch();
    const {
        isEditDropdownChip,
        editingDropdownChipId,
        editingDropdownOptionList,
        editingDropdownType,
        editingDropdownAddValue,
        editingDropdownClassType
    } = useSelector(state => state);

    const [zhData, setZhData] = useState([]);
    const [enData, setEnData] = useState([]);

    const separateLabels = data => {
        let zhLabel = "";
        let enLabel = "";

        data.forEach(item => {
            const { label } = item;
            if (label.includes("@en")) {
                // eslint-disable-next-line prefer-destructuring
                enLabel = label;
            } else {
                zhLabel = label.includes("@zh") ? label : label;
            }
        });

        return { zhLabel, enLabel };
    };

    const onSave = async id => {
        if (isEmpty(zhData) || isEmpty(enData)) return;
        const filterData = editingDropdownOptionList.filter(
            i => i.value === id
        );

        const { zhLabel, enLabel } = separateLabels(filterData);

        // const ListForProtegeGraph = ["TLAAward", "TLACategory"];
        // const graph = ListForProtegeGraph.includes(editingDropdownClassType)
        //     ? "tltc"
        //     : "tltc";
        const graph = "tltc";
        const isCreating = editingDropdownType === "create";
        const srcId = isCreating
            ? await getReservedNewId(editingDropdownClassType)
            : editingDropdownChipId;

        const entry = {
            graph,
            classType: editingDropdownClassType,
            value: {
                label: [zhData, enData]
            },
            srcId
        };

        const apiCall = isCreating
            ? axios.post(Api.getGeneric, { entry })
            : axios.put(Api.getGeneric, {
                entrySrc: {
                    ...entry,
                    value: { label: [zhLabel, enLabel] }
                },
                entryDst: entry
            });

        await apiCall.then(() => {
            // 確認目前已點擊儲存按鈕
            dispatch({
                type: PeakAct.SET_EDITDROPDOWNISSAVING,
                payload: true
            });

            // 退出Modal
            dispatch({
                type: PeakAct.SET_ISEDITDROPDOWNCHIP,
                payload: false
            });

            if (isCreating) {
                const updateData = [
                    { value: srcId, label: zhData },
                    { value: srcId, label: enData }
                ];

                // 儲存新增的選項
                dispatch({
                    type: PeakAct.SET_EDITDROPDOWNCREATEVALUE,
                    payload: updateData
                });
            }
        });
    };

    const onchangeZhHandler = e => {
        setZhData(e.target.value);
    };
    const onchangeEnHandler = e => {
        setEnData(e.target.value);
    };

    useEffect(() => {
        const filterData = editingDropdownOptionList.filter(
            i => i.value === editingDropdownChipId
        );

        const { zhLabel, enLabel } = separateLabels(filterData);

        if (zhLabel) {
            setZhData(zhLabel);
        } else {
            setZhData([]);
        }

        if (enLabel) {
            setEnData(enLabel);
        } else {
            setEnData([]);
        }
    }, [editingDropdownChipId, editingDropdownOptionList]);

    useEffect(() => {
        // 新增時不帶語系
        const enLabels = editingDropdownAddValue.concat("@en");
        const zhLabels = editingDropdownAddValue.concat("@zh");
        setEnData(enLabels);
        setZhData(zhLabels);
    }, [editingDropdownAddValue]);

    return (
        <Modal
            onClose={onClick}
            onOpen={onClick}
            open={isEditDropdownChip}
            size="tiny"
        >
            <Modal.Header>
                {editingDropdownType === "create" ? "新增" : "編輯"}
            </Modal.Header>
            <Modal.Content
                style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "1rem"
                }}
            >
                <div>
                    <Modal.Description>中文版顯示名稱</Modal.Description>
                    <Input
                        style={{ width: "100%", marginTop: "0.5rem" }}
                        value={zhData}
                        onChange={onchangeZhHandler}
                    />
                </div>

                <div>
                    <Modal.Description>外文版顯示名稱</Modal.Description>
                    <Input
                        style={{ width: "100%", marginTop: "0.5rem" }}
                        value={enData}
                        onChange={onchangeEnHandler}
                    />
                </div>
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={onClick} negative>
                    取消
                </Button>
                <Button onClick={() => onSave(editingDropdownChipId)} positive>
                    儲存
                </Button>
            </Modal.Actions>
        </Modal>
    );
};
export default CustomEditModal;

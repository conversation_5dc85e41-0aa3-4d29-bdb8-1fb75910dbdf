import React, { useContext } from "react";

// component
import FileFolderControlPanel from "./FileFolderControlPanel";
import Folder from "../../../../common/imageCommon/folder";
import FilePickerModal from "../../upload/uploadImage/filePicker/FilePickerModal";
// import FullTextFilePickerModal from "../filePicker/FullTextFilePickerModal";
// store
import { StoreContext } from "../../../../../store/StoreProvider";

const CurrentFileFolder = () => {
    const [state] = useContext(StoreContext);
    const { common } = state;
    const { pickConfig } = common;

    // FilePickerModal可用於嵌入在 Gallery 中的 firstChild
    return (
        <Folder
            pickConfig={pickConfig.uploadPage}
            FolderControlPanel={FileFolderControlPanel}
            galleryFirstChild={<FilePickerModal />}
        />
    );
};

export default CurrentFileFolder;

import React from "react";

// ui
import { Tab } from "semantic-ui-react";

// custom
import CustomSingleTab from "./CustomSimgleTab";
import CustomAllTab from "./CustomAllTab";

const panes = [
    {
        menuItem: "下載「目前頁面」內容",
        render: () => (
            <Tab.Pane attached={false}>
                <CustomSingleTab />
            </Tab.Pane>
        )
    },
    {
        menuItem: "下載「所有頁面」內容",
        render: () => (
            <Tab.Pane attached={false}>
                <CustomAllTab />
            </Tab.Pane>
        )
    }
];

const CustomTab = () => <Tab menu={{ pointing: true }} panes={panes} />;

export default CustomTab;

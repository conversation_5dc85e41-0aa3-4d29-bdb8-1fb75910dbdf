import React from "react";
import { getApiByField } from "../../../../api/nmtl/ApiField";
import {
    splitMultiValues,
    MAX_OPTION
} from "../../../common/sheetCrud/sheetCrudHelper";

const CustomMulitPersonSpan = ({ cellId, fields, defaultValue }) => {
    const apiName = getApiByField(cellId);

    const rawItems = splitMultiValues(cellId, defaultValue);
    const lookupTable = fields[apiName]
        .filter(item => rawItems.includes(item.id))
        .map(item => ({
            id: item.id,
            label: item.label
        }))
        .reduce((d, x) => ({ ...d, [x.id]: x.label }), {})
        .slice(0, MAX_OPTION);

    return <span>{rawItems.map(itemId => lookupTable[itemId] || itemId)}</span>;
};

export default CustomMulitPersonSpan;

import React, { useContext, useState, useMemo, useEffect } from 'react';

// ui
import { Input } from 'semantic-ui-react';

// store
import { StoreContext } from '../../../../store/StoreProvider';
import Act from '../../../../store/actions';
import { getLangTag } from '../../../common/sheetCrud/utils';
import useDebounce from '../../../common/hooks/useDebounce';

const CustomSingleInput = ({
  rowId,
  cellId,
  idx,
  defaultValue,
  createState,
  setCallback,
  disabled = false,
  isDiffValue = false,
  ...rest
}) => {
  const [, dispatch] = useContext(StoreContext);

  // input value
  const [inputValue, setInputValue] = useState(defaultValue);
  const debInputValue = useDebounce(inputValue, 800); // debounce value

  // searchbar change
  const handleChange = (value) => {
    if (disabled) {
      return;
    }

    setInputValue(value);
  };

  const handleAddClick = () => {
    if (disabled) {
      return;
    }

    // 找下一個可以用的欄位，但是可能 createState: {3:"", 4:"", 8:""}
    // 目前機制從 0 開始找
    const objKeys = Object.keys(createState);
    let nextIdx = '-1';
    for (let i = 0; i < objKeys.length + 1; i += 1) {
      const iStr = i.toString();
      if (objKeys.indexOf(iStr) < 0) {
        nextIdx = iStr;
        break;
      }
    }
    // eslint-disable-next-line no-param-reassign
    createState[nextIdx] = '';
    // -1 為新增 item
    setCallback(cellId, rowId, -1, Object.assign({}, createState));
  };

  useEffect(() => {
    const cellValue = {
      ...createState,
      // null 代表要刪除，不能帶 ""，因為還是會被存進去
      ...{ [idx]: debInputValue === '' ? null : debInputValue },
    };

    if (debInputValue !== null) {
      // keep input value when it changed
      setInputValue(debInputValue);

      // change input background color when value is diff
      if (debInputValue !== defaultValue) {
        // dispatch
        dispatch({
          type: Act.DATA_CONTENT_ROW_CHANGED,
          payload: {
            rowId,
            cellId,
            cellValue,
          },
        });
      } else {
        // dispatch
        dispatch({
          type: Act.DATA_CONTENT_ROW_NO_CHANGED,
          payload: {
            rowId,
            cellId,
            idx,
          },
        });
      }
    }

    setCallback(cellId, rowId, idx, cellValue);
  }, [debInputValue]);

  // console.log(cellId, createState);
  return useMemo(
    () => (
      <Input
        {...rest}
        label={{
          basic: true,
          icon: 'add',
          content: getLangTag(inputValue),
          size: 'mini',
          onClick: handleAddClick,
        }}
        labelPosition="right"
        // keep input value
        value={inputValue || ''}
        // this is not an error., it just used to change the input background color
        style={{
          backgroundColor: isDiffValue ? '#fadbd4' : '',
        }}
        // onChange event
        onChange={(e, data) => handleChange(data.value)}
      />
    ),
    [cellId, inputValue, createState, isDiffValue],
  );
};

export default CustomSingleInput;

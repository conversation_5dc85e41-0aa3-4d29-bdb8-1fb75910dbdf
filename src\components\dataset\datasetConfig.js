import { createNmtlData, updateNmtlData } from '../../api/nmtl';
import textMsg from '../websiteSetting/commons/textMsg';
import Api from '../../api/nmtl/Api';

const PAGE_ITEM_NUM = 10;
const { sheetName } = textMsg;
const apiStr = Api.getGeneric;
const graph = 'tltc';
const classType = 'Publication';
const getMenuPlacement = (rowId) => (rowId >= PAGE_ITEM_NUM * 0.5 ? 'top' : 'bottom');

// 決定CustomEditor要不要開上傳Image功能
const openImageUpload = (mainSubject = {}, sheet = {}, actHeader = '') => {
    if (actHeader === 'introduction') {
        return true;
    }

    if (mainSubject && sheet) {
        const tlvmDescCell = {
            tmpMainSubject: 'tlvm',
            tmpSheetName: 'TlvmPeriod',
            tmpCellName: 'desc',
        };
        const { tmpMainSubject, tmpSheetName, tmpCellName } = tlvmDescCell;
        if (
            mainSubject.selected?.dataset === tmpMainSubject &&
            sheet.selected?.key === tmpSheetName &&
            actHeader === tmpCellName
        ) {
            return true;
        }
    }
    return false;
};
//
const deletePeakData = (user, srcId, pekId) =>
    new Promise(async (resolve, reject) => {
        const entry = {
            classType,
            graph,
            srcId,
            value: {
                hasPeakMono: pekId,
            },
        };
        const entryDst = {
            classType,
            graph,
            srcId,
            value: { hasPeakMono: [] },
        };
        updateNmtlData(user, apiStr, graph, sheetName, entry, entryDst)
            .then((res) => resolve(res))
            .catch((err) => reject(err));
    });

const createPeakData = (user, srcId, pekId) =>
    new Promise(async (resolve, reject) => {
        const entry = {
            classType,
            graph,
            srcId,
            value: {
                hasPeakMono: [pekId],
            },
        };
        createNmtlData(user, apiStr, graph, sheetName, entry)
            .then((res) => resolve(res))
            .catch((err) => reject(err));
    });

export { PAGE_ITEM_NUM, getMenuPlacement, openImageUpload, createPeakData, deletePeakData };

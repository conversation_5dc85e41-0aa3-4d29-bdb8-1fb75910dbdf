import React, { useContext, useEffect, useState } from 'react';

//
import { useLocation } from 'react-router-dom';

import '../../Style/systemData.css';
import { Menu } from 'semantic-ui-react';
import { StoreContext } from '../../store/StoreProvider';
import Act from '../../store/actions';
import systemDataMenu from './menuConfig';
import NoAuthority from '../pages/NoAuthority';
import { filterSubMenu } from '../../commons/filterGroup';

function SystemData() {
    const [menu, setMenu] = useState(systemDataMenu);
    const [itemIndex, setItemIndex] = useState(null);
    const location = useLocation();
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { role } = user;
    const { groupInfo } = state.data;
    useEffect(() => {
        const menuItem = systemDataMenu
            .filter((element) => element.authority.indexOf(role) >= 0)
            .filter((subRoute) => filterSubMenu(subRoute, location, groupInfo));
        setItemIndex(menuItem[0]);
        setMenu(menuItem);
        if (menuItem.length > 0) {
            dispatch({
                type: Act.SET_SYSTEMDATAACTIVEITEM,
                payload: menuItem[0].name,
            });
        }
    }, [user, groupInfo]);

    return (
        <div className="SystemData">
            {menu.length > 0 && (
                <div className="leftArea">
                    {itemIndex !== null && (
                        <Menu pointing vertical style={{ width: '100%' }} className="menuBar">
                            {menu.map((element, index) => (
                                <Menu.Item
                                    key={index}
                                    name={element.name}
                                    active={itemIndex.name === element.name}
                                    onClick={() => {
                                        setItemIndex(element);
                                        dispatch({
                                            type: Act.SET_SYSTEMDATAACTIVEITEM,
                                            payload: element.name,
                                        });
                                    }}
                                />
                            ))}
                        </Menu>
                    )}
                </div>
            )}
            {menu.length > 0 && itemIndex !== null && (
                <div className="rightArea">
                    <itemIndex.component />
                </div>
            )}

            {menu.length === 0 && <NoAuthority />}
        </div>
    );
}

export default SystemData;

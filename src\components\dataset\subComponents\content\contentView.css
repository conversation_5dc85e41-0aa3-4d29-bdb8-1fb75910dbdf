.freeze-head-and-col td:nth-of-type(3) {
  position: -webkit-sticky;
  position: sticky;
  top: 0px;
  background-color: #f9fafb;
  left: 0px;
  z-index: 1;
}

.freeze-head-and-col thead tr th:nth-of-type(3) {
  position: -webkit-sticky;
  position: sticky;
  top: 0px;
  background-color: #f9fafb;
  left: 0px;
  z-index: 5;
}

/*.freeze-head-and-col thead tr:first-of-type {*/
/*    position: -webkit-sticky;*/
/*    position: sticky;*/
/*    top: 0px;*/
/*    background-color: #f9fafb;*/
/*    left: 0px;*/
/*    z-index: 5;*/
/*}*/

.freeze-head-and-col .tableResize {
  max-width: 100%;
  resize: horizontal;
  overflow: hidden;
  height: 100%;
}

/*for srcId style*/
.ui.transparent.input.srcIdStyle>input{
  color: rgba(0,0,0,0.6);
}
.ui.disabled.input.srcIdStyle{
  opacity: 1;
}
.ui.transparent.srcIdStyle{
  background-color: #e8e8e8;
  padding: 0.5833em 0.833em;
  border-radius: 0.28571429rem;
}
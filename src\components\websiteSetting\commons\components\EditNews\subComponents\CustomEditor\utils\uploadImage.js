import axios from "axios";
import {
    fileServerAPI,
    fileServerMethod,
    fsFrontEdit
} from "../../../../../../../../api/fileServer";
import uploadConfig from "../../../../../../../toolPages/components/upload/uploadConfig";

// const checkFileType = fileList => {
//     // eslint-disable-next-line no-restricted-syntax
//     for (const file of fileList) {
//         if (!/^image\//.test(file.type)) {
//             return false;
//         }
//     }
//     return true;
// };

// for CustomEditor component use only
const uploadImage = (editorRef, subject, fsFolderName) => {
    if (!subject) return;
    const inputEl = document.createElement("input");
    inputEl.setAttribute("type", "file");
    inputEl.setAttribute("accept", "image/*");
    inputEl.setAttribute("multiple", "");
    inputEl.click();

    // const fsFolderName = "news";
    inputEl.onchange = () => {
        const preReqUrl = `${fileServerAPI.uploadFile.replace(
            "[type]",
            uploadConfig.image
        )}`;
        const reqUrl = `${preReqUrl}/${fsFrontEdit}/${subject}/${fsFolderName}`;

        // // eslint-disable-next-line no-restricted-syntax
        // for (const file of inputEl.files) {
        //     // create formData
        //     const formData = new FormData();
        //     formData.append(uploadConfig.ImageFormName, file);
        //     console.log(formData);
        //     // upload image
        //     axios({
        //         method: fileServerMethod.uploadFile,
        //         url: reqUrl,
        //         headers: {
        //             "Access-Control-Allow-Origin": "*"
        //         },
        //         data: formData
        //     })
        //         .then(res => {
        //             if (res.status === 200) {
        //                 const curIdx = editorRef.current
        //                     .getEditor()
        //                     .getSelection().index;
        //                 console.log(res.data);
        //                 res.data.images.forEach(({ imgUrl }) => {
        //                     // 上傳圖片後顯示在畫面上
        //                     editorRef.current
        //                         .getEditor()
        //                         .insertEmbed(curIdx, "image", imgUrl);
        //                 });
        //             }
        //         })
        //         .catch(err => {
        //             console.log("err ", err);
        //         });
        // }

        const files = Array.from(inputEl.files);

        const uploadFile = file =>
            new Promise((resolve, reject) => {
                const formData = new FormData();
                formData.append(uploadConfig.ImageFormName, file);

                axios({
                    method: fileServerMethod.uploadFile,
                    url: reqUrl,
                    headers: {
                        "Access-Control-Allow-Origin": "*"
                    },
                    data: formData
                })
                    .then(res => {
                        if (res.status === 200) {
                            resolve(
                                res.data.images.map(({ imgUrl }) => imgUrl)
                            );
                        } else {
                            reject(new Error("Failed to upload file"));
                        }
                    })
                    .catch(err => {
                        reject(err);
                    });
            });

        // Chain the promises in reverse order to control the order of image insertion
        files.reduceRight(
            (chain, file) =>
                chain
                    .then(() => uploadFile(file))
                    .then(imageUrls => {
                        const curIdx = editorRef.current
                            .getEditor()
                            .getSelection().index;
                        imageUrls.forEach(imgUrl => {
                            editorRef.current
                                .getEditor()
                                .insertEmbed(curIdx, "image", imgUrl);
                        });
                    })
                    .catch(err => {
                        console.log("err ", err);
                    }),
            Promise.resolve()
        );
    };
};

export default uploadImage;

import React, { useEffect, useState } from "react";
import CustomPagination from "../../../../../common/CustomPagination/CustomPagination";

const VillagesCustomPagination = props => {
    const { data, id, setCurPage, setPerPageNum, curPage, perPageNum } = props;
    const [totalPages, setTotalPages] = useState(1);

    useEffect(() => {
        setTotalPages(Math.ceil(data.length / perPageNum));
    }, [data, perPageNum]);

    const handlePage = (evt, { activePage }) => {
        setCurPage(activePage);
    };

    const handleDDPage = (evt, tmpData) => {
        setCurPage(tmpData.value);
    };

    const handlePerPageNum = (evt, tmpData) => {
        setCurPage(1);
        setPerPageNum(tmpData.value);
    };

    return (
        <div id={id}>
            <CustomPagination
                currentPage={curPage}
                totalPages={totalPages}
                handlePage={handlePage}
                handlePerPageNum={handlePerPageNum}
                handleDDPage={handleDDPage}
            />
        </div>
    );
};

export default VillagesCustomPagination;

import React from "react";

// redux
import { useSelector } from "react-redux";

// scss
import "../../Style/commonStyle.scss";

// components
import MenuBar from "./subComponents/MenuBar";

function AccountManagement() {
    const state = useSelector(tmpState => tmpState.accMng);
    const { activeItemACC } = state;
    return (
        <div className="AccountManagement">
            <div className="leftArea">
                <MenuBar />
            </div>
            <div className="rightArea">
                <activeItemACC.component />
            </div>
        </div>
    );
}

export default AccountManagement;

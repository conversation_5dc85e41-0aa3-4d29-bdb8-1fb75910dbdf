import React from "react";
import { getApiByField } from "../../../../api/nmtl/ApiField";
import { MAX_OPTION } from "../../../common/sheetCrud/sheetCrudHelper";

const CustomSinglePersonSpan = ({ cellId, fields, defaultValue }) => {
    const apiName = getApiByField(cellId);

    // 切換表單時，資料尚未準備完成。
    if (Object.keys(fields).indexOf(apiName) < 0) {
        return null;
    }

    return (
        <span>
            {fields[apiName]
                .filter(item => item.id === defaultValue)
                .slice(0, MAX_OPTION)[0]?.label || defaultValue}
        </span>
    );
};

export default CustomSinglePersonSpan;

import React, { useContext, useEffect, useState } from "react";

// semantic-ui-react
import { Button, Image, Icon } from "semantic-ui-react";

// components
import Selector from "../../components/Selector";
import UploadImageModal from "../../components/UploadImageModal";
import UpdateText from "../../components/UpdateText";
import NewUpdateText from "../../commons/components/UpdateText";
import LanguageSelect from "../../components/LanguageSelect";
import WaterMarkSelect from "../../components/WaterMarkSelect";
import SaveButton from "../../components/SaveButton";
import SaveResultModal from "../../commons/components/SaveResultModal";

// general
import dragImage from "../../../../images/dragImage.svg";
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import { setDropDownFromMsListData } from "../../commons";
import { isEmpty } from "../../../../commons";
import Api from "../../../../api/nmtl/Api";
import { fileServerAPI } from "../../../../api/fileServer";

// configs
import NMTL_WEB_CONFIGS from "../config";

// functions
import { readNmtlData, updateNmtlData } from "../../../../api/nmtl";

const { VRMUSEUM, IMAGE, TEXT } = NMTL_WEB_CONFIGS.MENU_ACTIVE_ITEM;

function VrMuseum() {
  const [state, dispatch] = useContext(StoreContext);
  const {
    updatedData,
    originData,
    menuActiveItem,
    selectOption,
    msListData,
    fusekiData: {
      vrMuseum: {
        logo: { oriData, tempData },
      },
    },
  } = state.websiteSetting;
  const [openModal, setOpenModal] = useState(false);
  const [language, setLanguage] = useState("zh");
  const [dropDown, setDropDown] = useState({});
  const [imageType, setImageType] = useState("main");
  const [mainImageUrl, setmainImageUrl] = useState("");
  const [resultModal, setResultModal] = useState({
    isOpen: false,
    message: "",
  });

  useEffect(() => {
    // 下拉選單選項清空
    dispatch({
      type: Act.SET_SELECTOPTION,
      payload: "",
    });
  }, []);

  useEffect(() => {
    if (originData.length !== 0 && msListData.length !== 0) {
      const originObj = originData.find(
        (element) => element.id === menuActiveItem.key
      );
      const originArray = Object.keys(originObj);
      const msListArray = msListData.filter((element) => element.type === "vr");
      const newDropDown = Object.assign(
        {},
        originObj,
        setDropDownFromMsListData(originArray, msListArray, VRMUSEUM)
      );
      setDropDown(newDropDown);
      // 更新 updatedData
      const tmpAllData = JSON.parse(JSON.stringify(updatedData));
      const tmpObjIndex = tmpAllData.findIndex(
        (element) => element.id === menuActiveItem.key
      );
      tmpAllData[tmpObjIndex] = newDropDown;
      dispatch({
        type: Act.SET_UPDATEDDATA,
        payload: tmpAllData,
      });
    }
  }, [originData, msListData]);

  useEffect(() => {
    if (selectOption === "" || isEmpty(originData)) return;
    const tmpDropDown = originData.find(
      (element) => element.id === menuActiveItem.key
    );
    if (tmpDropDown[selectOption]) {
      setmainImageUrl(tmpDropDown[selectOption].url.main);
      // setWatermarkImageUrl(tmpDropDown[selectOption].url.watermark);
    }
  }, [selectOption, originData]);

  useEffect(() => {
    selectOption &&
      readNmtlData(Api.getLogoImage).then(({ data }) => {
        const targetLogoData = data.find(
          ({ vrId }) => vrId.replace("CARD", "") === selectOption
        );
        const {
          g: graph = "",
          vrId = "",
          urlId: srcId = "",
          logoWords: label = "",
          logoImage: imageName = "",
          logoType: type = "",
        } = targetLogoData;

        // dispatch
        dispatch({
          type: Act.SET_VRMUSEUM_LOGO,
          payload: {
            oriData: {
              type,
              vrId,
              graph,
              label,
              srcId,
              imageName,
              classType: "URLEvent",
            },
            tempData: {
              text: label,
              imageUrl:
                (imageName && `${fileServerAPI.readLogoImage}/${imageName}`) ||
                "",
              type,
            },
          },
        });
      });
  }, [selectOption]);

  // for update logo data to Apache Jena Fuseki
  const updateNmtlLogoData = async ({ user, api, dataset, sheetName }) => {
    const { graph, srcId, label, type, imageName, classType } = oriData;
    const entrySrc = {
      graph,
      srcId,
      classType,
      value: { label, imageName, type },
    };
    const entryDst = {
      ...entrySrc,
      value: { label, imageName: "", type },
    };
    await updateNmtlData(user, api, dataset, sheetName, entrySrc, entryDst)
      .then(() => {
        dispatch({
          type: Act.SET_VRMUSEUM_LOGO,
          payload: {
            oriData: { ...oriData, label, imageName: "", type },
            tempData: { ...tempData, imageUrl: "" },
          },
        });
        setResultModal({
          isOpen: true,
          message: "移除圖片成功!",
        });
      })
      .catch((error) =>
        console.error(`Remove Nmtl logo Data Failed: ${error}`)
      );
  };

  return (
    <div className="VrMuseum">
      <div className="Selector">
        <Selector dropDown={dropDown} />
      </div>
      <div className="pictureUpdate">
        <h1>館設照片</h1>
        <div className="updateArea">
          <Image
            style={{
              cursor: selectOption === "" ? "default" : "pointer",
              height: "100%",
            }}
            src={mainImageUrl === "" ? dragImage : mainImageUrl}
            onClick={() => {
              if (!selectOption) return false;
              setOpenModal(true);
              setImageType("main");
            }}
          />
        </div>
      </div>
      <div className="textUpdate">
        <div className="updateAreaTop">
          <h1>概述</h1>
          <LanguageSelect language={language} setLanguage={setLanguage} />
        </div>
        <div className="updateArea">
          <UpdateText
            dropDown={dropDown}
            language={language}
            option={{
              column: "description",
              priority: "No priority",
            }}
            disabled={!selectOption}
          />
        </div>
      </div>
      <div className="watermark">
        <div className="updateAreaTop">
          <h1>珍品典藏浮水印</h1>
          <WaterMarkSelect />
        </div>
        <div className="updateArea">
          {tempData.type === IMAGE && (
            <div className="watermarkImg">
              <Button
                className="cancelBtn"
                onClick={() => {
                  if (!selectOption || !tempData.imageUrl) return;

                  // remove logo image data
                  updateNmtlLogoData({
                    user: state.user,
                    api: Api.getGeneric,
                    dataset: selectOption,
                    sheetName: "frontEdit Data",
                  });
                }}
              >
                <Icon name="cancel" color="red" />
              </Button>
              <Image
                style={{
                  cursor: selectOption === "" ? "default" : "pointer",
                  height: "100%",
                  width: "80%",
                }}
                src={tempData.imageUrl || dragImage}
                onClick={() => {
                  if (!selectOption) return false;
                  setOpenModal(true);
                  setImageType("watermark");
                }}
              />
            </div>
          )}
          {tempData.type === TEXT && (
            <NewUpdateText
              desc={tempData.text || ""}
              disabled={!selectOption}
              onChange={(event) => {
                event.persist();
                dispatch({
                  type: Act.SET_VRMUSEUM_LOGO_TEMP_DATA,
                  payload: { ...tempData, text: event.target.value },
                });
              }}
            />
          )}
        </div>
      </div>
      <div className="btnArea">
        <SaveButton language={language} />
      </div>

      {selectOption && openModal && (
        <UploadImageModal
          setOpenModal={setOpenModal}
          imageType={imageType}
          setAllData={setmainImageUrl}
        />
      )}

      <SaveResultModal
        openModal={resultModal.isOpen}
        modalMessage={resultModal.message}
        onClose={(resultModal) =>
          setResultModal({ ...resultModal, isOpen: false })
        }
        onClick={() => setResultModal({ ...resultModal, isOpen: false })}
      />
    </div>
  );
}

export default VrMuseum;

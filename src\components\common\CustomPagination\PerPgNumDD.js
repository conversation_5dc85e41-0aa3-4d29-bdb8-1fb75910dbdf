import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";

// plugins
import { Dropdown } from "semantic-ui-react";

// utils
import { isEmpty } from "../../../commons";

function PerPgNumDd({ handlePerPageNum, pageOption }) {
    // const dispatch = useDispatch();
    const [options, setOptions] = useState([]);

    useEffect(() => {
        const tmp = pageOption.map(value => ({
            key: value,
            text: value,
            value
        }));
        setOptions(tmp);
    }, []);

    return (
        <React.Fragment>
            <div style={{ marginRight: "0.5rem" }}>
                <p>選擇單頁顯示數量</p>
            </div>
            {!isEmpty(options) && (
                <Dropdown
                    compact
                    selection
                    options={options}
                    onChange={handlePerPageNum}
                    defaultValue={options[0].value}
                />
            )}
        </React.Fragment>
    );
}

PerPgNumDd.propTypes = {
    /** page change callback */
    handlePerPageNum: PropTypes.func,
    /** 一頁顯示幾筆選項 */
    pageOption: PropTypes.arrayOf(PropTypes.number)
};

PerPgNumDd.defaultProps = {
    /** page change callback */
    handlePerPageNum: () => {},
    /** 一頁顯示幾筆選項 */
    pageOption: [5, 10, 15]
};

export default PerPgNumDd;

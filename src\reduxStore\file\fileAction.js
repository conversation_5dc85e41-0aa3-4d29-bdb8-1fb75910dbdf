const FileAct = {
    FILES_FILE_SELECTED: "FILES_FILE_SELECTED",
    SET_FILES_FOLDER_SETTINGS: "SET_FILES_FOLDER_SETTINGS",
    SELECT_FOLDER: "SELECT_FOLDER",
    FOLDER_FILES_URL: "FOLDER_FILES_URL",
    CUR_FOLDER_FILES_STATUS: "CUR_FOLDER_FILES_STATUS",
    SET_DEFAULT_VALUE: "SET_DEFAULT_VALUE",
    SELECT_FILE: "SELECT_FILE",
    UPLOAD_IMAGE: "UPLOAD_IMAGE",
    SET_FORM_DATA: "SET_FORM_DATA",
    UPLOAD_TMP_IMAGES: "UPLOAD_TMP_IMAGES",
    DROP_FOLDER_FILES_STATUS: "DROP_FOLDER_FILES_STATUS",
    UPLOAD_LOADING: "UPLOAD_LOADING",
    IMAGE_EDITOR_DISPLAY: "IMAGE_EDITOR_DISPLAY",
    IMAGE_EDITOR_CURRENT_DATA: "IMAGE_EDITOR_CURRENT_DATA",
    CLEAR_CUR_FOLDER_FILES_STATUS: "CLEAR_CUR_FOLDER_FILES_STATUS",
    CLEAR_DROP_FOLDER_FILES_STATUS: "CLEAR_DROP_FOLDER_FILES_STATUS",
    IMG_PICKER_INITIAL_STATE: "IMG_PICKER_INITIAL_STATE",
    SET_FILE_SERVER_URL: "SET_FILE_SERVER_URL",
    UPLOAD_CLEAR_CACHE: "UPLOAD_CLEAR_CACHE",
    PICKER_CUR_FOLDER_MESSAGE: "PICKER_CUR_FOLDER_MESSAGE",
    CLEAN_PICKER_CUR_FOLDER_MESSAGE: "CLEAN_PICKER_CUR_FOLDER_MESSAGE",
    PICKER_DROP_MESSAGE: "PICKER_DROP_MESSAGE",
    CLEAN_PICKER_DROP_MESSAGE: "CLEAN_PICKER_DROP_MESSAGE",
    SET_FIRSTLAYERFILENAME: "SET_FIRSTLAYERFILENAME",
    INIT_FOLDERPATTERN: "INIT_FOLDERPATTERN",
    INIT_SELECT_FOLDER: "INIT_SELECT_FOLDER",
    UPLOAD_IMAGES_LATEST: "UPLOAD_IMAGES_LATEST",
    INIT_FILE_SETTINGS: "INIT_FILE_SETTINGS",
    SELECTED_CROP_IMG: "SELECTED_CROP_IMG",
    OPEN_CROP_IMG_MODAL: "OPEN_CROP_IMG_MODAL"
};

export default FileAct;

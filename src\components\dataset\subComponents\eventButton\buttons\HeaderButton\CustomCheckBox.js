import React, { useState } from "react";

// ui
import { Checkbox, Popup } from "semantic-ui-react";

const CustomCheckBox = ({
    item,
    sheetName,
    allChecked,
    defaultChecked,
    setHeader,
    ...rest
}) => {
    // handle checkbox
    const [checked, setChecked] = useState(defaultChecked || false);

    // handle checkbox for delete
    const handleCheckbox = () => {
        // checkbox state
        setChecked(!checked);
        setHeader(pre => {
            if (!pre || !pre[sheetName]) {
                return { [sheetName]: [item] };
            }

            const idx = pre[sheetName].findIndex(i => i.id === item.id);
            // item is not in the list, add it.
            if (idx < 0) {
                return { [sheetName]: pre[sheetName].concat(item) };
            }

            // item is in the list, remove it.
            return {
                [sheetName]: pre[sheetName]
                    .slice(0, idx)
                    .concat(pre[sheetName].slice(idx + 1))
            };
        });
    };

    const myStyle = {
        /* 固定寬度 */
        width: "110px",
        /* 文本强制不换行 */
        whiteSpace: "nowrap",
        /* 文本溢出显示省略号 */
        textOverflow: "ellipsis",
        /* 溢出的部分隐藏 */
        overflow: "hidden"
    };

    const myCheckBox = (
        <Checkbox
            {...rest}
            style={myStyle}
            label={item.label}
            checked={allChecked || defaultChecked}
            onClick={handleCheckbox}
        />
    );

    // 如果超過6字元，則使用Popup顯示
    return item.label.length > 6 ? (
        <Popup content={item.label} trigger={myCheckBox} />
    ) : (
        myCheckBox
    );
};

export default CustomCheckBox;

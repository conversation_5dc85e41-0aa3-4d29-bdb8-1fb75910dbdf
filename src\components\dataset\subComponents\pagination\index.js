import React, { useContext } from "react";

// ui
import { Pagination, Icon, Divider, Container } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import { PAGE_ITEM_NUM } from "../../datasetConfig";

const CustomPagination = () => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);

    // get data from dataReducer
    const { data } = state;

    // get dataset(mainSubject) and sheet
    const { pagination } = data;
    const { activePage, totalPage } = pagination;

    const handlePageChange = (event, { activePage }) => {
        // update page to TopContent.js
        dispatch({
            type: Act.DATA_PAGINATION_ACTIVE_PAGE,
            payload: activePage
        });
        // refresh all parameter
        // refresh changed when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });
        // refresh checked when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });
        // refresh created when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CREATED_CLEAN });
    };

    return (
        <React.Fragment>
            <Divider hidden style={{ margin: ".4rem 0" }} />
            <Container textAlign="center">
                <Pagination
                    activePage={activePage}
                    ellipsisItem={{
                        content: <Icon name="ellipsis horizontal" />,
                        icon: true
                    }}
                    firstItem={{
                        content: <Icon name="angle double left" />,
                        icon: true
                    }}
                    lastItem={{
                        content: <Icon name="angle double right" />,
                        icon: true
                    }}
                    prevItem={{
                        content: <Icon name="angle left" />,
                        icon: true
                    }}
                    nextItem={{
                        content: <Icon name="angle right" />,
                        icon: true
                    }}
                    totalPages={
                        totalPage ? Math.ceil(totalPage / PAGE_ITEM_NUM) : 1
                    }
                    onPageChange={handlePageChange}
                />
            </Container>
        </React.Fragment>
    );
};

export default CustomPagination;

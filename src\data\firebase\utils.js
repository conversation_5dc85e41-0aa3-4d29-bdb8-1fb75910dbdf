import { getSingleLayerCollection } from "../../api/firebase/cloudFirestore";
import frontendSettingsConfig from "../../config/config-frontendSettings";
import Act from "../../store/actions";

const getAllGPInfo = async (userInfo, dispatch) => {
    const tmpGPInfo = await getSingleLayerCollection(
        frontendSettingsConfig.GROUPINFO
    );
    // 找出每個所屬group
    const tmpGroups = tmpGPInfo.filter(gpEl =>
        gpEl.members.find(memEl => memEl.uid === userInfo.uid)
    );
    dispatch({
        type: Act.FIREBASE_GROUP_INFO_BY_USER,
        payload: tmpGroups
    });
};

export default getAllGPInfo;

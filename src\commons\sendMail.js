import axios from "axios";

// const sendMail = {
// 	from: "<EMAIL>",
// 	to: email,
// 	subject: `ATC_${card_id}`+" card status has been changed (Orient-CC)",
// 	text: context,
// };
//
// apiUrl: "https://api.daoyidh.com/mail"
const handleSendEmail = (sendMail, apiUrl, onSuccess, timeout = 10000) => {
    // timeout
    axios.defaults.timeout = timeout;
    try {
        // axios api
        return axios
            .post(apiUrl, { sendMail })
            .then(res => onSuccess({ state: res && res.status === 200 }))
            .catch(err => {
                console.error("api:axios:catch:error: ", err.message);
                return { state: false, error: err.message };
            });
    } catch (err) {
        console.error("api:axios:try:catch:error: ", err.message);
        return { state: false, error: err.message };
    }
};

export default handleSendEmail;

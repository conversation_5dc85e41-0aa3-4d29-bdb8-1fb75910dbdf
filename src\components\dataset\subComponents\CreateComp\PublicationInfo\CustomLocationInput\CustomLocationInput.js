import React, { useState, useMemo, useEffect, useContext } from "react";

// ui
import { Form, Input } from "semantic-ui-react";
import useDebounce from "../../../../../common/hooks/useDebounce";
import {
    LOCATION_KEY,
    LOCATION_LABEL,
    LOCATION_LAT,
    LOCATION_LONG
} from "../../../../../common/sheetCrud/sheetCrudHelper";
import { isEmpty } from "../../../../../../commons";
import { assignSubValues } from "../../../../../common/sheetCrud/utils";
import { StoreContext } from "../../../../../../store/StoreProvider";

// store
const CustomLocationInput = ({
    cellId,
    defaultValue,
    createState,
    disabled = false,
    setCallback,
    menuName
    // ...rest
}) => {
    const [state] = useContext(StoreContext);
    const { mainSubject } = state.data;
    const { dataset } = mainSubject.selected;
    const [input, setInput] = useState(defaultValue || "");
    const debInput = useDebounce(input, 800); // debounce value

    const getSubData = async locId => {
        // console.log("locId", locId);
        const res = await assignSubValues(
            [{ [LOCATION_KEY]: { 0: locId[0] } }],
            dataset
        );

        if (!isEmpty(res[0])) {
            const value = res[0][cellId] ? Object.values(res[0][cellId]) : null;
            setCallback(cellId, value, menuName);
            setInput(value);
        }
    };
    // 如果出版地點為空，同時刪掉內容
    useEffect(() => {
        if (!createState || !Object.hasOwn(createState, LOCATION_LABEL)) {
            return;
        }

        if (isEmpty(createState?.srcId_hasPlaceOfPublication)) {
            setInput(null);
            setCallback(cellId, null, menuName);
        } else {
            getSubData(createState?.srcId_hasPlaceOfPublication);
        }
    }, [createState?.srcId_hasPlaceOfPublication]);

    const handleChange = value => {
        if (disabled) {
            return;
        }

        setInput(value);
    };
    //
    useEffect(() => {
        if (!debInput) return;

        // debFirstInput === "" 存 null
        if (debInput === "") {
            // setCallback(`${cellId}_${LOCATION_KEY}`, debInput, menuName);
            setCallback(cellId, debInput, menuName);
            return;
        }

        setCallback(cellId, debInput, menuName);
        // setCallback(`${cellId}_${LOCATION_KEY}`, debInput, menuName);
    }, [debInput]);

    return useMemo(
        () => (
            <Input
                fluid
                disabled={!createState?.srcId_hasPlaceOfPublication}
                value={input || ""}
                // onChange event
                onChange={(e, data) => handleChange(data.value)}
            />
        ),
        [cellId, input, createState]
    );
};
export default CustomLocationInput;

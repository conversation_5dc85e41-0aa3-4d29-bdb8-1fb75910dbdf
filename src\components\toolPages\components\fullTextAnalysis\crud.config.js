//
export const DATA_TYPE_RANGE = [
    { range: "string", sparql: str => `"${str}"^^xsd:string` },
    { range: "integer", sparql: str => `"${str}"^^xsd:integer` },
    { range: "decimal", sparql: str => `"${str}"^^xsd:decimal` },
    { range: "float", sparql: str => `"${str}"^^xsd:float` },
    { range: "double", sparql: str => `"${str}"^^xsd:double` },
    { range: "boolean", sparql: str => `"${str}"^^xsd:boolean` },
    { range: "dateTimes", sparql: str => `"${str}"^^xsd:dateTime` }
];

export const EVENT_PREFIX = [
    { prefix: "EVT", eventType: "Event" },
    { prefix: "PAREVT", eventType: "ParticipantEvent" },
    { prefix: "HISEVT", eventType: "HistoryEvent" }
];

export const CLASS_PREFIX = [
    { prefix: "DIARY", eventType: "Diary" },
    { prefix: "OCR", eventType: "Ocr" },
    { prefix: "PARAG", eventType: "Paragraph" },
    //
    { prefix: "TAG", eventType: "Tag" },
    { prefix: "PER", eventType: "Person" },
    { prefix: "PLA", eventType: "Place" },
    { prefix: "ORG", eventType: "Organization" },
    { prefix: "EVTTYPE", eventType: "EventType" },
    { prefix: "GRP", eventType: "Group" },
    //
    { prefix: "PGE", eventType: "PartGroupEvent" }
].concat(EVENT_PREFIX);

// range 是否為 class  type
export const rangeIsClass = range =>
    !!CLASS_PREFIX.find(c => c.eventType.toLowerCase() === range.toLowerCase());

export const domainIsClass = domain =>
    !!CLASS_PREFIX.find(
        c => c.eventType.toLowerCase() === domain.toLowerCase()
    );

export const findClassPrefix = eventType =>
    CLASS_PREFIX.find(
        c => c.eventType.toLowerCase() === eventType.toLowerCase()
    )?.prefix;

export const findClassType = eventType =>
    CLASS_PREFIX.find(
        c => c.eventType.toLowerCase() === eventType.toLowerCase()
    )?.eventType;

// range 是否為 data value type
export const rangeIsValue = range => !rangeIsClass(range);

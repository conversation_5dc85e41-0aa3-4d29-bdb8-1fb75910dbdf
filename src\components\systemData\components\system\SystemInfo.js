import React, { useState, useEffect, useCallback, useContext } from "react";

import {
    Container,
    Segment,
    Divider,
    Grid,
    Loader,
    Button,
    Icon
} from "semantic-ui-react";

// 3djs
import <PERSON><PERSON><PERSON> from "./subComponents/PieChart";

// nmtl Api
import { readNmtlData } from "../../../../api/nmtl";
import Api from "../../../../api/nmtl/Api";
import StatusTable from "./subComponents/StatusTable";
import { StoreContext } from "../../../../store/StoreProvider";
import { createHistoryEvent } from "../../../downloadData/components/history/common/common";

const SystemInfo = () => {
    const [onlineState, setOnlinesState] = useState(false);
    const [fetchData, setFetchData] = useState({
        hard: undefined,
        network: undefined
    });
    const [error, setError] = useState(undefined);

    const [state, _] = useContext(StoreContext);
    const { headerActiveName, systemDataActiveItem } = state.common;
    const { displayName } = state.user;
    const columns = [headerActiveName, systemDataActiveItem];

    // useCallback 保存方法與 useEffect 搭配，而不再另外生成避免不斷更新(會產生無限迴圈)
    const fetchSysInfo = useCallback(async () => {
        // fetch data
        const startTime = Date.now();
        const sysInfo = await readNmtlData(Api.getSystemInfo);
        const responseTime = Date.now() - startTime;

        if (!sysInfo.error) {
            // extract parameter
            const {
                cpu,
                mem,
                disk,
                rx_bytes,
                rx_dropped,
                rx_errors,
                tx_bytes,
                tx_dropped,
                tx_errors
            } = sysInfo;

            // insert parameter to array
            const hardInfoArray = [
                {
                    label: "CPU",
                    values: [{ value: 100 - cpu }, { value: cpu }]
                },
                {
                    label: "Memory",
                    values: [{ value: 100 - mem }, { value: mem }]
                },
                {
                    label: "Disk",
                    values: [{ value: 100 - disk }, { value: disk }]
                }
            ];

            const networkInfo = {
                rx_bytes,
                rx_dropped,
                rx_errors,
                tx_bytes,
                tx_dropped,
                tx_errors,
                responseTime
            };
            // set online statue
            setOnlinesState(true);
            // update parameter
            setFetchData({ hard: hardInfoArray, network: networkInfo });

            createHistoryEvent(displayName, "查詢", columns.join("/"));
        } else {
            setOnlinesState(false);
            setError(sysInfo.error);

            createHistoryEvent(
                displayName,
                "查詢失敗",
                columns.join("/"),
                sysInfo.error
            );
        }
    }, []);

    // 初次載入只執行一次
    useEffect(() => {
        fetchSysInfo();
    }, [fetchSysInfo]);

    // 間隔時間執行 (必須這樣寫才能使 setInterval 正常 working
    // 跟 react 喧染特性有關， useEffect 必須 return unmount function，
    // 否則會無限迴圈，因為 setInterval 在重新喧染時會產生新的自身元件，
    // 這時就會變成兩個 setInterval 元件，依此類推，這將會產生次方級別的元件數量，
    // 因此，需要再在產生新的時期就將舊的 setInterval 元件執行 clearInterval 進行註銷)
    useEffect(() => {
        const interval = setInterval(() => {
            fetchSysInfo();
        }, 15 * 1000);
        return () => clearInterval(interval);
    }, [fetchSysInfo]);

    return (
        <Container>
            <Segment basic compact>
                <Segment.Inline>
                    <h2>系統訊息</h2>
                    {onlineState ? (
                        <Icon color="green" name="circle" size="large" />
                    ) : (
                        <Icon color="red" name="circle" size="large" />
                    )}
                    連線狀態&nbsp;&nbsp;&nbsp;&nbsp;
                    <Icon color="orange" name="circle" size="large" />
                    已使用&nbsp;&nbsp;&nbsp;&nbsp;
                    <Icon color="blue" name="circle" size="large" />
                    未使用&nbsp;&nbsp;&nbsp;&nbsp;
                    <Button
                        onClick={fetchSysInfo}
                        icon="sync"
                        content="更新資訊"
                    />
                </Segment.Inline>
            </Segment>

            <Divider />

            <Grid columns={3} divided textAlign="center">
                <Grid.Row>
                    {fetchData.hard ? (
                        fetchData.hard.map((item, idx) => (
                            <Grid.Column key={idx}>
                                <PieChart
                                    text={item.label}
                                    data={item.values}
                                    width={200}
                                    height={200}
                                    innerRadius={60}
                                    outerRadius={100}
                                />
                            </Grid.Column>
                        ))
                    ) : error ? (
                        <span>{error}</span>
                    ) : (
                        <Loader active size="large" inline="centered" />
                    )}
                </Grid.Row>
            </Grid>

            <Divider />

            <StatusTable networkInfo={fetchData.network} />

            <Divider />
        </Container>
    );
};

export default SystemInfo;

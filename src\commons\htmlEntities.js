// "html-to-react"，會有版本不相容問題，在後台目前沒有轉成React component需求，先不安裝此套件
// const HtmlToReactParser = require('html-to-react').Parser;
//
// const htmlToReactParser = new HtmlToReactParser();

// full options list (defaults)
const md = require("markdown-it")({
    html: true, // Enable HTML tags in source
    xhtmlOut: true, // Use '/' to close single tags (<br />).
    // This is only for full CommonMark compatibility.
    breaks: true, // Convert '\n' in paragraphs into <br>
    langPrefix: "language-", // CSS language prefix for fenced blocks. Can be
    // useful for external highlighters.
    linkify: true, // Autoconvert URL-like text to links

    // Enable some language-neutral replacement + quotes beautification
    // For the full list of replacements, see https://github.com/markdown-it/markdown-it/blob/master/lib/rules_core/replacements.js
    typographer: false,

    // Double + single quotes replacement pairs, when typographer enabled,
    // and smartquotes on. Could be either a String or an Array.
    //
    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,
    // and ['«\xA0', '\xA0»', '‹\xA0', '\xA0›'] for French (including nbsp).
    quotes: "“”‘’",

    // Highlighter function. Should return escaped HTML,
    // or '' if the source string is not changed and should be escaped externally.
    // If result starts with <pre... internal wrapper is skipped.
    highlight(/* str, lang */) {
        return "";
    }
});
// .use(require("markdown-it-footnote"));

// eslint-disable-next-line import/prefer-default-export
// export const convert2HtmlEntities = text => {
// 	if (text === undefined) {
// 		return '';
// 	}
//
// 	const reg0 = /[\n]+/g;
// 	const reg1 = /[\n]+[ \t]+/g; // [ \t] 為縮排的一種
// 	const reg2 = /[\n]+[\s]+/g; // [\s] 為縮排的另一種
// 	const safeText = text
// 		.trim()
// 		.replace(/\\\\n/g, '\n\n') // "\\n" 換成 \n
// 		.replace(reg0, '\n\n') // 先把 只有一個(或多個) \n , 都轉換成 \n\n
// 		.replace(reg1, '\n\n') // 把 縮排移除
// 		.replace(reg2, '\n\n'); // 把 縮排移除
//
// 	return htmlToReactParser.parse(md.render(safeText.replaceAll('&emsp;&emsp;', '')));
// };

export const convert2HtmlEntities = text => {
    if (text === undefined) {
        return "";
    }

    const reg0 = /[\n]+/g;
    const reg1 = /[\n]+[ \t]+/g; // [ \t] 為縮排的一種
    const reg2 = /[\n]+[\s]+/g; // [\s] 為縮排的另一種
    const safeText = text
        .trim()
        .replace(/\\\\n/g, "\n\n") // "\\n" 換成 \n
        .replace(reg0, "\n\n") // 先把 只有一個(或多個) \n , 都轉換成 \n\n
        .replace(reg1, "\n\n") // 把 縮排移除
        .replace(reg2, "\n\n"); // 把 縮排移除

    return md.render(safeText.replaceAll("&emsp;&emsp;", ""));
};

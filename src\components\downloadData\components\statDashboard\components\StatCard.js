import React, { useState, useRef, useEffect } from "react";
import "./statCard.scss";
import { Loader } from "semantic-ui-react";

const StatCard = ({
    statsCount,
    title,
    stat,
    detailStatsArr,
    topicStatsRef,
    detailStats
}) => {
    const [isFocused, setIsFocused] = useState(false);
    const [detailStatsPosition, setDetailStatsPosition] = useState({
        top: -100000,
        width: 0,
        height: 0
    });

    const statCardRef = useRef(null);
    const detailStatsRef = useRef(null);

    useEffect(() => {
        if (isFocused && topicStatsRef.current && statCardRef.current) {
            const topicStatsRect = topicStatsRef.current.getBoundingClientRect();
            const statCardRect = statCardRef.current.getBoundingClientRect();
            setDetailStatsPosition(prev => ({
                ...prev,
                top: statCardRect.bottom - topicStatsRect.top,
                width: topicStatsRect.width
            }));
        }
    }, [isFocused, topicStatsRef, statCardRef]);

    useEffect(() => {
        if (detailStatsRef.current) {
            setDetailStatsPosition(prev => ({
                ...prev,
                height: detailStatsRef.current.offsetHeight
            }));
        } else {
            setDetailStatsPosition(prev => ({
                ...prev,
                height: 0
            }));
        }
    }, [isFocused, detailStatsRef]);

    return (
        <div
            className={
                statsCount ? "statCardWithCountContainer" : "statCardContainer"
            }
            ref={statCardRef}
        >
            <div
                className={
                    isFocused && detailStats && !statsCount
                        ? "statCardFocused"
                        : statsCount
                        ? "statCardWithCount"
                        : "statCard"
                }
                tabIndex={0}
                onFocus={() => {
                    detailStats && setIsFocused(true);
                }}
                onBlur={() => {
                    detailStats && setIsFocused(false);
                }}
            >
                <p>{title}</p>
                {!stat ? <Loader active inline size="tiny" /> : <p>{stat}</p>}
            </div>
            {isFocused && detailStats && detailStatsArr.length > 0 && (
                <div
                    className="detailStatsContainer"
                    style={{
                        top: `${detailStatsPosition.top + 16}px`,
                        width: `${detailStatsPosition.width - 80}px`
                    }}
                    ref={detailStatsRef}
                >
                    {detailStatsArr.map(el => (
                        <div key={el.id} className="detailStats">
                            <p>{el.title}</p>
                            {el.stat && <p>{el.stat}</p>}
                        </div>
                    ))}
                </div>
            )}
            {detailStatsPosition.height > 0 && (
                <div
                    style={{ height: `${detailStatsPosition.height + 16}px` }}
                ></div>
            )}
        </div>
    );
};

export default StatCard;

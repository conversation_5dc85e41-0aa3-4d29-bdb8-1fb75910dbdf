import React, { useContext, useEffect, useState } from "react";
import ReactQuill, { Quill } from "react-quill";
import htmlEditButton from "quill-html-edit-button";
import ResizeModule from "@botom/quill-resize-module";
import TurndownService from "turndown";
import { isEmpty } from "../../../../commons";
import { convert2HtmlEntities } from "../../../../commons/htmlEntities";
import uploadImage from "../../commons/components/EditNews/subComponents/CustomEditor/utils/uploadImage";
import { StoreContext } from "../../../../store/StoreProvider";
import ImageFormat from "../../commons/components/EditNews/subComponents/CustomEditor/utils/quillImage";
import Act from "../../../../store/actions";
import "../../websiteSetting.scss";

export const initToolbarOptions = {
    container: [
        [{ header: [1, 2, 3, 4, 5, 6, false] }],
        ["bold", "italic", "underline", "strike", "blockquote", "link"], // toggled buttons
        // [{ header: 1 }, { header: 2 }], // custom button values
        [{ list: "ordered" }, { list: "bullet" }],
        [{ script: "sub" }, { script: "super" }], // superscript/subscript

        // [{ size: ["small", false, "large", "huge"] }], // custom dropdown

        // [{ color: [] }, { background: [] }], // dropdown with defaults from theme
        // [{ font: [11, 12, 13] }],
        // [{ direction: "rtl" }, { align: [] }, { indent: "-1" }, { indent: "+1" }], // text direction/align/outdent/indent
        ["video"],
        ["clean"] // remove formatting button
    ]
};

// image新增class
const Image = Quill.import("formats/image");
Image.className = "img-fluid";
Quill.register(Image, true);
// 將p tag換成 div，為了上下兩張圖片中間無空隙
// const Block = Quill.import("blots/block");
// Block.className = "peakDiv";
// Block.tagName = "div";
// Quill.register(Block);

ReactQuill.Quill.register(ImageFormat, true);

// embedded ReactQuill 第三方套件
ReactQuill.Quill.register({
    "modules/htmlEditButton": htmlEditButton, // 顯示html樣式
    "modules/resize": ResizeModule // 調整圖片大小
});

const CustomQuill = props => {
    const { quillId, tmpRef, dropDown, language, option } = props;
    const [toolBar, setToolBar] = useState({});
    const [globalState] = useContext(StoreContext);
    const [state, dispatch] = useContext(StoreContext);

    const { websiteSubject } = globalState.websiteSetting;
    const {
        menuActiveItem,
        updatedData,
        selectOption
        // isEditedDisable
    } = state.websiteSetting;

    const [showDescription, setShowDescription] = useState("");
    const [editDescription, setEditDescription] = useState("");
    const turndownService = new TurndownService();

    useEffect(
        () => () => {
            setToolBar(initToolbarOptions);
        },
        []
    );

    useEffect(() => {
        const tmpToolBar = JSON.parse(JSON.stringify(initToolbarOptions));
        tmpToolBar.handlers = {
            image: () => uploadImage(tmpRef, websiteSubject, "peak")
        };
        setToolBar(tmpToolBar);
    }, []);

    // 讀出資料顯示
    useEffect(() => {
        if (selectOption && selectOption.length !== 0) {
            if (option.data) {
                // LinkingPage才有帶data property
                // Fix bug: 在更新selectOption後，dropDown帶過來的object還沒及時更新
                // 導致沒辦法抓到正確欄位名稱，解決方式是直接傳字串過來，也多帶一個property當作該頁判斷依據
                // message一開始會有錯，但後面會立即更新為正確的
                setShowDescription(option.message);
            } else if (language !== "" && option.column && !isEmpty(dropDown)) {
                // 有下拉選單，也有語系選擇
                if (menuActiveItem.key === "ManualPage") {
                    if (dropDown[selectOption][option.part]) {
                        setShowDescription(
                            dropDown[selectOption][option.part][language][
                                option.column
                            ]
                        );
                    }
                } else {
                    setShowDescription(
                        dropDown[selectOption][language][option.column]
                    );
                }
            } else if (option && !isEmpty(dropDown)) {
                // 有下拉選單，但是沒有語系選擇
                setShowDescription(dropDown[selectOption][option.column]);
            }
        } else if (option) {
            setShowDescription(option.message);
        }
    }, [selectOption, language, option]);

    useEffect(() => {
        setEditDescription(showDescription);
    }, [showDescription]);

    // 寫入資料
    useEffect(() => {
        const tmpAllData = JSON.parse(JSON.stringify(updatedData));
        const mdEditDescription = !isEmpty(editDescription)
            ? turndownService.turndown(editDescription)
            : "";
        if ((selectOption && selectOption !== "") || selectOption === null) {
            const tmpObj = tmpAllData.find(
                element => element.id === menuActiveItem.key
            );
            if (tmpObj) {
                if (language !== "" && selectOption !== null) {
                    // 給有下拉選單且有語系的欄位使用
                    if (menuActiveItem.key === "LinkingPage") {
                        const keys = Object.keys(tmpObj[selectOption]);
                        const targetKeyName = keys.find(
                            keyName =>
                                tmpObj[selectOption][keyName].priority ===
                                option.priority
                        );
                        if (targetKeyName) {
                            if (option.column === "title") {
                                tmpObj[selectOption][targetKeyName][language][
                                    option.column
                                ] = editDescription;
                            } else {
                                tmpObj[selectOption][targetKeyName][
                                    option.column
                                ] = editDescription;
                            }
                        }
                    } else if (menuActiveItem.key === "ManualPage") {
                        const keys = Object.keys(tmpObj[selectOption]);
                        const targetKeyName = keys.find(
                            keyName => keyName === option.part
                        );
                        if (targetKeyName) {
                            tmpObj[selectOption][targetKeyName][language][
                                option.column
                            ] = editDescription;
                        }
                    } else if (option.data) {
                        const keys = Object.keys(
                            tmpObj[selectOption][language]
                        );
                        const targetKeyName = keys.find(
                            keyName =>
                                tmpObj[selectOption][language][keyName]
                                    .priority === option.priority
                        );
                        if (targetKeyName) {
                            tmpObj[selectOption][language][targetKeyName][
                                option.column
                            ] = editDescription;
                        }
                    } else {
                        tmpObj[selectOption][language][option.column] =
                            option.column === "introduction"
                                ? mdEditDescription
                                : editDescription;
                    }
                } else if (language !== "" && option?.column) {
                    // 給沒有下拉選單且有語系的欄位使用
                    const keys = Object.keys(tmpObj[language]);
                    const targetKeyName = keys.find(
                        keyName =>
                            tmpObj[language][keyName].priority ===
                            option.priority
                    );
                    // DB有priority欄位
                    if (targetKeyName) {
                        tmpObj[language][targetKeyName][
                            option.column
                        ] = editDescription;
                    } else {
                        // DB沒有priority欄位
                        tmpObj[language][option.column] = editDescription;
                    }
                } else if (option?.column && selectOption !== null) {
                    // 給有下拉選單但是沒有語系的欄位使用
                    tmpObj[selectOption][option.column] = editDescription;
                } else {
                    // 給沒有下拉選單也沒有語系的欄位使用
                    const keys = Object.keys(tmpObj);
                    const targetKeyName = keys.find(
                        keyName => tmpObj[keyName].priority === option.priority
                    );
                    // DB有priority欄位
                    if (targetKeyName) {
                        tmpObj[targetKeyName][option.column] = editDescription;
                    }
                }
            }
        }

        dispatch({
            type: Act.SET_UPDATEDDATA,
            payload: tmpAllData
        });
    }, [editDescription, selectOption]);

    return (
        <div>
            {!isEmpty(toolBar) && (
                <ReactQuill
                    id={quillId}
                    theme="snow"
                    // value={convert2HtmlEntities(tmpValue)}
                    value={convert2HtmlEntities(editDescription)}
                    // onChange={onChangeFct}
                    onChange={setEditDescription}
                    modules={{
                        htmlEditButton: {
                            buttonHTML: "HTML",
                            prependSelector: `div#${quillId}`
                        },
                        toolbar: toolBar,
                        resize: {
                            locale: {},
                            toolbar: {
                                alignTools: false
                            }
                        }
                    }}
                    ref={tmpRef}
                    style={{ minHeight: "200px" }}
                />
            )}
        </div>
    );
};

export default CustomQuill;

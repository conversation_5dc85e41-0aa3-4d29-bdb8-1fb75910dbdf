import { useContext } from "react";
import { StoreContext } from "../../../store/StoreProvider";

// custom hook for useContext to avoid import "useContext" and "StoreContext" too many times in each component
function useCusContext() {
    const context = useContext(StoreContext);
    if (!context) {
        throw new Error("Please use context in context provider");
    }

    return context;
}

export default useCusContext;

import React, { useEffect } from "react";

// plugins
import { Segment, Divider } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";

// scss
import "./GroupInfo.scss";

// component
import textConfig from "../Utils/textConifg";
import { BtnName, segmentName, tableNames } from "../Utils/compoConfig";
import CustomPagination from "./subComponents/CustomPagination/CustomPagination";
import CustomTable from "./subComponents/CustomTable/CustomTable";
import GroupDetail from "./subComponents/GroupDetail/GroupDetail";
import GroupInfoButton from "./subComponents/CustomButton/GroupInfoButton";
import CustomModal from "./subComponents/CustomModal/CustomModal";
import accMngAct from "../../../../reduxStore/accManage/accManageAction";
import { getGroupData } from "../Utils/utils";

function GroupInfo() {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { isEditGroup } = state;

    useEffect(() => {
        getGroupData(dispatch);
    }, []);

    // init tableSelectPool
    const initTableSelectPool = () => {
        const tmpObj = {};
        Object.keys(tableNames).forEach(key => {
            tmpObj[key] = [];
        });
        dispatch({
            type: accMngAct.SET_TABLESELECTPOOL,
            payload: tmpObj
        });
    };

    useEffect(() => {
        initTableSelectPool();
    }, [isEditGroup]);

    const showMainArea = () => (
        <div className="GroupInfo">
            <Segment basic compact>
                <h2>{textConfig.GROUPINFO_TITLE}</h2>
            </Segment>
            <Divider />
            <div className="GroupInfo__TopArea">
                {BtnName.filter(
                    ({ segment }) => segment === segmentName.allGroup
                ).map(btnInfo => (
                    <GroupInfoButton
                        compoInfo={btnInfo}
                        key={btnInfo.typeName}
                    />
                ))}
            </div>
            <div className="GroupInfo__TableArea">
                <CustomTable />
            </div>
            <div className="GroupInfo__PaginationArea">
                <CustomPagination />
            </div>
        </div>
    );

    return (
        <React.Fragment>
            {!isEditGroup ? showMainArea() : <GroupDetail />}
            <CustomModal />
        </React.Fragment>
    );
}

export default GroupInfo;

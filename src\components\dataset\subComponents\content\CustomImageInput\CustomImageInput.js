import React, { useContext, useState, useMemo } from 'react';
import { useDispatch } from 'react-redux';

// ui
import { Image, Label, Icon } from 'semantic-ui-react';

import ImagePicker from './ImagePicker';

// store
import { StoreContext } from '../../../../../store/StoreProvider';
import Act from '../../../../../store/actions';
import noImage from '../../../../../images/No_image_200x200.png';
import uploadConfig from '../../../../toolPages/components/upload/uploadConfig';
import FileAct from '../../../../../reduxStore/file/fileAction';
import { getImgFolderPattern } from '../../../../common/imageCommon/FolderList/folderListHelper';
import getYouTubeId from './utils/getYouTubeId';

const noImgPlaceholderUrl = 'https://dummyimage.com/200x200/fff/000&text=No_image';

const CustomImageInput = ({
    rowId,
    cellId,
    idx = 0,
    defaultValue,
    createState,
    setCallback,
    isDiffValue = false,
}) => {
    const dispatchRedux = useDispatch();

    const [, dispatch] = useContext(StoreContext);
    const [open, setOpen] = useState(false);
    // const [inputValue, setInputValue] = useState(defaultValue);
    const [isHover, setIsHover] = useState({ zIndex: 'auto', scale: 1 });

    const style = {
        // display: "inline-block",
        // position: "absolute",
        zIndex: isHover.zIndex,
        transform: `scale(${isHover.scale})`,
        borderRadius: 'unset',
    };

    const setValue = (value) => {
        // keep input value when it changed
        const cellValue = {
            ...createState,
            ...{ [idx]: value },
        };

        // change input background color whsen value is diff
        if (value !== defaultValue) {
            // changed data
            dispatch({
                type: Act.DATA_CONTENT_ROW_CHANGED,
                payload: {
                    rowId,
                    cellId,
                    cellValue,
                },
            });
        } else {
            // dispatch
            dispatch({
                type: Act.DATA_CONTENT_ROW_NO_CHANGED,
                payload: {
                    rowId,
                    cellId,
                    idx,
                },
            });
        }
        setCallback(cellId, rowId, idx, cellValue);
    };

    const onValueChange = (newValue) => {
        setValue(newValue);
    };

    const handleAddClick = () => {
        // -1 為新增 item
        // FIXME: 原本 newState 以新 item {value: ""} 傳入，但不知為何 createState 的值沒有改變
        const newState = Object.assign({}, createState);
        newState[Object.keys(createState).length] = '';
        setCallback(cellId, rowId, -1, Object.assign({}, newState));
    };

    const handleCellClick = () => {
        dispatchRedux({
            type: FileAct.SET_DEFAULT_VALUE,
            payload: createState[idx] || '',
        });
        dispatchRedux({
            type: FileAct.SELECT_FILE,
            payload: '',
        });
        getImgFolderPattern(dispatchRedux, uploadConfig.ApiGetImages);
        setOpen(true);
    };

    const onMouseEnter = () => {
        // 沒有圖片，不需要做放大效果
        if (!createState[idx]) {
            return;
        }
        setIsHover({ zIndex: '100', scale: 10 });
    };
    const onMouseLeave = () => {
        setIsHover({ zIndex: 'auto', scale: 1 });
    };

    const getYouTubeThumb = (url, quality = 'hqdefault') => {
        const id = getYouTubeId(url);
        return id ? `https://i.ytimg.com/vi/${id}/${quality}.jpg` : '';
    };

    const rawValue = createState && createState[idx] ? createState[idx] : '';
    const ytId = getYouTubeId(rawValue);
    const imageSrc = ytId
        ? getYouTubeThumb(rawValue) || noImage || noImgPlaceholderUrl
        : rawValue || noImage || noImgPlaceholderUrl;

    let imageName = '';

    if (rawValue && rawValue.length > 0) {
        const lastIdx = rawValue.lastIndexOf('/');
        imageName = rawValue.slice(lastIdx + 1);

        // 因為從 ImagePicker 取得的 image 有帶 size: 600x600_filename.jpg
        // 需要把 600x600 拿掉
        const sizeMatch = imageName.match(/^(?<width>\d+)[xX×╳](?<height>\d+)_/);

        if (sizeMatch && sizeMatch.length === 3) {
            // 420x420_0001-2-1667697481055.jpg
            // Array(3) [ "420x420_", "420", "420" ]
            imageName = imageName.replace(sizeMatch[0], '');
        }
    }

    // imageURL_hasURL
    // Object { 0: "" }
    // console.log(cellId, createState);
    return useMemo(
        () => (
            <div>
                <Label image basic>
                    <Image
                        src={imageSrc}
                        size="tiny"
                        error={isDiffValue.toString()}
                        alt={imageName}
                        style={style}
                        onClick={handleCellClick}
                        onMouseEnter={onMouseEnter}
                        onMouseLeave={onMouseLeave}
                    />
                    {imageName}
                    <Label.Detail as={Icon} name="add" onClick={handleAddClick} />
                </Label>
                <ImagePicker
                    open={open}
                    setOpen={setOpen}
                    defaultValue={rawValue}
                    onValueChange={onValueChange}
                />
            </div>
        ),
        [cellId, createState, open, isHover, imageSrc, imageName, rawValue],
    );
};

export default CustomImageInput;

import React, { useContext, useEffect, useState } from "react";

// components
import LanguageSelect from "../../components/LanguageSelect";
import UpdateText from "../../components/UpdateText";
import SaveButton from "../../components/SaveButton";

// general
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import { isEmpty } from "../../../../commons";

function LiteralProducer() {
    const [language, setLanguage] = useState("zh");
    const [state, dispatch] = useContext(StoreContext);
    const { originData, menuActiveItem } = state.websiteSetting;
    const [dbObject, setDbObject] = useState({});

    useEffect(() => {
        // 沒有下拉選單
        dispatch({
            type: Act.SET_SELECTOPTION,
            payload: null
        });
    }, []);

    useEffect(() => {
        if (isEmpty(originData)) return;
        const tmpObj = originData.find(
            element => element.id === menuActiveItem.key
        )[language];
        setDbObject(tmpObj);
    }, [language, originData]);

    return (
        <div className="LiteralProducer">
            <div className="topArea">
                <h1>使用說明</h1>
                <LanguageSelect language={language} setLanguage={setLanguage} />
            </div>
            <div className="bottomArea">
                <div className="updateArea">
                    <UpdateText
                        language={language}
                        option={{
                            message: Object.values(dbObject)[0],
                            column: Object.keys(dbObject)[0],
                            priority: "No priority"
                        }}
                    />
                </div>
                <div className="btnArea">
                    <SaveButton language={language} />
                </div>
            </div>
        </div>
    );
}

export default LiteralProducer;

import React, { useEffect, useContext, useState, useCallback } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Grid, Segment, Container, Divider, But<PERSON> } from "semantic-ui-react";

// components
import FilePicker from "../upload/uploadImage/filePicker/FilePicker";
import FolderMenu from "../upload/uploadImage/folderMenu";
import CurrentFolder from "../upload/uploadImage/curFolder";

// css
import "../../../../Style/upload.scss";

// store
import { StoreContext } from "../../../../store/StoreProvider";
// helper
import { getFileFolderPattern } from "../../../common/imageCommon/FolderList/folderListHelper";
import Act from "../../../../store/actions";
import FileAct from "../../../../reduxStore/file/fileAction";
import {
    getFileServerUrl,
    getMainSubject
} from "../../../../api/firebase/cloudFirestore";
import { uuidv4 } from "../../../../commons/utility";
import uploadConfig from "../upload/uploadConfig";

const modulesSetting = {
    tabs: false,
    tabsContent: true
};

function FulltextUpload() {
    const dispatchRedux = useDispatch();
    const {
        files: { fileServerUrl, currentFolder, curFolderFiles, dropFolderFiles }
    } = useSelector(state => state);

    const [_, dispatch] = useContext(StoreContext);

    const [tab, setTab] = useState("folder"); // ENUM("folder", "upload")
    const [, setError] = useState(undefined);

    // get folder pattern
    useEffect(() => {
        getFileFolderPattern(dispatchRedux, uploadConfig.ApiGetFiles);
    }, [dispatchRedux]);

    // 取得 file-server url
    const handleGetFileServerUrl = useCallback(async () => {
        const fileserverUrl = await getFileServerUrl();
        if (!fileserverUrl.error) {
            // reset error if user reload
            setError(undefined);
            dispatchRedux({
                type: FileAct.SET_FILE_SERVER_URL,
                payload: fileserverUrl[0]
            });
        } else {
            setError(fileserverUrl.error);
        }
    }, [dispatch]);

    useEffect(() => {
        if (!fileServerUrl) {
            handleGetFileServerUrl();
        }
    }, [handleGetFileServerUrl]);

    const tabs = {
        folder: {
            name: "folder",
            label: "目前資料夾",
            show: true,
            onClick: () => setTab("folder"),
            leftComp: <FolderMenu />,
            rightComp: <CurrentFolder type={uploadConfig.file} /> // 將 FilePickerModal 嵌入在 CurrentFolder 的 Gallery 中
        },
        upload: {
            name: "upload",
            label: "上傳全文檔案",
            show: false, // 關閉 upload, 將 FilePickerModal 嵌入在 CurrentFolder 的 Gallery 中
            onClick: () => setTab("upload"),
            leftComp: <FolderMenu />,
            rightComp: <FilePicker type={uploadConfig.file} />
        }
    };

    useEffect(() => {
        // currentFolder 切換時, 要先清空 curFolderFiles.checked, dropFolderFiles.checked
        dispatchRedux({
            type: FileAct.CUR_FOLDER_FILES_STATUS,
            payload: {
                ...curFolderFiles,
                checked: []
            }
        });
        dispatchRedux({
            type: FileAct.DROP_FOLDER_FILES_STATUS,
            payload: {
                ...dropFolderFiles,
                checked: []
            }
        });
    }, [currentFolder]);

    useEffect(
        // 離開此頁面時, initialize global state
        () => () => {
            dispatchRedux({
                type: FileAct.UPLOAD_CLEAR_CACHE
            });
        },
        []
    );

    return (
        <Container>
            <Segment basic compact>
                <h2>全文批次上傳</h2>
            </Segment>
            <Grid textAlign="center" celled stackable>
                {/* upload image */}
                <Grid.Row>
                    {/* left side */}
                    <Grid.Column width={3}>
                        <Segment>
                            <h3>選擇資料夾</h3>
                        </Segment>
                        <Segment basic compact>
                            {tab in tabs && tabs[tab].leftComp}
                        </Segment>
                    </Grid.Column>

                    {/* right side */}
                    <Grid.Column width={13}>
                        {modulesSetting.tabs && (
                            <Button.Group>
                                {Object.values(tabs)
                                    .filter(t => t.show)
                                    .map((btn, idx) => (
                                        <React.Fragment key={uuidv4()}>
                                            <Button
                                                key={uuidv4()}
                                                positive={tab === btn.name}
                                                onClick={() => btn.onClick()}
                                            >
                                                {btn.label}
                                            </Button>
                                            {idx <
                                                Object.values(tabs).length -
                                                    1 && <Button.Or />}
                                        </React.Fragment>
                                    ))}
                            </Button.Group>
                        )}
                        {modulesSetting.tabs && modulesSetting.tabsContent && (
                            <Divider />
                        )}

                        <Grid textAlign="center" celled stackable>
                            <Grid.Row>
                                <Grid.Column>
                                    {tab in tabs && tabs[tab].rightComp}
                                </Grid.Column>
                            </Grid.Row>
                        </Grid>
                    </Grid.Column>
                </Grid.Row>
            </Grid>
        </Container>
    );
}

export default FulltextUpload;

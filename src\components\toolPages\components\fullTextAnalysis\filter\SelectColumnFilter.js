// This is a custom filter UI for selecting
// a unique option from a list
import React from "react";
import { Dropdown } from "semantic-ui-react";

// todo: 此功能尚未完成
function SelectColumnFilter({
    column: { filterValue, setFilter, preFilteredRows, id }
}) {
    // Calculate the options for filtering
    // using the preFilteredRows
    const options = React.useMemo(() => {
        // eslint-disable-next-line no-shadow
        const options = new Set();
        preFilteredRows.forEach(row => {
            options.add({
                key: row.values[id],
                value: row.values[id],
                label: row.values[id]
            });
        });
        return [...options.values()];
    }, [id, preFilteredRows]);

    const handleClick = (event, { value }) => {
        // update
        setFilter(value || undefined);
    };

    return (
        <Dropdown
            fluid
            search
            selection
            floating
            value={filterValue}
            options={options}
            onChange={handleClick}
            placeholder="資料集"
        />
    );
}

export default SelectColumnFilter;

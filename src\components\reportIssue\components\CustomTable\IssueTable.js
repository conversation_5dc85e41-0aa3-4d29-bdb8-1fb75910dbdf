import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";

// semantic ui
import { Table } from "semantic-ui-react";

// components
import StatusDropdown from "../CustomDropdown/StatusDropdown";
import CustomTextArea from "../CustomTextArea";
import HistoryBtn from "../CustomButton/HistoryBtn";
import DescField from "../OtherCompo/DescField";
import CustomPagination from "../../../common/CustomPagination/CustomPagination";

// config
import textConfig from "../../common/textConfig";
import { colConfig } from "../../common/colConfig";
import { statusKey } from "../../common/statusConfig";
import fbConfig from "../../common/fbConfig";

/**
 * text: 顯示名稱
 * key: 對應firebase欄位名稱
 * component(optional): 欄位對應的UI component，沒有需要特別帶，就用資料基本型別顯示
 * uiSetting(optional): {}, semantic ui Table.Cell property
 */
const headerName = [
    {
        text: "日期",
        key: colConfig[fbConfig.issueID],
        uiSetting: { collapsing: true }
    },
    {
        text: "E-mail",
        key: colConfig.email,
        uiSetting: { collapsing: true, singleLine: true }
    },
    {
        text: textConfig.label.desc,
        key: colConfig.desc,
        component: DescField
    },
    {
        text: "處理狀態",
        key: colConfig.status,
        component: StatusDropdown,
        uiSetting: { collapsing: true }
    },
    {
        text: "處理摘要",
        key: colConfig.response,
        component: CustomTextArea
    },
    {
        text: "處理者",
        key: colConfig.staff,
        uiSetting: { collapsing: true }
    },
    {
        text: "歷史紀錄",
        key: colConfig.history,
        component: HistoryBtn,
        uiSetting: { collapsing: true, textAlign: "center" }
    }
];

function IssueTable() {
    const { allData, tabStatus } = useSelector(state => state.report);

    const [tbData, setTbData] = useState([]);

    // page control
    const [curPage, setCurPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [perPageNum, setPerPageNum] = useState(5);

    useEffect(() => {
        const newData = [];
        allData.forEach(el => {
            const tmpObj = {};
            Object.keys(colConfig).forEach(colKey => {
                tmpObj[colKey] = Object.hasOwn(el, colKey) ? el[colKey] : "";
            });

            tmpObj.id = el.id;
            newData.push(tmpObj);
        });
        setTbData(newData);
    }, [allData]);

    useEffect(() => {
        setTotalPages(Math.ceil(tbData.length / perPageNum));
    }, [tbData, perPageNum]);

    // 回傳component的table根據不同的欄位，帶不一樣的元件參數
    const getCellComponent = (colKey, Component, data) => {
        switch (colKey) {
            case colConfig.history:
            case colConfig.status:
            case colConfig.response:
                return <Component fsID={data.id} />;
            case colConfig.desc:
                return <Component data={data} />;
            default:
                return <Component />;
        }
    };

    // 回傳無字串資料的default value
    const getDefaultVal = colKey => {
        switch (colKey) {
            case colConfig.staff:
                return "暫無人處理";
            default:
                return "";
        }
    };

    const getTbBodyCell = (findColObj, key, data) => {
        const { collapsing, singleLine, textAlign } =
            findColObj?.uiSetting || {};

        const tmpIssueID = data?.issueID?.split(" ")[0];
        const tmpData = { ...data, issueID: tmpIssueID };
        return (
            <Table.Cell
                collapsing={collapsing}
                singleLine={singleLine}
                textAlign={textAlign}
                key={`${data.issueID}-${key}`}
            >
                {findColObj?.component
                    ? getCellComponent(key, findColObj.component, tmpData)
                    : tmpData[key] || getDefaultVal(key)}
            </Table.Cell>
        );
    };

    const handlePage = (evt, { activePage }) => {
        setCurPage(activePage);
    };

    const handleDDPage = (evt, data) => {
        setCurPage(data.value);
    };

    const handlePerPageNum = (evt, data) => {
        setCurPage(1);
        setPerPageNum(data.value);
    };

    const customDateSort = datesArray => {
        const parseDateString = dateString => {
            const [datePart, timePart] = dateString.split(" ");
            const [year, month, day] = datePart.split("/").map(Number);

            const [ampm, time] = timePart
                .split(/(\d+:\d+:\d+)/)
                .filter(Boolean);

            // eslint-disable-next-line prefer-const
            let [hour, minute, second] = time.split(":").map(Number);
            if (ampm === "下午" && hour < 12) {
                hour += 12;
            }

            return new Date(year, month - 1, day, hour, minute, second);
        };
        const compareDates = (dateObj1, dateObj2) => {
            const dateString1 = dateObj1.issueID;
            const dateString2 = dateObj2.issueID;

            const dateObject1 = parseDateString(dateString1);
            const dateObject2 = parseDateString(dateString2);

            return dateObject2 - dateObject1;
        };

        return datesArray.sort(compareDates);
    };

    return (
        <>
            <Table celled padded>
                <Table.Header>
                    <Table.Row>
                        {headerName.map(el => (
                            <Table.HeaderCell key={el.key} singleLine>
                                {el.text}
                            </Table.HeaderCell>
                        ))}
                    </Table.Row>
                </Table.Header>

                <Table.Body>
                    {customDateSort(tbData)
                        // .sort((cur, next) => {
                        //     const id1 =
                        //         cur?.[fbConfig.issueID]?.substring(2) || "0";
                        //     const id2 =
                        //         next?.[fbConfig.issueID].substring(2) || "0";
                        //     return parseInt(id2, 10) - parseInt(id1, 10);
                        // })
                        .filter(({ status }) => {
                            if (tabStatus === statusKey.all) return true;
                            return status === tabStatus;
                        })
                        .slice((curPage - 1) * perPageNum, curPage * perPageNum)
                        .map(data => (
                            <Table.Row key={data.issueID}>
                                {Object.keys(data)
                                    .filter(key => key !== fbConfig.id)
                                    .map(key => {
                                        const findColObj = headerName.find(
                                            el => el.key === key
                                        );
                                        if (findColObj) {
                                            return getTbBodyCell(
                                                findColObj,
                                                key,
                                                data
                                            );
                                        }

                                        return (
                                            <Table.Cell
                                                key={`${data.issueID}-${key}`}
                                            />
                                        );
                                    })}
                            </Table.Row>
                        ))}
                </Table.Body>
            </Table>
            <CustomPagination
                currentPage={curPage}
                totalPages={totalPages}
                handlePage={handlePage}
                handlePerPageNum={handlePerPageNum}
                handleDDPage={handleDDPage}
            />
        </>
    );
}

export default IssueTable;

import React, { useContext, useState, useEffect, useCallback } from "react";

// ui
import { Message } from "semantic-ui-react";
import {StoreContext} from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

// store

/* message example
    message = {
        title: 'Create',
        success: 0,
        error: 0,
        // This renderSignal has been monitor in TopContent.js at "3. get content useEffect"
        renderSignal: `create-${new Date().getTime()}`
    };
 */

const AlertMessage = () => {
    // console.log('I am AlertMessage');

    const [open, setOpen] = useState(false);

    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);

    // get data from dataReducer
    const { data } = state;

    // get message
    const { infoMessage } = data;
    const { title, success, error } = infoMessage;

    const newMessage = `${title || "unKnown"}, Success: ${success ||
        "0"}, Error: ${error || "0"}`;

    const handleAlert = useCallback(() => {
        if (title) {
            // show message
            setOpen(true);
            // disable message after 3 seconds
            setTimeout(() => {
                // close
                setOpen(false);
                // clean message
                dispatch({ type: Act.DATA_INFO_MESSAGE_CLEAN });
            }, 3 * 1000);
        } else {
            // disable message
            setOpen(false);
        }
    }, [dispatch, error, title]);

    useEffect(() => {
        handleAlert();
    }, [handleAlert]);

    return open ? (
        <Message
            color={infoMessage?.error ? "red" : "green"}
            size="tiny"
            header={newMessage}
            style={{ textAlign: "center", width: "100%" }}
            onDismiss={() => setOpen(false)}
        />
    ) : null;
};

export default AlertMessage;

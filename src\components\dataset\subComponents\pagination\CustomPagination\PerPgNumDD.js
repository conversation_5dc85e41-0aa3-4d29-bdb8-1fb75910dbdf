import React, { useContext, useEffect, useState } from "react";

// plugins
import { Dropdown } from "semantic-ui-react";

//
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";
import { isEmpty } from "../../../../../commons";

function PerPgNumDd() {
    const [, dispatch] = useContext(StoreContext);
    // const { pageNum } = state.pagination;
    const [options, setOptions] = useState([]);

    useEffect(() => {
        const pageOption = [5, 10, 15];
        const tmp = pageOption.map(value => ({
            key: value,
            text: value,
            value
        }));
        setOptions(tmp);
        dispatch({
            type: Act.DATA_PAGINATION_PAGENUM,
            payload: pageOption[1]
        });
    }, []);

    const handleChange = (evt, data) => {
        dispatch({
            type: Act.DATA_PAGINATION_PAGENUM,
            payload: data.value
        });
        dispatch({
            type: Act.DATA_PAGINATION_ACTIVE_PAGE_INIT
        });
    };

    return (
        <React.Fragment>
            <div style={{ marginRight: "0.5rem" }}>
                <p>選擇單頁顯示數量</p>
            </div>
            {!isEmpty(options) && (
                <Dropdown
                    compact
                    selection
                    options={options}
                    onChange={handleChange}
                    defaultValue={options[1].value}
                />
            )}
        </React.Fragment>
    );
}

export default PerPgNumDd;

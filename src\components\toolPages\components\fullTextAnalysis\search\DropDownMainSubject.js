import React, { useContext, useEffect, useState } from "react";
//
import { useDispatch, useSelector } from "react-redux";

// ui
import { Dropdown, Loader } from "semantic-ui-react";

// cloud
import { getMainSubject } from "../../../../../api/firebase/cloudFirestore";
//
import FltAnaAct from "../../../../../reduxStore/fltAnalysis/FltAnaAct";
import { filterDataSet } from "../../../../../commons/filterGroup";
import { StoreContext } from "../../../../../store/StoreProvider";

const DisableMS = "0";
const MUST_EXCLUDE_MS = ["authority"];

const DropDownMainSubject = () => {
    const [stateContext, _] = useContext(StoreContext);
    const { groupInfo } = stateContext.data;
    const { user } = stateContext;
    const dispatch = useDispatch();
    const { fltAna } = useSelector(state => state);
    const { mainSubject } = fltAna;
    const { value: keepMainSubjectValue } = mainSubject.selected;

    const [MSList, setMSList] = useState(undefined);
    const [error, setError] = useState(undefined);

    const handleGetMainSubject = async () => {
        const mainSubjectData = await getMainSubject();
        if (!mainSubjectData.error) {
            setMSList(
                Object.values(mainSubjectData)
                    .filter(ms => filterDataSet(ms, groupInfo))
                    // filter out ms in MUST_EXCLUDE_MS
                    .filter(ms => !MUST_EXCLUDE_MS.includes(ms.id))
                    .filter(ms => ms.enable !== DisableMS)
                    .map(item => ({
                        // react key
                        key: item.id,
                        // onChange event return value
                        value: item.label,
                        // ui show text
                        text: `${item.label}(${item.id})`,
                        // database  for sparql query
                        dataset: item.id,
                        // for ordering
                        seq: item.seq
                    }))
                    .sort((itemA, itemB) => itemA.seq - itemB.seq)
            );
        } else {
            setError(mainSubjectData.error);
        }
    };

    useEffect(() => {
        handleGetMainSubject();
    }, [groupInfo]);

    const handleClick = (event, { value }) => {
        // get selected item
        const item = MSList.filter(o => o.value === value)[0];
        // update
        if (item) {
            dispatch({
                type: FltAnaAct.setMainSubject,
                payload: item
            });
        }
        // 清空 table 資料
        dispatch({
            type: FltAnaAct.fetchSignalBasic
        });
        // 先清空訊息
        dispatch({
            type: FltAnaAct.setMessage,
            payload: null
        });

        // 回到第一頁?

        //
    };

    // eslint-disable-next-line no-nested-ternary
    return MSList ? (
        <Dropdown
            fluid
            search
            selection
            value={keepMainSubjectValue}
            options={MSList}
            onChange={handleClick}
            placeholder="資料集"
        />
    ) : error ? (
        <span>{error}</span>
    ) : (
        <Loader active inline="centered" />
    );
};

export default DropDownMainSubject;

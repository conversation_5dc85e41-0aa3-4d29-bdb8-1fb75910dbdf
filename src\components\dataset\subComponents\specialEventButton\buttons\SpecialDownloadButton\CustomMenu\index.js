import React, { useContext, useMemo, useEffect } from "react";

// ui
import { Menu } from "semantic-ui-react";
import { StoreContext } from "../../../../../../../store/StoreProvider";

const CustomMenu = ({
    curMenu,
    setCurMenu
    // tabSelection,
    // setTabSelection
}) => {
    // console.log("hasTab", hasTab);
    const [state] = useContext(StoreContext);
    const { sheet } = state.data;
    const { key: sheetName, value: sheetLabel, hasTab } = sheet.selected;
    const { tabClass } = sheet.tabKey;

    useEffect(() => {
        if (hasTab) {
            setCurMenu(tabClass);
        }
    }, []);

    // const handleClick = type => {
    //     setCurMenu(type);
    // };

    return useMemo(
        () => (
            <Menu
                secondary
                fluid
                borderless
                widths={hasTab ? hasTab.length : 1}
            >
                {hasTab ? (
                    hasTab.map(menuItem => (
                        <Menu.Item
                            key={`Custom-${menuItem.tabClass}`}
                            name={menuItem.label}
                            active={curMenu === menuItem.tabClass}
                            // onClick={() => handleClick(menuItem.tabClass)}
                        />
                    ))
                ) : (
                    <Menu.Item
                        key={`Custom-${sheetName}`}
                        name={sheetLabel}
                        active
                    />
                )}
            </Menu>
        ),
        [hasTab, curMenu]
    );
};
export default CustomMenu;

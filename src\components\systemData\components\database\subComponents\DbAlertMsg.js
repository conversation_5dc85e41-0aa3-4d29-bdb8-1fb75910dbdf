import React, { useContext } from "react";
import AlertMsg from "../../../../common/AlertMsg";
import { StoreContext } from "../../../../../store/StoreProvider";

const DbAlertMsg = () => {
    const [state] = useContext(StoreContext);
    const { database } = state;
    const { databaseMsg } = database;

    return (
        <div>
            {databaseMsg.title && databaseMsg.title.length > 0 && (
                <AlertMsg message={databaseMsg} />
            )}
        </div>
    );
};

export default DbAlertMsg;

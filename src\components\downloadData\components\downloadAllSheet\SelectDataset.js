import React, { useContext, useEffect, useState } from "react";

// ui
import { Dropdown, Loader } from "semantic-ui-react";
import CustomDownloadAllSheets from "./CustomDownloadAllSheets";

// cloud
import { getMainSubject } from "../../../../api/firebase/cloudFirestore";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import { filterDataSet } from "../../../../commons/filterGroup";

const SelectDataset = () => {
    const [state, dispatch] = useContext(StoreContext);
    const { displayName } = state.user;
    const { mainSubject, groupInfo } = state.data;
    const { dataset, value: datasetName } = mainSubject.selected;

    const [MSList, setMSList] = useState(undefined);
    const [error, setError] = useState(undefined);

    const handleGetMainSubject = async () => {
        const mainSubjectData = await getMainSubject();
        if (!mainSubjectData.error) {
            setMSList(
                Object.values(mainSubjectData)
                    .filter(ms => filterDataSet(ms, groupInfo))
                    .map(item => ({
                        // react key
                        key: item.id,
                        // onChange event return value
                        value: item.label,
                        // ui show text
                        text: item.label,
                        // database  for sparql query
                        dataset: item.id,
                        // for ordering
                        seq: item.seq
                    }))
                    .sort((itemA, itemB) => itemA.seq - itemB.seq)
            );
        } else {
            setError(mainSubjectData.error);
        }
    };

    useEffect(() => {
        handleGetMainSubject();
    }, [groupInfo]);

    const handleClick = (event, { value }) => {
        // get selected item
        const item = MSList.filter(ms => ms.value === value)[0];
        // update
        if (item) {
            dispatch({
                type: Act.DATA_MAINSUBJECT,
                payload: item
            });
        }
    };

    if (error) {
        return <span>{error}</span>;
    }
    return MSList ? (
        <React.Fragment>
            <Dropdown
                fluid
                search
                selection
                value={datasetName}
                options={MSList}
                onChange={handleClick}
                placeholder="資料集"
            />
            {datasetName && (
                <CustomDownloadAllSheets
                    limit={-1}
                    offset={0}
                    dataset={dataset}
                    datasetName={datasetName}
                    displayName={displayName}
                    groupInfo={groupInfo}
                />
            )}
        </React.Fragment>
    ) : (
        <Loader active inline="centered" />
    );
};

export default SelectDataset;

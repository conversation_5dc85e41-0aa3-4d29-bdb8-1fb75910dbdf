import React, { useState, useEffect, useCallback } from 'react';
import { useDispatch } from 'react-redux';
import { Form, Input, Button, Segment, Image as SUIImage, Message, Icon } from 'semantic-ui-react';
import CurrentImage from './CurrentImage';
import FileAct from '../../../../../reduxStore/file/fileAction';
import getYouTubeId from './utils/getYouTubeId';

const UploadLink = ({ defaultValue, currentValue }) => {
    const dispatchRedux = useDispatch();

    const [linkUrl, setLinkUrl] = useState('');
    const [checkedUrl, setCheckedUrl] = useState('');
    const [previewKind, setPreviewKind] = useState(null);
    const [previewSrc, setPreviewSrc] = useState('');
    const [isChecking, setIsChecking] = useState(false);

    const isLikelyUrl = (s) => {
        if (typeof s !== 'string') return false;
        const v = s.trim();
        if (!/^https?:\/\//i.test(v)) return false;
        try {
            const u = new URL(v);
            return !!u;
        } catch {
            return false;
        }
    };

    const probeImage = (url, timeoutMs = 6000) =>
        new Promise((resolve) => {
            const img = new Image();
            let done = false;
            let timer;

            const cleanup = (ok) => {
                if (done) return;
                done = true;
                clearTimeout(timer);
                img.onload = null;
                img.onerror = null;
                img.src = '';
                resolve(ok);
            };

            timer = setTimeout(() => cleanup(false), timeoutMs);

            img.onload = () => cleanup(true);
            img.onerror = () => cleanup(false);
            img.referrerPolicy = 'no-referrer';
            img.src = url;
        });

    const handleConfirm = useCallback(
        (e) => {
            if (e?.preventDefault) e.preventDefault();
            const url = (linkUrl || '').trim();
            setCheckedUrl(url);
        },
        [linkUrl],
    );

    const handleApplyLink = useCallback(() => {
        if (!checkedUrl) return;
        dispatchRedux({ type: FileAct.SELECT_FILE, payload: checkedUrl });
    }, [checkedUrl]);

    const handleClearInput = () => {
        setLinkUrl('');
        setCheckedUrl('');
        setPreviewKind(null);
        setPreviewSrc('');
        setIsChecking(false);
    };

    const handleClearApplied = () => {
        dispatchRedux({ type: FileAct.SELECT_FILE, payload: '' });
    };

    useEffect(() => {
        let alive = true;

        const checkPreview = async () => {
            setIsChecking(true);
            setPreviewKind(null);

            const url = (checkedUrl || '').trim();

            if (!url) {
                if (!alive) return;
                setPreviewKind(null);
                setPreviewSrc('');
                setIsChecking(false);
                return;
            }

            const yt = getYouTubeId(url);
            if (yt) {
                if (!alive) return;
                setPreviewKind('youtube');
                setPreviewSrc(yt);
                setIsChecking(false);
                return;
            }

            if (!isLikelyUrl(url)) {
                if (!alive) return;
                setPreviewKind('unknown');
                setPreviewSrc(url);
                setIsChecking(false);
                return;
            }

            const isImage = await probeImage(url);
            if (!alive) return;

            setPreviewKind(isImage ? 'image' : 'unknown');
            setPreviewSrc(url);
            setIsChecking(false);
        };

        checkPreview();

        return () => {
            alive = false;
        };
    }, [checkedUrl]);

    const renderPreview = () => {
        if (!checkedUrl) return null;

        if (isChecking) {
            return <Message info content="正在檢查連結可否預覽…" />;
        }

        if (previewKind === 'youtube') {
            const thumbUrl = `https://i.ytimg.com/vi/${previewSrc}/hqdefault.jpg`;
            return (
                <SUIImage
                    src={thumbUrl}
                    bordered
                    rounded
                    alt="YouTube 影片封面"
                    referrerPolicy="no-referrer"
                    onError={(e) => {
                        e.currentTarget.onerror = null;
                        e.currentTarget.src = `https://i.ytimg.com/vi/${previewSrc}/mqdefault.jpg`;
                    }}
                    style={{
                        height: '300px',
                        width: 'auto',
                        objectFit: 'cover',
                        maxWidth: '100%',
                    }}
                />
            );
        }

        if (previewKind === 'image') {
            return (
                <SUIImage
                    src={previewSrc}
                    bordered
                    rounded
                    onError={(e) => {
                        e.currentTarget.style.display = 'none';
                        setPreviewKind('unknown');
                    }}
                    style={{ height: '300px', width: 'auto', objectFit: 'cover', maxWidth: '100%' }}
                />
            );
        }

        return (
            <Message
                info
                content="無法判斷此連結是否為可直接預覽的圖片或影片，你仍可套用連結。若需預覽，請提供可直接讀取的圖片或影片直連（避免需登入或權限的網址）。"
            />
        );
    };

    return (
        <>
            <Form onSubmit={handleConfirm}>
                <Form.Field>
                    <p>檔案連結</p>
                    <Input action>
                        <input
                            value={linkUrl}
                            onChange={(e, data) => setLinkUrl(data?.value ?? e.target.value)}
                            placeholder="輸入連結..."
                        />
                        <Button
                            type="button"
                            icon
                            loading={isChecking}
                            disabled={!linkUrl.trim() || isChecking}
                            aria-label="清除連結"
                            onClick={handleClearInput}
                            title="清除連結"
                        >
                            <Icon name="close" />
                        </Button>

                        <Button
                            type="submit"
                            icon
                            loading={isChecking}
                            disabled={!linkUrl.trim() || isChecking}
                            aria-label="確認預覽"
                            title="確認預覽"
                        >
                            <Icon name="search" />
                        </Button>
                    </Input>
                </Form.Field>
            </Form>

            {checkedUrl && (
                <Segment>
                    <div style={{ marginBottom: 8, fontWeight: 600 }}>連結預覽</div>
                    {renderPreview()}
                </Segment>
            )}

            <div style={{ display: 'flex', justifyContent: 'end', gap: '4px', marginTop: '14px' }}>
                <Button
                    primary
                    type="button"
                    onClick={handleApplyLink}
                    disabled={!checkedUrl || isChecking}
                >
                    套用連結
                </Button>
                <Button
                    type="button"
                    onClick={handleClearApplied}
                    disabled={!currentValue || isChecking}
                >
                    清除套用
                </Button>
            </div>

            <CurrentImage defaultValue={defaultValue} currentValue={currentValue} />
        </>
    );
};

export default UploadLink;

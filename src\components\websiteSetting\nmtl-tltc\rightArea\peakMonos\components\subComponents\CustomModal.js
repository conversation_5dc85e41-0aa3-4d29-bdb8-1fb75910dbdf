import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "semantic-ui-react";
import { useSelector } from "react-redux";
import "../EditCate.scss";

const CustomModal = ({ onClick, message }) => {
    const { isModalOpen, isDelModalOpen } = useSelector(state => state);
    return (
        <Modal
            onClose={onClick}
            onOpen={onClick}
            open={message === "儲存成功" ? isModalOpen : isDelModalOpen}
        >
            <Modal.Content image>
                <Modal.Description>
                    <Header>{message}</Header>
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={onClick} positive>
                    確認
                </Button>
            </Modal.Actions>
        </Modal>
    );
};
export default CustomModal;

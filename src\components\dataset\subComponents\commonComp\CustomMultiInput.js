import React, { useContext, useMemo, useState } from "react";

// ui
import { Form, TextArea } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

const CustomMultiInput = ({
    rowId,
    cellId,
    defaultValue,
    createState,
    setCallback
}) => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);

    // set true when input and default vale are diff
    const [isDiffValue, setDiffValue] = useState(false);

    // change input value
    const handleInputChange = (event, { value }) => {
        // keep input value when it changed
        setCallback(cellId, rowId, { value });

        // change input background color when value is diff
        if (value !== defaultValue) {
            // update diff state
            setDiffValue(true);
            // dispatch
            dispatch({
                type: Act.DATA_CONTENT_ROW_CHANGED,
                payload: {
                    rowId,
                    cellId,
                    cellData: value
                }
            });
        }

        if (value === defaultValue) {
            setDiffValue(false);

            dispatch({
                type: Act.DATA_CONTENT_ROW_NO_CHANGED,
                payload: {
                    rowId,
                    cellId
                }
            });
        }
    };

    return useMemo(
        () => (
            <Form>
                <TextArea
                    rows={1}
                    value={createState.value ? createState.value : undefined}
                    onChange={handleInputChange}
                    style={{
                        padding: "0",
                        border: "0px #f9fafb",
                        color: isDiffValue ? "#9f3a38" : "#000"
                    }}
                />
            </Form>
        ),
        [cellId, createState.value]
    );
};

export default CustomMultiInput;

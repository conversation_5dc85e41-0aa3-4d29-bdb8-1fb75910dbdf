import React, { useContext, useState, useMemo, useEffect } from "react";

// store
import { createFilter } from "react-select";
import CreatableSelect from "react-select/creatable";
import axios from "axios";
import { StoreContext } from "../../../../../../../store/StoreProvider";
import { createNmtlData, readNmtlData } from "../../../../../../../api/nmtl";
import Api from "../../../../../../../api/nmtl/Api";
import Act from "../../../../../../../store/actions";
import { isEmpty } from "../../../../../../../commons";
import {
    getReservedNewId,
    specialConvertToOption
} from "../../../../../../common/sheetCrud/utils";
import getKeyBySingle from "../../../../../../../api/nmtl/ApiKey";
import getSingleByApi from "../../../../../../../api/nmtl/ApiSingle";
import { isCorrectSuffix } from "../../../../../../../api/nmtl/ApiField";
import { MAX_OPTION } from "../../../../../../common/sheetCrud/sheetCrudHelper";
import { findSameIdList, updateObjectValue } from "../../../helper";
import ExistedModal from "../../../../commonComp/specialTableComp/ExistedModal";

// 關閉偵測同一id的彈跳視窗
const showExistedModal = true;
// only for accordion hasAuthor and hasTranslator
const CustomSingleDropdownForAcc = ({
    itemAt,
    cellId,
    // defaultValue,
    createState,
    setCallback,
    linkToHeader,
    // isCreateNew = false,
    menuName,
    cloneLocalCreateState,
    setCloneLocalCreateStateFct,
    rValue,
    isInDraggingMode,
    curPage
}) => {
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { sheet, mainSubject } = state.data;
    const { dataset } = mainSubject.selected;
    const { key: sheetName } = sheet.selected;
    const { headerFields } = sheet;
    const [inputValue, setInputValue] = useState(null);
    const [option, setOption] = useState(null);
    const [equalValue, setEqualValue] = useState(null);
    const [open, setOpen] = useState(false);

    const getOption = async ids => {
        const res = await readNmtlData(
            Api.getOtherNameList.replace("{ds}", dataset).replace("{ids}", ids)
        );

        const opt = res.data.map(el => el.label);

        if (Object.hasOwn(cloneLocalCreateState, linkToHeader)) {
            const nameList = cloneLocalCreateState[linkToHeader];

            const zhValue =
                nameList
                    .find(el => el?.endsWith("@zh"))
                    ?.slice(0, -3)
                    ?.split("、") || null;
            const enValue =
                nameList
                    .find(el => el?.endsWith("@en"))
                    ?.slice(0, -3)
                    ?.split("、") || null;

            const filterZhName = zhValue
                ? zhValue.filter(el => opt.indexOf(el) === -1)
                : null;
            const filterEnName = enValue
                ? enValue.filter(el => opt.indexOf(el) === -1)
                : null;

            const zhData = !isEmpty(filterZhName)
                ? `${filterZhName.join("、")}@zh`
                : null;
            const enData = !isEmpty(filterEnName)
                ? `${filterEnName.join("、")}@en`
                : null;

            setCallback(linkToHeader, [zhData, enData], menuName);
            const updateData = updateObjectValue(
                cloneLocalCreateState,
                linkToHeader,
                [zhData, enData]
            );

            setCloneLocalCreateStateFct(updateData);
        }
    };

    const handleChange = selectValue => {
        const cellValue = isEmpty(selectValue) ? null : selectValue[0];

        const list =
            cloneLocalCreateState && cloneLocalCreateState[cellId]
                ? cloneLocalCreateState[cellId]
                : [];
        // 刪除資料時接著刪除對應的作者名稱 || 譯者名稱
        if (
            Object.hasOwn(list, itemAt) &&
            !isEmpty(list[itemAt]) &&
            !cellValue
        ) {
            dispatch({
                type:
                    cellId === "hasAuthor"
                        ? Act.DATA_IS_FILL_ALL_AUTHOR_OTHERNAME
                        : Act.DATA_IS_FILL_ALL_TRANSLATOR_OTHERNAME,
                payload: false
            });

            getOption(list[itemAt]);
        }

        list[itemAt] = cellValue ? cellValue.id : null;

        // setCallback(cellId, list, menuName);

        if (list.includes(null)) {
            dispatch({
                type:
                    cellId === "hasAuthor"
                        ? Act.DATA_IS_FILL_ALL_AUTHOR_OTHERNAME
                        : Act.DATA_IS_FILL_ALL_TRANSLATOR_OTHERNAME,
                payload: false
            });

            return;
        }

        const updateData = updateObjectValue(
            cloneLocalCreateState,
            cellId,
            list
        );

        dispatch({
            type: Act.DATA_SET_CLONE_CREATE_STATE,
            payload: updateData
        });

        setCloneLocalCreateStateFct(updateData);
    };

    const handleInputChange = value => {
        if (!value) {
            return;
        }
        setInputValue(value);
    };
    const newOptions = useMemo(() => {
        if (!headerFields) {
            return [];
        }

        return specialConvertToOption(cellId, headerFields) || [];
    }, [cellId, headerFields]);

    const getCreateNmtlItemResult = async (item, newClass, newApi) => {
        if (isEmpty(item) || isEmpty(dataset) || isEmpty(sheetName)) {
            return { newSrcId: null };
        }

        const apiUrl = Api.getGeneric;
        const itemKey = getKeyBySingle(getSingleByApi(newApi));

        // 先 reserved id
        const newSrcId = await getReservedNewId(itemKey);
        if (!newSrcId) {
            dispatch({
                type: Act.DATA_INFO_MESSAGE,
                payload: {
                    title: `${cellId} 欄位請填入正確後綴，如：@PER, @ORG 或 @EVT`,
                    error: 1,
                    renderSignal: `update-${new Date().getTime()}`
                }
            });
            return { newSrcId: null };
        }

        if (apiUrl && itemKey) {
            const newItem = {
                graph: dataset,
                classType: newClass || itemKey,
                srcId: newSrcId || "",
                value: {
                    label: [item, item.replace("@zh", "@en")]
                }
            };

            const createResult = await createNmtlData(
                user,
                apiUrl,
                dataset,
                sheetName,
                newItem
            ).then(res => res === "OK");

            return { newSrcId, createResult };
        }
        return { newSrcId: null };
    };

    const handleCreate = async value => {
        // 移除@zh、@PER、@en等
        function removeAfterFirstAt(s) {
            const atPosition = s.indexOf("@");
            if (atPosition === -1) {
                return s;
            }
            return s.slice(0, atPosition);
        }

        const sameList = await findSameIdList(
            removeAfterFirstAt(value),
            sheetName,
            Api.getSameListWithGraphForPerOrg
        );

        if (!isEmpty(sameList)) {
            setEqualValue(sameList);
            setOpen(true);
            return;
        }
        // suffix feature
        const { isSuffix, newValue, newClass, newApi } = isCorrectSuffix(
            cellId,
            value
        );
        if (!isSuffix) {
            dispatch({
                type: Act.DATA_INFO_MESSAGE,
                payload: {
                    title: `${cellId} 欄位請填入正確後綴，如：@PER, @ORG 或 @EVT`,
                    error: 1,
                    renderSignal: `update-${new Date().getTime()}`
                }
            });
            return;
        }

        const { newSrcId } = await getCreateNmtlItemResult(
            newValue,
            newClass,
            newApi
        );

        if (newSrcId) {
            await axios.post(Api.getGeneric, {
                entry: {
                    graph: dataset,
                    classType: newClass,
                    value: { otherName: value.replace("@PER", "") },
                    srcId: newSrcId
                }
            });

            const newOpt = {
                id: newSrcId,
                label: value.replace("@zh", ""),
                value: newSrcId,
                graph: dataset
            };

            // 如果此欄位為多重 type，增加到該 type
            // hasPublisher: ORG, PER
            if (Object.keys(headerFields).indexOf(newApi) > -1) {
                headerFields[newApi].push(newOpt);
            }

            setOption(newOpt);
            // const list =
            //     createState && createState[cellId] ? createState[cellId] : [];
            // list[itemAt] = newOpt?.id;
            const list =
                cloneLocalCreateState && cloneLocalCreateState[cellId]
                    ? cloneLocalCreateState[cellId]
                    : [];
            list[itemAt] = newOpt?.id;

            // setCallback(cellId, list, menuName);

            const updateData = updateObjectValue(
                cloneLocalCreateState,
                cellId,
                list
            );

            dispatch({
                type: Act.DATA_SET_CLONE_CREATE_STATE,
                payload: updateData
            });

            setCloneLocalCreateStateFct(updateData);
        }
    };

    const customStyles = {
        container: styles => ({
            ...styles,
            margin: "-9px",
            minWidth: "360px",
            width: "max-content"
        }),
        control: (styles, { selectProps: { controlColor } }) => ({
            ...styles,
            borderStyle: "none",
            borderRadius: "unset",
            backgroundColor: controlColor
        })
    };

    const removeBrackets = tmpOption =>
        tmpOption.replace(/\s*\([^)]*\)\s*/g, "");

    useEffect(() => {
        if (!option) return;
        newOptions.push(option);
    }, [option]);

    return useMemo(() => {
        const seenLabels = new Set();
        const processedArr =
            cloneLocalCreateState &&
            cloneLocalCreateState[cellId] &&
            cloneLocalCreateState[cellId].filter((_, idx) => idx !== itemAt);
        const filteredOptions =
            cloneLocalCreateState &&
            cloneLocalCreateState[cellId] &&
            cloneLocalCreateState[cellId][itemAt] &&
            newOptions
                ? newOptions
                      .filter(
                          o =>
                              o.id === cloneLocalCreateState[cellId][itemAt] &&
                              o.graph === "tltc"
                      )
                      .map(option => ({
                          ...option,
                          label: removeBrackets(option.label)
                      }))
                      .filter(option => {
                          if (seenLabels.has(option.label)) {
                              return false;
                          }
                          seenLabels.add(option.label);
                          return true;
                      })
                : null;

        return (
            <>
                <CreatableSelect
                    // set isMulti : @zh || @en 才會一起顯示
                    isMulti
                    isClearable={false}
                    isDisabled={!isInDraggingMode}
                    styles={customStyles}
                    options={
                        inputValue && newOptions
                            ? newOptions
                                .filter(
                                    option =>
                                        option.id ===
                                              cloneLocalCreateState[cellId][
                                                  itemAt
                                              ] ||
                                          !processedArr.includes(option.id)
                                )
                                  .filter(
                                      o =>
                                          o.label.includes(inputValue) &&
                                          o.graph === "tltc"
                                  )
                                  .slice(0, MAX_OPTION)
                            : newOptions.slice(0, MAX_OPTION)
                    }
                    value={filteredOptions}
                    onChange={handleChange}
                    onInputChange={handleInputChange}
                    onCreateOption={handleCreate}
                    // components={{ MenuList }}
                    // [fixed] select lag issue: https://github.com/JedWatson/react-select/issues/3128 by M1K3Yio commented on 19 Oct 2018
                    filterOption={createFilter({ ignoreAccents: false })}
                />
                {/* 關閉偵測同一id的彈跳視窗 */}
                {showExistedModal && (
                    <ExistedModal
                        equalValue={equalValue}
                        open={open}
                        setOpen={setOpen}
                        cellId={cellId}
                        newOptions={newOptions}
                        setCallback={setCallback}
                        menuName={menuName}
                        createState={createState}
                        itemAt={itemAt}
                        headerFields={headerFields}
                        cloneLocalCreateState={cloneLocalCreateState}
                        setCloneLocalCreateStateFct={
                            setCloneLocalCreateStateFct
                        }
                    />
                )}
            </>
        );
    }, [
        cellId,
        createState,
        inputValue,
        newOptions,
        menuName,
        open,
        cloneLocalCreateState,
        isInDraggingMode,
        curPage,
        itemAt
    ]);
};

export default CustomSingleDropdownForAcc;

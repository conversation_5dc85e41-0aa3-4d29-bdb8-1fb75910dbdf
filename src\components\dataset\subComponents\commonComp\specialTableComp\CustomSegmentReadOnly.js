import React, { useMemo, useState } from "react";

// ui
import { Segment } from "semantic-ui-react";

const CustomSegmentReadOnly = ({ defaultValue }) => {
    // console.log("createState", createState[cellId]);
    // console.log(defaultValue);
    // const [inputValue] = useState(defaultValue);
    // console.log(inputValue);
    return useMemo(
        () => (
            <Segment compact basic style={{ padding: 0 }}>
                {defaultValue}
            </Segment>
        ),
        [defaultValue]
    );
};

export default CustomSegmentReadOnly;

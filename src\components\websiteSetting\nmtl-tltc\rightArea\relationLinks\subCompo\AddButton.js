import React, { useContext } from "react";
import PropTypes from "prop-types";

// semantic ui
import { Button } from "semantic-ui-react";

// utils
import { handleEdit } from "../utils/utils";
import { StoreContext } from "../../../../../../store/StoreProvider";

function AddButton({ setIsEdited }) {
    // eslint-disable-next-line no-unused-vars
    const [_, dispatch] = useContext(StoreContext);

    return (
        <Button color="teal" onClick={() => handleEdit(setIsEdited, dispatch)}>
            新增
        </Button>
    );
}

AddButton.propTypes = {
    /** 開啟編輯模式的callback */
    setIsEdited: PropTypes.func
};

AddButton.defaultProps = {
    /** 開啟編輯模式的callback */
    setIsEdited: () => null
};

export default AddButton;

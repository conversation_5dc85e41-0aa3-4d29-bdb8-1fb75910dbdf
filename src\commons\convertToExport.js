// contentData儲存的是下拉選單中的key，所以要找到對應的 lable 再輸出成excel
import { SHOW_ID } from "../components/common/sheetCrud/sheetCrudHelper";
import {
    graphOptions,
    literaryOptions
} from "../components/common/sheetCrud/config";

const convertToExport = ({ contentData, contentLookupTable }) =>
    contentData &&
    contentData.map(row => {
        const newRow = {};
        Object.keys(row).forEach(header => {
            if (Array.isArray(row[header])) {
                const bunchRow = [];
                row[header].forEach(r => {
                    bunchRow.push(
                        (SHOW_ID.indexOf(header) === -1 &&
                            contentLookupTable[r] &&
                            contentLookupTable[r].join("\n")) ||
                            r
                    );
                });
                newRow[header] = bunchRow.join("\n");
            } else {
                newRow[header] = row[header];
            }
        });
        return newRow;
    });

const datasetCoverToExport = contentData => {
    if (contentData.length === 0) return [];

    const allDataset = [...graphOptions, ...literaryOptions];

    // 回傳新的處理過後的資料
    return contentData.map(row => {
        const newRow = {};
        Object.keys(row).forEach(header => {
            if (
                (header === "graph" || header === "literary") &&
                Array.isArray(row[header])
            ) {
                const bunchRow = [];
                row[header].forEach(r => {
                    const dataSet = allDataset.find(el => el.id === r);

                    if (dataSet) {
                        bunchRow.push(dataSet.label);
                    }
                });

                newRow[header] = bunchRow;
            } else {
                newRow[header] = row[header];
            }
        });
        return newRow;
    });
};

// eslint-disable-next-line import/prefer-default-export
export { convertToExport, datasetCoverToExport };

import React, { useEffect, useState } from "react";
import { Table } from "semantic-ui-react";
import { useSelector } from "react-redux";
import { isEmpty } from "../../../../../../../commons";

function MemberDelCnt() {
    const userTableHeader = ["姓名", "電子郵件"];
    const state = useSelector(tmpState => tmpState.accMng);
    const { tableSelectPool, groupData } = state;
    const [delMembers, setDelMembers] = useState([]);

    useEffect(() => {
        const findGroups = groupData.members.filter(
            el => tableSelectPool.users.indexOf(el.uid) !== -1
        );
        setDelMembers(findGroups);
    }, []);

    return (
        <Table celled structured size="small">
            <Table.Header>
                <Table.Row>
                    {userTableHeader.map(content => (
                        <Table.HeaderCell key={content}>
                            {content}
                        </Table.HeaderCell>
                    ))}
                </Table.Row>
            </Table.Header>
            <Table.Body>
                {!isEmpty(delMembers) &&
                    delMembers.map(el => (
                        <Table.Row key={el.uid}>
                            <Table.Cell>{el.displayName}</Table.Cell>
                            <Table.Cell>{el.email}</Table.Cell>
                        </Table.Row>
                    ))}
            </Table.Body>
        </Table>
    );
}

export default MemberDelCnt;

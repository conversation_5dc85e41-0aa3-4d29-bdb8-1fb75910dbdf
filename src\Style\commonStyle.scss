*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

@mixin center {
  display: flex;
  justify-content: center;
  align-items: center;
}

@mixin rightComponentMain {
  width: 90%;
  //border: 1px red solid;
  height: 90%;
  padding: 20px;
}

.AccountManagement {
  width: 100%;
  height: 85vh;
  padding: 10px;
  display: flex;
  .leftArea {
    padding-right: 5px;
    width: 15%;
  }
  .rightArea {
    border: 1px black solid;
    width: 85%;
    //@include center;
    overflow-y: scroll;
  }
}

import React, { useContext, useMemo, useState } from 'react';

// store
import { createFilter } from 'react-select';
import CreatableSelect from 'react-select/creatable';
import axios from 'axios';
import { StoreContext } from '../../../../store/StoreProvider';
import Act from '../../../../store/actions';
import { getMenuPlacement } from '../../datasetConfig';

// custom ui
import MenuList from './MenuList';

// common
import { isEmpty, isArrayEqual } from '../../../../commons';
import {
  MAX_OPTION,
  notShowingExistedModalHeader,
  graph4CreateInstance,
} from '../../../common/sheetCrud/sheetCrudHelper';
import { getReservedNewId } from '../../../common/sheetCrud/utils';

// api
import Api from '../../../../api/nmtl/Api';
import { createNmtlData } from '../../../../api/nmtl';
import {
  checkDataInGraph,
  existedDataInOtherGraph,
  isCorrectSuffix,
  returnClasstype,
} from '../../../../api/nmtl/ApiField';
import getSingleByApi from '../../../../api/nmtl/ApiSingle';
import getKeyBySingle from '../../../../api/nmtl/ApiKey';
import ExistedModalForOldForm from './ExistedModalForOldForm';

// 可以新增
const CustomMultiDropdownCreatable = ({
  cellId,
  rowId,
  idx = 0,
  createState,
  setCallback,
  // isShowId
  isDiffValue = false,
}) => {
  // eslint-disable-next-line no-unused-vars
  const [state, dispatch] = useContext(StoreContext);
  const { user } = state;
  const { mainSubject, sheet } = state.data;
  const { headerFields } = sheet;
  const { dataset } = mainSubject.selected;
  const { key: sheetName } = sheet.selected;
  const [equalValue, setEqualValue] = useState(null);
  const [open, setOpen] = useState(false);
  const [inputIds, setInputIds] = useState([]);

  const notShowingExistedModal = notShowingExistedModalHeader.includes(cellId);

  // change dropdown value
  const handleChange = async (selectValue) => {
    // selectValue: [{ id: "PER96766", label: "施仁思@PER", value: "PER96766" }...]
    const cellValue = isEmpty(selectValue) ? [] : selectValue;
    // keep input value when it changed
    // extract and combine id, e.g. ORG1/ORG2
    const selectCombinedIds = cellValue.map((item) => item.id).sort();
    setInputIds(selectCombinedIds);
    // 如果selectValue已存在其他資料集，跳modal讓使用者選擇要搬動的資料
    const isInGraph = !isEmpty(cellValue)
      ? await checkDataInGraph(cellValue?.at(-1).id, dataset)
      : true;
    const tmpClasstype =
      !isEmpty(selectValue) && returnClasstype(cellId, selectValue.at(-1)?.label);

    if (!isInGraph && !isEmpty(selectValue) && !notShowingExistedModal) {
      const sameList = await existedDataInOtherGraph(
        tmpClasstype,
        selectValue.at(-1).label,
        cellId,
      );
      setEqualValue(sameList);
      setOpen(true);
      return;
    }

    // createState.value: {0: "PER1", 1: "PER2"...}
    const oldValues = createState.value ? Object.values(createState.value).sort() : [];
    if (isArrayEqual(selectCombinedIds, oldValues)) {
      // dispatch
      dispatch({
        type: Act.DATA_CONTENT_ROW_NO_CHANGED,
        payload: {
          rowId,
          cellId,
          idx,
        },
      });
    } else {
      // dispatch
      dispatch({
        type: Act.DATA_CONTENT_ROW_CHANGED,
        payload: {
          rowId,
          cellId,
          idx,
          // 因為 multiple drop down 不需要管順序
          // cellData: {0: "value1", 1: "value2"}
          cellValue: Object.assign({}, selectCombinedIds),
        },
      });
    }

    // keep input value when it changed
    if (cellValue.length === 0) {
      // clear
      setCallback(cellId, rowId, idx, { isOption: true, value: null });
    } else {
      setCallback(cellId, rowId, idx, {
        isOption: true,
        value: Object.assign({}, selectCombinedIds),
      });
    }
    // clear error
    dispatch({
      type: Act.DATA_MESSAGE_CLEAN,
    });
  };

  const handleInputChange = (inputValue) => {
    if (!inputValue) {
      return;
    }

    setCallback(cellId, rowId, idx, { isOption: true, input: inputValue });
    // clear error
    dispatch({
      type: Act.DATA_MESSAGE_CLEAN,
    });
  };

  const getDatasetByClass = (newClass) => {
    // if newClass is Person, return "authority"
    if (newClass in graph4CreateInstance) {
      return graph4CreateInstance[newClass];
    }
    return dataset;
  };

  const getCreateNmtlItemResult = async (item, newClass, newApi) => {
    if (isEmpty(item) || isEmpty(dataset) || isEmpty(sheetName)) {
      return { newSrcId: null };
    }

    const apiUrl = Api.getGeneric;
    const itemKey = getKeyBySingle(getSingleByApi(newApi));

    const createGraph = getDatasetByClass(newClass);
    console.log('createGraph', createGraph);

    // 先 reserved id
    const newSrcId = await getReservedNewId(itemKey);
    if (!newSrcId) {
      dispatch({
        type: Act.DATA_MESSAGE,
        payload: {
          title: `${cellId} 欄位請填入正確後綴，如：@PER, @ORG 或 @EVT`,
          error: 1,
          renderSignal: `update-${new Date().getTime()}`,
        },
      });
      return { newSrcId: null };
    }

    if (apiUrl && itemKey) {
      const newItem = {
        graph: createGraph,
        classType: newClass || itemKey,
        srcId: newSrcId || '',
        value: {
          label: item,
        },
      };

      const createResult = await createNmtlData(user, apiUrl, dataset, sheetName, newItem).then(
        (res) => res === 'OK',
      );

      return { newSrcId, createResult };
    }
    return { newSrcId: null };
  };

  const handleCreate = async (inputValue) => {
    // suffix feature
    const { isSuffix, newValue, newClass, newApi } = isCorrectSuffix(cellId, inputValue);
    if (!isSuffix) {
      dispatch({
        type: Act.DATA_MESSAGE,
        payload: {
          title: `${cellId} 欄位請填入正確後綴，如：@PER, @ORG 或 @EVT`,
          error: 1,
          renderSignal: `update-${new Date().getTime()}`,
        },
      });
      return;
    }

    const { newSrcId } = await getCreateNmtlItemResult(newValue, newClass, newApi);

    setCallback(cellId, rowId, idx, { isOption: true, isLoading: true });
    if (newSrcId) {
      const newOpt = { id: newSrcId, label: inputValue, value: newSrcId };

      // 如果此欄位為多重 type，增加到該 type
      // hasPublisher: ORG, PER
      if (Object.keys(headerFields).indexOf(newApi) > -1) {
        headerFields[newApi].push(newOpt);
      }

      // 更新目前的總列表
      if (!createState.options) {
        // eslint-disable-next-line no-param-reassign
        createState.options = [];
      }
      createState.options.push(newOpt);

      const selectCombinedIds = createState.value
        ? Object.values(createState.value).concat(newOpt.id)
        : [newOpt.id];
      // 塞到 changed
      dispatch({
        type: Act.DATA_CONTENT_ROW_CHANGED,
        payload: {
          rowId,
          cellId,
          idx,
          cellValue: Object.assign({}, selectCombinedIds),
        },
      });

      // 更新 createState
      setCallback(cellId, rowId, idx, {
        isOption: true,
        value: Object.assign({}, selectCombinedIds),
        options: createState.options,
        isLoading: false,
        input: null,
      });
    }
  };

  const customStyles = {
    container: (styles) => ({
      ...styles,
      margin: '-9px',
      minWidth: '100%',
    }),
    control: (styles, { selectProps: { controlColor } }) => ({
      ...styles,
      borderStyle: 'none',
      borderRadius: 'unset',
      backgroundColor: controlColor,
    }),
  };

  const customPlacement = getMenuPlacement(rowId);
  const customControlBgColor = isDiffValue ? '#f9c09a66' : '';

  const newOptions = useMemo(() => {
    if (!createState) {
      return [];
    }
    if (!createState.options) {
      return [];
    }

    // if (isShowId) {
    //     return removeDuplicateId(createState);
    // }
    return createState.options;
  }, [cellId, createState.options]);

  return useMemo(
    () => (
      <>
        <CreatableSelect
          isMulti
          isClearable
          styles={customStyles}
          isDisabled={createState.isLoading}
          isLoading={createState.isLoading}
          options={
            createState && createState.input && newOptions
              ? newOptions.filter((o) => o.label.includes(createState.input)).slice(0, MAX_OPTION)
              : newOptions.slice(0, MAX_OPTION)
          }
          value={
            createState.value
              ? newOptions.filter((o) => Object.values(createState.value).indexOf(o.id) > -1)
              : null
          }
          onChange={handleChange}
          onInputChange={handleInputChange}
          onCreateOption={handleCreate}
          components={{ MenuList }}
          menuPlacement={customPlacement}
          controlColor={customControlBgColor}
          autosize
          // [fixed] select lag issue: https://github.com/JedWatson/react-select/issues/3128 by M1K3Yio commented on 19 Oct 2018
          filterOption={createFilter({ ignoreAccents: false })}
        />
        <ExistedModalForOldForm
          equalValue={equalValue}
          open={open}
          setOpen={setOpen}
          inputIds={inputIds}
          cellId={cellId}
          rowId={rowId}
          idx={idx}
          setCallback={setCallback}
        />
      </>
    ),
    [cellId, createState.value, createState.input, createState.isLoading, open, inputIds],
  );
};

export default CustomMultiDropdownCreatable;

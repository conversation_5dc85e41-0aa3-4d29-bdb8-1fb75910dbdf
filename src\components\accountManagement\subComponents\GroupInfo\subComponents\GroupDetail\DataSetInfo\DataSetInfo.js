import React, { useEffect, useState } from "react";

// plugins
import { Segment, Icon, Label } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";

//
import "./DataSetInfo.scss";
import { getMainSubject } from "../../../../../../../api/firebase/cloudFirestore";
import { customSubjectOption } from "../../../../../../../commons/utility";
import textConfig from "../../../../Utils/textConifg";
import { isEmpty } from "../../../../../../../commons";
import { BtnName, TypeName } from "../../../../Utils/compoConfig";
import accMngAct from "../../../../../../../reduxStore/accManage/accManageAction";
import DataSetInfoBtn from "../../CustomButton/DataSetInfoBtn";

function DataSetInfo() {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { unselectedSet, groupData } = state;

    // label onClick type
    const PLUS = "plus";
    const DELETE = "delete";

    // 控制unselectedSet、groupData.dataSet
    const handleLabelClick = (dataItem, type) => {
        if (type === PLUS) {
            let tmpListDel = JSON.parse(JSON.stringify(unselectedSet));
            const tmpGPData = JSON.parse(JSON.stringify(groupData));

            tmpListDel = tmpListDel.filter(el => el.id !== dataItem.id);
            dispatch({
                type: accMngAct.SET_UNSELECTEDSET,
                payload: tmpListDel
            });

            tmpGPData.dataSet.push(dataItem);
            dispatch({
                type: accMngAct.SET_GROUPDATA,
                payload: tmpGPData
            });
        } else if (type === DELETE) {
            const tmpGPData = JSON.parse(JSON.stringify(groupData));
            const tmpListAdd = JSON.parse(JSON.stringify(unselectedSet));

            tmpGPData.dataSet = tmpGPData.dataSet.filter(
                el => el.id !== dataItem.id
            );
            dispatch({
                type: accMngAct.SET_GROUPDATA,
                payload: tmpGPData
            });

            tmpListAdd.push(dataItem);
            dispatch({
                type: accMngAct.SET_UNSELECTEDSET,
                payload: tmpListAdd
            });
        }
    };

    const createLabel = (dataItem, type) => (
        <Label
            key={dataItem.id}
            style={{ margin: "0.5rem", cursor: "pointer" }}
            onClick={() => handleLabelClick(dataItem, type)}
        >
            {dataItem.label}
            <Icon name={type} style={{ margin: " 0 0 0 0.5rem" }} />
        </Label>
    );

    useEffect(() => {
        if (isEmpty(groupData)) return;
        // 取得主題名稱
        getMainSubject()
            .then(res => {
                const nmtlSet = customSubjectOption();
                const tmpSet = [...res, ...nmtlSet];
                const tmpUnselect = tmpSet.filter(
                    el => !groupData.dataSet.find(obj => obj.id === el.id)
                );
                dispatch({
                    type: accMngAct.SET_UNSELECTEDSET,
                    payload: tmpUnselect
                });
            })
            .catch(err => console.log(err));
    }, [groupData]);

    return (
        <div className="DataSetInfo">
            <div className="DataSetInfo__Title">
                <h2>{textConfig.DATASETAREA_TITLE}</h2>
                {BtnName.filter(
                    ({ typeName }) => typeName === TypeName.RemoveAll
                ).map(btnInfo => (
                    <DataSetInfoBtn
                        compoInfo={btnInfo}
                        key={btnInfo.typeName}
                    />
                ))}
            </div>
            <Segment>
                <Label.Group color="red">
                    {!isEmpty(groupData.dataSet)
                        ? groupData.dataSet
                            .filter(el =>
                                groupData.name === "外譯房"
                                    ? el
                                    : el.id !== "tltc"
                            )
                            .map(el => createLabel(el, DELETE))
                        : textConfig.DATASETPOOL_TITLE}
                </Label.Group>
            </Segment>
            <div className="DataSetInfo__Title">
                <h2>{textConfig.DATASETPOOL_TITLE}</h2>
                {BtnName.filter(
                    ({ typeName }) => typeName === TypeName.SeletAll
                ).map(btnInfo => (
                    <DataSetInfoBtn
                        compoInfo={btnInfo}
                        key={btnInfo.typeName}
                    />
                ))}
            </div>
            <Segment>
                <Label.Group color="teal">
                    {!isEmpty(unselectedSet)
                        ? unselectedSet
                            .filter(el =>
                                groupData.name === "外譯房"
                                    ? el
                                    : el.id !== "tltc"
                            ) // nmtl後臺資料集不顯示tltc資料集
                            .map(el => createLabel(el, PLUS))
                        : textConfig.DATASETPOOL_EMPTY}
                </Label.Group>
            </Segment>
        </div>
    );
}

export default DataSetInfo;

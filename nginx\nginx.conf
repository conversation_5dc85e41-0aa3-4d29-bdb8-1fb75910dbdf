server {

  listen 80;

  location / {
    root   /usr/share/nginx/html;
    index  index.html index.htm;
    try_files $uri $uri/ /index.html;
    add_header X-Frame-Options DENY;
    proxy_hide_header X-powered-by;
  }

  # proxy: systemInfo
  location /zh-tw/systemInfo {
    proxy_pass http://nmtl_api:3000;
  }

  # proxy: elastic log
  location /log {
    proxy_pass http://nmtl_log_api:3000;
  }

  # proxy: elastic log list (getList)
  location /log/list {
    proxy_pass http://nmtl_log_api:3000;
  }

  # proxy: nmtl api part 1
  # e.g. /dl_basicinfo/1.0
  #      /singleAcademicDegree/1.0
  location ~ ^/zh-tw/[a-zA-Z_]+/1\.0 {
    proxy_pass http://nmtl_api:3000;
  }

  # proxy: nmtl api part 2
  # e.g. /location/list/1.0
  location ~ ^/zh-tw/[a-zA-Z_]+/list/1\.0 {
    proxy_pass http://nmtl_api:3000;
  }

  # proxy: nmtl api part 3
  # e.g. /dl_basicinfo/search/1.0
  location ~ ^/zh-tw/[a-zA-Z_]+/search/1\.0 {
    proxy_pass http://nmtl_api:3000;
  }

  error_page   500 502 503 504  /50x.html;

  location = /50x.html {
    root   /usr/share/nginx/html;
  }

}

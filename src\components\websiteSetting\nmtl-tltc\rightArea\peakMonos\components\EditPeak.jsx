import React, { useContext, useEffect, useRef, useState } from "react";
import "./EditPeak.scss";
import { Button, Grid, Icon, Input, Segment } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import { StoreContext } from "../../../../../../store/StoreProvider";
import { isEmpty } from "../../../../../../commons";

import EditCate from "./EditCate";
import { uploadFile } from "../../../../commons/components/EditNews/Utils/utils";
import PeakAct from "../PeakMonosAction";
import { getFileName } from "../../../../commons/components/EditNews/Utils/saveDataUtils";
import CustomQuill from "./subComponents/CustomQuill";
import Api from "../../../../../../api/nmtl/Api";
import { savePeakData } from "../utils/utils";
import CustomButton from "./subComponents/CustomButton";
import DraggableTable from "./subComponents/PeakChapterList";

const EditPeak = () => {
    const editorRef = useRef(null);

    // eslint-disable-next-line no-unused-vars
    const [globalState, _] = useContext(StoreContext);
    const [stateContext] = useContext(StoreContext);
    const { user } = stateContext;
    const { websiteSubject } = globalState.websiteSetting;
    const dispatch = useDispatch();
    const {
        updatePeakBrief,
        cateIsEdited,
        peakInfo,
        newPeakInfo,
        editingPeakId,
        isEditedChapterList
    } = useSelector(state => state);

    const [value, setValue] = useState("");
    const [peakYear, setPeakYear] = useState("");
    const [fileValue, setFileValue] = useState("");
    const [files, setFiles] = useState([]);
    const [tmpPeakInfo, setTmpPeakInfo] = useState([]);
    const [isFirstEdit, setIsFirstEdit] = useState(true);
    const [chapterList, setChapterList] = useState([]);

    const setUpdatePeakBrief = (tmpDispatch, tmpData) => {
        tmpDispatch({
            type: PeakAct.SET_UPDATEPEAKBRIEF,
            payload: tmpData
        });
    };

    // Upload pdf
    const handleChange = async (e, tmpData) => {
        const tmpUpdatedData = JSON.parse(JSON.stringify(updatePeakBrief));
        setFileValue(tmpData.value);
        tmpUpdatedData.allfileAvailableAt = await uploadFile(
            e.currentTarget,
            websiteSubject,
            tmpUpdatedData.allfileAvailableAt,
            "peak"
        );
        setUpdatePeakBrief(dispatch, tmpUpdatedData);
    };

    const handleChangePeakYear = e => {
        const newValue = e.target.value;
        setPeakYear(newValue);
    };

    // 取消回到上一頁
    const handleCancel = () => {
        dispatch({
            type: PeakAct.SET_ISEDITEDPEAK,
            payload: false
        });
    };

    // 移除檔案
    const handleDeleteFiles = (id, tmpFilesData) => {
        const newFilesArr = tmpFilesData.filter(file => file !== id);
        setFiles(newFilesArr);
    };

    // 儲存
    const handleSave = () => {
        // 不是首次編輯的話，則將上次的newPeakInfo當成oldstate
        if (!isFirstEdit) {
            const oldPeakInfo = newPeakInfo;
            dispatch({
                type: PeakAct.SET_PEAKINFO,
                payload: {
                    ...oldPeakInfo
                }
            });
        }

        setIsFirstEdit(false);

        savePeakData(
            dispatch,
            peakInfo,
            newPeakInfo,
            user,
            websiteSubject,
            "PeakMono"
        );

        dispatch({
            type: PeakAct.SET_ISMODALOPEN,
            payload: true
        });
    };

    // 儲存newPeakInfo
    useEffect(() => {
        dispatch({
            type: PeakAct.SET_NEWPEAKINFO,
            payload: {
                hasPeakYear: `${peakYear}-00-00`,
                fileAvailableAt: files[0],
                introduction: value,
                peakId: editingPeakId
            }
        });
    }, [files, peakYear, fileValue, value, tmpPeakInfo]);

    // Get files url
    useEffect(() => {
        if (updatePeakBrief.allfileAvailableAt) {
            setFiles(
                updatePeakBrief.allfileAvailableAt.map(url => getFileName(url))
            );
        }
    }, [updatePeakBrief]);

    useEffect(() => {
        const initTemplate = { allfileAvailableAt: [], peakBrief: "" };
        if (updatePeakBrief) {
            setUpdatePeakBrief(dispatch, initTemplate);
        }
    }, []);

    useEffect(() => {
        if (isEmpty(updatePeakBrief)) return;
        setFiles(updatePeakBrief.allfileAvailableAt || "");
        setFileValue(updatePeakBrief.allfileAvailableAt || "");
    }, [updatePeakBrief]);

    useEffect(() => {
        // 拿api內初始的資料
        const getPeakBrief = async () => {
            const api = Api.getPeakMonosInfo.replace(
                "{peakMonoId}",
                editingPeakId
            );
            const response = await axios
                .get(api)
                .then(res => setTmpPeakInfo(res?.data?.data));
            return response;
        };
        getPeakBrief();
    }, [editingPeakId]);

    useEffect(() => {
        if (tmpPeakInfo && tmpPeakInfo[0]?.fileAvailableAt) {
            setFiles([tmpPeakInfo[0]?.fileAvailableAt]);
        }
        if (tmpPeakInfo && tmpPeakInfo[0]?.introduction) {
            setValue(tmpPeakInfo[0]?.introduction);
        }
        if (tmpPeakInfo && tmpPeakInfo[0]?.hasPeakYear) {
            setPeakYear(tmpPeakInfo[0]?.hasPeakYear?.substring(3, 7));
        }
    }, [tmpPeakInfo]);

    useEffect(() => {
        const initialTemp = {
            fileAvailableAt: null,
            introduction: null
        };
        // setTmpPeakInfo(initialTemp);
        const { fileAvailableAt, introduction, srcId, hasPeakYear } =
            tmpPeakInfo[0] || {};

        if (tmpPeakInfo[0]?.fileAvailableAt || tmpPeakInfo[0]?.introduction) {
            dispatch({
                type: PeakAct.SET_PEAKINFO,
                payload: {
                    hasPeakYear: hasPeakYear?.substring(3, 13) || "",
                    fileAvailableAt: fileAvailableAt || "",
                    introduction: introduction || "",
                    peakId: srcId || ""
                }
            });
            setIsFirstEdit(false);
        } else {
            dispatch({ type: PeakAct.SET_PEAKINFO, payload: {} });
            setIsFirstEdit(true);
        }

        dispatch({ type: PeakAct.SET_NEWPEAKINFO, payload: initialTemp });
    }, [tmpPeakInfo]);

    useEffect(() => {
        const getChapterList = async () => {
            const api = Api.getPeakMonoChapterList.replace(
                "{peakMonoId}",
                editingPeakId
            );
            const response = await axios.get(api).then(res => {
                setChapterList(res?.data?.data);
                const length = res?.data?.data?.length + 1;
                dispatch({
                    type: PeakAct.SET_CHAPTERLISTLENGTH,
                    payload: length
                });
            });
            return response;
        };
        getChapterList();
    }, [editingPeakId, cateIsEdited, isEditedChapterList]);

    return (
        <>
            {!cateIsEdited ? (
                <div
                    style={{
                        display: "flex",
                        flexDirection: "column",
                        width: "100%",
                        alignItems: "center",
                        // overflowY: "scroll",
                        padding: "20px"
                        // height: "100%"
                    }}
                >
                    <Segment className="EditPdf">
                        <div className="topArea">
                            <h1>上傳專刊</h1>
                            <Button
                                color="yellow"
                                content="回上一層"
                                onClick={handleCancel}
                            />
                        </div>

                        <Grid celled>
                            <Grid.Row>
                                <Grid.Column
                                    width={3}
                                    style={{
                                        backgroundColor: "#e0e1e2"
                                    }}
                                >
                                    <div className="topArea__left">
                                        <span>專刊全書PDF檔案</span>
                                    </div>
                                </Grid.Column>
                                <Grid.Column width={13}>
                                    <div className="topArea__right">
                                        <div className="topArea__right--first">
                                            <span>上傳檔案</span>
                                        </div>
                                        <div className="topArea__right--second">
                                            {files.map((i, idx) => (
                                                <div key={idx}>
                                                    <span>
                                                        {i.split("/").pop()}
                                                    </span>
                                                    <Button
                                                        icon
                                                        circular
                                                        style={{
                                                            marginLeft: "0.5rem"
                                                        }}
                                                    >
                                                        <Icon
                                                            name="cancel"
                                                            style={{
                                                                color: "red"
                                                            }}
                                                            onClick={() => {
                                                                handleDeleteFiles(
                                                                    i,
                                                                    files
                                                                );
                                                            }}
                                                        />
                                                    </Button>
                                                </div>
                                            ))}
                                        </div>
                                        <div className="topArea__right--third">
                                            {/* eslint-disable-next-line jsx-a11y/label-has-associated-control,jsx-a11y/label-has-for */}
                                            <label
                                                htmlFor="files"
                                                style={{
                                                    background: "#e4f5e8",
                                                    color: "#21ba45",
                                                    fontSize: "12px",
                                                    cursor: "pointer",
                                                    textAlign: "center",
                                                    padding: "0.5rem 1rem",
                                                    borderRadius: "4px"
                                                }}
                                            >
                                                上傳檔案
                                            </label>
                                            <Input
                                                id="files"
                                                onChange={handleChange}
                                                // value={fileValue}
                                                style={{
                                                    visibility: "hidden",
                                                    display: "none"
                                                }}
                                                type="file"
                                                accept=".pdf"
                                            />
                                        </div>
                                    </div>
                                </Grid.Column>
                            </Grid.Row>
                        </Grid>
                    </Segment>
                    <Segment className="EditPeakYear">
                        <div className="topArea">
                            <h1>Peak收錄的得獎作品年份</h1>
                        </div>

                        <Grid celled>
                            <Grid.Row>
                                <Grid.Column
                                    width={3}
                                    style={{
                                        backgroundColor: "#e0e1e2"
                                    }}
                                >
                                    <div className="topArea__left">
                                        <span>Peak收錄的得獎作品年份</span>
                                    </div>
                                </Grid.Column>
                                <Grid.Column width={13}>
                                    <div className="topArea__right">
                                        <Input
                                            value={peakYear}
                                            onChange={e =>
                                                handleChangePeakYear(e)
                                            }
                                        />
                                    </div>
                                </Grid.Column>
                            </Grid.Row>
                        </Grid>
                    </Segment>
                    <Segment className="EditInfo">
                        <div className="topArea">
                            <h1>專刊簡介</h1>
                        </div>
                        <div className="EditInfo__content">
                            <CustomQuill
                                quillId="CustomEditorPeak"
                                tmpRef={editorRef}
                                tmpValue={value}
                                onChangeFct={setValue}
                            />
                        </div>
                        <div className="EditInfo__buttonBox">
                            <CustomButton
                                onClick={handleSave}
                                message="儲存成功"
                                content="儲存"
                                color="green"
                            />
                            <Button
                                color="grey"
                                content="取消"
                                onClick={handleCancel}
                            />
                        </div>
                    </Segment>
                    <Segment className="EditChapters">
                        <DraggableTable
                            list={chapterList}
                            user={user}
                            websiteSubject={websiteSubject}
                        />
                    </Segment>
                </div>
            ) : (
                <EditCate user={user} websiteSubject={websiteSubject} />
            )}
        </>
    );
};

export default EditPeak;

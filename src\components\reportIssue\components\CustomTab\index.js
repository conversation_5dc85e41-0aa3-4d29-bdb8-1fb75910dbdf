import React, { useState, useEffect } from "react";
import { useDispatch } from "react-redux";

// semantic ui
import { Tab } from "semantic-ui-react";

// config
import { statusConfig } from "../../common/statusConfig";
import RPAct from "../../reportIssueAction";

// components
import IssueTable from "../CustomTable/IssueTable";

function CustomTab() {
    const dispatch = useDispatch();
    const [panes, setPanes] = useState([]);

    useEffect(() => {
        const tmpArr = statusConfig.map(({ status, text }) => ({
            menuItem: text,
            status,
            render: () => (
                <Tab.Pane attached={false}>
                    <IssueTable />
                </Tab.Pane>
            )
        }));
        setPanes(tmpArr);
    }, []);

    const handleChange = (evt, data) => {
        dispatch({
            type: RPAct.SET_RPTABSTATUS,
            payload: panes[data.activeIndex].status
        });
    };
    return (
        <Tab
            menu={{ secondary: true, pointing: true }}
            panes={panes}
            onTabChange={handleChange}
        />
    );
}

export default CustomTab;

import React, { useCallback, useContext, useEffect, useState } from "react";

// store
import Act from "../../../../../store/actions";

// utils
import axios from "axios";
import Api from "../../../../../api/nmtl/Api";
import { readNmtlData } from "../../../../../api/nmtl";
import { isEmpty } from "../../../../../commons";
import { addSuffix, convertSuffixToClass } from "../../../../../api/nmtl/ApiField";
import { StoreContext } from "../../../../../store/StoreProvider";

// components
import { Button, Input, Modal } from "semantic-ui-react";


const DropdownEditModal = ({ id, onClick, open, options, cellId }) => {
    const [state, dispatch] = useContext(StoreContext);

    const { notifyDropdownListUpdate, sheet, mainSubject } = state.data;
    const { headerFields } = sheet;
    const { dataset } = mainSubject.selected;

    const [ogData, setOgData] = useState([]);
    const [zhData, setZhData] = useState([]);
    const [enData, setEnData] = useState([]);

    // 更新下拉選單資料
    const handleGetSheetHeaderFields = useCallback(async (type) => {
        if (isEmpty(headerFields)) return;

        const keyMapping = {
            Person: "getPersonlist",
            Location: "getLocationList",
            Organization: "getOrganizationList"
        };

        const replaceApiNameList = {
            getPersonlist: "getScvPersonList",
            getLocationList: "getScvLocationList",
            getOrganizationList: "getScvOrganizationList"
        };

        const apiKey = keyMapping[type];
        const scvApi = replaceApiNameList[apiKey];

        const results = await readNmtlData(Api[scvApi].replace("{ds}", dataset));

        const apiResults = results?.data.map(d => ({
            id: d.id,
            label: `${d.label}${addSuffix(apiKey)}`,
            value: d.id,
            graph: d.Graph
        }));

        dispatch({
            type: Act.DATA_SHEET_HEADER_FIELD,
            payload: { ...headerFields, [apiKey]: apiResults }
        });
    }, [headerFields, dataset, dispatch]);

    const formatString = useCallback((tmpData) => {
        const cloneData = { ...tmpData };
        const parts = cloneData.label.split("@");

        if (parts.length >= 2) {
            parts[0] = parts[0].replace(/\s?\(其他名稱.*?\)/, "");
            cloneData.label = parts[0];
        }

        return cloneData;
    }, []);

    const onSave = useCallback(async () => {
        if (isEmpty(zhData) || isEmpty(enData) || isEmpty(ogData)) return;

        const judgeClassTypeValue = ogData[0].label;
        const foundPrefix = Object.keys(convertSuffixToClass).find(prefix => judgeClassTypeValue.endsWith(prefix));
        let classType = convertSuffixToClass[foundPrefix];
        if (cellId === 'srcId_hasPlaceOfPublication') {
            classType = 'Location';
        }

        const srcZhLabel = ogData.filter(el => el.label.includes("@zh")).map(el => `${formatString(el).label}@zh`);
        const srcEnLabel = ogData.filter(el => el.label.includes("@en")).map(el => `${formatString(el).label}@en`);
        const dstZhLabel = zhData.filter(el => !isEmpty(el.label)).map(el => `${el.label}@zh`);
        const dstEnLabel = enData.filter(el => !isEmpty(el.label)).map(el => `${el.label}@en`);

        const entry = {
            graph: "tltc",
            classType,
            srcId: id
        };

        await axios.put(Api.getGeneric, {
            entrySrc: { ...entry, value: { label: [...srcZhLabel, ...srcEnLabel] } },
            entryDst: { ...entry, value: { label: [...dstZhLabel, ...dstEnLabel] } }
        });

        dispatch({
            type: Act.NOTIFY_DROPDOWN_LIST_UPDATE,
            payload: !notifyDropdownListUpdate
        });

        await handleGetSheetHeaderFields(classType);
        onClick();
    }, [zhData, enData, ogData, id, cellId, notifyDropdownListUpdate, handleGetSheetHeaderFields, onClick, dispatch, formatString]);

    useEffect(() => {
        if (isEmpty(options)) return;
        const filterOptions = options.filter(i => i.value === id);

        const tmpZhData = filterOptions.filter(el => el.label.includes("@zh")).map(formatString);
        const tmpEnData = filterOptions.filter(el => el.label.includes("@en")).map(formatString);

        setZhData(tmpZhData.length ? tmpZhData : [{ id, label: "", value: id, graph: 'tltc' }]);
        setEnData(tmpEnData.length ? tmpEnData : [{ id, label: "", value: id, graph: 'tltc' }]);
        setOgData(filterOptions);
    }, [options, id, formatString]);

    const onchangeHandler = useCallback((e, index, data, setData) => {
        const tmpData = [...data];
        tmpData[index] = { ...tmpData[index], label: e.target.value };
        setData(tmpData);
    }, []);

    const renderInputs = useCallback((data, setData, lang) => (
        <div>
            <Modal.Description>{lang === 'zh' ? '中文版顯示名稱' : '外文版顯示名稱'}</Modal.Description>
            {data?.map((el, index) => (
                <Input
                    key={`${el.id}_@${lang}`}
                    style={{ width: "100%", marginTop: "0.5rem" }}
                    value={el.label}
                    onChange={e => onchangeHandler(e, index, data, setData)}
                />
            ))}
        </div>
    ), [onchangeHandler]);

    return (
        <Modal onClose={onClick} onOpen={onClick} open={open} size="tiny">
            <Modal.Header>編輯</Modal.Header>
            <Modal.Content style={{ display: "flex", flexDirection: "column", gap: "1rem" }}>
                {!isEmpty(zhData) && renderInputs(zhData, setZhData, 'zh')}
                {!isEmpty(enData) && renderInputs(enData, setEnData, 'en')}
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={onClick} negative>取消</Button>
                <Button onClick={onSave} positive>儲存</Button>
            </Modal.Actions>
        </Modal>
    );
};

export default DropdownEditModal;

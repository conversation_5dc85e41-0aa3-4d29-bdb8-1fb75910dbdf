import React, { useContext, useState, useMemo } from 'react';
import { useDispatch } from 'react-redux';

// ui
import { Button, Input, Label, Icon } from 'semantic-ui-react';

// store
import { string } from 'prop-types';
import { StoreContext } from '../../../../../store/StoreProvider';
import Act from '../../../../../store/actions';
import FullTextFilePicker from './FullTextFilePicker';
import FileAct from '../../../../../reduxStore/file/fileAction';
import { getFileFolderPattern } from '../../../../common/imageCommon/FolderList/folderListHelper';
import uploadConfig from '../../../../toolPages/components/upload/uploadConfig';
import { getLangTag } from '../../../../common/sheetCrud/utils';

const CustomFileUpload = ({
  cellId,
  rowId,
  idx,
  createState,
  defaultValue,
  setCallback,
  isDiffValue = false,
}) => {
  const dispatchRedux = useDispatch();

  // eslint-disable-next-line no-unused-vars
  const [_, dispatch] = useContext(StoreContext);
  const [open, setOpen] = useState(false);

  const setValue = (value) => {
    const cellValue = {
      ...createState,
      // null 代表要刪除，不能帶 ""，因為還是會被存進去
      ...{ [idx]: value === '' ? null : value },
    };

    // change input background color when value is diff
    if (value !== defaultValue) {
      // dispatch
      dispatch({
        type: Act.DATA_CONTENT_ROW_CHANGED,
        payload: {
          rowId,
          cellId,
          cellValue,
        },
      });
    } else {
      // dispatch
      dispatch({
        type: Act.DATA_CONTENT_ROW_NO_CHANGED,
        payload: {
          rowId,
          cellId,
          idx,
        },
      });
    }
    // keep input value when it changed
    setCallback(cellId, rowId, idx, cellValue);
  };

  const onValueChange = (newValue) => {
    setValue(newValue);
  };

  // change input value
  const handleInputChange = (event, { value }) => {
    // keep input value when it changed
    setValue(value);
  };

  const handleAddClick = () => {
    // -1 為新增 item
    // FIXME: 原本 newState 以新 item {value: ""} 傳入，但不知為何 createState 的值沒有改變
    const newState = Object.assign({}, createState);

    // 找下一個可以用的欄位，但是可能 createState: {3:"", 4:"", 8:""}
    // 目前機制從 0 開始找
    const objKeys = Object.keys(createState);
    let nextIdx = '-1';
    for (let i = 0; i < objKeys.length + 1; i += 1) {
      const iStr = i.toString();
      if (objKeys.indexOf(iStr) < 0) {
        nextIdx = iStr;
        break;
      }
    }
    newState[nextIdx] = '';
    setCallback(cellId, rowId, -1, Object.assign({}, newState));
  };

  const handleCellClick = () => {
    dispatchRedux({
      type: FileAct.SET_DEFAULT_VALUE,
      payload: createState.value || '',
    });
    dispatchRedux({
      type: FileAct.SELECT_FILE,
      payload: '',
    });
    getFileFolderPattern(dispatchRedux, uploadConfig.ApiGetFiles);
    setOpen(true);
  };

  // file pdf
  return useMemo(
    () => (
      <div>
        <Input
          label={<Button icon="file alternate outline" onClick={handleCellClick} />}
          labelPosition="left"
          // keep input value
          value={createState[idx]}
          // this is not an error., it just used to change the input background color
          error={isDiffValue}
          // onChange event
          onChange={handleInputChange}
        />
        <Label basic size="mini">
          <Icon name="add" onClick={handleAddClick} />
          {getLangTag(createState[idx])}
        </Label>
        <FullTextFilePicker
          open={open}
          setOpen={setOpen}
          defaultValue={createState[idx]}
          onValueChange={onValueChange}
        />
      </div>
    ),
    [cellId, createState, open],
  );
};

export default CustomFileUpload;

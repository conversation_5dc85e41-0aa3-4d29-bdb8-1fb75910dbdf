import React, { useEffect, useState } from "react";

// scss
import "./CarouselImgInput.scss";

// plugins
import { useSelector } from "react-redux";

//
import CustomButton from "../CustomButton/CustomButton";
import DraggableTable from "./DraggableTable";
import { isEmpty } from "../../../../../../../commons";
import initColumnDef from "../../Utils/initColumnDef";

function CarouselImgInput() {
    const { updateNewsInfo } = useSelector(state => state);
    const [carImgArr, setCarImgArr] = useState([]);

    useEffect(() => {
        if (isEmpty(updateNewsInfo[initColumnDef.hasURL])) {
            setCarImgArr([]);
            return;
        }

        if (updateNewsInfo[initColumnDef.hasURL]) {
            setCarImgArr(updateNewsInfo[initColumnDef.hasURL]);
        }
    }, [updateNewsInfo]);

    return (
        <div className="CarouselImgInput">
            <CustomButton className="AddPic" />
            {!isEmpty(carImgArr) && <DraggableTable carImgArr={carImgArr} />}
        </div>
    );
}

export default CarouselImgInput;

import React, { useEffect, useMemo, useState } from "react";
import { Form, TextArea } from "semantic-ui-react";
import useDebounce from "../../../../common/hooks/useDebounce";

const CustomSingleTextArea = ({
    cellId,
    // createState,
    disabled = false,
    setCallback,
    defaultValue,
    menuName
}) => {
    // const [state] = useContext(StoreContext);
    const [inputValue, setInputValue] = useState(
        Array.isArray(defaultValue) ? defaultValue[0] : defaultValue || ""
    );
    const debInputValue = useDebounce(inputValue, 500); // debounce value

    const handleChange = value => {
        if (disabled) {
            return;
        }

        setInputValue(value);
    };
    //
    useEffect(() => {
        if (debInputValue === null) return;

        if (debInputValue === "") {
            setCallback(cellId, null, menuName);
            return;
        }

        setCallback(cellId, debInputValue, menuName);
    }, [debInputValue]);

    // useEffect(() => {
    //     setInputValue(defaultValue);
    // }, [menuName]);

    return useMemo(
        () => (
            <Form>
                <TextArea
                    rows={5}
                    value={inputValue || ""}
                    onChange={(e, data) => handleChange(data.value)}
                />
            </Form>
        ),
        [cellId, inputValue]
    );
};

export default CustomSingleTextArea;

import { useState, useEffect } from "react";
import axios from "axios";

const useFetchData = (api, dependencies = []) => {
    const [data, setData] = useState([]);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const res = await axios.get(api);
                const tmpArray =
                    res?.data?.data.map(w => ({
                        value: w.perId || w.category || w.orgId,
                        label: w.label || w.labelWithLang
                    })) || [];
                setData(tmpArray);
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        };

        fetchData();
    }, dependencies);

    return data;
};

export default useFetchData;

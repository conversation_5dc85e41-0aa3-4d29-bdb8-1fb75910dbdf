import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, Ref, Table } from "semantic-ui-react";
import { DragD<PERSON><PERSON>ontext, Draggable, Droppable } from "react-beautiful-dnd";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import { tableEditPeakHeaderConfig } from "../../config";
import iconDrag from "../../../../../../../images/icon_drag.svg";
import PeakAct from "../../PeakMonosAction";
import { getReservedNewId } from "../../../../../../common/sheetCrud/utils";
import { deletePeakChapter, savePeakChapter } from "../../utils/utils";
import { isEmpty } from "../../../../../../../commons";
import CustomButton from "./CustomButton";
import Api from "../../../../../../../api/nmtl/Api";
import DeleteModal from "./DeleteModal";

const PeakChapterList = ({ list, user, websiteSubject }) => {
    const dispatch = useDispatch();
    const { editingPeakId, isEditedChapterList, deletingChapter } = useSelector(
        state => state
    );
    const [characters, updateCharacters] = useState(list);
    const [combinedChapterList, setCombinedChapterList] = useState([]);
    const [isPCLDraggingOver, setIsPCLDraggingOver] = useState(true);
    const [isDragged, setIsDragged] = useState(false);
    // react beautiful dnd settings
    const grid = 8;
    const getItemStyle = (isDragging, draggableStyle) => ({
        userSelect: "none",
        padding: grid * 2,
        margin: `0 0 ${grid}px 0`,
        width: "100%",
        background: isDragging ? "lightgreen" : "initial",
        ...draggableStyle
    });

    const getListStyle = isDraggingOver => ({
        background: isDraggingOver ? "initial" : "initial",
        padding: grid
    });

    const onDragStart = () => {
        setIsPCLDraggingOver(true);
    };

    const onDragEnd = ({ destination, source }) => {
        if (!destination) return;
        const items = Array.from(characters);

        const [reorderedItem] = items.splice(source.index, 1);
        items.splice(destination.index, 0, reorderedItem);
        const updatedItems = items.map((item, index) => ({
            ...item,
            order: (index + 1).toString()
        }));

        const sortedData = data => {
            const tmpData = data.sort((a, b) => {
                const numA = parseInt(a.pchId.slice(3), 10);
                const numB = parseInt(b.pchId.slice(3), 10);
                return numA - numB;
            });
            return tmpData;
        };

        const tmpNewItems = sortedData(
            JSON.parse(JSON.stringify(updatedItems))
        );
        const tmpPrevItems = sortedData(isEmpty(list) ? items : list);

        const combinedData = tmpPrevItems.map((prevData, index) => {
            const newData = tmpNewItems[index];
            return {
                prevData,
                newData
            };
        });

        setCombinedChapterList(combinedData);
        updateCharacters(updatedItems);
        setIsPCLDraggingOver(false);
        setIsDragged(true);
    };
    // 新增篇目
    const handleAddCate = async () => {
        const tmpPchId = await getReservedNewId("Publication");
        const initTemplate = {
            pchId: tmpPchId,
            order: "",
            title: ""
        };
        dispatch({ type: PeakAct.SET_ISADDINGCHAPTER, payload: true });
        dispatch({ type: PeakAct.SET_PEAKCHAPTER, payload: {} });
        dispatch({ type: PeakAct.SET_NEWPEAKCHAPTER, payload: initTemplate });
        dispatch({ type: PeakAct.SET_EDITINGPCHID, payload: tmpPchId });
        dispatch({ type: PeakAct.SET_OLDPEAKCHAPTER, payload: null });
        dispatch({ type: PeakAct.SET_ISEDITEDCATE, payload: true });
        dispatch({ type: PeakAct.SET_ISDELETEDCHAPTER, payload: false });
    };

    const handleEdit = i => {
        dispatch({ type: PeakAct.SET_ISADDINGCHAPTER, payload: false });
        dispatch({
            type: PeakAct.SET_ISEDITEDCATE,
            payload: true
        });
        dispatch({
            type: PeakAct.SET_EDITINGPCHID,
            payload: i.pchId
        });

        dispatch({
            type: PeakAct.SET_OLDPEAKCHAPTER,
            payload: {
                pchId: i.pchId,
                order: i.order,
                title: i.title
            }
        });
        dispatch({ type: PeakAct.SET_ISDELETEDCHAPTER, payload: false });
    };

    const handleDelete = () => {
        // 移除該書目與PEK的關聯
        deletePeakChapter(
            dispatch,
            editingPeakId,
            deletingChapter.pchId,
            user,
            websiteSubject
        )
            .then(() => {
                const tmpArr = [];
                const getChapterList = async () => {
                    const api = Api.getPeakMonoChapterList.replace(
                        "{peakMonoId}",
                        editingPeakId
                    );

                    const response = await axios.get(api).then(res => {
                        const length = res?.data?.data?.length + 1;
                        dispatch({
                            type: PeakAct.SET_CHAPTERLISTLENGTH,
                            payload: length
                        });
                        tmpArr.push(...res?.data?.data);
                    });

                    return response;
                };

                // 重新抓取PEK目前的書目列表
                getChapterList().then(() => {
                    const tmpPCH = JSON.parse(JSON.stringify(tmpArr)).sort(
                        (a, b) => a.order - b.order
                    );

                    const newPCH = tmpPCH
                        .map((item, index) => ({
                            ...item,
                            order: (index + 1).toString()
                        }))
                        .sort((a, b) => {
                            const numA = parseInt(a.pchId.slice(3), 10);
                            const numB = parseInt(b.pchId.slice(3), 10);
                            return numA - numB;
                        });

                    const generateUpdateData = (oldData, newData) =>
                        newData.map(newItem => {
                            const prevItem = oldData.find(
                                oldItem => oldItem.pchId === newItem.pchId
                            );
                            return {
                                newData: newItem,
                                prevData: prevItem
                            };
                        });

                    // 整理舊排序與新排序
                    const updateOrderData = generateUpdateData(tmpPCH, newPCH);

                    // 更新排序
                    const promises = updateOrderData
                        .filter(
                            item => item.prevData.order !== item.newData.order
                        )
                        .map(pch =>
                            savePeakChapter(
                                dispatch,
                                pch.prevData,
                                pch.newData,
                                user,
                                websiteSubject,
                                "Publication",
                                editingPeakId
                            )
                        );

                    Promise.all(promises)
                        .then(() => {
                            dispatch({
                                type: PeakAct.SET_ISEDITEDCHAPTERLIST,
                                payload: !isEditedChapterList
                            });
                            dispatch({
                                type: PeakAct.SET_ISDELMODALOPEN,
                                payload: false
                            });
                        })
                        .catch(error => {
                            console.error("Error:", error);
                        });
                });
            })
            .then(() => {
                dispatch({
                    type: PeakAct.SET_ISEDITEDCATE,
                    payload: false
                });
                dispatch({ type: PeakAct.SET_ISDELETEDCHAPTER, payload: true });
                setIsPCLDraggingOver(true);
            });
    };

    const handleSaveChapterOrder = () => {
        const promises = combinedChapterList
            .filter(item => item.prevData.order !== item.newData.order)
            .map(i =>
                savePeakChapter(
                    dispatch,
                    i.prevData,
                    i.newData,
                    user,
                    websiteSubject,
                    "Publication",
                    editingPeakId
                )
            );
        Promise.all(promises)
            .then(() => {
                dispatch({
                    type: PeakAct.SET_ISEDITEDCHAPTERLIST,
                    payload: !isEditedChapterList
                });
                dispatch({
                    type: PeakAct.SET_ISMODALOPEN,
                    payload: true
                });
                setIsPCLDraggingOver(true);
                setIsDragged(false);
            })
            .catch(error => {
                console.error("Error:", error);
            });
    };

    useEffect(() => {
        updateCharacters(list);
        list.sort((a, b) => a.order - b.order);
    }, [list]);

    return (
        <>
            <div className="topArea">
                <div className="topArea__title">
                    <h1>收錄作品</h1>
                </div>
                <div className="topArea__btn">
                    <Button
                        content="+ 新增書目"
                        style={{
                            background: "#e4f5e8",
                            color: "#21ba45",
                            fontSize: "12px",
                            cursor: "pointer",
                            textAlign: "center",
                            padding: "0.5rem 1rem",
                            borderRadius: "4px",
                            height: "100%"
                        }}
                        onClick={handleAddCate}
                    />
                </div>
            </div>
            <div className="bookArea">
                <DragDropContext
                    onDragEnd={onDragEnd}
                    onDragStart={onDragStart}
                >
                    <Table celled structured size="small" selectable>
                        <Table.Header>
                            <Table.Row>
                                {tableEditPeakHeaderConfig.map(i => (
                                    <Table.HeaderCell
                                        key={i.key}
                                        textAlign={i.display}
                                        colSpan={i.row}
                                    >
                                        {i.header}
                                    </Table.HeaderCell>
                                ))}
                            </Table.Row>
                        </Table.Header>
                        <Droppable droppableId="tableBody">
                            {(tProvided, tSnapshot) => (
                                // semantic-ui-react 的 table 需此 ref 才可使用react beautiful dnd
                                <Ref innerRef={tProvided.innerRef}>
                                    <Table.Body
                                        {...tProvided.droppableProps}
                                        style={getListStyle(
                                            tSnapshot.isDraggingOver
                                        )}
                                    >
                                        {characters.map((i, idx) => (
                                            <Draggable
                                                key={i.pchId}
                                                draggableId={i.pchId}
                                                index={idx}
                                            >
                                                {(tmpProvided, snapshot) => (
                                                    // semantic-ui-react 的 table 需此ref才可使用react beautiful dnd
                                                    <Ref
                                                        innerRef={
                                                            tmpProvided.innerRef
                                                        }
                                                    >
                                                        <Table.Row
                                                            key={i.id}
                                                            id="tableData"
                                                            {...tmpProvided.draggableProps}
                                                            {...tmpProvided.dragHandleProps}
                                                            style={getItemStyle(
                                                                snapshot.isDragging,
                                                                tmpProvided
                                                                    .draggableProps
                                                                    .style
                                                            )}
                                                        >
                                                            <Table.Cell
                                                                colSpan={1}
                                                                width={1}
                                                                textAlign="center"
                                                            >
                                                                <img
                                                                    src={
                                                                        iconDrag
                                                                    }
                                                                    alt="drag"
                                                                />
                                                            </Table.Cell>
                                                            <Table.Cell
                                                                colSpan={1}
                                                                width={1}
                                                                textAlign="center"
                                                            >
                                                                {i.order}
                                                            </Table.Cell>
                                                            <Table.Cell
                                                                verticalAlign="middle"
                                                                colSpan={12}
                                                                width={12}
                                                            >
                                                                {i.title}
                                                            </Table.Cell>
                                                            <Table.Cell
                                                                colSpan={1}
                                                                textAlign="center"
                                                            >
                                                                <Button
                                                                    style={{
                                                                        backgroundColor:
                                                                            "initial",
                                                                        margin:
                                                                            "0 0.5rem 0 0",
                                                                        padding:
                                                                            "0.3rem",
                                                                        color:
                                                                            "black",
                                                                        fontSize:
                                                                            "1.3rem",
                                                                        width:
                                                                            "100%"
                                                                    }}
                                                                    onClick={() =>
                                                                        handleEdit(
                                                                            i
                                                                        )
                                                                    }
                                                                    disabled={
                                                                        isDragged
                                                                    }
                                                                    icon="edit"
                                                                />
                                                            </Table.Cell>
                                                            <Table.Cell
                                                                colSpan={1}
                                                                textAlign="center"
                                                            >
                                                                <Button
                                                                    style={{
                                                                        backgroundColor:
                                                                            "initial",
                                                                        margin:
                                                                            "0",
                                                                        padding:
                                                                            "0.3rem",
                                                                        fontSize:
                                                                            "1.3rem"
                                                                    }}
                                                                    onClick={() => {
                                                                        dispatch(
                                                                            {
                                                                                type:
                                                                                    PeakAct.SET_DELETINGCHAPTER,
                                                                                payload: i
                                                                            }
                                                                        );
                                                                        dispatch(
                                                                            {
                                                                                type:
                                                                                    PeakAct.SET_ISDELMODALOPEN,
                                                                                payload: true
                                                                            }
                                                                        );
                                                                    }}
                                                                    disabled={
                                                                        isDragged
                                                                    }
                                                                    icon="trash"
                                                                />
                                                                <DeleteModal
                                                                    onClick={() => {
                                                                        handleDelete(
                                                                            i
                                                                        );
                                                                    }}
                                                                    message="請問是否確認刪除?"
                                                                />
                                                            </Table.Cell>
                                                        </Table.Row>
                                                    </Ref>
                                                )}
                                            </Draggable>
                                        ))}
                                        {tProvided.placeholder}
                                    </Table.Body>
                                </Ref>
                            )}
                        </Droppable>
                    </Table>
                </DragDropContext>
            </div>
            <div className="btnArea">
                <CustomButton
                    onClick={handleSaveChapterOrder}
                    disableBool={isPCLDraggingOver}
                    message="儲存成功"
                    content="儲存"
                    color="green"
                />
            </div>
        </>
    );
};

export default PeakChapterList;

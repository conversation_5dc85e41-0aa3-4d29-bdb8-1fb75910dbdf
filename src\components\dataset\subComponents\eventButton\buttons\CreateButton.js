import React, {
    useContext,
    useState,
    useEffect,
    useMemo,
    useCallback
} from "react";

// ui
import { <PERSON><PERSON>, Modal, Table } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

// common
import { isEmpty } from "../../../../../commons";

// custom
import {
    CREATE_ID,
    CREATED_PREFIX,
    REPLACE_ORG_LABEL,
    TO_CREATE_REPLACE_ORG
} from "../../../../common/sheetCrud/sheetCrudHelper";
import {
    convertToCreateState,
    getReservedNewId
} from "../../../../common/sheetCrud/utils";
import TableCellValue from "../../commonComp/TableCellValue";
import { isCorrectSuffix } from "../../../../../api/nmtl/ApiField";
import Api from "../../../../../api/nmtl/Api";
import getKeyBySingle from "../../../../../api/nmtl/ApiKey";
import getSingleByApi from "../../../../../api/nmtl/ApiSingle";
import { createNmtlData } from "../../../../../api/nmtl";

const CreateButton = () => {
    // button
    const [open, setOpen] = useState(false);

    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { sheet, mainSubject } = state.data;
    const { dataset } = mainSubject.selected;
    const { key: sheetName } = sheet.selected;
    const { header, headerFields } = sheet;

    const [createState, setCreateState] = useState(null);
    const [cHeaders, setCHeaders] = useState([]);
    // FIXME: 因為在按了 handleCreate 之後，createState 的 value 值被清掉了
    // 來不及找出原因，所以先將改變的值存在 myData
    const [myData, setMyData] = useState({});

    useEffect(() => {
        if (Object.keys(headerFields).length === 0) {
            return;
        }
        // firebase 設定 forCreate 才會顯示
        const headerCreate = header.filter(hd => hd.forCreate === "1");
        setCHeaders(headerCreate);
        setCreateState(convertToCreateState(headerCreate, headerFields));
    }, [headerFields]);

    const setToEmpty = () => {
        // clean checked
        // dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });
        // // clean edit record after updated
        // dispatch({ type: Act.DATA_CONTENT_ROW_CREATED_CLEAN });
        // dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });

        // 只要清掉 value 和 input 即可
        setCreateState(preState => {
            Object.keys(preState).forEach(cellId => {
                if (!preState[cellId].value) {
                    // eslint-disable-next-line no-param-reassign
                    preState[cellId].value = {};
                }
                // eslint-disable-next-line no-param-reassign
                preState[cellId].value = {};

                if (!preState[cellId].input) {
                    // eslint-disable-next-line no-param-reassign
                    preState[cellId].input = null;
                }
                // eslint-disable-next-line no-param-reassign
                preState[cellId].input = null;

                // 移除設定為需要新增的 options
                // eslint-disable-next-line no-param-reassign
                preState[cellId].options = preState[cellId].options.filter(
                    of => !of.id.startsWith(CREATED_PREFIX)
                );
            });

            return {
                ...preState
            };
        });
        setMyData({});
    };

    // eslint-disable-next-line no-unused-vars
    const setCallback = (cellId, rowId, idx, jsonVal) => {
        setCreateState(preState => {
            const newRow = {};

            if (Object.keys(jsonVal).indexOf("isOption") > -1) {
                // options, multi-options 必須帶一個固定值來判斷
                if (Object.keys(jsonVal).indexOf("value") > -1) {
                    newRow.value = jsonVal.value;
                }
                // 有些特殊值需要改
                if (Object.keys(jsonVal).indexOf("input") > -1) {
                    newRow.input = jsonVal.input;
                }
                if (Object.keys(jsonVal).indexOf("isLoading") > -1) {
                    newRow.isLoading = jsonVal.isLoading;
                }
                if (Object.keys(jsonVal).indexOf("options") > -1) {
                    newRow.options = jsonVal.options;
                }
            }

            return {
                ...preState,
                [cellId]: { ...preState[cellId], ...newRow }
            };
        });

        setMyData({ ...jsonVal, cellId });
    };

    const getCreateNmtlItemResult = async (item, newClass, newApi, cellId) => {
        //
        if (
            isEmpty(item) ||
            isEmpty(dataset) ||
            isEmpty(sheetName) ||
            isEmpty(cellId)
        ) {
            return { newSrcId: null };
        }

        const apiUrl = Api.getGeneric;
        const itemKey = getKeyBySingle(getSingleByApi(newApi));

        // 先 reserved id
        const newSrcId = await getReservedNewId(itemKey);
        if (apiUrl && itemKey) {
            let newItem = {
                graph: dataset,
                classType: newClass || itemKey,
                srcId: newSrcId || "",
                value: {
                    label: item
                }
            };
            if (cellId === REPLACE_ORG_LABEL) {
                newItem = {
                    graph: dataset,
                    classType: newClass || itemKey,
                    srcId: newSrcId || "",
                    value: {
                        [TO_CREATE_REPLACE_ORG]: item
                    }
                };
            }

            const createResult = await createNmtlData(
                user,
                apiUrl,
                dataset,
                sheetName,
                newItem
            ).then(res => res === "OK");
            return { newSrcId, createResult };
        }
        return { newSrcId: null };
    };

    const addToOptions = ({ newSrcId, newApi, label, cellId }) => {
        const newOpt = {
            id: newSrcId,
            label,
            value: newSrcId
        };

        // 如果此欄位為多重 type，增加到該 type
        // hasPublisher: ORG, PER
        if (newApi && Object.keys(headerFields).indexOf(newApi) > -1) {
            headerFields[newApi].push(newOpt);
        }

        // 更新目前的總列表
        if (!Object.keys(createState).indexOf(cellId) < 0) {
            // eslint-disable-next-line no-param-reassign
            createState[cellId] = {};
        }

        if (!createState[cellId].options) {
            // eslint-disable-next-line no-param-reassign
            createState[cellId].options = [];
        }
        createState[cellId].options.push(newOpt);
    };

    const handleCreate = async () => {
        let createItems = [];

        // 找出需要新增的 items
        Object.keys(createState).forEach(cellId => {
            // 從 options 取，而不從 value 取的原因是，如果多個選項，會只有取到一個
            let newItems = [];

            if (!isEmpty(createState[cellId]?.options)) {
                //
                newItems = createState[cellId].options
                    .filter(o => o.id.startsWith(CREATED_PREFIX))
                    .map(m => ({ ...m, ...{ cellId } }));
            }
            createItems = createItems.concat(newItems);
        });

        // 如果沒有任何需要 create 的，表示此為選擇已存在的 instance
        // 將此 instance 加入表格
        if (createItems.length === 0) {
            // valObj: {0: "xxx"} or {101: "xxx"}
            const { value: valObj, cellId } = myData;

            const oldItem = createState[cellId].options.filter(
                o => o.id === Object.values(valObj)[0]
            )[0];

            const { newValue, newClass } = isCorrectSuffix(
                cellId,
                oldItem.label
            );

            const newItem = {
                graph: dataset,
                classType: newClass,
                srcId: oldItem.id,
                value: {
                    label: newValue
                }
            };

            // console.log(newValue, newClass, newItem);
            const createResult = await createNmtlData(
                user,
                Api.getGeneric,
                dataset,
                sheetName,
                newItem
            ).then(res => res === "OK");

            if (createResult) {
                addToOptions({
                    newSrcId: oldItem.id,
                    newApi: null,
                    label: oldItem.label,
                    cellId
                });

                dispatch({
                    type: Act.DATA_MESSAGE,
                    payload: {
                        title: `${oldItem.label} 已加入資料集`,
                        error: 0,
                        renderSignal: `update-${new Date().getTime()}`
                    }
                });
            }

            setToEmpty();
            setOpen(false);
            return;
        }

        // create newSrcId 並且 append 到 options
        const promises = [];
        const newApiArr = [];
        createItems.forEach(item => {
            const { cellId, label } = item;
            // suffix feature
            const { newValue, newClass, newApi } = isCorrectSuffix(
                cellId,
                label
            );
            newApiArr.push({ newApi, cellId, label });
            promises.push(
                getCreateNmtlItemResult(newValue, newClass, newApi, cellId)
            );
        });

        // get results from promises
        const results = await Promise.allSettled(promises).then(res => res);

        let successCount = 0;
        let errorCount = 0;
        results.forEach((res, idx) => {
            // Array: [{
            // status: "fulfilled"
            // value: Object { newSrcId: "PER_c7a2a4e3_6d4c_4079_bf53_8989af378ec6_770", createResult: true }
            // ]
            const { status, value } = res;
            if (status === "fulfilled") {
                const { newApi, cellId, label } = newApiArr[idx];
                const { newSrcId } = value;

                if (newSrcId) {
                    addToOptions({ newSrcId, newApi, label, cellId });
                }
                successCount += 1;
            } else {
                errorCount += 1;
            }
        });

        dispatch({
            type: Act.DATA_MESSAGE,
            payload: {
                title: `${newApiArr.map(m => m.label).join("、")} 已創建完成`,
                success: successCount,
                error: errorCount,
                renderSignal: `update-${new Date().getTime()}`
            }
        });

        setToEmpty();
        setOpen(false);
    };

    const handleCreateClose = () => {
        // clean edit record after updated
        setToEmpty();
        // close
        setOpen(false);
    };

    const tableBody = useCallback(() => {
        if (!createState) {
            return null;
        }

        if (!cHeaders || isEmpty(cHeaders)) {
            return (
                <Table.Row>
                    <Table.Cell warning>
                        <p>please choose 「mainSubject」 and 「sheet」</p>
                    </Table.Cell>
                </Table.Row>
            );
        }

        return cHeaders.map((sh, shIdx) => (
            <Table.Row warning key={`create-button-row-${shIdx}`}>
                <Table.Cell key={`create-button-row-cell-label-${shIdx}`}>
                    {sh.label}
                    <br />
                    {sh.id}
                </Table.Cell>
                <Table.Cell
                    key={`create-button-row-cell-value-${shIdx}`}
                    style={{ width: "100%", minWidth: "100%" }}
                >
                    {/* {tableCell(sh, shIdx)} */}
                    <TableCellValue
                        actHeader={sh.id}
                        cellValue=""
                        ctIdx={CREATE_ID}
                        shIdx={shIdx}
                        createState={
                            createState
                                ? createState[sh.id]
                                : {
                                      isLoading: false,
                                      options: [],
                                    value: undefined
                                }
                        }
                        setCallback={setCallback}
                        isCreateNew
                    />
                </Table.Cell>
            </Table.Row>
        ));
    }, [cHeaders, createState]);

    return useMemo(
        () => (
            <Modal
                open={open}
                onOpen={() => setOpen(true)}
                onClose={handleCreateClose}
                trigger={
                    <Button
                        color="teal"
                        floated="right"
                        disabled={!cHeaders || isEmpty(cHeaders)}
                    >
                        新增
                    </Button>
                }
            >
                <Modal.Header>新增確認</Modal.Header>

                <Modal.Content image>
                    <Modal.Description>
                        <Table celled selectable>
                            <Table.Header>
                                <Table.Row>
                                    {!isEmpty(cHeaders) && (
                                        <React.Fragment>
                                            <Table.HeaderCell singleLine>
                                                欄位
                                            </Table.HeaderCell>
                                            <Table.HeaderCell singleLine>
                                                內容
                                            </Table.HeaderCell>
                                        </React.Fragment>
                                    )}
                                </Table.Row>
                            </Table.Header>
                            <Table.Body>{tableBody()}</Table.Body>
                        </Table>
                    </Modal.Description>
                </Modal.Content>

                <Modal.Actions>
                    <Button onClick={handleCreate} color="green">
                        確認
                    </Button>
                    <Button onClick={handleCreateClose} color="red">
                        取消
                    </Button>
                </Modal.Actions>
            </Modal>
        ),
        [cHeaders, createState, open]
    );
};

export default CreateButton;

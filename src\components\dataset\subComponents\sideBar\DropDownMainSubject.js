import React, { useContext, useEffect, useState } from 'react';

// ui
import { Dropdown, Loader } from 'semantic-ui-react';

// cloud
import { getMainSubject } from '../../../../api/firebase/cloudFirestore';

// store
import { StoreContext } from '../../../../store/StoreProvider';
import Act from '../../../../store/actions';

//
import { isEmpty } from '../../../../commons';
import { filterDataSet } from '../../../../commons/filterGroup';

const DisableMS = '0';
const MUST_EXCLUDE_MS = ['authority'];

const DropDownMainSubject = () => {
    // console.log('I am DropDownMainSubject');

    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { mainSubject, groupInfo } = state.data;
    // const { user } = state;
    const { value: keepMainSubjectValue } = mainSubject.selected;

    const [MSList, setMSList] = useState(undefined);
    const [error, setError] = useState(undefined);

    const handleGetMainSubject = async () => {
        const mainSubjectData = await getMainSubject();
        if (!mainSubjectData.error) {
            setMSList(
                Object.values(mainSubjectData)
                    .filter((ms) => filterDataSet(ms, groupInfo))
                    // filter out ms in MUST_EXCLUDE_MS
                    .filter((ms) => !MUST_EXCLUDE_MS.includes(ms.id))
                    .filter((ms) => ms.enable !== DisableMS)
                    .map((item) => ({
                        // react key
                        key: item.id,
                        // onChange event return value
                        value: item.label,
                        // ui show text
                        text: item.label,
                        // database  for sparql query
                        dataset: item.id,
                        // for ordering
                        seq: item.seq,
                    }))
                    .sort((itemA, itemB) => itemA.seq - itemB.seq),
            );
        } else {
            setError(mainSubjectData.error);
        }
    };

    useEffect(() => {
        if (isEmpty(groupInfo)) return;
        handleGetMainSubject();
    }, [groupInfo]);

    const handleClick = (event, { value }) => {
        // get selected item
        const item = MSList.filter((item) => item.value === value)[0];
        // update
        if (item) {
            dispatch({
                type: Act.DATA_MAINSUBJECT,
                payload: item,
            });
        }
        // refresh all parameter
        // refresh pagination when sheet changed
        dispatch({ type: Act.DATA_PAGINATION_ACTIVE_PAGE_INIT });
        // refresh changed when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });
        // refresh checked when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });
        // refresh created when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CREATED_CLEAN });
        // refresh created when sheet changed
        dispatch({ type: Act.DATA_SEARCH_KEYWORD_CLEAN });
        // clean content when sheet changed
        dispatch({ type: Act.DATA_SORTED_CLEAN });
        dispatch({ type: Act.DATA_TAB_COUNT_SHEET_CLEAN });
        dispatch({ type: Act.DATA_TAB_KEY_CLEAN });
        // clean content
        dispatch({ type: Act.DATA_CONTENT_ROWS_CLEAN });

        dispatch({ type: Act.DATA_SHEET_CLEAN });
    };

    return MSList ? (
        <Dropdown
            fluid
            search
            selection
            value={keepMainSubjectValue}
            options={MSList}
            onChange={handleClick}
            placeholder="資料集"
            className="DropDownMainSubject"
        />
    ) : error ? (
        <span>{error}</span>
    ) : (
        <Loader active inline="centered" />
    );
};

export default DropDownMainSubject;

* {
  box-sizing: border-box;
}

.EditNews {
  width: 90%;
  height: 100%;
  padding: 20px;
  flex-direction: column;
  display: flex;
  &_header {
    display: flex;
    justify-content: space-between;
  }
  &_main {
    height: 85%;
    &_header {
      display: flex;
      justify-content: flex-end;
    }
    &_search {
      margin-top: 1rem;
    }
    &_table {
      margin-top: 1rem;
      overflow-y: scroll;
      height: 80%;
      table thead {
        position: sticky !important;
        top: 0;
        z-index: 2;
      }
    }
  }

  &_footer {
    margin-top: 1rem;
    height: 100%;
    &_header {
      display: flex;
      justify-content: space-between;
    }
    &_NewsInfoArea {
      height: 85%;
      overflow-y: scroll;
      table {
        thead {
          text-align: center;
        }
      }
    }
  }
}

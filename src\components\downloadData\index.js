import React, { useContext, useEffect, useState } from 'react';

//
import { useLocation } from 'react-router-dom';

//
import '../../Style/downloadData.css';
import { Menu } from 'semantic-ui-react';
import { StoreContext } from '../../store/StoreProvider';
import Act from '../../store/actions';

import NoAuthority from '../pages/NoAuthority';
import downloadDataMenu from './menuConfig';
import { filterSubMenu } from '../../commons/filterGroup';

function DownloadData() {
    const [menu, setMenu] = useState(downloadDataMenu);
    const [itemIndex, setItemIndex] = useState(downloadDataMenu[0]);
    const location = useLocation();
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { role } = state.user;
    const { groupInfo } = state.data;

    useEffect(() => {
        const menuItem = downloadDataMenu
            .filter((element) => element.authority.indexOf(role) >= 0)
            .filter((subRoute) => filterSubMenu(subRoute, location, groupInfo));
        setItemIndex(menuItem[0]);
        setMenu(menuItem);
        if (menuItem.length > 0) {
            dispatch({
                type: Act.SET_DOWNLOADDATAACTIVENAME,
                payload: menuItem[0].name,
            });
        }
    }, [user, groupInfo]);

    return (
        <div
            className="DownloadData"
            style={{
                height: itemIndex.name === '數據統計' && 'calc(100vh - 58px)',
            }}
        >
            {menu.length > 0 && (
                <div className="leftArea">
                    <Menu pointing vertical style={{ width: '100%' }} className="menuBar">
                        {menu.map((element, index) => (
                            <Menu.Item
                                key={index}
                                name={element.name}
                                active={itemIndex.name === element.name}
                                onClick={() => {
                                    setItemIndex(element);
                                    dispatch({
                                        type: Act.SET_DOWNLOADDATAACTIVENAME,
                                        payload: element.name,
                                    });
                                }}
                            />
                        ))}
                    </Menu>
                </div>
            )}
            {menu.length > 0 && itemIndex !== null && (
                <div
                    className="rightArea"
                    style={{
                        border:
                            (itemIndex.name === '數據統計' || itemIndex.name === '歷史訊息') &&
                            'none',
                        overflowY:
                            (itemIndex.name === '數據統計' && 'auto') ||
                            (itemIndex.name === '歷史訊息' && 'initial'),
                        height: itemIndex.name === '數據統計' && '100%',
                    }}
                >
                    <itemIndex.component />
                </div>
            )}
            {menu.length === 0 && <NoAuthority />}
        </div>
    );
}

export default DownloadData;

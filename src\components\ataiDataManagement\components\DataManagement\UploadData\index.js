import React, { useState } from "react";
import { Loader } from "semantic-ui-react";
import "./UploadData.scss";
import uploadIcon from "../../../images/uploadIcon.svg";
import { useDropzone } from "react-dropzone";
import textConfig from "../../../textConifg";
import axios from "axios";
import getAtaiData from "../../../utils/utils";
import Api from "../../../../../api/nmtl/Api";
import { useDispatch, useSelector } from "react-redux";

const UploadData = () => {
    const state = useSelector(tmpState => tmpState.ataiMng);
    const { isUpdating } = state;
    const dispatch = useDispatch();
    const [isUploading, setIsUploading] = useState(false);

    const onDrop = async acceptedFiles => {
        setIsUploading(true);
        try {
            const results = await Promise.allSettled(
                acceptedFiles.map(file => {
                    const formData = new FormData();
                    formData.append("files", file);
                    return axios.post(Api.uploadAtaiData, formData);
                })
            );

            const errRes = results
                .filter(
                    result =>
                        result.status === "rejected" &&
                        result.reason.response.status === 400
                )
                .map(result => result.reason.response.data.message[0]);

            if (errRes.length > 0) {
                alert(`檔案重複: ${errRes}`);
            }
        } catch (e) {
            console.error("error:", e);
        } finally {
            getAtaiData(dispatch);
            setIsUploading(false);
        }
    };

    const { getRootProps, getInputProps, isDragActive } = useDropzone({
        accept: ".pdf",
        maxSize: textConfig.maxDataSize,
        onDrop
    });

    return (
        <>
            {isUploading ? (
                <div className="uploadData">
                    <Loader active inline="centered" className="loader" />
                </div>
            ) : (
                <div
                    className="uploadData"
                    {...getRootProps()}
                    style={{
                        backgroundColor: isUpdating && "#FAFAFA",
                        pointerEvents: isUpdating && "none"
                    }}
                >
                    <input {...getInputProps()} />
                    {isDragActive ? (
                        <p className="dragTitle">
                            {textConfig.draggingFileTitle}
                        </p>
                    ) : (
                        <p
                            className="dragTitle"
                            style={{ color: isUpdating && "#BDBDBD" }}
                        >
                            {textConfig.dropzoneTitle}
                        </p>
                    )}
                    <p className="or">{textConfig.uploadChoice}</p>
                    <button
                        className="uploadBtn"
                        style={{
                            backgroundColor: isUpdating && "#EEEEEE",
                            color: isUpdating && "#BDBDBD",
                            border: isUpdating && "none"
                        }}
                    >
                        <img src={uploadIcon} alt="" />
                        <span>{textConfig.uploadDataBtnTitle}</span>
                    </button>
                    {!isUpdating && (
                        <p className="explain">{textConfig.uploadExplain}</p>
                    )}
                </div>
            )}
        </>
    );
};

export default UploadData;

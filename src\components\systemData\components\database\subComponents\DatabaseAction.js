import axios from "axios";
import Api from "../../../../../api/nmtl/Api";
import { fileServerAPI, fileServerMethod } from "../../../../../api/fileServer";
import Act from "../../../../../store/actions";
import { createHistoryEvent } from "../../../../downloadData/components/history/common/common";
import uploadConfig from "../../../../toolPages/components/upload/uploadConfig";

/* 使 database 建立新的 database，並回傳檔案路徑。 */
const backupDB = (setDownloadLink, dispatch) => {
    dispatch({
        type: Act.DOWNLOAD_DB_FILE,
        payload: true
    });

    return new Promise((resolve, reject) => {
        /* 搜尋結果 */
        axios
            .get(Api.getDatabase)
            .then(response => {
                dispatch({
                    type: Act.DOWNLOAD_DB_FILE,
                    payload: false
                });
                setDownloadLink(
                    fileServerAPI.downloadDB.replace(
                        "[filename]",
                        response.data
                    )
                );
                resolve("Backup Success");
            })
            .catch(error => {
                console.log(error);
                // eslint-disable-next-line prefer-promise-reject-errors
                reject("Backup Failed");
            });
    });
};

const uploadDB = (file, dispatch, displayName, columns) => {
    const fetchUrl = fileServerAPI.uploadDB.replace("[filename]", file.name);
    const formData = new FormData();
    formData.append(uploadConfig.DbFormName, file);

    dispatch({
        type: Act.SAVE_UPLOAD_DB_FILE,
        payload: true
    });

    // fetch API
    return axios({
        method: fileServerMethod.uploadDB,
        url: fetchUrl,
        headers: {
            "Access-Control-Allow-Origin": "*"
        },
        data: formData
    })
        .then(res => {
            const { status, dbFileName } = res.data;
            if (res.status === 200 && status === "success") {
                const message = {
                    type: "success",
                    title: "儲存成功",
                    text: `檔名 ${dbFileName}`
                };
                dispatch({
                    type: Act.DATABASE_MSG,
                    payload: message
                });
                dispatch({
                    type: Act.SAVE_UPLOAD_DB_FILE,
                    payload: false
                });
                dispatch({
                    type: Act.INITIAL_DB_UPLOAD_STATE
                });

                const apiUrl = Api.uploadDatabase.replace(
                    "[dbFilename]",
                    dbFileName
                );

                createHistoryEvent(displayName, "更新", columns.join("/"));
                // TODO: check API
                // send filename to API
                return axios({
                    method: "get",
                    url: apiUrl,
                    headers: {
                        "Access-Control-Allow-Origin": "*"
                    }
                })
                    .then(_res => {
                        console.log("_res", _res);
                    })
                    .catch(err => {
                        console.log("err", err);
                    });
            }
            const message = {
                type: "success",
                title: "儲存失敗",
                text: ""
            };

            createHistoryEvent(
                displayName,
                "更新失敗",
                columns.join("/"),
                message.title
            );
            return dispatch({
                type: Act.DATABASE_MSG,
                payload: message
            });
        })
        .catch(err => {
            const message = {
                type: "success",
                title: "儲存失敗",
                text: err.message
            };

            createHistoryEvent(
                displayName,
                "更新失敗",
                columns.join("/"),
                message.title
            );
            return dispatch({
                type: Act.DATABASE_MSG,
                payload: message
            });
        });
};

export { backupDB, uploadDB };

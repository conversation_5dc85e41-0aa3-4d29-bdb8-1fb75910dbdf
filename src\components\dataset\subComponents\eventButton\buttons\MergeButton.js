import React, { useContext, useState, useEffect, useCallback } from "react";

// ui
import { Button, Label, Message, Modal, Table } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

// common
import { isEmpty } from "../../../../../commons";
import {
    convertToGenericSingle,
    PEICES_INFO
} from "../../../../common/sheetCrud/sheetCrudHelper";
import { createHistoryEvent } from "../../../../downloadData/components/history/common/common";
import { uuidv4 } from "../../../../../commons/utility";
import role, { mergeBtnRole } from "../../../../../App-role";

// api
import { updateNmtlData } from "../../../../../api/nmtl";
import Api from "../../../../../api/nmtl/Api";

const MergeButton = () => {
    // button
    const [open, setOpen] = useState(false);
    const [openYesNo, setOpenYesNo] = useState(false);

    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { content, sheet, mainSubject } = state.data;
    const { dataset } = mainSubject.selected;
    const { key: sheetName, contentWritePath, hasTab } = sheet.selected;
    const { checked, rows } = content;
    const { headerActiveName } = state.common;
    const { displayName } = state.user;
    const { tabClass, tabIndex } = sheet.tabKey;

    const [mergeValue, setMergeValue] = useState({});

    const safeRole = user?.role || role.anonymous;

    // 記錄在歷史訊息的欄位資訊
    const columns = [
        headerActiveName,
        mainSubject.selected.value,
        sheet.selected.value
    ];

    const handleMerge = async () => {
        const apiWritePath =
            contentWritePath || hasTab[tabIndex]?.contentWritePath || null;

        if (
            isEmpty(content) ||
            isEmpty(apiWritePath) ||
            isEmpty(dataset) ||
            isEmpty(sheetName)
        )
            return;

        // merge rows and changed
        if (isEmpty(checked) || isEmpty(rows)) return;

        // target to merge
        if (isEmpty(mergeValue)) return;

        const entryDst = convertToGenericSingle(
            mergeValue.value,
            dataset,
            contentWritePath
        );
        //
        let historyMsg = "";

        // promise list
        const promises = checked
            .filter(item => `${item.rowId}` !== mergeValue.idx)
            .map(item => {
                const apiUrl = Api.getMerge;

                const entrySrc = convertToGenericSingle(
                    rows[item.rowId],
                    dataset,
                    contentWritePath
                );

                historyMsg += `${JSON.stringify(entrySrc)}\n`;

                return updateNmtlData(
                    user,
                    apiUrl,
                    dataset,
                    sheetName,
                    entrySrc,
                    entryDst
                );
            });

        historyMsg += `合併至：\n${JSON.stringify(entryDst)}`;

        // get all result
        await Promise.allSettled(promises)
            .then(results => {
                // count
                let successCount = 0;
                let errorCount = 0;
                results.forEach(res => {
                    if (res.value) {
                        successCount += 1;
                    } else {
                        errorCount += 1;
                    }
                });
                const message = {
                    title: "Merge",
                    success: successCount,
                    error: errorCount,
                    renderSignal: `Merge-${new Date().getTime()}`
                };
                // alert message
                dispatch({
                    type: Act.DATA_MESSAGE,
                    payload: message
                });

                // 歷史紀錄，合併成功
                createHistoryEvent(
                    displayName,
                    "合併",
                    `${columns.join("/")}：${historyMsg}`
                );
            })
            .catch(error => {
                // 歷史紀錄，刪除失敗
                createHistoryEvent(
                    displayName,
                    "合併失敗",
                    `${columns.join("/")}：${historyMsg}`,
                    error
                );
            });

        // clean edit record after updated
        dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });

        if (checked.length === Object.values(rows).length) {
            dispatch({ type: Act.DATA_PAGINATION_ACTIVE_PAGE_INIT });
        }

        // close
        setOpen(false);
        setOpenYesNo(false);
    };

    // clear checked
    const handleInitChecked = useCallback(
        () =>
            !isEmpty(checked) &&
            dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN }),
        []
    );

    const getValue = mergeIdx => {
        if (tabClass === PEICES_INFO) {
            const targetIndex = checked.find(item => {
                const [rIdx, cIdx] = item.rowId.split("-");

                if (!rIdx || !cIdx) return "";

                return (
                    rows[rIdx]?.transList[cIdx].hasTranslationBook === mergeIdx
                );
            });

            const [rId, cId] = targetIndex.rowId.split("-");

            // 重組資料
            const targetData = Object.keys(rows[rId]).reduce((acc, cur) => {
                if (cur === "transList") {
                    return Object.assign(acc, rows[rId][cur][cId]);
                }
                acc[cur] = rows[rId][cur];

                return acc;
            }, {});

            return { tValue: targetData, tIndex: targetIndex.rowId };
        }

        return {
            tValue: Object.values(rows).find(
                rowData => rowData.srcId === mergeIdx
            ),
            tIndex: Object.keys(rows).find(idx => rows[idx].srcId === mergeIdx)
        };
    };

    useEffect(() => {
        handleInitChecked();
        setMergeValue({});
    }, [handleInitChecked]);

    useEffect(() => {
        // merge rows and changed
        if (isEmpty(checked) || isEmpty(rows)) {
            return;
        }

        let mergeId = [];
        if (tabClass === PEICES_INFO) {
            mergeId =
                checked
                    .map(item => {
                        const [rIdx, cIdx] = item.rowId.split("-");

                        if (!rIdx || !cIdx) return "";

                        return (
                            rows[rIdx]?.transList[cIdx].hasTranslationBook || ""
                        );
                    })
                    .filter(el => el)
                    .sort((a, b) => a - b) || [];
        } else {
            mergeId =
                checked
                    .map(item => rows[item.rowId]?.srcId)
                    .sort((a, b) => a - b) || [];
        }

        const { tValue, tIndex } = getValue(mergeId[0]);

        // get value --> mergeId[0]
        setMergeValue({
            value: tValue || {},
            idx: tIndex || undefined
        });
    }, [checked]);

    const mergeLabel = item => {
        if (tabClass === PEICES_INFO) {
            const [rIdx, cIdx] = item.rowId.split("-");

            if (!rIdx || !cIdx) return "";

            return rows[rIdx]?.transList[cIdx].hasTranslationBook || "";
        }

        return rows[item.rowId]?.srcId || "";
    };

    const tableBody = !isEmpty(rows) && !isEmpty(checked) && (
        <Table.Row>
            <Table.Cell>
                {checked.map(item => (
                    <div key={uuidv4()}>
                        <br />
                        <Label content={mergeLabel(item)} />
                    </div>
                ))}
            </Table.Cell>
            <Table.Cell>
                <Label
                    key={uuidv4()}
                    content={
                        (tabClass === PEICES_INFO
                            ? mergeValue?.value?.hasTranslationBook
                            : mergeValue?.value?.srcId) || ""
                    }
                />
            </Table.Cell>
        </Table.Row>
    );

    const mergeDisable = (
        <Modal
            size="small"
            open={open}
            onClose={() => setOpen(false)}
            onOpen={() => setOpen(true)}
            trigger={
                <Button
                    color="olive"
                    floated="right"
                    disabled={checked.length === 0}
                >
                    合併
                </Button>
            }
        >
            <Modal.Header>合併</Modal.Header>
            <Modal.Content scrolling>
                <Message error>
                    <Message.Header>權限不足</Message.Header>
                    <p>
                        {`此功能僅開放於 ${mergeBtnRole?.join(
                            ", "
                        )} 使用，若有需要請告知權限管理者。`}
                    </p>
                </Message>
            </Modal.Content>

            <Modal.Actions>
                <Button onClick={() => setOpen(false)} color="red">
                    關閉
                </Button>
            </Modal.Actions>
        </Modal>
    );

    // 權限控制
    if (!mergeBtnRole.includes(safeRole)) return mergeDisable;

    return (
        <Modal
            open={open}
            onClose={() => setOpen(false)}
            onOpen={() => setOpen(true)}
            trigger={
                <Button
                    color="olive"
                    floated="right"
                    disabled={checked.length <= 1}
                >
                    合併
                </Button>
            }
        >
            <Modal.Header>合併確認</Modal.Header>

            <Modal.Content image scrolling>
                <Modal.Description>
                    <span>請確認已勾選 ID 是否有誤</span>
                    <Table celled selectable>
                        <Table.Header>
                            <Table.Row>
                                {!isEmpty(rows) && !isEmpty(checked) && (
                                    <React.Fragment>
                                        <Table.HeaderCell singleLine>
                                            已勾選 ID
                                        </Table.HeaderCell>
                                        <Table.HeaderCell singleLine>
                                            合併為
                                        </Table.HeaderCell>
                                    </React.Fragment>
                                )}
                            </Table.Row>
                        </Table.Header>
                        <Table.Body>
                            {!isEmpty(checked) && !isEmpty(rows) && tableBody}
                        </Table.Body>
                    </Table>
                </Modal.Description>
            </Modal.Content>

            <Modal.Actions>
                <Button onClick={() => setOpenYesNo(true)} color="green">
                    確認
                </Button>
                <Button onClick={() => setOpen(false)} color="red">
                    取消
                </Button>
            </Modal.Actions>

            <Modal
                open={openYesNo}
                onClose={() => setOpenYesNo(false)}
                onOpen={() => setOpenYesNo(true)}
            >
                <Modal.Header>合併提示</Modal.Header>
                <Modal.Content>
                    <Message warning>
                        <p>
                            即將合併資料，如果您不知道此功能為何，請按「否」離開。
                        </p>
                    </Message>
                </Modal.Content>

                <Modal.Actions>
                    <Button onClick={handleMerge} color="green">
                        是
                    </Button>
                    <Button
                        onClick={() => setOpenYesNo(false) && setOpen(false)}
                        color="red"
                    >
                        否
                    </Button>
                </Modal.Actions>
            </Modal>
        </Modal>
    );
};

export default MergeButton;

import React from "react";
import { Message } from "semantic-ui-react";
import PropTypes from "prop-types";

export const ALERT_MSG_TYPE = {
    success: "success",
    info: "info",
    warning: "warning",
    error: "error"
};

const AlertMsg = ({ message }) => {
    const [visible, setVisible] = React.useState();

    // 手動清除 message
    const handleDismiss = () => {
        setVisible(false);
    };

    React.useEffect(() => {
        if (message?.title || message?.text) {
            setVisible(true);
        } else {
            setVisible(false);
        }
    }, [message]);

    if (!visible) return null;

    return (
        message && (
            <Message
                style={{ width: "100%" }}
                success={message.type === "success"}
                info={message.type === "info"}
                error={message.type === "error"}
                warning={message.type === "warning"}
                onDismiss={handleDismiss}
            >
                <Message.Header>{message.title}</Message.Header>
                <Message.Content>{message.text}</Message.Content>
            </Message>
        )
    );
};

AlertMsg.defaultProps = {
    message: { type: "", title: "", text: "" }
};

AlertMsg.propTypes = {
    // eslint-disable-next-line react/forbid-prop-types
    message: PropTypes.objectOf(PropTypes.any)
};

export default AlertMsg;

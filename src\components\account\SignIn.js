import React, { useContext } from "react";
import { Redirect } from "react-router";

// firebase
import firebase from "firebase/app";
import "firebase/auth";
import StyledFirebaseAuth from "react-firebaseui/StyledFirebaseAuth";

// ui
import { Divider, Container } from "semantic-ui-react";

// config
import firebaseUiConfig from "../../config/config-firebase-ui";

// store
import { StoreContext } from "../../store/StoreProvider";
import act from "../../store/actions";

// commons code
import { isEmpty, getFormatUser } from "../../commons";

// api
// eslint-disable-next-line import/no-duplicates
import { saveUser } from "../../api/firebase/realtimeDatabase";

// api
// eslint-disable-next-line import/no-duplicates
import { getUser } from "../../api/firebase/realtimeDatabase";
import { createHistoryEvent } from "../downloadData/components/history/common/common";

const SignIn = () => {
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;

    // add callbacks function to firebaseUiConfig
    firebaseUiConfig.callbacks = {
        // eslint-disable-next-line no-unused-vars
        signInSuccessWithAuthResult: (authResult, redirectUrl) => {
            // reFormat userInfo
            const userInfo = getFormatUser(authResult.user);
            const { uid, displayName, email } = userInfo;
            // add role data
            // userInfo.role = role.anonymous;
            const userData = uid && getUser(uid);
            if (isEmpty(userData)) {
                if (uid && (displayName || email)) {
                    // save user to reducer
                    dispatch({
                        type: act.FIREBASE_LOGIN_USER,
                        payload: userInfo
                    });
                    // save user to firebase realtime database
                    saveUser(uid, { ...userInfo });
                }
                // 登入成功
                createHistoryEvent(displayName, "登入");
            }
            return false;
        }
    };

    //  center style
    const textAlign = { textAlign: "center" };

    return isEmpty(user) ? (
        <React.Fragment>
            <br />
            <Container text>
                <h1 style={textAlign}>文學好臺誌後台</h1>
                <Divider />
            </Container>
            <StyledFirebaseAuth
                uiConfig={firebaseUiConfig}
                firebaseAuth={firebase.auth()}
            />
        </React.Fragment>
    ) : (
        <Redirect to="/" />
    );
};

export default SignIn;

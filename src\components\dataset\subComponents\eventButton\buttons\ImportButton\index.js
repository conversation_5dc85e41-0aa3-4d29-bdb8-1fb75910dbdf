import React, { useContext, useState } from "react";

// ui
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "semantic-ui-react";

// xlsx
import XLSX from "xlsx";
import InputFiles from "react-input-files";

// common
import { isEmpty } from "../../../../../../commons";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";
import Act from "../../../../../../store/actions";

// custom
import CustomTab from "./CustomTab";
import { createHistoryEvent } from "../../../../../downloadData/components/history/common/common";

const ImportButton = () => {
    const [open, setOpen] = useState(false);

    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    // get dataset(mainSubject) and sheet
    const { mainSubject, sheet } = state.data;
    const { headerActiveName } = state.common;
    const { displayName } = state.user;
    const { dataset: datasetLabel } = mainSubject.selected;
    const { key: sheetName } = sheet.selected;
    const { uploaded } = state.data;
    const { record } = uploaded;

    // 記錄在歷史訊息的欄位資訊
    const columns = [
        headerActiveName,
        mainSubject.selected.value,
        sheet.selected.value
    ];

    const onImportExcel = async files => {
        // 獲取上傳的文件對象
        // const { files } = file.target; // 通過FileReader對象讀取文件
        const fileReader = new FileReader();
        for (let index = 0; index < files.length; index++) {
            fileReader.name = files[index].name;
        }
        fileReader.onload = event => {
            try {
                // 判斷上傳檔案的類型 可接受的附檔名
                const validExts = [".xlsx", ".xls"];
                const fileExt = event.target.name;

                if (fileExt === null) {
                    throw new Error("檔案為空值");
                }

                const fileExtlastof = fileExt.substring(
                    fileExt.lastIndexOf(".")
                );
                if (validExts.indexOf(fileExtlastof) === -1) {
                    throw new Error(
                        `檔案類型錯誤，可接受的副檔名有：${validExts.toString()}`
                    );
                }

                const { result } = event.target; // 以二進制流方式讀取得到整份excel表格對象
                const workbook = XLSX.read(result, { type: "binary" });
                let data = []; // 存儲獲取到的數據 // 遍歷每張工作表進行讀取（這裡默認只讀取第一張表）
                for (const sheet in workbook.Sheets) {
                    if (workbook.Sheets.hasOwnProperty(sheet)) {
                        // 利用 sheet_to_json 方法將 excel 轉成 json 數據
                        data = data.concat(
                            XLSX.utils.sheet_to_json(workbook.Sheets[sheet])
                        ); // break; // 如果只取第一張表，就取消註釋這行
                    }
                }
                if (!isEmpty(data)) {
                    // 歷史紀錄，匯入成功
                    createHistoryEvent(displayName, "匯入", columns.join("/"));
                    // 塞入內容
                    dispatch({
                        type: Act.DATA_CONTENT_UPLOADED,
                        payload: data
                    });
                }
            } catch (e) {
                // 歷史紀錄，匯入失敗
                createHistoryEvent(
                    displayName,
                    "匯入錯誤",
                    columns.join("/"),
                    e
                );
                // 這裡可以拋出文件類型錯誤不正確的相關提示
                alert(e);
                // console.log("文件類型不正確");
            }
        }; // 以二進制方式打開文件
        fileReader.readAsBinaryString(files[0]);
    };

    const handleCancel = () => {
        setOpen(false);
    };

    const handleCompleted = () => {
        dispatch({
            type: Act.DATA_CONTENT_UPLOADED_RECORD_CLEAN
        });
        setOpen(false);
    };

    return (
        !isEmpty(datasetLabel) &&
        !isEmpty(sheetName) && (
            <Modal
                onClose={() => setOpen(false)}
                onOpen={() => setOpen(true)}
                open={open}
                trigger={
                    <Button color="orange" floated="right">
                        匯入
                    </Button>
                }
            >
                <Modal.Header>上傳檔案</Modal.Header>
                <Modal.Content scrolling>
                    <Modal.Description>
                        <Header>選擇檔案</Header>

                        <InputFiles
                            accept=".xlsx, .xls"
                            onChange={onImportExcel}
                        >
                            <Button positive>匯入</Button>
                        </InputFiles>

                        <Divider />

                        <CustomTab />
                    </Modal.Description>
                </Modal.Content>
                <Modal.Actions>
                    {record?.changeTabSignal === 1 ? (
                        <Button color="blue" onClick={handleCompleted}>
                            完成
                        </Button>
                    ) : (
                        <Button color="red" onClick={handleCancel}>
                            取消
                        </Button>
                    )}
                </Modal.Actions>
            </Modal>
        )
    );
};

export default ImportButton;

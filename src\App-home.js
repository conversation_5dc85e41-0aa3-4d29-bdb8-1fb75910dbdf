import authority from "./App-authority";

const isPublic = env => env === "true" || env === true;

const home = [
    {
        id: "home_account",
        icon: "users",
        href: "/Account",
        label: "權限管理",
        authority: authority.Account,
        public: true
    },
    {
        id: "home_dataset",
        icon: "file alternate outline",
        href: "/Dataset",
        label: "資料管理",
        authority: authority.Dataset,
        public: true
    },
    {
        id: "home_authority",
        icon: "clipboard alternate outline",
        href: "/Authority",
        label: "權威檔",
        authority: authority.Authority,
        public: true
    },
    {
        id: "home_systemData",
        icon: "hdd",
        href: "/SystemData",
        label: "系統相關",
        authority: authority.SystemData,
        public: true
    },
    {
        id: "home_toolPages",
        icon: "cog",
        href: "/ToolPages",
        label: "工具相關",
        authority: authority.ToolPages,
        public: true
    },
    {
        id: "home_websiteSetting",
        icon: "pencil alternate",
        href: "/WebsiteSetting",
        label: "網頁設定選項",
        authority: authority.WebsiteSetting,
        public: true
    },
    {
        id: "home_downloadData",
        icon: "database",
        href: "/DownloadData",
        label: "數據資料",
        authority: authority.DownloadData,
        public: true
    },
    {
        id: "home_ReportIssue",
        icon: "cog",
        href: "/ReportIssue",
        label: "問題回報",
        authority: authority.ReportIssue,
        public: true
    },
    {
        id: "home_ataiDataManagement",
        icon: "file alternate outline",
        href: "/AtaiDataManagement",
        label: "阿台資料管理",
        authority: authority.AtaiDataManagement,
        public: true
    }
];

export default home;

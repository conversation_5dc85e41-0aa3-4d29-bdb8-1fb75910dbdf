import React, { useContext, useState, useEffect } from "react";

// ui
import { Menu } from "semantic-ui-react";
import { Link, useHistory } from "react-router-dom";

// menu config
import menus from "../../App-header";

// store
import { StoreContext } from "../../store/StoreProvider";
import Act from "../../store/actions";

// common
import { isEmpty } from "../../commons";

// role
import { filterRoute } from "../../commons/filterGroup";

//
import GroupDropdown from "./components/GroupDropdown";

const Header = () => {
    const isLogin = JSON.parse(localStorage.getItem("isLogin"));
    const [activeItem, setActiveItem] = useState(undefined);
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { groupInfo } = state.data;
    const history = useHistory();
    const { pathname } = history.location;

    useEffect(() => {
        const findPath = menus.menuLeft.find(
            element => element.path === pathname
        );
        if (findPath) {
            dispatch({
                type: Act.SET_HEADERACTIVENAME,
                payload: findPath.name
            });
        }
    }, [pathname]);

    const handleItemClick = (e, { name }) => setActiveItem(name);

    return (
        <Menu>
            {/* console.log('debug: 只能執行一次') */}
            {menus &&
                menus.menuLeft &&
                menus.menuLeft
                    .filter(o => o.public)
                    .filter(route => filterRoute(user, route, groupInfo))
                    .map(item => (
                        <Menu.Item
                            key={item.id}
                            name={item.name}
                            active={activeItem === item.name}
                            onClick={handleItemClick}
                            as={Link}
                            to={item.path}
                        >
                            {item.name}
                        </Menu.Item>
                    ))}
            <Menu.Menu position="right">
                {menus &&
                    menus.menuRight &&
                    menus.menuRight.map(item => (
                        <Menu.Item
                            key={item.id}
                            name={item.name}
                            active={activeItem === item.name}
                            onClick={handleItemClick}
                            as={Link}
                            to={item.path}
                        >
                            {item.name}
                        </Menu.Item>
                    ))}
                {(isLogin || !isEmpty(user)) && <GroupDropdown />}
                {isLogin || !isEmpty(user) ? (
                    <Menu.Item
                        key="SignOut"
                        name="SignOut"
                        active={activeItem === "SignOut"}
                        onClick={handleItemClick}
                        as={Link}
                        to="/SignOut"
                    >
                        登出
                    </Menu.Item>
                ) : (
                    <Menu.Item
                        key="SignIn"
                        name="SignIn"
                        active={activeItem === "SignIn"}
                        onClick={handleItemClick}
                        as={Link}
                        to="/SignIn"
                    >
                        登入
                    </Menu.Item>
                )}
            </Menu.Menu>
        </Menu>
    );
};

export default Header;

import React, { useEffect } from "react";
//
import { useTable, useFilters } from "react-table";
import { Loader, Table } from "semantic-ui-react";
import { useSelector, useDispatch } from "react-redux";
import { uuidv4 } from "../../../../../commons/utility";
//
import FltAnaAct from "../../../../../reduxStore/fltAnalysis/FltAnaAct";

const Content = ({
    data,
    columns,
    columnVisibility: columnVisibilityDef,
    tableMeta,
    loading,
    contentType // ENUM['read','edit'] => 會影響儲存 react-table 實體
}) => {
    // store
    const dispatch = useDispatch();
    const { refreshTable } = useSelector(state => state.fltAna);

    //
    const [columnVisibility, setColumnVisibility] = React.useState({
        ...(columnVisibilityDef || {})
    });
    const [meta, setMeta] = React.useState(tableMeta);

    const CONTENT_TYPE = [
        {
            type: "read",
            tableReadyAct: _table => {
                dispatch({
                    type: FltAnaAct.setReadTable,
                    payload: _table
                });
            }
        }
    ];

    // Define a default UI for filtering
    function DefaultColumnFilter({
        column: { filterValue, preFilteredRows, setFilter }
    }) {
        return <div />;
    }

    const defaultColumn = React.useMemo(
        () => ({
            // Let's set up our default Filter UI
            Filter: DefaultColumnFilter
        }),
        []
    );

    const table = useTable(
        {
            data,
            columns,
            initialState: {
                hiddenColumns: Object.keys(columnVisibility)
            },
            meta: {
                ...(meta || {})
            },
            defaultColumn
        },
        useFilters
    );

    const {
        getTableProps,
        getTableBodyProps,
        headerGroups,
        footerGroups,
        rows,
        prepareRow
    } = table;

    useEffect(() => {
        // table 狀態變更時,傳到 store, 提供給 其他component 使用
        try {
            if (table) {
                const findSet = CONTENT_TYPE.find(
                    ct =>
                        ct.type.toLowerCase() ===
                        (contentType || "").toLowerCase()
                );
                if (findSet) findSet.tableReadyAct(table);
            }
        } catch (e) {
            console.log("e", e);
        }
    }, [table]);

    return React.useMemo(() => {
        if (loading)
            return (
                <div>
                    <Loader active inline="centered" />
                </div>
            );
        return (
            <div>
                <Table celled fixed {...getTableProps()}>
                    <Table.Header>
                        {headerGroups.map((headerGroup, idx) => (
                            <Table.Row
                                key={uuidv4()}
                                {...headerGroup.getHeaderGroupProps()}
                            >
                                {headerGroup.headers.map((column, subIdx) => (
                                    <Table.HeaderCell
                                        key={uuidv4()}
                                        style={{ textAlign: "center" }}
                                        {...column.getHeaderProps()}
                                    >
                                        {column.render("Header")}
                                        <div>
                                            {column.canFilter
                                                ? column.render("Filter")
                                                : null}
                                        </div>
                                    </Table.HeaderCell>
                                ))}
                            </Table.Row>
                        ))}
                    </Table.Header>
                </Table>
                <div
                    style={{
                        maxHeight: "calc(100vh - 300px)",
                        overflowY: "auto"
                    }}
                >
                    <Table celled fixed selectable>
                        <Table.Body {...getTableBodyProps()}>
                            {rows.map((row, idx) => {
                                prepareRow(row);
                                return (
                                    <Table.Row
                                        key={uuidv4()}
                                        {...row.getRowProps()}
                                    >
                                        {row.cells.map((cell, cIdx) => (
                                            <Table.Cell
                                                key={uuidv4()}
                                                {...cell.getCellProps()}
                                            >
                                                {cell.render("Cell")}
                                            </Table.Cell>
                                        ))}
                                    </Table.Row>
                                );
                            })}
                        </Table.Body>
                    </Table>
                    <Table celled fixed>
                        <Table.Header>
                            {footerGroups.map((group, idx) => (
                                <Table.Row
                                    key={uuidv4()}
                                    {...group.getFooterGroupProps()}
                                >
                                    {group.headers.map((column, subIdx) => (
                                        <Table.HeaderCell
                                            key={uuidv4()}
                                            style={{ textAlign: "center" }}
                                            {...column.getFooterProps()}
                                        >
                                            {column.render("Footer")}
                                        </Table.HeaderCell>
                                    ))}
                                </Table.Row>
                            ))}
                        </Table.Header>
                    </Table>
                </div>
            </div>
        );
    }, [data, table, columns, refreshTable.signal, loading]);
};

export default Content;

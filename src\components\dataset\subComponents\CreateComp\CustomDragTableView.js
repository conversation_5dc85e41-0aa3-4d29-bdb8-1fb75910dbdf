import React, {useContext, useEffect, useState} from "react";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import {readNmtlData} from "../../../../api/nmtl";
import Api from "../../../../api/nmtl/Api";
import {isEmpty} from "../../../../commons";
import {checkLabels, updateObjectValue} from "./helper";
import { Ref } from "semantic-ui-react";
import CustomSubTableView from "./CustomSubTableView";


const CustomDragTableView = ({
    keyName,
    createState,
    content,
    sheetName,
    setCallback,
    setCreateState,
    menuName,
    header,
    ct,
    data,
                                 filterIds,
    setLocCreateState,cloneLocalCreateState,setCloneLocalCreateStateFct,isInDraggingMode
}) => {
    const [state,dispatch] = useContext(StoreContext);
    const {
        authorOtherNameForDragTable,
        translatorOtherNameForDragTable,
        cloneCreateState,
        isFillAllAuthorOtherName,
        isFillAllTranslatorOtherName,
        isSearchingOver
    } = state.data;
    const { dataset } = state.data.mainSubject.selected;
    const [rowData, setRowData] = useState([cloneLocalCreateState]);
    const [options,setOptions] = useState([]);

    const sortNames = (names) => {
        return names.sort((a, b) => {
            const aIsZh = a.endsWith('@zh');
            const bIsZh = b.endsWith('@zh');
            return aIsZh && !bIsZh ? -1 : !aIsZh && bIsZh ? 1 : 0;
        });
    };

    const splitAndFlattenNames = (names) => {
        return names.flatMap(name => name.split('、'));
    };

    const getMinIndex = (label, labelIndexMap) => {
        const parts = label.split('、');
        return Math.min(...parts.map(part => labelIndexMap[part]));
    };

    const getOptionForOtherName = async (ds, ids) => {
        if (!ids) return;
        const filteredIds = ids.filter(i => i !== null);
        const res = await readNmtlData(Api.getOtherNameList.replace("{ds}", ds).replace("{ids}", filteredIds));
        const opt = res.data.map(el => ({ id: el.srcId, label: el.label, value: el.label }));
        setOptions(opt);
        return opt;
    };

    const processNamesAndDispatch = (keyName, tmpCloneLocalCreateState, otherNameArr, idArr) => {
        const isFillAllOtherName = idArr.length === otherNameArr.length / 2;
        dispatch({
            type: keyName === "hasAuthor" ? Act.DATA_IS_FILL_ALL_AUTHOR_OTHERNAME : Act.DATA_IS_FILL_ALL_TRANSLATOR_OTHERNAME,
            payload: isFillAllOtherName
        });
        getOptionForOtherName(dataset, tmpCloneLocalCreateState[keyName]).then(res => {
            const result = tmpCloneLocalCreateState[keyName].map(id => {
                const labels = otherNameArr.filter(label =>
                    res.some(item => item.id === id && item.label === label.replace(/@zh|@en/, ""))
                ).join("、");

                return { id, label: labels };
            });

            setRowData(result);
        });
    };

    useEffect(() => {
        if (isEmpty(cloneLocalCreateState)) return;

        const idArr = cloneLocalCreateState[keyName];
        const otherName = sortNames(cloneLocalCreateState[keyName === "hasAuthor" ? "authorName" : "translatorName"]);
        const otherNameArr = splitAndFlattenNames(otherName);

        processNamesAndDispatch(keyName, cloneLocalCreateState, otherNameArr, idArr);
    }, [cloneCreateState]);

    /**
     * 因cloneLocalCreateState是state，
     * 在更新id順序的時候，會有誤差，
     * 故需同時處理author及translator的id順序
     */
    useEffect(() => {
        if (isEmpty(cloneLocalCreateState)) return;

        const authorIdArr = JSON.parse(JSON.stringify(cloneLocalCreateState['hasAuthor']));
        const translatorIdArr = JSON.parse(JSON.stringify(cloneLocalCreateState['hasTranslator']));

        const sortedAuthorNames = sortNames(cloneLocalCreateState["authorName"]);
        const sortedTranslatorNames = sortNames(cloneLocalCreateState["translatorName"]);

        const otherAuthorNameArr = splitAndFlattenNames(sortedAuthorNames);
        const otherTranslatorNameArr = splitAndFlattenNames(sortedTranslatorNames);

        const isFillAllAuthorOtherName = cloneLocalCreateState['hasAuthor'].filter(i => i !== null).length === otherAuthorNameArr.length / 2;
        const isFillAllTranslatorOtherName = cloneLocalCreateState['hasTranslator'].filter(i => i !== null).length === otherTranslatorNameArr.length / 2;

        dispatch({ type: Act.DATA_IS_FILL_ALL_AUTHOR_OTHERNAME, payload: isFillAllAuthorOtherName });
        dispatch({ type: Act.DATA_IS_FILL_ALL_TRANSLATOR_OTHERNAME, payload: isFillAllTranslatorOtherName });

        getOptionForOtherName(dataset, authorIdArr.concat(translatorIdArr)).then(res => {
            const processResults = (idArr, otherNameArr, labelIndexMap) => {
                return idArr.map(id => {
                    const labels = otherNameArr.filter(label =>
                        res.some(item => item.id === id && item.label === label.replace(/@zh|@en/, ""))
                    ).join("、");

                    return { id, label: labels };
                }).sort((a, b) => getMinIndex(a.label, labelIndexMap) - getMinIndex(b.label, labelIndexMap));
            };

            const authorLabelIndexMap = {};
            const translatorLabelIndexMap = {};
            otherAuthorNameArr.forEach((label, index) => { authorLabelIndexMap[label] = index; });
            otherTranslatorNameArr.forEach((label, index) => { translatorLabelIndexMap[label] = index; });

            const sortedAuthorArr = processResults(authorIdArr, otherAuthorNameArr, authorLabelIndexMap);
            const sortedTranslatorArr = processResults(translatorIdArr, otherTranslatorNameArr, translatorLabelIndexMap);

            dispatch({ type: Act.DATA_SET_AUTHOR_OTHERNAME_FOR_DRAG_TABLE, payload: sortedAuthorArr });
            dispatch({ type: Act.DATA_SET_TRANSLATOR_OTHERNAME_FOR_DRAG_TABLE, payload: sortedTranslatorArr });

            const sortedAuthorIds = sortedAuthorArr.map(i => i.id);
            const sortedTranslatorIds = sortedTranslatorArr.map(i => i.id);

            const updateData = updateObjectValue(cloneLocalCreateState, 'hasAuthor', sortedAuthorIds);
            const updatedState = updateObjectValue(updateData, 'hasTranslator', sortedTranslatorIds);

            setCloneLocalCreateStateFct(updatedState);
            setRowData(keyName === 'hasAuthor' ? sortedAuthorArr : sortedTranslatorArr);
        });
    }, [createState]);

    const onDragEnd = ({ destination, source }) => {
        if (!destination) return;

        const keyValue = keyName === 'hasAuthor' ? isFillAllAuthorOtherName : isFillAllTranslatorOtherName;

        // 檢查rowData內的label是否有空字串，有空字串代表發表名稱沒填完整，需擋住
        // 當有搜尋值時，也需擋住使用者禁止拖拉
        if (!keyValue || !checkLabels(rowData) || !isSearchingOver) {
            dispatch({
                type: Act.DATA_SET_IS_FILL_ALL_OTHERNAME_MODAL_OPEN,
                payload: true
            });
            dispatch({
                type: Act.DATA_SET_IS_DRAGGING,
                payload: true
            });
            return;
        }

        const items = Array.from(rowData);

        const ids = rowData.map(obj => obj.id);
        const [reorderedIds] = ids.splice(source.index, 1);
        ids.splice(destination.index, 0, reorderedIds);

        const [reorderedItem] = items.splice(source.index, 1);
        items.splice(destination.index, 0, reorderedItem);
        const updatedItems = items.map((item, index) => ({
            ...item}));

        setRowData(updatedItems);

        let tmpObj;

        // 排序時，須將hasTranslator、translatorName及hasAuthor、authorName排序後的資料更新到createState
        const produceAuthorOrTranslator = () => {
            if (!authorOtherNameForDragTable || !translatorOtherNameForDragTable){
                return;
            }

            const { zhLabels, enLabels } = updatedItems.reduce((acc, item) => {
                const [zhPart, enPart] = item.label.split('、');
                acc.zhLabels.push(zhPart.split('@')[0]);
                acc.enLabels.push(enPart.split('@')[0]);
                return acc;
            }, { zhLabels: [], enLabels: [] });

            const zhData = `${zhLabels.join('、')}@zh`;
            const enData = `${enLabels.join('、')}@en`;

            const targetKey = keyName === 'hasAuthor' ? 'authorName' : 'translatorName';
           tmpObj =  updateObjectValue(cloneLocalCreateState, targetKey, [zhData, enData]);
        }

        produceAuthorOrTranslator()

        setLocCreateState(preState => ({
            ...preState,
            [keyName]: ids
        }));

        const updateData = updateObjectValue(tmpObj, keyName, ids);

        setCloneLocalCreateStateFct(updateData)
    };

    return (
        <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId="droppable">
                {provided => (
                    <Ref innerRef={provided.innerRef}>
                        <div {...provided.droppableProps}>
                            {rowData?.filter(item =>
                                !isEmpty(filterIds)? filterIds.includes(item.id) : true)
                                .map((value, idx) => {
                                // 從filter後的id陣列，找到原始陣列的index
                                let findIdx = content?.findIndex(
                                    id => id === value.id
                                );

                                if (keyName === 'hasAuthor' || keyName === 'hasTranslator') {
                                    findIdx = cloneLocalCreateState[keyName]?.findIndex(
                                        id => id === value.id
                                    );
                                }

                                return (
                                    <Draggable
                                        key={`CustomTableView-accordion-${findIdx}-${keyName}`}
                                        draggableId={`CustomTableView-accordion-${findIdx}-${keyName}`}
                                        index={idx}
                                    >
                                        {tProvided => (
                                            <Ref innerRef={tProvided.innerRef}>
                                                <div
                                                    {...tProvided.draggableProps}
                                                    {...tProvided.dragHandleProps}
                                                >
                                                    <CustomSubTableView
                                                        itemAt={findIdx}
                                                        sheetName={sheetName}
                                                        header={header}
                                                        content={ct}
                                                        setCallback={
                                                            setCallback
                                                        }
                                                        setCreateState={
                                                            setCreateState
                                                        }
                                                        createState={
                                                            createState
                                                        }
                                                        menuName={menuName}
                                                        rValue={value}
                                                        options={options}
                                                        cloneLocalCreateState={cloneLocalCreateState}
                                                        setCloneLocalCreateStateFct={setCloneLocalCreateStateFct}
                                                        isInDraggingMode={isInDraggingMode}
                                                    />
                                                </div>
                                            </Ref>
                                        )}
                                    </Draggable>
                                );
                            })}
                            {provided.placeholder}
                        </div>
                    </Ref>
                )}
            </Droppable>
        </DragDropContext>
    );
};

export default CustomDragTableView;

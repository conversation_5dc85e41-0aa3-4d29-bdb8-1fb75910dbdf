import role from './App-role';

const authority = {
    SignIn: [role.admin, role.editor, role.reader, role.developer, role.anonymous],
    SignOut: [role.admin, role.editor, role.reader, role.developer, role.anonymous],
    Home: [role.admin, role.editor, role.reader, role.developer, role.anonymous],
    State: [role.admin, role.developer],
    Account: [role.admin, role.developer],
    SystemData: [role.admin, role.developer],
    Dataset: [role.admin, role.editor, role.developer, role.reader],
    Authority: [role.admin, role.developer],
    Database: [role.admin, role.editor, role.developer],
    ToolPages: [role.admin, role.developer, role.reader],
    SystemInfo: [role.admin, role.editor, role.reader, role.developer],
    Ontology: [role.admin, role.developer],
    Api: [role.admin, role.developer],
    DownloadData: [role.admin, role.developer, role.reader],
    History: [role.admin, role.editor, role.developer],
    Stats: [role.admin, role.editor, role.reader, role.developer],
    AuthorityFile: [role.admin, role.editor, role.developer, role.reader],
    Env: [role.admin, role.developer],
    Upload: [role.admin, role.editor, role.developer, role.reader],
    FullTxtAna: [role.admin, role.developer],
    Example: [role.admin, role.editor, role.developer],
    WebsiteSetting: [role.admin, role.developer, role.reader],
    ReportIssue: [role.admin, role.developer, role.reader],
    AtaiDataManagement: [role.admin, role.editor, role.developer, role.reader],
    ImageLinkChecker: [role.admin, role.editor, role.reader, role.developer],
};

export default authority;

import React from "react";

// ui
import { Icon, Table } from "semantic-ui-react";

const CustomOperateTableRowCell = ({type}) => {
    switch (type) {
        case 'create':
            return (
                <Table.Cell
                    positive
                >
                    <Icon color='teal' name='check' />
                    {type}
                </Table.Cell>
            );
        case 'update':
            return (
                <Table.Cell
                    warning
                >
                    <Icon name='sync alternate' />
                    {type}
                </Table.Cell>
            );
        case 'delete':
            return (
                <Table.Cell
                    negative
                >
                    <Icon color='red' name='delete' />
                    {type}
                </Table.Cell>
            );
        default:
            return (
                <Table.Cell>
                    {type}
                </Table.Cell>
            );
    }
};

export default CustomOperateTableRowCell;
import firebase from "firebase";
// import { collection, add } from "firebase/firestore";

export default async function addHistoryEvent(data) {
    if (process.env.NODE_ENV === "production") {
        firebase
            .firestore()
            .collection(process.env.REACT_APP_HISTORY_LOG_FIREBASE_DOC_NAME)
            .add(data)
            .then(() => {
                console.log("Add History event success !!");
            })
            .catch(err => {
                console.log(err);
            });
    }
}

import React, { useContext } from 'react';
import { Divider, Grid, Segment, Container, Dimmer, Loader } from 'semantic-ui-react';

// components
import { QueryClient, QueryClientProvider } from 'react-query';
import UploadDbFile from './subComponents/UploadDbFile';
import DownloadDbFile from './subComponents/DownloadDbFile';
import DbAlertMsg from './subComponents/DbAlertMsg';

// store
import { StoreContext } from '../../../../store/StoreProvider';

// react-query

// Create a client
const queryClient = new QueryClient();

// ui
const DatabaseManagement = () => {
    const [state] = useContext(StoreContext);
    const { database } = state;
    const { dbFileUpload, dbFileDownload } = database;

    // clear useQuery cache
    queryClient.clear();

    return (
        <QueryClientProvider client={queryClient}>
            <Container>
                <Segment basic compact>
                    <h2>資料庫備份</h2>
                </Segment>
                <Divider />

                <Container text>
                    <Segment>
                        <Dimmer
                            active={dbFileUpload.isUploading || dbFileDownload.isDownloading}
                            inverted
                        >
                            <Loader inverted>Loading</Loader>
                        </Dimmer>
                    </Segment>
                    <Grid centered stackable>
                        <Grid.Row>
                            <Grid.Column width={8} />
                        </Grid.Row>
                    </Grid>
                    {/* alert masseage */}
                    <DbAlertMsg />

                    <Segment placeholder>
                        <Grid columns={2} textAlign="center">
                            <Divider vertical>Or</Divider>

                            <Grid.Row>
                                <Grid.Column>
                                    {/* download db file */}
                                    <DownloadDbFile />
                                </Grid.Column>

                                <Grid.Column>
                                    {/* upload db file */}
                                    <UploadDbFile />
                                </Grid.Column>
                            </Grid.Row>
                        </Grid>
                    </Segment>
                </Container>
            </Container>
        </QueryClientProvider>
    );
};

export default DatabaseManagement;

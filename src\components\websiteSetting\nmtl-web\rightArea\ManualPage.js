import React, { useContext, useEffect, useState } from "react";

// components
import LanguageSelect from "../../components/LanguageSelect";
import UpdateText from "../../components/UpdateText";
import SaveButton from "../../components/SaveButton";
import Selector from "../../components/Selector";

// general
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import {isEmpty} from "../../../../commons";

function ManualPage() {
    const [language, setLanguage] = useState("zh");
    const [state, dispatch] = useContext(StoreContext);
    const { originData, menuActiveItem, selectOption } = state.websiteSetting;
    const [dropDown, setDropDown] = useState({});
    const [showUpdateArea, setShowUpdateArea] = useState([]);

    useEffect(() => {
        // 下拉選單選項清空
        dispatch({
            type: Act.SET_SELECTOPTION,
            payload: ""
        });
    }, []);

    useEffect(() => {
        if (originData.length !== 0) {
            setDropDown(originData.find(element => element.id === menuActiveItem.key));
        }
    }, [originData]);

    useEffect(() => {
        if (selectOption === "" || isEmpty(dropDown)) return;
        let keys = Object.keys(dropDown[selectOption]).filter(
            key => key.indexOf("part") >= 0
        );
        keys = keys
            .map(key => key.replace("part", ""))
            .sort((a, b) => a - b)
            .map(number => `part${number}`);
        let tmpUpdatedArea = [];
        tmpUpdatedArea = keys.map((part, index) => (
            <div key={index} style={{marginTop: "5px", height: `${100 / keys.length}%`}}>
                {keys.length > 1 && <h3>{part.toUpperCase()}</h3>}
                <div style={{ height: keys.length > 1 ? "70%" : "100%" }}>
                    <UpdateText
                        dropDown={dropDown}
                        language={language}
                        option={{
                            column: "description",
                            priority: "No priority",
                            part
                        }}
                    />
                </div>
            </div>
        ));
        setShowUpdateArea(tmpUpdatedArea);
    }, [selectOption, language]);

    return (
        <div className="ManualPage">
            <div className="topArea">
                <Selector dropDown={dropDown} />
                <LanguageSelect language={language} setLanguage={setLanguage} />
            </div>
            <div className="bottomArea">
                <div className="updateArea">
                    {showUpdateArea}
                </div>
                <div className="btnArea">
                    <SaveButton language={language} />
                </div>
            </div>
        </div>
    );
}

export default ManualPage;

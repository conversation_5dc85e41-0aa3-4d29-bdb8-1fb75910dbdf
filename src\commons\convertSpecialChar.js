// const specialChar = ["'", '"', '\\'];

const escapeRegExpKeyword = string =>
    string.replace(/[-"':.*+?^${}()|[\]\\ ]/g, "\\\\$&"); // $& means the whole matched string
// const convertSpecialChar = data => {
// 	let newData = data;
// 	specialChar.forEach(c => {
// 		// found
// 		if (data.indexOf(c) > -1) {
// 			newData = newData.replaceAll(c, `\\${c}`);
// 		}
// 	});
// 	return newData;
// };

export default escapeRegExpKeyword;

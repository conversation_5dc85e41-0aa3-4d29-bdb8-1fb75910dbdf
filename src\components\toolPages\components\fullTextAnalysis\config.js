// import SelectColumnFilter from "./filter/SelectColumnFilter";
import role from "../../../../App-role";

// 是否為開發模式,利用此變數來變更 Table Footer 的顯示與否
const isLocalDevelop = process.env.NODE_ENV === "development";
const isDeveloper = userRole => [role.developer].includes(userRole);
// const isAdmin = userRole => [role.admin].includes(userRole);

// 資料欄位的 column
export const fltColumns = [
    {
        accessor: "id",
        label: "著作/文章id",
        r: true,
        group: "metadata",
        groupLabel: "基本資訊"
    },
    {
        accessor: "docId",
        label: "docId",
        // r: isLocalDevelop || isDeveloper,
        r: false,
        group: "metadata",
        groupLabel: "基本資訊"
    },
    {
        accessor: "docIds__MultiLang",
        label: "docId(多語系)",
        r: isLocalDevelop || isDeveloper,
        group: "metadata",
        groupLabel: "基本資訊"
    },
    {
        accessor: "graph",
        label: "資料集",
        r: true,
        group: "metadata",
        groupLabel: "基本資訊"
    },
    {
        accessor: "type",
        label: "類型",
        r: true,
        group: "metadata",
        groupLabel: "基本資訊"
    },
    {
        accessor: "_type",
        label: "classType",
        r: isLocalDevelop || isDeveloper,
        group: "metadata",
        groupLabel: "基本資訊"
    },
    {
        accessor: "label",
        label: "著作/文章名稱",
        r: true,
        group: "metadata",
        groupLabel: "基本資訊"
    },
    {
        accessor: "author",
        label: "作者",
        r: true,
        group: "metadata",
        groupLabel: "基本資訊"
    },
    {
        accessor: "source",
        label: "pdf生成來源",
        r: true,
        group: "metadata",
        groupLabel: "基本資訊"
    },
    {
        accessor: "fullWorkAvailableAt",
        label: "全文",
        r: false,
        group: "metadata",
        groupLabel: "基本資訊"
    },
    {
        accessor: "fullWorkLang",
        label: "全文語系",
        r: false,
        group: "metadata",
        groupLabel: "基本資訊"
    },
    {
        accessor: "fullWorkAvailableAts__MultiLang",
        label: "全文(多語系)",
        r: true,
        group: "metadata",
        groupLabel: "基本資訊"
    },
    {
        accessor: "fileAvailableAt",
        label: "pdf路徑",
        r: false,
        group: "metadata",
        groupLabel: "基本資訊"
    },
    {
        accessor: "fileLang",
        label: "pdf語系",
        r: false,
        group: "metadata",
        groupLabel: "基本資訊"
    },
    {
        accessor: "fileAvailableAts__MultiLang",
        label: "pdf路徑(多語系)",
        r: true,
        group: "metadata",
        groupLabel: "基本資訊"
    },
    {
        accessor: "fltId",
        label: "全文字詞id",
        r: false,
        group: "metadata",
        groupLabel: "基本資訊"
        // 在 Header Cell 放 filter, 因欄位過多,導致單一欄位太窄,操作上不方便
        // Filter: SelectColumnFilter,
        // filter: "includes"
    },
    {
        accessor: "fltIds__MultiLang",
        label: "全文字詞id(多語系)",
        r: true,
        group: "metadata",
        groupLabel: "基本資訊"
        // 在 Header Cell 放 filter, 因欄位過多,導致單一欄位太窄,操作上不方便
        // Filter: SelectColumnFilter,
        // filter: "includes"
    },
    {
        accessor: "doc2pdf__Status",
        label: "docx轉pdf",
        r: true,
        group: "status",
        groupLabel: "處理狀態"
    },
    {
        accessor: "md2pdf__Status",
        label: "全文轉pdf",
        r: true,
        group: "status",
        groupLabel: "處理狀態"
    },
    {
        accessor: "pdf2png__Status",
        label: "pdf轉圖片",
        r: true,
        group: "status",
        groupLabel: "處理狀態"
    },
    {
        accessor: "png2ocrJson__Status",
        label: "OCR及全文寫入DB",
        r: true,
        group: "status",
        groupLabel: "處理狀態"
    },
    {
        accessor: "service2segApi__Status",
        label: "斷詞",
        r: true,
        group: "status",
        groupLabel: "處理狀態"
    },
    {
        accessor: "service2nmtlApi__Status",
        label: "斷詞寫入DB",
        r: true,
        group: "status",
        groupLabel: "處理狀態"
    }
];

// 非資料欄位的 column
export const specialColumns = [{ accessor: "actions", label: "table action" }];

// 取得欄位的名稱
export const findColLabel = accessor =>
    fltColumns.find(
        col => col.accessor.toLowerCase() === accessor.toLowerCase()
    )?.label ||
    specialColumns.find(
        col => col.accessor.toLowerCase() === accessor.toLowerCase()
    )?.label ||
    accessor;

// table 需要的資料格式
const fltDataFormatter = [
    // ※important: uniq key 必須是 id,方便 crud 製作 srcEntry,dstEntry
    // key: 產生的 data 的 key
    // originKey: 原本 data 的 key
    // originKey 若為 string, 則代表此 key 的 value 為 string
    // def: 預設值
    // pass: 忽略處理,避免第一次 query API 有的 key, 但第二次沒有 該 key 時,第二次會覆蓋過去
    { key: "id", originKey: "id", def: "" },
    { key: "occurrence", originKey: "occurrence", def: "0", pass: true },
    { key: "label", originKey: "label", def: "" },
    { key: "type", originKey: "type", def: "" },
    {
        key: "docIds__MultiLang",
        originKey: "docIds",
        def: []
    },
    {
        key: "fullWorkAvailableAts__MultiLang",
        originKey: "fullWorkAvailableAts",
        def: []
    },
    // { key: "fileAvailableAt", originKey: "fileAvailableAt", def: "" },
    {
        key: "fileAvailableAts__MultiLang",
        originKey: "fileAvailableAts",
        def: []
    },
    {
        key: "fltIds__MultiLang",
        originKey: "fltIds",
        def: []
    },
    { key: "doc2pdf__Status", originKey: "doc2pdf", def: {} },
    { key: "md2pdf__Status", originKey: "md2pdf", def: {} },
    { key: "pdf2png__Status", originKey: "pdf2png", def: {} },
    { key: "png2ocrJson__Status", originKey: "png2ocrJson", def: {} },
    { key: "service2segApi__Status", originKey: "service2segApi", def: {} },
    { key: "service2nmtlApi__Status", originKey: "service2nmtlApi", def: {} }
];

export const columnDefault = [
    {
        classType: "FullText",
        title: "全文字詞分析",
        name: "全文字詞",
        classColumns: fltColumns,
        dataFormatter: fltDataFormatter
    }
];

export const getDataFormatter = classType =>
    columnDefault.find(
        col => col.classType.toLowerCase() === classType.toLowerCase()
    )?.dataFormatter;

export const dataFormatter = (data, classType) => {
    const formatter = getDataFormatter(classType);
    if (!formatter) return data;
    const safeData = Array.isArray(data) ? data : [data];
    return safeData.map(dt =>
        formatter.reduce(
            (newData, obj) => {
                const { key, originKey, def, requireKey, pass } = obj;
                if (pass) return newData;
                if (typeof originKey === "string") {
                    if (
                        (key in newData &&
                            JSON.stringify(newData[key]) ===
                                JSON.stringify(def)) ||
                        !(key in newData)
                    ) {
                        // eslint-disable-next-line no-param-reassign
                        newData[key] =
                            dt?.[originKey] != null ? dt?.[originKey] : def;
                    }
                } else if (Array.isArray(originKey)) {
                    if (!(requireKey in dt)) {
                        // eslint-disable-next-line no-param-reassign
                        newData[key] = def;
                    } else {
                        // eslint-disable-next-line no-param-reassign
                        newData[key] = originKey.reduce((subData, subObj) => {
                            const {
                                key: subKey,
                                originKey: subOriKey,
                                def: subDef
                            } = subObj;
                            // eslint-disable-next-line no-param-reassign
                            subData[subKey] =
                                dt?.[subOriKey] != null
                                    ? dt?.[subOriKey]
                                    : subDef;
                            return subData;
                        }, {});
                    }
                }
                return newData;
            },
            { ...dt }
        )
    );
};

// 處理流程設定, 可用來篩選要處理的步驟
export const FLT_PROCESS = {
    // doc2pdf: { name: "doc2pdf", label: "docx轉pdf" },
    md2pdf: { name: "md2pdf", label: "全文轉pdf" },
    pdf2png: {
        name: "pdf2png",
        id: "pdf2png",
        label: "pdf轉圖片"
    },
    png2ocrJson: {
        name: "png2ocrJson",
        label: "OCR"
    },
    service2segApi: {
        name: "service2segApi",
        label: "斷詞"
    },
    service2nmtlApi: {
        name: "service2nmtlApi",
        label: "寫入資料庫"
    }
};

export const HTTP_METHOD = {
    GET: "GET",
    POST: "POST",
    PUT: "PUT",
    DELETE: "DELETE"
};

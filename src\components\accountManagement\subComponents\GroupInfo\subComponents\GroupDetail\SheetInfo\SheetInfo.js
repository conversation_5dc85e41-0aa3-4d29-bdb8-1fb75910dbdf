import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
    Accordion,
    Checkbox,
    Form,
    Grid,
    Header,
    Menu
} from "semantic-ui-react";
import {
    getSheetHeader,
    getSheets
} from "../../../../../../../api/firebase/cloudFirestore";
import { sheetMapping } from "../../../../../../common/sheetCrud/utils";
import accMngAct from "../../../../../../../reduxStore/accManage/accManageAction";
import { isEmpty } from "../../../../../../../commons";

function SheetInfo() {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { groupData } = state;
    const [sheets, setSheets] = useState([]);
    const [error, setError] = useState(undefined);

    const handleGetSheets = async () => {
        const sheetsData = await getSheets();
        if (!sheetsData.error) {
            const tmpSheets = await Promise.all(
                sheetMapping(sheetsData).map(async el => {
                    if (el.useTab) {
                        const tabData = Object.keys(el.hasTab).reduce(
                            (acc, curType) => {
                                // const h =

                                acc[curType] = el.hasTab[curType].headers.sort(
                                    (itemA, itemB) => itemA.seq - itemB.seq
                                );

                                return acc;
                            },
                            {}
                        );

                        return { ...el, active: false, data: tabData };
                    }

                    const sortheader = await getSheetHeader(el.key);
                    return { ...el, active: false, data: sortheader };
                })
            );
            // set to sheets
            setSheets(tmpSheets);
        } else {
            setError(sheetsData.error);
        }
    };

    useEffect(() => {
        handleGetSheets();
    }, []);

    if (error) {
        return <span>{error}</span>;
    }

    const clickTitleChB = (evt, sheetItem) => {
        const { key, text, data } = sheetItem;
        evt.stopPropagation();
        const tmpGPData = JSON.parse(JSON.stringify(groupData));
        const findObj = tmpGPData?.sheets?.find(el => el?.sheet?.key === key);
        if (findObj) {
            findObj.column =
                findObj?.column?.length === data.length ? [] : data;
        } else {
            const tmpObj = { sheet: { key, text }, column: data };
            if (!tmpGPData.sheets) {
                // init groupData.sheets with empty array
                tmpGPData.sheets = [];
            }
            tmpGPData.sheets.push(tmpObj);
        }
        dispatch({
            type: accMngAct.SET_GROUPDATA,
            payload: tmpGPData
        });
    };

    const CheckBoxTitle = sheetItem => {
        const { text, key, data } = sheetItem;
        return (
            <Checkbox
                label={text}
                onClick={evt => clickTitleChB(evt, sheetItem)}
                checked={
                    groupData?.sheets?.find(el => el?.sheet?.key === key)
                        ?.column?.length === data.length
                }
                indeterminate={
                    groupData?.sheets?.find(el => el?.sheet?.key === key)
                        ?.column?.length !== data.length &&
                    groupData?.sheets?.find(el => el?.sheet?.key === key)
                        ?.column?.length > 0
                }
            />
        );
    };

    const clickAccTitle = el => {
        const tmpRouteInfo = JSON.parse(JSON.stringify(sheets));
        const findObj = tmpRouteInfo.find(item => item.key === el.key);
        if (findObj) {
            findObj.active = !findObj.active;
        }
        setSheets(tmpRouteInfo);
    };

    const clickChildChB = (colItem, dataItem) => {
        const { key, text } = colItem;
        const { id } = dataItem;
        const tmpGPData = JSON.parse(JSON.stringify(groupData));
        const findSheet = tmpGPData?.sheets?.find(el => el?.sheet?.key === key);
        if (findSheet) {
            const findCol = findSheet.column.find(el => el.id === id);
            if (findCol) {
                // remove from findSheet.column
                findSheet.column = findSheet.column.filter(
                    el => el.id !== findCol.id
                );
            } else {
                // add to findSheet.column
                findSheet.column.push(dataItem);
            }
        } else {
            // no sheet then add new one
            const tmpObj = { sheet: { key, text }, column: [dataItem] };
            if (!tmpGPData.sheets) {
                // init groupData.sheets with empty array
                tmpGPData.sheets = [];
            }
            tmpGPData.sheets.push(tmpObj);
        }
        dispatch({
            type: accMngAct.SET_GROUPDATA,
            payload: tmpGPData
        });
    };

    const cellCheckBox = (k, content, col) => (
        <Form>
            <Form.Group grouped widths="equal">
                <Grid>
                    <Grid.Row>
                        {content.map(dataItem => {
                            const { id, label } = dataItem;
                            return (
                                <Grid.Column width={5} key={`${k}-${id}`}>
                                    <Form.Checkbox
                                        label={label}
                                        value={label}
                                        onClick={() =>
                                            clickChildChB(col, dataItem)
                                        }
                                        checked={groupData?.sheets
                                            ?.find(el => el?.sheet?.key === k)
                                            ?.column?.some(el => el?.id === id)}
                                    />
                                </Grid.Column>
                            );
                        })}
                    </Grid.Row>
                </Grid>
            </Form.Group>
        </Form>
    );

    const CheckBoxForm = colItem => {
        const { key, data, useTab } = colItem;
        if (useTab && data) {
            return Object.keys(data).map(type => (
                <div>
                    <Header as="h4" style={{ margin: "0.5rem" }}>
                        {colItem.hasTab[type].label}
                    </Header>
                    {cellCheckBox(key, data[type], colItem)}
                </div>
            ));
        }

        return cellCheckBox(key, data, colItem);
    };

    return (
        <Accordion as={Menu} vertical fluid>
            {!isEmpty(sheets) &&
                sheets.map((el, idx) => (
                    <Menu.Item key={el.key}>
                        <Accordion.Title
                            active={el.active}
                            content={CheckBoxTitle(el)}
                            index={idx}
                            onClick={() => clickAccTitle(el)}
                        />
                        <Accordion.Content
                            active={el.active}
                            content={CheckBoxForm(el)}
                        />
                    </Menu.Item>
                ))}
        </Accordion>
    );
}

export default SheetInfo;

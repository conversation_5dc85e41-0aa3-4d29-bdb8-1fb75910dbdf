import React, { useContext, useEffect, useState } from "react";
import { Dropdown } from "semantic-ui-react";
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";
import { isEmpty } from "../../../../../commons";

function PgDD() {
    const [state, dispatch] = useContext(StoreContext);
    const { pagination } = state.data;
    const { totalPage, activePage: currentPage, totalCount } = pagination;
    const [optionArr, setOptionArr] = useState([]);

    useEffect(() => {
        const tmpOption = [];
        for (let i = 0; i < totalPage; i += 1) {
            tmpOption.push({
                key: i + 1,
                value: i + 1,
                text: i + 1
            });
        }
        setOptionArr(tmpOption);
    }, [totalPage]);

    const handleChange = (evt, data) => {
        dispatch({
            type: Act.DATA_PAGINATION_ACTIVE_PAGE,
            payload: data.value
        });
    };

    return (
        <React.Fragment>
            <div style={{ marginRight: "0.5rem" }}>
                <p>現在是第</p>
            </div>
            {!isEmpty(optionArr) && (
                <Dropdown
                    compact
                    selection
                    options={optionArr}
                    onChange={handleChange}
                    value={optionArr[currentPage - 1].value}
                />
            )}
            <div style={{ marginLeft: "0.5rem" }}>
                <p>
                    頁，共 {totalPage} 頁，共 {totalCount} 筆
                </p>
            </div>
        </React.Fragment>
    );
}

export default PgDD;

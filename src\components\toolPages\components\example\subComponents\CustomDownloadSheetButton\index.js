import React, { useContext, useState } from "react";

// ui
import { Button } from "semantic-ui-react";

import CustomExcelFile from "./CustomExcelFile";

// firebase api
import { getSheetHeader } from "../../../../../../api/firebase/cloudFirestore";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";

// common
import { isEmpty } from "../../../../../../commons";
import { createHistoryEvent } from "../../../../../downloadData/components/history/common/common";
import { filterColumn } from "../../../../../../commons/filterGroup";

const CustomDownloadSheetButton = () => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    // get dataset(mainSubject) and sheet
    const { selectedSheet } = state.example;
    const { key: sheetName } = selectedSheet;
    const { groupInfo } = state.data;
    const { headerActiveName, toolPagesActiveName } = state.common;
    const { displayName } = state.user;
    const columns = [headerActiveName, toolPagesActiveName];

    // rorce refresh CustomExcelFile component for download excel
    const [refreshKey, setRefreshKey] = useState(0);
    // isLoading
    const [isLoading, setIsLoading] = useState(false);
    // excel header
    const [excelHeader, setExcelHeader] = useState(undefined);
    // excel data

    // get sheet header
    const handleDownload = async () => {
        console.log("I am handleDownload");
        if (sheetName) {
            // set loading status
            setIsLoading(true);
            // get sheet header
            let header = await getSheetHeader(sheetName);

            // 根據群組資訊filter下載表單欄位
            header = header.filter(obj =>
                filterColumn(sheetName, obj, groupInfo)
            );
            //
            setExcelHeader(header);
            setRefreshKey(refreshKey + 1);

            createHistoryEvent(displayName, "下載", columns.join("/"));
        }
        // set loading status
        setIsLoading(false);
    };

    return (
        <React.Fragment>
            <Button
                disabled={isEmpty(sheetName)}
                loading={isLoading}
                onClick={handleDownload}
                color="blue"
            >
                下載公版表單
            </Button>
            {!isEmpty(excelHeader) && refreshKey !== 0 && (
                <CustomExcelFile
                    key={refreshKey}
                    name={sheetName}
                    data={[]}
                    header={excelHeader}
                />
            )}
        </React.Fragment>
    );
};

export default CustomDownloadSheetButton;

import React, { useEffect, useState } from "react";
import { Checkbox, Table } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import { isEmpty } from "../../../../../../../commons";
import textConfig from "../../../../Utils/textConifg";
import { clickChildChB, clickTitleChB } from "../commonUtils/commonUtils";
import accMngAct from "../../../../../../../reduxStore/accManage/accManageAction";
import optionList from "../../CustomSelectBar/optionList";
import { tableNames } from "../../../../Utils/compoConfig";

function GroupTable() {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { allGroupData, tableSelectPool, currentPage, pageNum } = state;
    const groupTableHeader = ["", "群組名稱", "群組人數"];
    const [detailInfo, setDetailInfo] = useState({});

    useEffect(() => {
        setDetailInfo({
            tableName: tableNames.groups,
            tableNames,
            data: allGroupData,
            tableSelectPool
        });
    }, [allGroupData, tableSelectPool]);

    useEffect(() => {
        dispatch({
            type: accMngAct.SET_TOTALPAGE,
            payload: Math.ceil(allGroupData.length / pageNum)
        });
    }, [allGroupData, pageNum]);

    const setEditGPMode = () => {
        dispatch({
            type: accMngAct.SET_EDITGROUPMODE,
            payload: true
        });
    };

    const editGroup = () => {
        setEditGPMode();
        dispatch({
            type: accMngAct.SET_GRODSELECTITEM,
            payload: optionList[0]
        });
    };

    const editMember = () => {
        setEditGPMode();
        const findObj = optionList.find(
            ({ name }) => name === textConfig.GROUPINFO_MENUBAR_MEMBERINFO
        );
        dispatch({
            type: accMngAct.SET_GRODSELECTITEM,
            payload: findObj
        });
    };

    const clickGroupTableRow = (evt, el) => {
        evt.stopPropagation();
        dispatch({
            type: accMngAct.SET_GROUPDATA,
            payload: el
        });
        dispatch({
            type: accMngAct.SET_GROUPFIRESTOREID,
            payload: el.id
        });
    };

    return (
        <Table celled structured size="small">
            <Table.Header>
                <Table.Row>
                    {groupTableHeader.map((content, idx) => {
                        if (idx === 0) {
                            return (
                                <Table.HeaderCell key={content} collapsing>
                                    <Checkbox
                                        onClick={() =>
                                            clickTitleChB(detailInfo, dispatch)
                                        }
                                        checked={
                                            tableSelectPool?.groups?.length ===
                                                allGroupData.slice(
                                                    (currentPage - 1) * pageNum,
                                                    currentPage * pageNum
                                                ).length &&
                                            allGroupData.length !== 0
                                        }
                                    />
                                </Table.HeaderCell>
                            );
                        }
                        return (
                            <Table.HeaderCell key={content}>
                                {content}
                            </Table.HeaderCell>
                        );
                    })}
                </Table.Row>
            </Table.Header>
            <Table.Body>
                {isEmpty(allGroupData) ? (
                    <Table.Row>
                        <Table.Cell
                            content={textConfig.NO_GROUPDATA}
                            colSpan={groupTableHeader.length}
                            textAlign="center"
                        />
                    </Table.Row>
                ) : (
                    allGroupData
                        .slice(
                            (currentPage - 1) * pageNum,
                            currentPage * pageNum
                        )
                        .map(el => (
                            <Table.Row
                                key={el.id}
                                onClick={evt => clickGroupTableRow(evt, el)}
                            >
                                <Table.Cell>
                                    <Checkbox
                                        onClick={() => {
                                            const infoObj = {
                                                rowId: el.id,
                                                tableSelectPool,
                                                poolName: tableNames.groups
                                            };
                                            clickChildChB(infoObj, dispatch);
                                        }}
                                        checked={
                                            tableSelectPool.groups.indexOf(
                                                el.id
                                            ) !== -1
                                        }
                                    />
                                </Table.Cell>
                                <Table.Cell
                                    style={{ cursor: "pointer" }}
                                    onClick={editGroup}
                                >
                                    {el.name}
                                </Table.Cell>
                                <Table.Cell
                                    style={{ cursor: "pointer" }}
                                    onClick={editMember}
                                >
                                    {el.members.length}
                                </Table.Cell>
                            </Table.Row>
                        ))
                )}
            </Table.Body>
        </Table>
    );
}

export default GroupTable;

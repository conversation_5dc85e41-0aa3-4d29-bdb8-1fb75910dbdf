import checkInstTag from "./checkInstTag";
import { splitTag } from "../config/config";
// import checkStr from "./checkStr";

const checkOnlyId = (cell, cellValue, propLabel, perOrgList) => {
    let tmpResStr = "";
    // 檢查ID唯一
    const allValue = cellValue.split(splitTag);
    const checkData = allValue.reduce((acc, next) => {
        const idList = perOrgList.filter(el => el.label === next);
        if (idList.length > 1) {
            acc[next] = idList.map(el => el.id);
        }
        return acc;
    }, {});

    if (Object.keys(checkData).length > 0) {
        const reason = `相同名稱有多個不同ID，請確認以下ID資料: ${JSON.stringify(
            checkData
        )}`;
        tmpResStr += `${cell.address}, [${cellValue}], 欄位:${propLabel}，${reason}。\n`;
    }

    return tmpResStr;
};

// const checkCellLangTag = (cell, cellValue, propLabel) => {
//     let tmpResStr = "";
//     const reg = /@([^@]+)@/;
//     const match = cellValue.match(reg);
//     if (match && !AllowedLang.includes(match[1])) {
//         const reason = `語系有誤，請使用下列清單提供的tag，帶在每行資料後面[${AllowedLang}]`;
//         tmpResStr += `${cell.address}, [${cell.value}], 欄位:${propLabel}，${reason}。\n`;
//     }
//     return tmpResStr;
// };

const checkZH = (cell, cellValue, propLabel) => {
    let tmpResStr = "";
    const allValue = cellValue.split(splitTag);
    const reg = /@([^@]+)@/;
    const check = allValue.every(elStr => elStr.match(reg)[1] === "zh");
    if (!check) {
        const reason = `每個名稱請帶'@zh'(半形符號)在最後一個'@'符號前`;
        tmpResStr += `${cell.address}, [${cellValue}], 欄位:${propLabel}，${reason}。\n`;
    }

    return tmpResStr;
};

const checkInstLabel = (cell, propLabel, perOrgList) => {
    let tmpResStr = "";
    if (cell.value) {
        // 檢查@PER、@ORG
        tmpResStr += checkInstTag(cell, propLabel);

        if (typeof cell.value === "string") {
            // 檢查每個名稱帶一個zh tag
            tmpResStr += checkZH(cell, cell.value, propLabel);

            // 檢查ID唯一
            tmpResStr += checkOnlyId(cell, cell.value, propLabel, perOrgList);
        } else if (Object.hasOwn(cell.value, "hyperlink")) {
            // 檢查每個名稱帶一個zh tag
            tmpResStr += checkZH(cell, cell.value.text, propLabel);

            // 檢查ID唯一
            tmpResStr += checkOnlyId(
                cell,
                cell.value.text,
                propLabel,
                perOrgList
            );
        }
        // else {
        //     tmpResStr += checkStr(cell, propLabel);
        // }
    }

    return tmpResStr;
};

export default checkInstLabel;

import React, { useContext, useEffect, useState } from "react";

// ui
import { <PERSON><PERSON>, <PERSON><PERSON>, Mo<PERSON> } from "semantic-ui-react";

// custom
import CustomTab from "./CustomTab";

// common
import { isEmpty } from "../../../../../../commons";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";

const DownloadButton = () => {
    const [open, setOpen] = useState(false);
    const [checkedRowIds, setCheckedRowIds] = useState([]);

    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    // get dataset(mainSubject) and sheet
    const { mainSubject, sheet, content } = state.data;
    const { checked, rows } = content;
    const { dataset: datasetLabel } = mainSubject.selected;
    const { key: sheetName } = sheet.selected;

    const handleCancel = () => {
        setOpen(false);
    };

    useEffect(() => {
        if (checked.length === 0 || rows.length === 0 || isEmpty(checked)) {
            setCheckedRowIds([]);
        } else {
            // 過濾掉未融合的 Id
            const newChecked = checked.filter(
                el => !rows[el.rowId].unintegrated
            );

            setCheckedRowIds(newChecked);
        }
    }, [checked]);

    return (
        !isEmpty(datasetLabel) &&
        !isEmpty(sheetName) && (
            <Modal
                onClose={() => setOpen(false)}
                onOpen={() => setOpen(true)}
                open={open}
                trigger={
                    <Button
                        color="orange"
                        floated="right"
                        disabled={checked.length !== checkedRowIds.length}
                    >
                        下載
                    </Button>
                }
            >
                <Modal.Header>下載檔案</Modal.Header>
                <Modal.Content scrolling>
                    <Modal.Description>
                        <Header>選擇下載方式</Header>

                        {/* 下載分頁 */}
                        <CustomTab />
                    </Modal.Description>
                </Modal.Content>
                <Modal.Actions>
                    <Button color="red" onClick={handleCancel}>
                        取消
                    </Button>
                </Modal.Actions>
            </Modal>
        )
    );
};

export default DownloadButton;

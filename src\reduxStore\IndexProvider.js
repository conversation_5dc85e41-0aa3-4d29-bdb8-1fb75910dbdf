/* eslint-disable no-underscore-dangle */
import React, { useEffect, useState } from "react";
import PropTypes, {object} from "prop-types";
import { createStore } from "redux";
import { Provider } from "react-redux";
import axios from "axios";

// 將 combineReducer 後的 Reducer import
import rootReducer from "./rootReducer";

const store = createStore(
    rootReducer /* preloadedState, */,
    window.__REDUX_DEVTOOLS_EXTENSION__ && window.__REDUX_DEVTOOLS_EXTENSION__()
);

const IndexProvider = ({ children }) => (
    <Provider store={store}>{children}</Provider>
);

export default IndexProvider;

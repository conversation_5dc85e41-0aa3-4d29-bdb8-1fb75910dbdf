import React, { useState } from "react";
import { useDropzone } from "react-dropzone";

import dragImage from "../../../images/dragImage.svg";
import rejectUploadFileMsg from "./rejectUploadFileMsg";
import { uploadHandleError, getImage } from "../../../api/firebase/storage";
import UploadConfig from "../../toolPages/components/upload/uploadConfig";

const thumbsContainer = {
  display: "flex",
  flexDirection: "row",
  flexWrap: "wrap",
  marginTop: 16,
};

const thumb = {
  display: "inline-flex",
  marginBottom: 8,
  marginRight: 8,
  width: "100%",
  height: "100%",
  padding: 4,
  boxSizing: "border-box",
};

const thumbInner = {
  display: "flex",
  minWidth: 0,
  overflow: "hidden",
};

const img = {
  display: "block",
  width: "100%",
  height: "100%",
};

const warningMsgStyle = {
  margin: "10px",
  fontSize: "1.5rem",
  color: "red",
};

// 接收特定的副檔名
function rejectFile(allAcceptedFiles, { setWarningMsg, validType }) {
  // 發生reject file時，要記錄錯誤原因
  const rejectEvent =
    validType.indexOf(allAcceptedFiles.type.split("/")[1].toLowerCase()) < 0
      ? true
      : false;

  rejectEvent &&
    rejectUploadFileMsg(allAcceptedFiles, {
      reason: "INVALID_FORMAT",
      setWarningMsg,
      warningMsg: `File type is not belong to one of following ${validType.join(
        "、"
      )}. \n`,
    });

  return rejectEvent;
}

function DropFileRegion({
  path,
  setImageDownloadPath,
  setUploading,
  validType = ["jpg", "jpeg", "png", "webp", "avif"],
  logoFiles,
  setLogoFiles,
  isVrMuseum,
}) {
  const [warningMsg, setWarningMsg] = useState("");
  const [files, setFiles] = useState(logoFiles || []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    multiple: false, // 不能一次丟多個檔案
    accept: "image/*",
    maxFiles: 1, // 上傳檔案數量總數最多一個
    maxSize: 4194304, // 上傳檔案最多4MB
    onDrop: (acceptedFiles) => {
      if (
        acceptedFiles.length === 0 ||
        // check accepted image format
        rejectFile(acceptedFiles[0], { setWarningMsg, validType })
      ) {
        isVrMuseum && setUploading("disabled");
        return;
      }
      setUploading(true);
      setWarningMsg(""); // clear warning message
      const [tmpFile] = acceptedFiles.map((file) =>
        Object.assign(file, {
          // 預覽圖片用的URL
          preview: URL.createObjectURL(file),
        })
      );
      setFiles([tmpFile]);

      if (isVrMuseum) {
        setLogoFiles([tmpFile]);
        setUploading(false);
      } else {
        // path => "frontend-settings/MainCardImage"
        uploadHandleError(tmpFile, `${path}/${tmpFile.name}`).then((result) => {
          if (result === "Upload finish") {
            getImage(`${path}/${tmpFile.name}`).then((urlToken) => {
              const { url } = urlToken;
              setImageDownloadPath(url);
              setUploading(false);
            });
          }
        });
      }
    },
    // 紀錄超出maxSize的reject file message
    onDropRejected: (allRejectFile) => {
      // check fle size
      const isInvalidSize =
        allRejectFile[0]?.errors.at(0)?.code.indexOf("file-too-large") !== -1;

      isInvalidSize &&
        rejectUploadFileMsg(allRejectFile[0].file, {
          reason: "EXCEED_MAX_SIZE",
          setWarningMsg,
        });

      isVrMuseum && setUploading("disabled"); // disabled confirm button
    },
  });

  const thumbs = files.map((file) => (
    <div style={thumb} key={file.name}>
      <div style={thumbInner}>
        {/* eslint-disable-next-line jsx-a11y/alt-text */}
        <img src={file.preview} style={img} />
      </div>
    </div>
  ));

  return (
    <div>
      <div {...getRootProps()} className="dropzone">
        <input {...getInputProps()} />
        {isDragActive ? (
          <div style={{ height: "50vh", width: "50vh" }}>
            <p>拖曳圖片至此....</p>
          </div>
        ) : (
          <div>
            <div className="Path-42">
              <img src={dragImage} alt="drapImage_svg" />
            </div>
            <p>拖曳圖片至此或點擊此區以選取圖片</p>
          </div>
        )}
      </div>
      {warningMsg.length > 0 && (
        <aside style={warningMsgStyle}>{warningMsg}</aside>
      )}
      <aside style={thumbsContainer}>{thumbs}</aside>
    </div>
  );
}

export default DropFileRegion;

import React, {useContext, useState} from "react";
import { Button } from "semantic-ui-react";
import Act from "../../../store/actions";
import {StoreContext} from "../../../store/StoreProvider";

function CancelButton() {
    const [state, dispatch] = useContext(StoreContext);
    const changeBtnStatus = () => {
        dispatch({
            type: Act.SET_ISEDITEDDISABLE,
            payload: true
        });
    };

    return (
        <div style={{display: 'flex', justifyContent: 'center', alignItems: 'center'}}>
            <Button primary onClick={changeBtnStatus}>取消</Button>
        </div>
    );
}

export default CancelButton;

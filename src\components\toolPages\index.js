import React, { useContext, useEffect, useState } from 'react';

import '../../Style/toolPages.css';
import { Menu } from 'semantic-ui-react';
import { useHistory, useLocation } from 'react-router-dom';
import queryString from 'query-string';
import NoAuthority from '../pages/NoAuthority';
import Act from '../../store/actions';
import { StoreContext } from '../../store/StoreProvider';
import { uuidv4 } from '../../commons/utility';
import toolPagesMenu from './config';
import { filterSubMenu } from '../../commons/filterGroup';

function ToolPages() {
    // route
    const history = useHistory();
    const {
        location: { search },
    } = history;
    const { tab } = queryString.parse(search);
    //
    const [menu, setMenu] = useState(toolPagesMenu);
    const [itemIndex, setItemIndex] = useState(toolPagesMenu[0]);
    const [state, dispatch] = useContext(StoreContext);
    const { role } = state.user;
    const location = useLocation();
    const { groupInfo } = state.data;

    useEffect(() => {
        const menuItem = toolPagesMenu
            .filter((element) => element.authority.indexOf(role) >= 0)
            .filter((subRoute) => filterSubMenu(subRoute, location, groupInfo));
        const tmpMenuItem = tab
            ? menuItem.find(({ id }) => id === tab) || menuItem[0]
            : menuItem[0];
        setItemIndex(tmpMenuItem);
        setMenu(menuItem);

        if (menuItem.length > 0) {
            dispatch({
                type: Act.SET_TOOLPAGESACTIVENAME,
                payload: menuItem[0].name,
            });
        }
    }, [tab, groupInfo]);

    return (
        <div className="ToolPages">
            {menu.length > 0 && (
                <div className="leftArea">
                    <Menu pointing vertical style={{ width: '100%' }} className="menuBar">
                        {menu.map((element) => (
                            <Menu.Item
                                key={uuidv4()}
                                name={element.name}
                                active={itemIndex.name === element.name}
                                onClick={() => {
                                    history.push({
                                        search: `?${queryString.stringify({
                                            tab: element.id,
                                        })}`,
                                    });
                                    dispatch({
                                        type: Act.SET_TOOLPAGESACTIVENAME,
                                        payload: element.name,
                                    });
                                }}
                            />
                        ))}
                    </Menu>
                </div>
            )}

            {menu.length > 0 && itemIndex !== null && (
                <div className="rightArea">
                    <itemIndex.component />
                </div>
            )}
            {menu.length === 0 && <NoAuthority />}
        </div>
    );
}

export default ToolPages;

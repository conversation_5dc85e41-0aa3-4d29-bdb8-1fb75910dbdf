import React, { useContext, useState, useEffect } from "react";

// store
import {
    <PERSON>ton,
    Modal,
    Card,
    Message,
    Divider,
    Checkbox
} from "semantic-ui-react";
import { StoreContext } from "../../../../../../store/StoreProvider";

// common
import { isEmpty } from "../../../../../../commons";
import CustomCheckBox from "./CustomCheckBox";
import Act from "../../../../../../store/actions";

const HeaderButton = () => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { sheet } = state.data;
    const { header, activeHeader, activeHeaderCount } = sheet;
    const { key: sheetName } = sheet.selected;
    const [curHeader, setCurHeader] = useState({});
    // handle button
    const [open, setOpen] = useState(false);

    // handle checkbox
    let allChecked = false;
    if (
        header.length > 0 &&
        Object.keys(curHeader).length > 0 &&
        Object.keys(curHeader).indexOf(sheetName) > -1
    ) {
        allChecked = header.length === curHeader[sheetName].length;
    }

    useEffect(() => {
        if (activeHeader) {
            setCurHeader(activeHeader);
        }
    }, [activeHeader]);

    const handleAllChecked = () => {
        if (allChecked) {
            setCurHeader(pre => ({
                ...pre,
                [sheetName]: header.slice(0, activeHeaderCount)
            }));
        } else {
            setCurHeader(pre => ({ ...pre, [sheetName]: header }));
        }
    };

    const handleOnClose = () => {
        dispatch({
            type: Act.DATA_SHEET_ACTIVATE_HEADER,
            payload: {
                [sheetName]: curHeader[sheetName].sort((a, b) => a.seq - b.seq)
            }
        });
        setOpen(false);
    };

    return (
        <Modal
            open={open}
            onClose={() => setOpen(false)}
            onOpen={() => setOpen(true)}
            trigger={
                <Button
                    color="green"
                    floated="right"
                    disabled={!header || isEmpty(header)}
                >
                    標題
                </Button>
            }
        >
            <Modal.Header>標題確認</Modal.Header>

            <Modal.Content scrolling>
                <Modal.Description>
                    <Card.Group itemsPerRow={5}>
                        {!isEmpty(header) && (
                            <Card key="card-header-all" raised>
                                <Card.Content>
                                    <Card.Description>
                                        <Checkbox
                                            label="全選"
                                            checked={allChecked}
                                            onClick={handleAllChecked}
                                        />
                                    </Card.Description>
                                </Card.Content>
                            </Card>
                        )}
                        {// show all header title to user to choose
                            !isEmpty(header) &&
                            header.map((item, idx) => (
                                <Card key={`card-header-${idx}`} raised>
                                    <Card.Content>
                                        <Card.Description>
                                            <CustomCheckBox
                                                key={`card-header-checkbox-${idx}-${item.id ||
                                                    ""}`}
                                                item={item}
                                                sheetName={sheetName}
                                                allChecked={allChecked}
                                                defaultChecked={
                                                    !isEmpty(
                                                        curHeader[
                                                            sheetName
                                                        ]?.filter(
                                                            ah =>
                                                                ah.id ===
                                                                item.id
                                                        ) || []
                                                    )
                                                }
                                                setHeader={setCurHeader}
                                            />
                                        </Card.Description>
                                    </Card.Content>
                                </Card>
                            ))}
                    </Card.Group>
                </Modal.Description>

                <Divider hidden style={{ margin: ".4rem 0" }} />

                {isEmpty(header) && (
                    <Message warning style={{ textAlign: "center" }}>
                        No Title
                    </Message>
                )}
            </Modal.Content>

            <Modal.Actions>
                <Button onClick={handleOnClose} color="red">
                    關閉
                </Button>
            </Modal.Actions>
        </Modal>
    );
};

export default HeaderButton;

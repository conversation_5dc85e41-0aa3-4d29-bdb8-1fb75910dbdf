import React, { useContext, useEffect, useState } from "react";

// ui
import { Label } from "semantic-ui-react";

// api
import { getUsers } from "../../../../../api/firebase/realtimeDatabase";

// config
import role from "../../../../../App-role";

// store
import { StoreContext } from "../../../../../store/StoreProvider";

const ApplicantNumLabel = () => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { account } = state;
    const { renderSignal } = account;
    const [users, setUsers] = useState(undefined);

    const handleGetUser = async () => {
        const usersData = await getUsers();
        const filteredUser = usersData.filter(
            user =>
                user.role === role.anonymous ||
                !Object.keys(role).includes(user.role)
        );
        setUsers(filteredUser);
    };

    useEffect(() => {
        handleGetUser();
    }, [renderSignal]);

    return <Label>{users?.length}</Label>;
};

export default ApplicantNumLabel;

import Act from '../actions';
import {
    bindSP,
    SubValues,
    IMAGE_URL,
    CREATE_ID,
} from '../../components/common/sheetCrud/sheetCrudHelper';

const initState = {
    // dataset
    mainSubject: { selected: {} },
    // sheet
    sheet: {
        selected: {},
        header: [],
        activeHeader: {},
        activeHeaderCount: 5,
        activeHeaderAllChecked: {},
        headerFields: {},
        tabCount: {},
        tabKey: {},
    },
    // pagination
    pagination: { activePage: 1, totalPage: 0, pageNum: 10, totalCount: 0 },
    // content
    content: {
        created: {},
        changed: {},
        checked: {},
        rows: {},
        curBookLang: '',
        showOnlyUnintegratedData: false,
    },
    // message
    message: { title: '', type: '', success: 0, error: 0, renderSignal: '' },
    infoMessage: {
        title: '',
        type: '',
        success: 0,
        error: 0,
        renderSignal: '',
    },
    // search
    search: { keyword: '', searchColumn: 'all', searchDataset: '' },
    // content: image editor
    // imageEditor: {
    //     displayModal: false
    // },
    // imageEditorData: {
    //     currentData: {}
    // },
    uploaded: { rawData: [], record: {} },
    groupInfo: {},
    sorted: { column: null, direction: null, sortIds: null },
    isFillAllAuthorOtherName: false,
    isFillAllTranslatorOtherName: false,
    setIsFillAllOtherNameModalOpen: false,
    authorOtherNameForDragTable: [],
    translatorOtherNameForDragTable: [],
    cloneCreateState: [],
    cloneCreateStateForUpdate: [],
    isDragging: false,
    isSearchingOver: true,
    isAuthorInDraggingMode: false,
    isTranslatorInDraggingMode: false,
    cloneLocalCreateState2: [],
    notifyDropdownListUpdate: false,
};

const dataReducer = (state = initState, action) => {
    switch (action.type) {
        // mainSubject
        case Act.DATA_MAINSUBJECT:
            return {
                ...state,
                mainSubject: { selected: action.payload },
            };
        case Act.DATA_MAINSUBJECT_CLEAN:
            return {
                ...state,
                mainSubject: { selected: {} },
            };
        // sheet
        case Act.DATA_TAB_KEY:
            return {
                ...state,
                sheet: { ...state.sheet, tabKey: action.payload },
            };
        case Act.DATA_TAB_KEY_CLEAN:
            return {
                ...state,
                sheet: { ...state.sheet, tabKey: {} },
            };
        case Act.DATA_TAB_COUNT_SHEET:
            return {
                ...state,
                sheet: { ...state.sheet, tabCount: action.payload },
            };
        case Act.DATA_TAB_COUNT_SHEET_CLEAN:
            return {
                ...state,
                sheet: { ...state.sheet, tabCount: {} },
            };
        case Act.DATA_SHEET:
            return {
                ...state,
                sheet: { ...state.sheet, selected: action.payload },
            };
        case Act.DATA_SHEET_CLEAN:
            return {
                ...state,
                sheet: { ...state.sheet, selected: {} },
            };
        case Act.DATA_SHEET_HEADER:
            return {
                ...state,
                sheet: { ...state.sheet, header: action.payload },
            };
        case Act.DATA_SHEET_HEADER_FIELD:
            return {
                ...state,
                sheet: {
                    ...state.sheet,
                    headerFields: {
                        ...state.sheet.headerFields,
                        ...action.payload,
                    },
                },
            };
        case Act.DATA_SHEET_HEADER_FIELD_ADD:
            return {
                ...state,
                sheet: {
                    ...state.sheet,
                    headerFields: Object.assign(state.sheet.headerFields, action.payload),
                },
            };
        case Act.DATA_SORTED:
            return {
                ...state,
                sorted: action.payload,
            };
        case Act.DATA_SORTED_CLEAN:
            return {
                ...state,
                sorted: { column: null, direction: null, sortIds: null },
            };
        case Act.DATA_SORTED_IDS:
            return {
                ...state,
                sorted: { ...state.sorted, sortIds: action.payload },
            };
        /*
        case Act.DATA_SHEET_HEADER_FIELD_REMOVE:
            return {
                ...state,
                sheet: {
                    ...state.sheet,
                    headerFields: Object.assign({},
                            ...Object.entries(state.sheet.headerFields)
                                .filter(([k,v]) => k !== action.payload)
                                .map(([k,v]) => ({[k]:v}))
                        )
                }
            };
        case Act.DATA_SHEET_HEADER_FIELD_REMOVE:
            return {
                ...state,
                sheet: {
                    ...state.sheet,
                    headerFields: Object.keys(state.sheet.headerFields).reduce((obj, key) => {
                        key !== action.payload && (obj[key] = state.sheet.headerFields[key]);
                        return obj;
                    }, {})
                }
            };
        */
        case Act.DATA_SHEET_HEADER_FIELD_UPDATE:
            return {
                ...state,
                sheet: {
                    ...state.sheet,
                    headerFields: action.payload,
                },
            };
        case Act.DATA_SHEET_ACTIVATE_HEADER:
            return {
                ...state,
                sheet: {
                    ...state.sheet,
                    activeHeader: {
                        ...state.sheet.activeHeader,
                        ...action.payload,
                    },
                },
            };

        case Act.DATA_SHEET_ACTIVATE_HEADER_ADD: {
            let allActivateIds;
            if (Array.isArray(action.payload.addHeader)) {
                // Only one case: Add all
                allActivateIds = action.payload.addHeader;
            } else {
                allActivateIds = state.sheet.activeHeader[action.payload.sheetName].filter(
                    (item) => item.id !== action.payload.addHeader.id,
                );
                allActivateIds.push(action.payload.addHeader);
            }

            const sortedActives = allActivateIds.sort((a, b) => a.seq - b.seq);
            return {
                ...state,
                sheet: {
                    ...state.sheet,
                    activeHeader: {
                        ...state.sheet.activeHeader,
                        [action.payload.sheetName]: sortedActives,
                    },
                },
            };
        }
        case Act.DATA_SHEET_ACTIVATE_HEADER_REMOVE:
            return {
                ...state,
                sheet: {
                    ...state.sheet,
                    activeHeader: {
                        ...state.sheet.activeHeader,
                        [action.payload.sheetName]: [
                            ...state.sheet.activeHeader[action.payload.sheetName].filter(
                                (item) => item.id !== action.payload.removeHeader.id,
                            ),
                        ],
                    },
                },
            };
        case Act.DATA_SHEET_ACTIVATE_HEADER_CLEAN:
            return {
                ...state,
                sheet: { ...state.sheet, activeHeader: action.payload },
            };
        // pagination
        case Act.DATA_PAGINATION_PAGENUM:
            return {
                ...state,
                pagination: { ...state.pagination, pageNum: action.payload },
            };
        case Act.DATA_PAGINATION_ACTIVE_PAGE:
            return {
                ...state,
                pagination: { ...state.pagination, activePage: action.payload },
            };
        case Act.DATA_PAGINATION_ACTIVE_PAGE_INIT:
            return {
                ...state,
                pagination: { ...state.pagination, activePage: 1 },
            };
        case Act.DATA_PAGINATION_PAGE_INIT:
            return {
                ...state,
                pagination: { ...state.pagination, activePage: 1, totalPage: 0 },
            };
        case Act.DATA_PAGINATION_TOTAL_PAGE:
            return {
                ...state,
                pagination: { ...state.pagination, totalPage: action.payload },
            };
        case Act.DATA_PAGINATION_TOTAL_COUNT:
            return {
                ...state,
                pagination: { ...state.pagination, totalCount: action.payload },
            };
        // set content
        case Act.DATA_CONTENT_SHOW_ONLY_UNINTEGRATED_DATA:
            return {
                ...state,
                content: {
                    ...state.content,
                    showOnlyUnintegratedData: action.payload,
                },
            };
        case Act.DATA_CONTENT_CURRENT_BOOK_LANGUAGE:
            return {
                ...state,
                content: { ...state.content, curBookLang: action.payload },
            };
        case Act.DATA_CONTENT_LOADING:
            return {
                ...state,
                content: { ...state.content, isLoading: true },
            };
        case Act.DATA_CONTENT_NO_LOADING:
            return {
                ...state,
                content: { ...state.content, isLoading: false },
            };
        case Act.DATA_CONTENT_ROWS:
            return {
                ...state,
                content: { ...state.content, rows: { ...action.payload } },
            };
        case Act.DATA_CONTENT_ROWS_CLEAN:
            return {
                ...state,
                content: { ...state.content, rows: {} },
            };
        case Act.DATA_CONTENT_ROW_CREATED:
            return {
                ...state,
                content: {
                    ...state.content,
                    created: Object.assign(state.content.created, action.payload),
                },
            };
        case Act.DATA_CONTENT_ROW_CREATED_CLEAN:
            return {
                ...state,
                content: { ...state.content, created: {} },
            };
        // record row change in content
        case Act.DATA_CONTENT_ROW_CHANGED: {
            const { rowId, cellId, cellValue } = action.payload;

            // sub values
            // Create 的時候用到的特殊 ID
            if (rowId === CREATE_ID) {
                return state;
            }

            // sub values
            Object.keys(SubValues).forEach((subKey) => {
                const { type, property } = SubValues[subKey];

                if (cellId.endsWith(subKey) || cellId.endsWith(type)) {
                    const DotCom = process.env.REACT_APP_DOMAIN_SUFFIX;
                    Object.keys(cellValue).forEach((idx) => {
                        let prevPropertyValue = null;

                        // 其它的值都要複製一份出來
                        property.forEach((subP) => {
                            const bindSubP =
                                subP === 'label' ? bindSP(subP, type) : bindSP(subP, subKey);

                            if (Object.keys(state.content.changed[rowId]).includes(bindSubP)) {
                                prevPropertyValue = state.content.changed[rowId][bindSubP][idx];
                            } else {
                                state.content.changed[rowId][bindSubP] = {};
                            }

                            if (prevPropertyValue) {
                                state.content.changed[rowId][bindSubP][idx] = prevPropertyValue;
                            } else if (state.content.rows[rowId][bindSubP]) {
                                state.content.changed[rowId][bindSubP][idx] =
                                    state.content.rows[rowId][bindSubP][idx];
                            } else {
                                state.content.changed[rowId][bindSubP][idx] = '';
                            }
                        });

                        if (Object.keys(state.content.changed[rowId]).indexOf(subKey) < 0) {
                            // eslint-disable-next-line no-param-reassign
                            state.content.changed[rowId][subKey] = {};
                        }
                        // arrangeSubValue 依靠 hasURL/hasSource/hasFormat 來判斷有幾筆
                        // eslint-disable-next-line no-param-reassign
                        state.content.changed[rowId][subKey][idx] = state.content.rows[rowId][
                            subKey
                        ]
                            ? state.content.rows[rowId][subKey][idx] || ''
                            : '';

                        // Special Cases: 需要產生新的 property
                        // imageURL_hasURL: 其它的值也要一起改，如：imagePath_hasURL, imageName_hasURL
                        // "https://fs-root.daoyidh.com/read/upload/portrait/420x420_forest.tiff"
                        if (cellId === IMAGE_URL) {
                            const fullImgUrl = cellValue[idx];
                            let imgPath = null;
                            let imgName = null;
                            // 需要同時修正 imagePath_hasURL, imageName_hasURL
                            const posCom = fullImgUrl.indexOf(DotCom);
                            if (posCom > -1) {
                                // found: read/upload/portrait/420x420_forest.tiff
                                const imgUrl = fullImgUrl.slice(posCom + DotCom.length + 1);

                                // 取 imagePath, imageName
                                const startSlash = imgUrl.indexOf('/');
                                const posSlash = imgUrl.lastIndexOf('/');

                                imgPath = imgUrl.slice(startSlash + 1, posSlash);
                                imgName = imgUrl.slice(posSlash + 1);
                                // 400x400_filename
                                const posUnder = imgName.indexOf('_');
                                imgName = imgName.slice(posUnder + 1);
                            } else if (
                                fullImgUrl.includes('http://') ||
                                fullImgUrl.includes('https://')
                            ) {
                                imgPath = 'tlvm';
                                imgName = fullImgUrl;
                            }

                            // FIXME: 應該要有個機制來做這個對應
                            // eslint-disable-next-line no-param-reassign
                            state.content.changed[rowId].imagePath_hasURL[idx] = imgPath;
                            // eslint-disable-next-line no-param-reassign
                            state.content.changed[rowId].imageName_hasURL[idx] = imgName;
                        }
                    });
                }
            });

            // eslint-disable-next-line no-param-reassign
            state.content.changed[rowId][cellId] = cellValue;
            return state;
        }
        // recovery row in content
        case Act.DATA_CONTENT_ROW_NO_CHANGED: {
            const { rowId, cellId, idx } = action.payload;

            // Create 的時候用到的特殊 ID
            if (rowId === CREATE_ID) {
                return state;
            }

            if (!state.content.changed) {
                // eslint-disable-next-line no-param-reassign
                state.content.changed = {};
            }
            if (!state.content.changed[rowId]) {
                // 要刪除，沒有元素，到這裡就好
                // 情況為啟動的第一次會到這裡
                return {
                    ...state,
                    content: {
                        ...state.content,
                        changed: {
                            ...state.content.changed,
                            ...{
                                [rowId]: {},
                            },
                        },
                    },
                };
            }
            if (!state.content.changed[rowId][cellId]) {
                return state;
            }
            if (!state.content.changed[rowId][cellId][idx]) {
                return state;
            }
            // 刪除元素
            // eslint-disable-next-line no-param-reassign
            delete state.content.changed[rowId][cellId][idx];

            return state;
        }
        // clean data when updated
        case Act.DATA_CONTENT_ROW_CHANGED_ONE_CLEAN: {
            const { rowId, cellId, idx } = action.payload;
            return {
                ...state,
                content: {
                    ...state.content,
                    changed: {
                        ...state.content.changed,
                        ...{
                            [rowId]: {
                                [cellId]: { [idx]: [] },
                            },
                        },
                    },
                },
            };
        }
        case Act.DATA_CONTENT_ROW_CHANGED_CLEAN:
            return {
                ...state,
                content: { ...state.content, changed: {} },
            };
        // record row change in content
        case Act.DATA_CONTENT_ROW_CHECKED:
            return {
                ...state,
                content: {
                    ...state.content,
                    checked: [...state.content.checked, action.payload],
                },
            };
        case Act.DATA_CONTENT_ROW_CHECKED_ALL:
            return {
                ...state,
                content: {
                    ...state.content,
                    checked: action.payload,
                },
            };
        case Act.DATA_CONTENT_ROW_NO_CHECKED:
            return {
                ...state,
                content: {
                    ...state.content,
                    checked: [
                        ...state.content.checked.filter(
                            (item) => item.rowId !== action.payload.rowId,
                        ),
                    ],
                },
            };
        // clean data when updated
        case Act.DATA_CONTENT_ROW_CHECKED_CLEAN:
            return {
                ...state,
                content: { ...state.content, checked: [] },
            };
        case Act.DATA_CONTENT_UPLOADED:
            return {
                ...state,
                uploaded: { ...state.uploaded, rawData: action.payload },
            };
        case Act.DATA_CONTENT_UPLOADED_CLEAN:
            return {
                ...state,
                uploaded: { ...state.uploaded, rawData: [] },
            };
        case Act.DATA_CONTENT_UPLOADED_RECORD:
            return {
                ...state,
                uploaded: { ...state.uploaded, record: action.payload },
            };
        case Act.DATA_CONTENT_UPLOADED_RECORD_CLEAN:
            return {
                ...state,
                uploaded: { ...state.uploaded, record: {} },
            };
        // show result when updated or created or deleted or read
        case Act.DATA_MESSAGE:
            return {
                ...state,
                message: Object.assign(state.message, action.payload),
            };
        // clean message
        case Act.DATA_MESSAGE_CLEAN:
            return {
                ...state,
                message: {
                    title: '',
                    type: '',
                    success: 0,
                    error: 0,
                    renderSignal: state.message.renderSignal,
                },
            };
        case Act.DATA_INFO_MESSAGE:
            return {
                ...state,
                infoMessage: Object.assign(state.infoMessage, action.payload),
            };
        // clean message
        case Act.DATA_INFO_MESSAGE_CLEAN:
            return {
                ...state,
                infoMessage: {
                    title: '',
                    type: '',
                    success: 0,
                    error: 0,
                    renderSignal: state.infoMessage.renderSignal,
                },
            };
        // clean message
        case Act.DATA_SEARCH_KEYWORD:
            return {
                ...state,
                search: {
                    ...state.search,
                    keyword: action.payload,
                },
            };
        case Act.DATA_SEARCH_SEARCH_COLUMN:
            return {
                ...state,
                search: {
                    ...state.search,
                    searchColumn: action.payload,
                },
            };
        case Act.DATA_SEARCH_DATASET:
            return {
                ...state,
                search: {
                    ...state.search,
                    searchDataset: action.payload,
                },
            };
        // clean message
        case Act.DATA_SEARCH_KEYWORD_CLEAN:
            return {
                ...state,
                search: { keyword: '', searchColumn: 'all' },
            };
        case Act.DATA_GROUPINFO:
            return {
                ...state,
                groupInfo: action.payload,
            };
        case Act.DATA_IS_FILL_ALL_AUTHOR_OTHERNAME:
            return {
                ...state,
                isFillAllAuthorOtherName: action.payload,
            };
        case Act.DATA_IS_FILL_ALL_TRANSLATOR_OTHERNAME:
            return {
                ...state,
                isFillAllTranslatorOtherName: action.payload,
            };
        case Act.DATA_SET_IS_FILL_ALL_OTHERNAME_MODAL_OPEN:
            return {
                ...state,
                setIsFillAllOtherNameModalOpen: action.payload,
            };
        case Act.DATA_SET_AUTHOR_OTHERNAME_FOR_DRAG_TABLE:
            return {
                ...state,
                authorOtherNameForDragTable: action.payload,
            };
        case Act.DATA_SET_TRANSLATOR_OTHERNAME_FOR_DRAG_TABLE:
            return {
                ...state,
                translatorOtherNameForDragTable: action.payload,
            };
        case Act.DATA_SET_CLONE_CREATE_STATE:
            return {
                ...state,
                cloneCreateState: action.payload,
            };
        case Act.DATA_SET_CLONE_CREATE_STATE_FOR_UPDATE:
            return {
                ...state,
                cloneCreateStateForUpdate: action.payload,
            };
        case Act.DATA_SET_IS_DRAGGING:
            return {
                ...state,
                isDragging: action.payload,
            };
        case Act.DATA_SET_IS_SEARCHING_OVER:
            return {
                ...state,
                isSearchingOver: action.payload,
            };
        case Act.DATA_SET_IS_AUTHOR_IN_DRAGGINGMODE:
            return {
                ...state,
                isAuthorInDraggingMode: action.payload,
            };
        case Act.DATA_SET_IS_TRANSLATOR_IN_DRAGGINGMODE:
            return {
                ...state,
                isTranslatorInDraggingMode: action.payload,
            };
        case Act.DATA_SET_UPDATE_CLONE_LOCAL_CREATE_STATE:
            return {
                ...state,
                cloneLocalCreateState2: state.cloneLocalCreateState2.some(
                    (item) => item.srcId === action.payload.srcId,
                )
                    ? state.cloneLocalCreateState2.map((item) =>
                        item.srcId === action.payload.srcId ? action.payload : item,
                    )
                    : [...state.cloneLocalCreateState2, action.payload],
            };

        case Act.DATA_CLEAR_CLONE_CREATE_STATE:
            return {
                ...state,
                cloneLocalCreateState2: [],
            };

        case Act.NOTIFY_DROPDOWN_LIST_UPDATE:
            return {
                ...state,
                notifyDropdownListUpdate: action.payload,
            };

        default:
            return state;
    }
};

export default dataReducer;

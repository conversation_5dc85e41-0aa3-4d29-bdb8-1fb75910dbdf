import React from "react";

// ui
import { Table, Popup } from "semantic-ui-react";

// common
import { getTimeSince } from "../../../../../../commons";

const CustomTimeTableRowCell = ({ time, timestamp }) => {

    const timeSince = getTimeSince(timestamp);

    return (
        <Popup
            trigger={<Table.Cell>{timeSince}</Table.Cell>}
            content={`${time}`}
            basic
        />
    )
};

export default CustomTimeTableRowCell;
import role from "../App-role";

/** @description 判斷route權限有兩種情況
 * 1. route path在firebase的group-information也有紀錄 => 根據groupInfo判斷權限
 * 2. route path沒有記錄在firebase的group-information => 根據App-authority.js規則判斷讀取權限
 * */

const filterRoute = (user, route, groupInfo) => {
    const safeRole = user?.role || role.anonymous;
    const pathName = route.path || route.href;
    if (!groupInfo.page || safeRole === role.anonymous) {
        // 還沒登入前，先開放首頁、登入、登出選項
        const rtDefaultPath = ["/", "/SignIn", "/SignOut"];
        if (rtDefaultPath.includes(pathName)) {
            return route.authority.includes(safeRole);
        }
        return false;
    }

    // 在findGroup.page確認，如果子選單有一個true，mainPathView也要改成true
    const tmpGPInfo = JSON.parse(JSON.stringify(groupInfo));
    tmpGPInfo.page = tmpGPInfo.page.map(pageEl => {
        if (pageEl.subMenu) {
            const checkSubView = pageEl.subMenu.find(el => el.viewable);
            if (checkSubView) {
                // 子選單有打開，表示header path也要可以被選擇
                return { ...pageEl, mainPathView: true };
            }
        }

        return pageEl;
    });

    const groupInfoPaths = tmpGPInfo.page.map(el => el.mainPath);
    if (groupInfoPaths.includes(pathName)) {
        const findObj = tmpGPInfo.page.find(info => info.mainPath === pathName);
        if (findObj) {
            return findObj.mainPathView;
        }
    }

    return route.authority.includes(safeRole);
};

const filterSubMenu = (route, location, groupInfo) => {
    if (!groupInfo.page) return false;

    const findMainMenu = groupInfo.page.find(
        obj => obj.mainPath === location.pathname
    );
    if (findMainMenu) {
        const findSubMenu = findMainMenu.subMenu.find(
            obj => obj.subMenuName === route.name
        );
        return findSubMenu && findSubMenu.viewable;
    }
    return false;
};

const filterDataSet = (ms, groupInfo) => {
    const tmpId = ms.id || ms.path;
    return groupInfo?.dataSet?.find(obj => obj.id === tmpId);
};

const filterSheet = (sheet, groupInfo) => {
    const findSheet = groupInfo?.sheets?.find(
        obj => obj.sheet.key === sheet.key
    );
    return findSheet && findSheet?.column?.length !== 0;
};

const filterColumn = (sheetKey, colObj, groupInfo) => {
    const findSheet = groupInfo?.sheets?.find(
        obj => obj.sheet.key === sheetKey
    );
    if (findSheet) {
        return findSheet.column.find(el => el.id === colObj.id);
    }
    return false;
};

export { filterRoute, filterSubMenu, filterDataSet, filterSheet, filterColumn };

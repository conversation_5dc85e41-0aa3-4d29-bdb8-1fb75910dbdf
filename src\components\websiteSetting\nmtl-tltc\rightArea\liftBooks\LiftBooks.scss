@mixin rightComponentMain {
  width: 90%;
  height: 90%;
  padding: 20px;
}

@mixin btnArea {
  display: flex;
  justify-content: flex-end;
}

.LiftBooks {
  @include rightComponentMain;
  .topArea{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px;
  }
  .textArea{
    margin-bottom: 5px;
    height: 40%;
  }
  .btnArea{
    @include btnArea;
    button {
      margin: 0;
    }
  }

}
import React, { useContext } from "react";
import { Provider } from "react-redux";

// isolated redux component
import editNewsStore from "./EditNewsReducer";
import EditNews from "./EditNews";
import { StoreContext } from "../../../../../store/StoreProvider";

function EditNewsProvider() {
    const [state] = useContext(StoreContext);
    const { websiteSubject } = state.websiteSetting;

    return (
        <Provider store={editNewsStore}>
            <EditNews websiteSubject={websiteSubject} />
        </Provider>
    );
}

export default EditNewsProvider;

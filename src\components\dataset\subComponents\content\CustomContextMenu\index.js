import React, { useContext, useState } from "react";

import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>up, Icon } from "semantic-ui-react";

import EditModalMenu from "./modals/EditModalMenu";
import DeleteModalMenu from "./modals/DeleteModalMenu";
import Act from "../../../../../store/actions";
import { StoreContext } from "../../../../../store/StoreProvider";

const ContextMenu = ({ rowId, rowData, isDisabled }) => {
    const [, dispatch] = useContext(StoreContext);

    const [popupOpen, setPopupOpen] = useState(false);
    const [editModalOpen, setEditModalOpen] = useState(false);
    const [deleteModalOpen, setDeleteModalOpen] = useState(false);

    const buttonStyle = {
        backgroundColor: "#f9fafb",
        margin: "-9px"
    };

    const PopupButton = (
        <Button
            icon="ellipsis vertical"
            size="mini"
            style={buttonStyle}
            disabled={isDisabled}
            onClick={() => setPopupOpen(true)}
        />
    );

    const handleItemClick = (e, { name }) => {
        switch (name) {
            case "edit":
                // open editModal
                setEditModalOpen(true);
                // init changed state
                dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });
                dispatch({ type: Act.DATA_CONTENT_ROW_CREATED_CLEAN });
                dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });
                break;
            case "delete":
                // open deleteModal
                setDeleteModalOpen(true);
                break;
            default:
                break;
        }
        // close popup
        setPopupOpen(false);
    };

    // cancel checked and changed row or cell
    const handleClose = () => {
        setPopupOpen(false);
    };

    return (
        <React.Fragment>
            <Popup
                flowing
                hoverable
                hideOnScroll
                trigger={PopupButton}
                open={popupOpen}
                // style={{ zIndex: "10" }}
                onClose={handleClose}
            >
                <Menu size="mini" vertical>
                    <Menu.Item
                        name="edit"
                        onClick={handleItemClick}
                        disabled={isDisabled}
                    >
                        <Icon name="edit" />
                        編輯
                    </Menu.Item>
                    <Menu.Item
                        name="delete"
                        onClick={handleItemClick}
                        disabled={isDisabled}
                    >
                        <Icon name="delete" />
                        刪除
                    </Menu.Item>
                </Menu>
            </Popup>

            <EditModalMenu
                rowId={rowId}
                rowData={rowData}
                open={editModalOpen}
                setOpen={setEditModalOpen}
            />

            <DeleteModalMenu
                rowId={rowId}
                rowData={rowData}
                open={deleteModalOpen}
                setOpen={setDeleteModalOpen}
            />
        </React.Fragment>
    );
};

export default ContextMenu;

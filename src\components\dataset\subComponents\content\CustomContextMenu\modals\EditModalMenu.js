import React, { useContext, useEffect, useState } from "react";

// ui
import { Button, Modal, Table } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";

// common
import { isEmpty } from "../../../../../../commons";

// custom
import { updateNmtlData } from "../../../../../../api/nmtl";
import Act from "../../../../../../store/actions";
import Api from "../../../../../../api/nmtl/Api";
import TableCellValue from "../../../commonComp/TableCellValue";
import { convertToGeneric } from "../../../../../common/sheetCrud/sheetCrudHelper";
import {
    convertToEditState,
    isNotShownInEdit
} from "../../../../../common/sheetCrud/utils";

const EditModalMenu = ({ open, setOpen, rowId, rowData }) => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { content, sheet, mainSubject } = state.data;
    const { dataset } = mainSubject.selected;
    const { key: sheetName, contentWritePath } = sheet.selected;
    const { header, headerFields } = sheet;

    const [createState, setCreateState] = useState(null);

    useEffect(() => {
        if (Object.keys(headerFields).length === 0) {
            return;
        }
        setCreateState(convertToEditState(header, headerFields, rowData));
    }, [headerFields]);

    const setCallback = (cellId, rId, jsonVal) => {
        setCreateState(preState => ({
            ...preState,
            [cellId]: {
                ...preState[cellId],
                ...jsonVal
            }
        }));
    };

    const handleUpdate = () => {
        if (
            isEmpty(content) ||
            isEmpty(contentWritePath) ||
            isEmpty(dataset) ||
            isEmpty(sheetName)
        ) {
            return;
        }
        const { rows, changed } = content;

        // merge rows and changed
        if (isEmpty(changed) || isEmpty(rows)) {
            return;
        }

        // record updated ids
        const updatedIds = [];
        // copy rows to new rows(deep clone)
        const newRows = JSON.parse(JSON.stringify(rows));

        // handle update value
        changed.forEach(item => {
            // update value
            newRows[item.rowId][item.cellId] = item.cellData;
            // record updated id
            if (updatedIds.indexOf(item.rowId) === -1) {
                updatedIds.push(item.rowId);
            }
        });

        // promise list
        const promises = updatedIds.map(id => {
            const apiUrl = Api.getGeneric;
            const [entrySrc, entryDst] = convertToGeneric(
                rows[id],
                newRows[id],
                contentWritePath,
                false
            );

            return updateNmtlData(
                user,
                apiUrl,
                dataset,
                sheetName,
                entrySrc,
                entryDst
            );
        });

        // get all result
        Promise.allSettled(promises).then(results => {
            // count
            let successCount = 0;
            let errorCount = 0;
            results.forEach(res => {
                if (res.value) {
                    successCount += 1;
                } else {
                    errorCount += 1;
                }
            });
            const message = {
                title: "Update",
                success: successCount,
                error: errorCount,
                renderSignal: `update-${new Date().getTime()}`
            };
            // alert message
            dispatch({
                type: Act.DATA_MESSAGE,
                payload: message
            });
        });

        // clean checked
        dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });

        // clean edit record after updated
        dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });

        // close
        setOpen(false);
    };

    const handleCancel = () => {
        // clean checked
        dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });
        // clean edit record after updated
        dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });
        setOpen(false);
    };

    // cancel checked and changed row or cell
    const handleClose = () => {
        // clean checked
        dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });
        // clean edit record after updated
        dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });
        setOpen(false);
    };

    const tableBody = headerIn => {
        if (!headerIn || isEmpty(headerIn)) {
            return (
                <Table.Row>
                    <Table.Cell warning>
                        <p>please choose 「mainSubject」 and 「sheet」</p>
                    </Table.Cell>
                </Table.Row>
            );
        }

        return headerIn.map((sh, shIdx) => {
            if (isNotShownInEdit(sh.id)) {
                return null;
            }

            const cellValue = rowData[sh.id] || "";
            return (
                <Table.Row warning key={`edit-button-row-${shIdx}`}>
                    <Table.Cell key={`edit-button-row-cell-label-${shIdx}`}>
                        {sh.label}
                        <br />
                        {sh.id}
                    </Table.Cell>
                    <Table.Cell key={`edit-button-row-cell-value-${shIdx}`}>
                        <TableCellValue
                            actHeader={sh.id}
                            cellValue={cellValue}
                            ctIdx={rowId}
                            shIdx={shIdx}
                            createState={
                                createState
                                    ? createState[sh.id]
                                    : {
                                        isLoading: false,
                                        options: [],
                                        value: undefined
                                    }
                            }
                            setCallback={setCallback}
                        />
                    </Table.Cell>
                </Table.Row>
            );
        });
    };

    return (
        <Modal open={open} onClose={handleClose} onOpen={() => setOpen(true)}>
            <Modal.Header>編輯內容</Modal.Header>
            <Modal.Content image scrolling>
                <Modal.Description>
                    <Table celled selectable>
                        <Table.Header>
                            <Table.Row>
                                {!isEmpty(header) && (
                                    <React.Fragment>
                                        <Table.HeaderCell singleLine>
                                            Label
                                        </Table.HeaderCell>
                                        <Table.HeaderCell singleLine>
                                            Value
                                        </Table.HeaderCell>
                                    </React.Fragment>
                                )}
                            </Table.Row>
                        </Table.Header>
                        <Table.Body>{tableBody(header)}</Table.Body>
                    </Table>
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={handleUpdate} color="green">
                    確認
                </Button>
                <Button onClick={handleCancel} color="red">
                    取消
                </Button>
            </Modal.Actions>
        </Modal>
    );
};

export default EditModalMenu;

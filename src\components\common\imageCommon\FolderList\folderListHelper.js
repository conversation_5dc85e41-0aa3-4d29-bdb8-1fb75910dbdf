import axios from "axios";
import {
    fileServerAPI,
    fileServerMethod
    // baseUrl
} from "../../../../api/fileServer";
import { uuidv4 } from "../../../../commons/utility";
import {
    getMainSubject,
    getUploadSettings,
    getImgFolder
} from "../../../../api/firebase/cloudFirestore";
import uploadConfig from "../../../toolPages/components/upload/uploadConfig";
import FileAct from "../../../../reduxStore/file/fileAction";

/* 剛進入該頁面時, 取得該專案的所有開放的 folderPattern */
const getFolderPattern = (dispatch, getPattern) => {
    axios({
        method: fileServerMethod[getPattern],
        url: fileServerAPI[getPattern],
        headers: {
            "Access-Control-Allow-Origin": "*"
        },
        // 增加 refresh key, 可以避掉 api cache
        params: { refresh: uuidv4() }
    })
        .then(res => {
            dispatch({
                type: FileAct.SET_FILES_FOLDER_SETTINGS,
                payload: {
                    folderPattern: res.data.data.folderPattern,
                    UPLOAD_CONFIG: res.data.data.UPLOAD_CONFIG,
                    ACCEPTABLE_EXTENSIONS: res.data.data.ACCEPTABLE_EXTENSIONS
                }
            });
        })
        .catch(err => {
            console.log(err);
        });
};

/* 剛進入該頁面時, 取得該專案的所有開放的 fileFolderPattern */
const getFileFolderPattern = async (dispatch, pattern) => {
    const ms = await getMainSubject();
    const upSetting = await getUploadSettings();

    // classId: "tww"
    // enable: "1"
    // id: "tww"
    // label: "2017作家作品目錄"
    // seq: 1
    // type: "nmtl"
    const fileFolder = ms.map(m => ({
        folderName: m.classId,
        folderName_ch: m.label,
        path: m.classId,
        path_ch: m.label,
        public: true,
        type: uploadConfig.file,
        uploadFullPath: m.classId,
        batchUploadFullPath: m.classId,
        active: false // 是否為 active
    }));

    const rootFolder = {
        folderName: "original",
        folderName_ch: "上傳目錄",
        path_ch: "/",
        public: true,
        folders: fileFolder
    };

    dispatch({
        type: FileAct.SET_FIRSTLAYERFILENAME,
        payload: []
    });

    dispatch({
        type: FileAct.SET_FILES_FOLDER_SETTINGS,
        payload: {
            folderPattern: Object.assign([], [rootFolder]),
            UPLOAD_CONFIG: Object.assign({}, upSetting[pattern].config),
            ACCEPTABLE_EXTENSIONS: Object.assign(
                [],
                upSetting[pattern].accept_extentions
            )
        }
    });
};

// eslint-disable-next-line no-unused-vars
const SINGLE_UPLOAD_IMAGE_BASEURL = "/upload/image";
// eslint-disable-next-line no-unused-vars
const BATCH_UPLOAD_IMAGE_BASEURL = "/upload/image/batch";

/* 剛進入該頁面時, 取得該專案的所有開放的 fileFolderPattern */
const getImgFolderPattern = async (dispatch, pattern) => {
    const folders = await getImgFolder();
    const upSetting = await getUploadSettings();

    // ====@source:Object[]
    // id: "ccw"
    // name: "ccw"
    // label: "陳千武文庫"
    // seq: 11
    // pid: ["VRcollectible", "VRavatar"]
    // ====@target:Object[]

    // folders已依照 seq 排序: 第一層 > 第二層
    const rootChildren = folders.reduce((list, cur) => {
        const { id, label, name, pid, seq } = cur;
        // pid: string | string[]
        const tmpPid = Array.isArray(pid) ? pid : [pid];
        tmpPid.forEach(tPid => {
            const findPidx = list.findIndex(o => o.id === tPid);
            const item = {
                ...cur,
                id,
                folderName: name,
                folderName_ch: label,
                public: true,
                type: uploadConfig.image,
                collapse: true // 是否展開
            };
            if (findPidx < 0) {
                list.push({
                    ...item,
                    path: `/${name}`,
                    path_ch: `/${label}`,
                    uploadFullPath: `${SINGLE_UPLOAD_IMAGE_BASEURL}/${name}`,
                    batchUploadFullPath: `${BATCH_UPLOAD_IMAGE_BASEURL}/${name}`,
                    folders: [],
                    active: false // 是否為 active
                });
            } else {
                // eslint-disable-next-line camelcase
                const { path: pPath, path_ch } = list[findPidx];
                list[findPidx].folders.push({
                    ...item,
                    path: `${pPath}/${name}`,
                    // eslint-disable-next-line camelcase
                    path_ch: `${path_ch}/${label}`,
                    uploadFullPath: `${SINGLE_UPLOAD_IMAGE_BASEURL}${pPath}/${name}`,
                    batchUploadFullPath: `${BATCH_UPLOAD_IMAGE_BASEURL}${pPath}/${name}`,
                    folders: [],
                    active: false // 是否為 active
                });
            }
        });
        return list;
    }, []);

    // 取得第一層資料夾檔名
    dispatch({
        type: FileAct.SET_FIRSTLAYERFILENAME,
        payload: rootChildren.map(el => el.id)
    });

    const rootFolder = {
        folderName: "original",
        folderName_ch: "上傳目錄",
        path_ch: "/",
        public: true,
        folders: rootChildren,
        collapse: true
    };

    dispatch({
        type: FileAct.SET_FILES_FOLDER_SETTINGS,
        payload: {
            folderPattern: Object.assign([], [rootFolder]),
            UPLOAD_CONFIG: Object.assign({}, upSetting[pattern].config),
            ACCEPTABLE_EXTENSIONS: Object.assign(
                [],
                upSetting[pattern].accept_extentions
            )
        }
    });
};

/* 為圖片路徑設定 size, 提升取得圖片速度 */
// 'http://localhost:9000/imgupload/portrait/2images.tiff' =>
// http://localhost:9000/imgupload/portrait/420x420_2images.tiff
const handleImgUrl = (imageName, imageSize = uploadConfig.ImageSize) => {
    const pathMatch = imageName.match(
        // eslint-disable-next-line no-useless-escape
        /(?<directory>.+)?[\\\/](?<filename>[^\\\/]+)\.(?<extension>.+)$/
    );

    if (!pathMatch) return imageName;
    const { directory, filename, extension } = pathMatch.groups;
    return `${directory}/${imageSize}_${filename}.${extension}`;
};

/* click folder 時, 取得該 folder 下的所有圖片資訊 */
const handleFolderClick = async (e, data, folderPattern, dispatch) => {
    try {
        const checkEachNode = _data => ({
            ..._data,
            // 變更 active 狀態
            active:
                // data 的 uniqId key 因 data.type 而不同
                data.type === uploadConfig.file
                    ? data.folderName === _data.folderName
                    : data.path === _data.path, // 是否展開
            folders: (_data.folders || []).map(c => checkEachNode(c))
        });
        const newFolderPattern = folderPattern.map(f => checkEachNode(f));
        dispatch({
            type: FileAct.SET_FILES_FOLDER_SETTINGS,
            payload: {
                folderPattern: Object.assign([], newFolderPattern)
            }
        });

        const currentFolder = Object.assign(
            {},
            {
                path: data.path, // e.g. /portrait
                path_ch: data.pathch || data.path_ch, // e.g. /人物像
                folderName: data.foldername || data.folderName, // portrait
                folderName_ch: data.foldernamech || data.folderName_ch, // 人物像
                type: data.type || ""
            }
        );
        dispatch({
            type: FileAct.SELECT_FOLDER,
            payload: currentFolder
        });

        // get all filename in current folder
        const res = await axios({
            method: fileServerMethod.getFolderFiles,
            url: fileServerAPI.getFolderFiles,
            headers: {
                "Access-Control-Allow-Origin": "*"
            },
            // 增加 refresh key, 可以避掉 api cache
            params: { folder: data.path, type: data.type, refresh: uuidv4() }
        });

        // 「圖片」或「檔案」
        let typeName;
        if (res.status === 200) {
            let fileList = res.data.data;

            if (data.type === uploadConfig.image) {
                typeName = "圖片";
                fileList =
                    (fileList &&
                        fileList.length > 0 &&
                        fileList.map(({ imgUrl, imgInfo, imgFileName }) => ({
                            url: handleImgUrl(`${imgUrl}`),
                            originalUrl: imgUrl,
                            imgInfo,
                            imgFileName,
                            type: data.type
                        }))) ||
                    [];
            } else if (data.type === uploadConfig.file) {
                // ['1.txt', '2.txt']
                typeName = "檔案";
                fileList =
                    (fileList &&
                        fileList.length > 0 &&
                        fileList
                            .filter(f => f.slice(-4) === ".pdf")
                            .map(name => ({
                                // item value
                                url: name,
                                originalUrl: `${uploadConfig.FilePrePath}${data.path}/${name}`,
                                imgInfo: "",
                                imgFileName: name,
                                type: data.type
                            }))) ||
                    [];
            }

            dispatch({
                type: FileAct.FOLDER_FILES_URL,
                payload: JSON.parse(JSON.stringify(fileList))
            });
            return dispatch({
                type: FileAct.PICKER_CUR_FOLDER_MESSAGE,
                payload: {
                    type: "success",
                    title: `資料夾中共 ${
                        fileList ? fileList.length : 0
                    } 張${typeName}`,
                    text: ""
                }
            });
        }
        // 切換資料夾但未取得圖片,則清空目前資料夾
        dispatch({
            type: FileAct.FOLDER_FILES_URL,
            payload: []
        });
        return dispatch({
            type: FileAct.PICKER_CUR_FOLDER_MESSAGE,
            payload: {
                type: "error",
                title: `取得${typeName}失敗`,
                text: ""
            }
        });
    } catch (err) {
        // 切換資料夾但未取得圖片,則清空目前資料夾
        dispatch({
            type: FileAct.FOLDER_FILES_URL,
            payload: []
        });
        return dispatch({
            type: FileAct.PICKER_CUR_FOLDER_MESSAGE,
            payload: {
                type: "error",
                title: "取得圖片失敗",
                text: `error: ${err.message}`
            }
        });
    }
};

const handleToggle = (e, data, folderPattern, dispatch) => {
    const checkEachNode = _data => ({
        ..._data,
        // 變更 collapse 狀態(是否展開子層)
        collapse: data.id === _data.id ? !_data.collapse : _data.collapse, // 是否展開
        folders: (_data.folders || []).map(c => checkEachNode(c))
    });
    const newFolderPattern = folderPattern.map(f => checkEachNode(f));
    dispatch({
        type: FileAct.SET_FILES_FOLDER_SETTINGS,
        payload: {
            folderPattern: Object.assign([], newFolderPattern)
        }
    });
};

export {
    getFolderPattern,
    getFileFolderPattern,
    getImgFolderPattern,
    handleFolderClick,
    handleToggle
};

// auth
import authority from "./App-authority";

const isPublic = env => env === "true" || env === true;

// HTML Header: Nav Bar
const menus = {
    menuLeft: [
        {
            id: "menu-left-01",
            label: "Home",
            name: "首頁",
            path: "/",
            authority: authority.Home,
            public: true
        },
        {
            id: "menu-left-02",
            label: "Account",
            name: "權限管理",
            path: "/Account",
            authority: authority.Account,
            public: true
        },
        {
            id: "menu-left-03",
            label: "Dataset",
            name: "資料管理",
            path: "/Dataset",
            authority: authority.Dataset,
            public: true
        },
        {
            id: "menu-left-09",
            label: "Authority",
            name: "權威檔",
            path: "/Authority",
            authority: authority.Authority,
            public: true
        },
        {
            id: "menu-left-04",
            label: "Database",
            name: "系統相關",
            path: "/SystemData",
            authority: authority.SystemData,
            public: true
        },
        {
            id: "menu-left-05",
            label: "Database",
            name: "工具相關",
            path: "/ToolPages",
            authority: authority.ToolPages,
            public: true
        },
        {
            id: "menu-left-06",
            label: "WebsiteSetting",
            name: "網頁設定選項",
            path: "/WebsiteSetting",
            authority: authority.WebsiteSetting,
            public: true
        },
        {
            id: "menu-left-07",
            label: "DownloadData",
            name: "數據資料",
            path: "/DownloadData",
            authority: authority.DownloadData,
            public: true
        },
        {
            id: "menu-left-08",
            label: "ReportIssue",
            name: "問題回報",
            path: "/ReportIssue",
            authority: authority.ReportIssue,
            public: true
        },
        {
            id: "menu-left-10",
            label: "AtaiDataManagement",
            name: "阿台資料管理",
            path: "/AtaiDataManagement",
            authority: authority.AtaiDataManagement,
            public: true
        }
    ],
    menuRight: [
        /* example
        {
            id: "menu-right-01",
            label: "SignIn",
            name: "SignIn",
            path: "/SignIn",
            authority: authority.SignIn
        }
        */
    ]
};

export default menus;

import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";

// component
import DropAlertMsg from "../../../../../../common/imageCommon/drop/DropAlertMsg";
import Drop from "../../../../../../common/imageCommon/drop/Drop";

// store
import helpers from "../../../../../../toolPages/components/upload/uploadImage/filePicker/filePickerHelper";
import FileAct from "../../../../../../../reduxStore/file/fileAction";

const FilePicker = ({ type }) => {
    const dispatch = useDispatch();
    const {
        files: { folderPattern },
        files
    } = useSelector(state => state);
    const { pickConfig } = files;

    useEffect(
        // 離開此頁面時, 清除 cache
        () => () => {
            dispatch({
                type: FileAct.UPLOAD_IMAGE,
                payload: []
            });
        },
        []
    );

    return (
        <Drop
            type={type}
            config={{
                ...pickConfig.datasetPage,
                withCheckbox: false
            }}
            DropAlertMsg={DropAlertMsg}
            handleDropCallback={helpers.handleDropCallback(
                files,
                dispatch,
                folderPattern
            )}
        />
    );
};
export default FilePicker;

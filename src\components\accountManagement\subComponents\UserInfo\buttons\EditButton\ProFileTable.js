import React, { useContext } from "react";

// ui
import { Table } from "semantic-ui-react";

// custom
import RoleDropdown from "./RoleDropdown";

// role config
import role from "../../../../../../App-role";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";

const ProFileTable = ({ user }) => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { user: userInfo } = state;
    const fields = [
        { label: "displayName", name: "姓名" },
        { label: "email", name: "電子郵件" },
        { label: "providerId", name: "註冊來源" },
        { label: "creationTime", name: "建立時間" },
        { label: "lastSignInTime", name: "上次登入" },
        { label: "role", name: "權限" }
    ];
    return (
        <Table celled padded>
            <Table.Header>
                <Table.Row>
                    <Table.HeaderCell>欄位</Table.HeaderCell>
                    <Table.HeaderCell>內容</Table.HeaderCell>
                </Table.Row>
            </Table.Header>

            <Table.Body>
                {
                    fields && fields.map(item => {
                        const { label, name } = item;
                        const { uid } = user;
                        const content = user[label];
                        return (
                            <Table.Row key={`profile-table-row-${label}`}>
                                <Table.Cell>{name}</Table.Cell>
                                <Table.Cell>
                                    {
                                        label === "role" && userInfo.role === role.admin ?
                                            <RoleDropdown
                                                userId={uid}
                                                userRole={content}
                                            />
                                            :
                                            <span>{content}</span>
                                    }
                                </Table.Cell>
                            </Table.Row>
                        )
                    })
                }
            </Table.Body>
        </Table>
    );
};

export default ProFileTable;

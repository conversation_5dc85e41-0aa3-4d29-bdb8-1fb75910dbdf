import React from "react";
import { Menu } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import optionList from "./optionList";
import accMngAct from "../../../../../../reduxStore/accManage/accManageAction";

function CustomSelectBar() {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { groDSelectItem } = state;

    const handleClick = el => {
        dispatch({
            type: accMngAct.SET_GRODSELECTITEM,
            payload: el
        });
    };
    return (
        <div>
            <Menu pointing style={{ width: "100%" }}>
                {/* 清單顯示 */}
                {optionList.map(el => {
                    const { key, name } = el;
                    return (
                        <Menu.Item
                            key={key}
                            name={name}
                            active={groDSelectItem.name === name}
                            onClick={() => handleClick(el)}
                        />
                    );
                })}
            </Menu>
        </div>
    );
}

export default CustomSelectBar;

import React from "react";
import CustomImageInput from "../../commonComp/specialTableComp/CustomImageInput";
import CustomCheckBox from "../../commonComp/specialTableComp/CustomCheckBox";
import { PICTURE_DISPLAY } from "../createConfig";

const CustomPersonImage = ({
    cellId,
    createState,
    setCreateState,
    checkBoxLabel,
    setCallback,
    menuName
}) => (
    <div
        style={{
            display: "flex",
            justifyContent: "space-around",
            alignItems: "center"
        }}
    >
        <CustomImageInput
            createState={createState || null}
            setCreateState={setCreateState}
            setCallback={setCallback}
            cellId={cellId}
            menuName={menuName}
        />
        {/* 有圖片 checkbox 才有動作 */}
        <CustomCheckBox
            cellId={PICTURE_DISPLAY}
            setCallback={setCallback}
            createState={createState || null}
            setCreateState={setCreateState}
            label={checkBoxLabel}
            disabled={!createState || !createState[cellId]}
            defaultValue={createState && createState[PICTURE_DISPLAY]}
            menuName={menuName}
        />
    </div>
);

export default CustomPersonImage;

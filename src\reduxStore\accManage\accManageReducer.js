import menuItem from "../../components/accountManagement/subComponents/MenuBar/menuItem";
import accMngAct from "./accManageAction";
import optionList from "../../components/accountManagement/subComponents/GroupInfo/subComponents/CustomSelectBar/optionList";

// GroupInfo
const INITIAL_STATE_GROUPINFO = {
    isEditGroup: false,
    unselectedSet: [],
    groDSelectItem: optionList[0],
    /** groupData說明
     * name: 群組名稱 -- string
     * page: 可閱覽頁面與項目
     * [
     *  {
     *      mainPath: "",
     *      childMenu: [{subMenuName: "", viewable: bool}],
     *      mainPathView: bool
     *  }
     * ]
     * dataSet: 可操作的資料集
     * [
     * {
     *      id: "",
     *      label: ""
     * },
     * {dataSet2}
     * ]
     * members: 使用者資訊，目前先規劃放id、name、email
     * [{
     *      displayName: "",
     *      email: "",
     *      uid: ""
     * }, {members2},....]
     * sheets: 表單選項
     * [{
     *    sheet: {key: "BasicInfo",text: "基本資料"},
     *    column: [{}, {}, ...]
     * }, {sheet2}.....]
     * */
    groupData: {
        name: "",
        page: [],
        dataSet: [],
        members: [],
        sheets: []
    },
    modalCaller: "",
    groupFsID: "",
    allGroupData: [],
    tableSelectPool: {},
    modalMessage: "",
    totalPage: 1,
    currentPage: 1,
    pageNum: 5
};

const INITIAL_STATE = {
    activeItemACC: menuItem[0],
    ...INITIAL_STATE_GROUPINFO
};

const accManageReducer = (state = INITIAL_STATE, action) => {
    switch (action.type) {
        case accMngAct.SET_GROUPINFOINIT:
            return {
                ...state,
                ...INITIAL_STATE_GROUPINFO
            };
        case accMngAct.SET_ACTIVEITEMACC:
            return {
                ...state,
                activeItemACC: action.payload
            };
        case accMngAct.SET_GRODSELECTITEM:
            return {
                ...state,
                groDSelectItem: action.payload
            };
        case accMngAct.SET_EDITGROUPMODE:
            return {
                ...state,
                isEditGroup: action.payload
            };
        case accMngAct.SET_UNSELECTEDSET:
            return {
                ...state,
                unselectedSet: action.payload
            };
        case accMngAct.SET_GROUPDATA:
            return {
                ...state,
                groupData: action.payload
            };
        case accMngAct.SET_MODALCALLER:
            return {
                ...state,
                modalCaller: action.payload
            };
        case accMngAct.SET_GROUPFIRESTOREID:
            return {
                ...state,
                groupFsID: action.payload
            };
        case accMngAct.SET_ALLGROUPDATA:
            return {
                ...state,
                allGroupData: action.payload
            };
        case accMngAct.INIT_GROUPDATA:
            return {
                ...state,
                groupData: {
                    name: "",
                    page: [],
                    dataSet: [],
                    members: [],
                    sheets: []
                }
            };
        case accMngAct.SET_TABLESELECTPOOL:
            return {
                ...state,
                tableSelectPool: action.payload
            };
        case accMngAct.SET_MODALMESSAGE:
            return {
                ...state,
                modalMessage: action.payload
            };
        case accMngAct.SET_CURRENTPAGE:
            return {
                ...state,
                currentPage: action.payload
            };
        case accMngAct.SET_TOTALPAGE:
            return {
                ...state,
                totalPage: action.payload
            };
        case accMngAct.SET_INITCURRENTPAGE:
            return {
                ...state,
                currentPage: 1
            };
        case accMngAct.SET_PAGENUM:
            return {
                ...state,
                pageNum: action.payload
            };
        default:
            return state;
    }
};

export default accManageReducer;

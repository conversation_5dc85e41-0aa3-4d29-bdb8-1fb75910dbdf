import React, { useEffect, useState } from "react";

// plugins
import { Button } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";

//
import { TypeName } from "../../../Utils/compoConfig";
import accMngAct from "../../../../../../reduxStore/accManage/accManageAction";

function GroupDetailBtn({ compoInfo }) {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { tableSelectPool } = state;
    const { typeName, btnName } = compoInfo;
    const [primBool, setPrimBool] = useState(false);
    const [negBool, setNegBool] = useState(false);
    const [disable, setDisable] = useState(false);

    useEffect(() => {
        switch (typeName) {
            case TypeName.GroupMemberADD:
                setPrimBool(true);
                break;
            case TypeName.GroupMemberDelete:
                setNegBool(true);
                setDisable(true);
                break;
            default:
                break;
        }
    }, []);

    useEffect(() => {
        if (typeName === TypeName.GroupMemberDelete) {
            setDisable(tableSelectPool.users.length === 0);
        }
    }, [tableSelectPool]);

    // eslint-disable-next-line no-shadow
    const handleClick = typeName => {
        dispatch({
            type: accMngAct.SET_MODALCALLER,
            payload: typeName
        });
    };
    return (
        <Button
            className={typeName}
            onClick={() => handleClick(typeName)}
            primary={primBool}
            negative={negBool}
            disabled={disable}
        >
            {btnName}
        </Button>
    );
}

export default GroupDetailBtn;

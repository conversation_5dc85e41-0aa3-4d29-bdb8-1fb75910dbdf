import textMsg from "../../../textMsg";
import Api from "../../../../../../api/nmtl/Api";
import initColumnDef from "./initColumnDef";
import { convertSimpleEntrySD } from "../../../../../common/sheetCrud/sheetCrudHelper";
import { isEmpty } from "../../../../../../commons";
import { createNmtlData, updateNmtlData } from "../../../../../../api/nmtl";
import { getClassType, getKeyData, rmHasProp } from "./saveDataUtils";

const saveNews = (originObj, updateObj, subject, user) =>
    new Promise(async (resolve, reject) => {
        // nmtl-web -> nmtl，因為資料庫沒有"CARDnmtl-web"這種instance，只有"CARDnmtl"
        const tmpSubject = subject === "nmtl-web" ? "nmtl" : subject;
        const { sheetName, graph } = textMsg;
        const apiStr = Api.getGeneric;

        const classType = getClassType(updateObj[initColumnDef.newsIdStr]);
        if (!classType) {
            resolve("No classType");
        }

        const keyData = getKeyData(initColumnDef.newsIdStr);
        const entryCol = {
            graph,
            classType,
            ...keyData
        };
        let { entrySrc, entryDst } = convertSimpleEntrySD(
            originObj,
            updateObj,
            entryCol
        );
        // 不處理連結instance的關係
        entrySrc = rmHasProp(entrySrc);
        entryDst = rmHasProp(entryDst);

        // remove newsIdStr property
        delete entrySrc?.value?.[initColumnDef.newsIdStr];

        // create
        if (isEmpty(entrySrc) && !isEmpty(entryDst)) {
            const newsId = entryDst?.value?.[initColumnDef.newsIdStr];
            // create hasNews link, e.g. nmtl:CARDtltc nmtl:hasNews nmtl:NEWS79.
            const tmpEntry = {
                classType: "FrontEdit",
                graph,
                srcId: `CARD${tmpSubject}`,
                value: {
                    hasNews: newsId
                }
            };
            const firstRes = await createNmtlData(
                user,
                apiStr,
                graph,
                sheetName,
                tmpEntry
            );
            // create new News instance
            if (firstRes === "OK") {
                delete entryDst?.value?.[initColumnDef.newsIdStr];
                createNmtlData(user, apiStr, graph, sheetName, entryDst)
                    .then(res => resolve(res))
                    .catch(err => reject(err));
            }
        } else {
            // update
            updateNmtlData(user, apiStr, graph, sheetName, entrySrc, entryDst)
                .then(res => resolve(res))
                .catch(err => reject(err));
        }
    });

export default saveNews;

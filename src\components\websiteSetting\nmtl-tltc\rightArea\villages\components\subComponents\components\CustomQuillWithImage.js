import React, { useEffect, useState } from "react";
import ReactQuill, { Quill } from "react-quill";
// import htmlEditButton from "quill-html-edit-button";
import ResizeModule from "@botom/quill-resize-module";
import { isEmpty } from "../../../../../../../../commons";
import { convert2HtmlEntities } from "../../../../../../../../commons/htmlEntities";
import { initToolbarOptions } from "../../../config";
import uploadImage from "../../../../../../commons/components/EditNews/subComponents/CustomEditor/utils/uploadImage";
import ImageFormat from "../../../../../../commons/components/EditNews/subComponents/CustomEditor/utils/quillImage";
import "../../../Villages.scss";
// image新增class
const Image = Quill.import("formats/image");
Image.className = "img-fluid";
Quill.register(Image, true);
// 將p tag換成 div，為了上下兩張圖片中間無空隙
// const Block = Quill.import("blots/block");
// class CustomBlock extends Block {
//     static tagName = "p";
//
//     static className = "village_div";
// }
// Block.className = "village_div";
// Block.tagName = "p";
// Quill.register(CustomBlock);

ReactQuill.Quill.register(ImageFormat, true);

// embedded ReactQuill 第三方套件
ReactQuill.Quill.register({
    // "modules/htmlEditButton": htmlEditButton, // 顯示html樣式
    "modules/resize": ResizeModule // 調整圖片大小
});

const CustomQuillWithImage = props => {
    const { quillId, onChangeFct, tmpValue, tmpRef } = props;
    const [toolBar, setToolBar] = useState({});

    useEffect(
        () => () => {
            setToolBar(initToolbarOptions);
        },
        []
    );
    useEffect(() => {
        const tmpToolBar = JSON.parse(JSON.stringify(initToolbarOptions));
        tmpToolBar.handlers = {
            image: () => uploadImage(tmpRef, "settings", "village")
        };
        setToolBar(tmpToolBar);
    }, []);

    return (
        <div>
            {!isEmpty(toolBar) && (
                <ReactQuill
                    id={quillId}
                    theme="snow"
                    value={convert2HtmlEntities(tmpValue)}
                    onChange={onChangeFct}
                    modules={{
                        htmlEditButton: {
                            buttonHTML: "HTML",
                            prependSelector: `div#${quillId}`
                        },
                        toolbar: toolBar,
                        resize: {
                            locale: {},
                            toolbar: {
                                alignTools: false
                            }
                        }
                    }}
                    ref={tmpRef}
                    style={{ minHeight: "200px" }}
                />
            )}
        </div>
    );
};

export default CustomQuillWithImage;

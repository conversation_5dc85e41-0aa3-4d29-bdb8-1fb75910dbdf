// import checkStr from "./checkStr";

const checkYYYYMMDD = (cell, propLabel) => {
    let tmpResStr = "";
    if (cell.value) {
        if (typeof cell.value === "string") {
            const reg = /^\d{4}-\d{2}-\d{2}$/;
            if (!reg.test(cell.value)) {
                const reason = "日期格式不符合YYYY-MM-DD";
                tmpResStr += `${cell.address}, [${cell.value}], 欄位:${propLabel}，${reason}`;
            }
        }
        // else {
        //     tmpResStr += checkStr(cell, propLabel);
        // }
    }

    return tmpResStr;
};

export default checkYYYYMMDD;

import React, { useContext, useEffect, useState } from "react";

// ui
import { Checkbox } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

const CustomCheckBox = ({ rowId, isChecked, ...rest }) => {
    const [, dispatch] = useContext(StoreContext);
    // handle checkbox
    const [checked, setChecked] = useState(isChecked);

    useEffect(() => {
        setChecked(isChecked);
    }, [isChecked]);

    // handle checkbox for delete
    const handleCheckbox = () => {
        // checkbox state
        setChecked(!checked);

        // record rowId if checked === true
        if (!checked) {
            // record checkbox for delete
            dispatch({
                type: Act.DATA_CONTENT_ROW_CHECKED,
                payload: { rowId: `${rowId}` }
            });
        } else {
            // cancel record checkbox for delete
            dispatch({
                type: Act.DATA_CONTENT_ROW_NO_CHECKED,
                // payload: { rowId }
                payload: { rowId: `${rowId}` }
            });
        }
    };

    return <Checkbox {...rest} checked={checked} onClick={handleCheckbox} />;
};

export default CustomCheckBox;

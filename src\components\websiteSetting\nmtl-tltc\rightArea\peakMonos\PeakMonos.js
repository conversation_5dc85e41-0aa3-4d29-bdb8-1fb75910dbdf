import React, { useCallback, useEffect, useState } from "react";

// css
import "./PeakMonos.scss";

// components
import { Button, Form, Table } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import LanguageSelect from "../../../components/LanguageSelect";
import SaveButton from "../../../commons/components/SaveButton";
import EditPeak from "./components/EditPeak";

// store & api
import Api from "../../../../../api/nmtl/Api";
import { readNmtlData } from "../../../../../api/nmtl";

// utils

// icons
import iconEdit from "../../../../../images/icon_edit.svg";
import PeakCustomPagination from "./components/subComponents/PeakCustomPagination";
import { tablePeakHeaderConfig } from "./config";
import PeakAct from "./PeakMonosAction";

function PeakMonos() {
    const dispatch = useDispatch();
    const [language, setLanguage] = useState("");
    const [srcPeakData, setSrcPeakData] = useState({}); // 資料原始值
    const [peakData, setPeakData] = useState({}); // 資料更新值
    // const [isEdit, setIsEdit] = useState(false);
    const { cateIsEdited, peakIsEdited } = useSelector(state => state);
    const [isFinishEffect, setIsFinishEffect] = useState(false);
    const [peakMonosList, setPeakMonosList] = useState([]);
    // page control
    const [curPage, setCurPage] = useState(1);
    const [perPageNum, setPerPageNum] = useState(5);

    const getData = () => {
        // 待改動API
        const apiStr = Api.getTltcpeakPageData;
        readNmtlData(apiStr).then(res => {
            if (res?.data) {
                setSrcPeakData(res?.data[0] || {});
                setPeakData(res?.data[0] || {});
            }
        });
    };

    useEffect(() => {
        getData();
    }, []);

    useEffect(() => {
        setIsFinishEffect(false);
        if (cateIsEdited) {
            // setIsEdit(true);
            dispatch({
                type: PeakAct.SET_ISEDITEDPEAK,
                payload: true
            });
        }
        setIsFinishEffect(true);
    }, [cateIsEdited]);

    // 用useCallback減少component UpdateText re-render問題
    const handleChange = useCallback(
        evt => {
            if (language === "zh") {
                setPeakData({ ...peakData, peakDescZH: evt.target.value });
            } else {
                setPeakData({ ...peakData, peakDescEN: evt.target.value });
            }
        },
        [language, peakData]
    );

    useEffect(() => {
        const fetchData = async () => {
            const apiStr = Api.getPeakMonosList;
            await axios
                .get(apiStr)
                .then(res => setPeakMonosList(res?.data?.data));
        };
        fetchData();
    }, []);

    return (
        <>
            {isFinishEffect && !peakIsEdited ? (
                <div className="PeakMonos">
                    <div className="topArea">
                        <h1>臺灣文學獎</h1>
                        <LanguageSelect
                            language={language}
                            setLanguage={setLanguage}
                        />
                    </div>
                    <div className="textArea">
                        <Form style={{ height: "100%", width: "100%" }}>
                            <textarea
                                style={{ resize: "none", height: "100%" }}
                                // disabled={isEditedDisable}
                                value={
                                    language === "zh"
                                        ? peakData.peakDescZH
                                        : peakData.peakDescEN
                                }
                                onChange={handleChange}
                            />
                        </Form>
                    </div>
                    <div className="btnArea">
                        <SaveButton
                            srcData={srcPeakData}
                            dstData={peakData}
                            closeCallBack={getData}
                        />
                    </div>
                    <div className="bookArea">
                        <Table celled structured size="small" selectable>
                            <Table.Header>
                                <Table.Row>
                                    {tablePeakHeaderConfig.map(i => (
                                        <Table.HeaderCell
                                            width={i.row}
                                            key={i.header}
                                            textAlign={i.display}
                                        >
                                            {i.header}
                                        </Table.HeaderCell>
                                    ))}
                                </Table.Row>
                            </Table.Header>
                            <Table.Body>
                                {peakMonosList &&
                                    peakMonosList
                                        .slice(
                                            (curPage - 1) * perPageNum,
                                            curPage * perPageNum
                                        )
                                        .map(i => (
                                            <Table.Row key={i.id}>
                                                <Table.Cell>{i.id}</Table.Cell>
                                                <Table.Cell>
                                                    {i.label}
                                                </Table.Cell>
                                                <Table.Cell
                                                    textAlign="center"
                                                    verticalAlign="middle"
                                                >
                                                    <Button
                                                        style={{
                                                            backgroundColor:
                                                                "initial",
                                                            margin: "0",
                                                            padding: "0"
                                                        }}
                                                        onClick={() => {
                                                            dispatch({
                                                                type:
                                                                    PeakAct.SET_ISEDITEDPEAK,
                                                                payload: true
                                                            });
                                                            dispatch({
                                                                type:
                                                                    PeakAct.SET_EDITINGPEAKID,
                                                                payload:
                                                                    i.peakMonoID
                                                            });
                                                        }}
                                                    >
                                                        <img
                                                            src={iconEdit}
                                                            alt="Edit"
                                                        />
                                                    </Button>
                                                </Table.Cell>
                                            </Table.Row>
                                        ))}
                            </Table.Body>
                        </Table>
                    </div>
                    <PeakCustomPagination
                        data={peakMonosList}
                        id="editPeak"
                        setCurPage={setCurPage}
                        setPerPageNum={setPerPageNum}
                        curPage={curPage}
                        perPageNum={perPageNum}
                    />
                </div>
            ) : (
                <EditPeak />
            )}
        </>
    );
}

export default PeakMonos;

export const mustColumn = ["type", "labelZH", "labelEN"];

// 儲存資料前檢查必填欄位
export const checkColumn = editItem => {
    let pass = false;
    mustColumn.forEach(col => {
        if (typeof col === "string") {
            pass = Object.hasOwn(editItem, col) && editItem[col];
        } else if (Array.isArray(col)) {
            // type是array的只要其中一個存在即可
            pass = Object.keys(editItem).some(
                key => col.includes(key) && editItem[key]
            );
        }
    });

    return pass;
};

import React from "react";
import { useSelector } from "react-redux";

// component
import FileFolderControlPanel from "./FileFolderControlPanel";
import Folder from "../../../../common/imageCommon/folder";

const CurrentFolder = ({ type }) => {
    const {
        files: { pickConfig }
    } = useSelector(state => state);

    return (
        <Folder
            type={type}
            pickConfig={pickConfig.datasetPage}
            FolderControlPanel={FileFolderControlPanel}
            mode="radioSelect"
        />
    );
};

export default CurrentFolder;

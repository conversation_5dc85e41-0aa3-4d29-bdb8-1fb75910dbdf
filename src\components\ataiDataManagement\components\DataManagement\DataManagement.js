import React, { useState } from "react";
import UploadData from "./UploadData";
import DataTable from "./DataTable";
import textConfig from "../../textConifg";

const DataManagement = () => {
    return (
        <div>
            <h2 style={{ padding: "0" }}>{textConfig.uploadDataTitle}</h2>
            <UploadData />
            <DataTable />
        </div>
    );
};

export default DataManagement;

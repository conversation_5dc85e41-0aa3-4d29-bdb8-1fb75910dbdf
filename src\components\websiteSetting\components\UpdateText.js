import React, { useState, useEffect, useContext } from "react";
import { Form, TextArea } from "semantic-ui-react";
import { StoreContext } from "../../../store/StoreProvider";
import Act from "../../../store/actions";
import { isEmpty } from "../../../commons";

/* 參數說明
 *  dropDown: 有下拉選單的頁面會傳送過來，selectOption才會有值
 *  language: 語系，有"zh"、"en"
 *  option: 沒有下拉選單的頁面使用，使用properties
 *           => priority: 欄位順序
 *           => message: 要顯示的訊息
 *           => column: DB要更改的欄位名稱
 *           => data: 要顯示在UpdateText的整包物件，目前只用在判斷是不是LinkingPage傳過來的資訊
 *           => part: 在ManualPage會根據不同的selectOption區分要給幾個UpdateText
 * */
function UpdateText({ dropDown, language, option, ...props }) {
    const [state, dispatch] = useContext(StoreContext);
    const {
        menuActiveItem,
        updatedData,
        selectOption,
        isEditedDisable
    } = state.websiteSetting;
    const [showDescription, setShowDescription] = useState("");
    const [editDescription, setEditDescription] = useState("");

    // 讀出資料顯示
    useEffect(() => {
        if (selectOption && selectOption.length !== 0) {
            if (option.data) {
                // LinkingPage才有帶data property
                // Fix bug: 在更新selectOption後，dropDown帶過來的object還沒及時更新
                // 導致沒辦法抓到正確欄位名稱，解決方式是直接傳字串過來，也多帶一個property當作該頁判斷依據
                // message一開始會有錯，但後面會立即更新為正確的
                setShowDescription(option.message);
            } else if (language !== "" && option.column && !isEmpty(dropDown)) {
                // 有下拉選單，也有語系選擇
                if (menuActiveItem.key === "ManualPage") {
                    if (dropDown[selectOption][option.part]) {
                        setShowDescription(
                            dropDown[selectOption][option.part][language][
                                option.column
                            ]
                        );
                    }
                } else {
                    setShowDescription(
                        dropDown[selectOption][language][option.column]
                    );
                }
            } else if (option && !isEmpty(dropDown)) {
                // 有下拉選單，但是沒有語系選擇
                setShowDescription(dropDown[selectOption][option.column]);
            }
        } else if (option) {
            setShowDescription(option.message);
        }
    }, [selectOption, language, option]);

    useEffect(() => {
        setEditDescription(showDescription);
    }, [showDescription]);

    // 寫入資料
    useEffect(() => {
        const tmpAllData = JSON.parse(JSON.stringify(updatedData));
        if ((selectOption && selectOption !== "") || selectOption === null) {
            const tmpObj = tmpAllData.find(
                element => element.id === menuActiveItem.key
            );
            if (tmpObj) {
                if (language !== "" && selectOption !== null) {
                    // 給有下拉選單且有語系的欄位使用
                    if (menuActiveItem.key === "LinkingPage") {
                        const keys = Object.keys(tmpObj[selectOption]);
                        const targetKeyName = keys.find(
                            keyName =>
                                tmpObj[selectOption][keyName].priority ===
                                option.priority
                        );
                        if (targetKeyName) {
                            if (option.column === "title") {
                                tmpObj[selectOption][targetKeyName][language][
                                    option.column
                                ] = editDescription;
                            } else {
                                tmpObj[selectOption][targetKeyName][
                                    option.column
                                ] = editDescription;
                            }
                        }
                    } else if (menuActiveItem.key === "ManualPage") {
                        const keys = Object.keys(tmpObj[selectOption]);
                        const targetKeyName = keys.find(
                            keyName => keyName === option.part
                        );
                        if (targetKeyName) {
                            tmpObj[selectOption][targetKeyName][language][
                                option.column
                            ] = editDescription;
                        }
                    } else if (option.data) {
                        const keys = Object.keys(
                            tmpObj[selectOption][language]
                        );
                        const targetKeyName = keys.find(
                            keyName =>
                                tmpObj[selectOption][language][keyName]
                                    .priority === option.priority
                        );
                        if (targetKeyName) {
                            tmpObj[selectOption][language][targetKeyName][
                                option.column
                            ] = editDescription;
                        }
                    } else {
                        tmpObj[selectOption][language][
                            option.column
                        ] = editDescription;
                    }
                } else if (language !== "" && option?.column) {
                    // 給沒有下拉選單且有語系的欄位使用
                    const keys = Object.keys(tmpObj[language]);
                    const targetKeyName = keys.find(
                        keyName =>
                            tmpObj[language][keyName].priority ===
                            option.priority
                    );
                    // DB有priority欄位
                    if (targetKeyName) {
                        tmpObj[language][targetKeyName][
                            option.column
                        ] = editDescription;
                    } else {
                        // DB沒有priority欄位
                        tmpObj[language][option.column] = editDescription;
                    }
                } else if (option?.column && selectOption !== null) {
                    // 給有下拉選單但是沒有語系的欄位使用
                    tmpObj[selectOption][option.column] = editDescription;
                } else {
                    // 給沒有下拉選單也沒有語系的欄位使用
                    const keys = Object.keys(tmpObj);
                    const targetKeyName = keys.find(
                        keyName => tmpObj[keyName].priority === option.priority
                    );
                    // DB有priority欄位
                    if (targetKeyName) {
                        tmpObj[targetKeyName][option.column] = editDescription;
                    }
                }
            }
        }

        dispatch({
            type: Act.SET_UPDATEDDATA,
            payload: tmpAllData
        });
    }, [editDescription, selectOption]);

    return (
        <Form style={{ height: "100%", width: "100%" }}>
            <TextArea
                style={{ resize: "none", height: "100%" }}
                disabled={isEditedDisable}
                value={editDescription}
                onChange={event => {
                    setEditDescription(event.target.value);
                }}
                {...props}
            />
        </Form>
    );
}

export default UpdateText;

import React, { useEffect, useState } from "react";

// plugins
import { useDispatch, useSelector } from "react-redux";
import { Dropdown } from "semantic-ui-react";
import { sortedResutls } from "twchar";

// config
import clsName from "../../Utils/clsName";
import { classType } from "../../Utils/fixedSelect";
import textConfig from "../../Utils/textConfig";
import { isEmpty } from "../../../../../../../commons";
import { setUpdateNewsInfo } from "../../Utils/utils";

function CustomDropdown({ className }) {
    const newsDispatch = useDispatch();
    const { updateNewsInfo } = useSelector(state => state);

    const [sortOption, setSortOption] = useState([]);
    const [value, setValue] = useState("");
    const [plholder, setPlholder] = useState("");

    useEffect(() => {
        // 只有一開始進到NewsInfoArea會用到
        if (isEmpty(updateNewsInfo)) return;
        switch (className) {
            case clsName.Classification:
                {
                    const tmpDropDown = classType.map((name, index) => ({
                        key: index,
                        value: name,
                        text: name
                    }));
                    setSortOption(sortedResutls(tmpDropDown, "text"));
                    setPlholder(textConfig.Header_Class);

                    setValue(updateNewsInfo.newsType);
                }
                break;
            default:
                break;
        }
    }, [updateNewsInfo]);

    const handleChange = (event, data) => {
        switch (className) {
            case clsName.Classification:
                {
                    setValue(data.value);

                    const tmpInfo = JSON.parse(JSON.stringify(updateNewsInfo));
                    tmpInfo.newsType = data.value;
                    setUpdateNewsInfo(newsDispatch, tmpInfo);
                }
                break;
            default:
                break;
        }
    };

    return (
        <Dropdown
            fluid
            selection
            placeholder={plholder}
            options={sortOption}
            onChange={handleChange}
            value={value}
        />
    );
}

export default CustomDropdown;

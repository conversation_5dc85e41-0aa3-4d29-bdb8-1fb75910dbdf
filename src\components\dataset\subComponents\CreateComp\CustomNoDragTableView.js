import React, { useContext, useEffect, useState } from "react";

import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import { readNmtlData } from "../../../../api/nmtl";
import Api from "../../../../api/nmtl/Api";
import { isEmpty } from "../../../../commons";
import { updateObjectValue } from "./helper";
import CustomSubTableView from "./CustomSubTableView";
import CustomPagination from "../../../common/CustomPagination/CustomPagination";

const CustomNoDragTableView = ({
    keyName,
    createState,
    content,
    sheetName,
    setCallback,
    setCreateState,
    menuName,
    header,
    ct,
    filterIds,
    cloneLocalCreateState,
    setCloneLocalCreateStateFct,
    isInDraggingMode,
    pageOption,
    curPage,
    setCurPage,
    totalPages,
    setTotalPages,
    perPageNum,
    setPerPageNum
}) => {
    const [state, dispatch] = useContext(StoreContext);
    const { cloneCreateState } = state.data;
    const { dataset } = state.data.mainSubject.selected;
    const [rowData, setRowData] = useState([cloneLocalCreateState]);
    const [options, setOptions] = useState([]);

    const sortNames = names =>
        names.sort((a, b) => {
            const aIsZh = a.endsWith("@zh");
            const bIsZh = b.endsWith("@zh");
            // eslint-disable-next-line no-nested-ternary
            return aIsZh && !bIsZh ? -1 : !aIsZh && bIsZh ? 1 : 0;
        });

    const splitAndFlattenNames = names =>
        names.flatMap(name => name.split("、"));

    const getMinIndex = (label, labelIndexMap) => {
        const parts = label.split("、");
        return Math.min(...parts.map(part => labelIndexMap[part]));
    };

    const getOptionForOtherName = async (ds, ids) => {
        if (!ids) return;
        const filteredIds = ids.filter(i => i !== null);
        const res = await readNmtlData(
            Api.getOtherNameList
                .replace("{ds}", ds)
                .replace("{ids}", filteredIds)
        );
        const opt = res.data.map(el => ({
            id: el.srcId,
            label: el.label,
            value: el.label
        }));
        setOptions(opt);
        // eslint-disable-next-line consistent-return
        return opt;
    };

    const processNamesAndDispatch = (
        tmpKeyName,
        tmpCloneLocalCreateState,
        otherNameArr,
        idArr
    ) => {
        const isFillAllOtherName = idArr.length === otherNameArr.length / 2;
        dispatch({
            type:
                tmpKeyName === "hasAuthor"
                    ? Act.DATA_IS_FILL_ALL_AUTHOR_OTHERNAME
                    : Act.DATA_IS_FILL_ALL_TRANSLATOR_OTHERNAME,
            payload: isFillAllOtherName
        });
        getOptionForOtherName(
            dataset,
            tmpCloneLocalCreateState[tmpKeyName]
        ).then(res => {
            const result = tmpCloneLocalCreateState[tmpKeyName].map(id => {
                const labels = otherNameArr
                    .filter(label =>
                        res.some(
                            item =>
                                item.id === id &&
                                item.label === label.replace(/@zh|@en/, "")
                        )
                    )
                    .join("、");

                return { id, label: labels };
            });
            setRowData(result);
        });
    };

    useEffect(() => {
        if (isEmpty(cloneLocalCreateState)) return;

        const idArr = cloneLocalCreateState[keyName];
        const otherName = sortNames(
            cloneLocalCreateState[
                keyName === "hasAuthor" ? "authorName" : "translatorName"
            ]
        );
        const otherNameArr = splitAndFlattenNames(otherName);

        processNamesAndDispatch(
            keyName,
            cloneLocalCreateState,
            otherNameArr,
            idArr
        );
    }, [cloneCreateState]);

    /**
     * 因cloneLocalCreateState是state，
     * 在更新id順序的時候，會有誤差，
     * 故需同時處理author及translator的id順序
     */
    useEffect(() => {
        if (isEmpty(cloneLocalCreateState)) return;

        const authorIdArr = JSON.parse(
            JSON.stringify(cloneLocalCreateState.hasAuthor)
        );
        const translatorIdArr = JSON.parse(
            JSON.stringify(cloneLocalCreateState.hasTranslator)
        );

        const sortedAuthorNames = sortNames(cloneLocalCreateState.authorName);
        const sortedTranslatorNames = sortNames(
            cloneLocalCreateState.translatorName
        );

        const otherAuthorNameArr = splitAndFlattenNames(sortedAuthorNames);
        const otherTranslatorNameArr = splitAndFlattenNames(
            sortedTranslatorNames
        );

        const isFillAllAuthorOtherName =
            cloneLocalCreateState.hasAuthor.filter(i => i !== null).length ===
            otherAuthorNameArr.length / 2;
        const isFillAllTranslatorOtherName =
            cloneLocalCreateState.hasTranslator.filter(i => i !== null)
                .length ===
            otherTranslatorNameArr.length / 2;

        dispatch({
            type: Act.DATA_IS_FILL_ALL_AUTHOR_OTHERNAME,
            payload: isFillAllAuthorOtherName
        });
        dispatch({
            type: Act.DATA_IS_FILL_ALL_TRANSLATOR_OTHERNAME,
            payload: isFillAllTranslatorOtherName
        });

        getOptionForOtherName(
            dataset,
            authorIdArr.concat(translatorIdArr)
        ).then(res => {
            const processResults = (idArr, otherNameArr, labelIndexMap) =>
                idArr
                    .map(id => {
                        const labels = otherNameArr
                            .filter(label =>
                                res.some(
                                    item =>
                                        item.id === id &&
                                        item.label ===
                                            label.replace(/@zh|@en/, "")
                                )
                            )
                            .join("、");

                        return { id, label: labels };
                    })
                    .sort(
                        (a, b) =>
                            getMinIndex(a.label, labelIndexMap) -
                            getMinIndex(b.label, labelIndexMap)
                    );

            const authorLabelIndexMap = {};
            const translatorLabelIndexMap = {};
            otherAuthorNameArr.forEach((label, index) => {
                authorLabelIndexMap[label] = index;
            });
            otherTranslatorNameArr.forEach((label, index) => {
                translatorLabelIndexMap[label] = index;
            });

            const sortedAuthorArr = processResults(
                authorIdArr,
                otherAuthorNameArr,
                authorLabelIndexMap
            );
            const sortedTranslatorArr = processResults(
                translatorIdArr,
                otherTranslatorNameArr,
                translatorLabelIndexMap
            );

            dispatch({
                type: Act.DATA_SET_AUTHOR_OTHERNAME_FOR_DRAG_TABLE,
                payload: sortedAuthorArr
            });
            dispatch({
                type: Act.DATA_SET_TRANSLATOR_OTHERNAME_FOR_DRAG_TABLE,
                payload: sortedTranslatorArr
            });

            const sortedAuthorIds = sortedAuthorArr.map(i => i.id);
            const sortedTranslatorIds = sortedTranslatorArr.map(i => i.id);

            const updateData = updateObjectValue(
                cloneLocalCreateState,
                "hasAuthor",
                sortedAuthorIds
            );
            const updatedState = updateObjectValue(
                updateData,
                "hasTranslator",
                sortedTranslatorIds
            );

            setCloneLocalCreateStateFct(updatedState);
            setRowData(
                keyName === "hasAuthor" ? sortedAuthorArr : sortedTranslatorArr
            );
        });
    }, [createState]);

    const handlePage = (tmpData, key) => {
        const { activePage } = tmpData;
        const tmpObj = JSON.parse(JSON.stringify(curPage));
        tmpObj[key] = activePage;
        setCurPage(tmpObj);
    };

    const handleDDPage = (tmpData, key) => {
        const tmpObj = JSON.parse(JSON.stringify(curPage));
        tmpObj[key] = tmpData.value;
        setCurPage(tmpObj);
    };

    const handlePerPageNum = (tmpData, tmpContent, key) => {
        setCurPage(prevState => ({ ...prevState, [key]: 1 }));

        setPerPageNum(prevState => ({ ...prevState, [key]: tmpData.value }));

        setTotalPages(prevState => ({
            ...prevState,
            [key]: Math.ceil(tmpContent?.length / tmpData.value)
        }));
    };

    return (
        <>
            <div>
                {rowData
                    ?.slice(
                        (curPage[keyName] - 1) * perPageNum[keyName],
                        curPage[keyName] * perPageNum[keyName]
                    )
                    .filter(item =>
                        !isEmpty(filterIds) ? filterIds.includes(item.id) : true
                    )
                    .map((value, idx) => {
                        // 從filter後的id陣列，找到原始陣列的index
                        let findIdx = content?.findIndex(id => id === value.id);

                        if (
                            keyName === "hasAuthor" ||
                            keyName === "hasTranslator"
                        ) {
                            findIdx = cloneLocalCreateState[keyName]?.findIndex(
                                id => id === value.id
                            );
                        }

                        return (
                            <div key={idx}>
                                <CustomSubTableView
                                    itemAt={findIdx}
                                    sheetName={sheetName}
                                    header={header}
                                    content={ct}
                                    setCallback={setCallback}
                                    setCreateState={setCreateState}
                                    createState={createState}
                                    menuName={menuName}
                                    rValue={value}
                                    options={options}
                                    cloneLocalCreateState={
                                        cloneLocalCreateState
                                    }
                                    setCloneLocalCreateStateFct={
                                        setCloneLocalCreateStateFct
                                    }
                                    isInDraggingMode={isInDraggingMode}
                                    curPage={curPage}
                                />
                            </div>
                        );
                    })}
            </div>
            <CustomPagination
                currentPage={curPage[keyName]}
                totalPages={totalPages[keyName]}
                handlePage={(evt, tmpData) => handlePage(tmpData, keyName)}
                handlePerPageNum={(evt, tmpData) =>
                    handlePerPageNum(
                        tmpData,
                        cloneLocalCreateState[keyName],
                        keyName
                    )
                }
                handleDDPage={(evt, tmpData) => handleDDPage(tmpData, keyName)}
                pageOption={pageOption}
            />
        </>
    );
};

export default CustomNoDragTableView;

import React from "react";

// ui
import { Container, Divider, Segment, Grid, Icon } from "semantic-ui-react";

// custom
import AuthorityTitle from "./AuthorityTitle";

const Authority = () => (
    <Container style={{ width: "95%" }}>
        <Segment basic compact>
            <h2>
                權威檔下載
                <Icon color="green" name="check circle outline" />
                <Icon color="red" name="exclamation circle" />
            </h2>
        </Segment>

        <Divider />

        <Grid celled>
            <Grid.Row>
                <Grid.Column width={16}>
                    <AuthorityTitle />
                </Grid.Column>
            </Grid.Row>
        </Grid>
    </Container>
);

export default Authority;

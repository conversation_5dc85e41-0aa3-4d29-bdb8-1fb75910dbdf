import React, { useContext, useEffect, useState } from "react";
import {
    Accordion,
    Button,
    Icon,
    Input,
    Modal,
    ModalActions,
    ModalContent,
    ModalHeader
} from "semantic-ui-react";
import CustomTableView from "../../../CustomTableView";
import { StoreContext } from "../../../../../../../store/StoreProvider";
import Api from "../../../../../../../api/nmtl/Api";
import { readNmtlData } from "../../../../../../../api/nmtl";
import { isEmpty } from "../../../../../../../commons";
import CustomDragTableView from "../../../CustomDragTableView";
import Act from "../../../../../../../store/actions";
import { updateMultipleValues, updateObjectValue } from "../../../helper";
import CustomNoDragTableView from "../../../CustomNoDragTableView";

const CREATE_TAG = "create";
const CustomAccordion = ({
    sheetName,
    contentSetting,
    header,
    createState,
    setCallback,
    setCreateState,
    menuName,
    isDisableSecondInput
    // cloneLocalCreateState,
    // setCloneLocalCreateState
}) => {
    const [state, dispatch] = useContext(StoreContext);
    const {
        setIsFillAllOtherNameModalOpen,
        isFillAllAuthorOtherName,
        isDragging,
        isAuthorInDraggingMode,
        isTranslatorInDraggingMode,
        cloneLocalCreateState2
    } = state.data;
    const { dataset } = state.data.mainSubject.selected;
    const [open, setOpen] = useState({});
    const [inputValueObj, setInputValueObj] = useState({});
    const [locCreateState, setLocCreateState] = useState(createState || {}); // Accordion 搜尋用

    const pageOption = [5, 10, 15, 50, 100];
    const [curPage, setCurPage] = useState({});
    const [totalPages, setTotalPages] = useState({});
    const [perPageNum, setPerPageNum] = useState({});
    const [cloneLocalCreateState, setCloneLocalCreateState] = useState([]);
    // const [cloneLocalCreateState2, setCloneLocalCreateState2] = useState([]);
    const [searchIds, setSearchIds] = useState([]);
    const [disabledAddAuthor, setDisabledAddAuthor] = useState(false);
    const [disabledAddTranslator, setDisabledAddTranslator] = useState(false);
    const [currentId, setCurrentId] = useState(createState?.srcId);
    const modalWord = isDragging
        ? `請填入所有${
              isFillAllAuthorOtherName ? "譯者顯示名稱" : "作者發表名稱"
          }或者請退出搜尋模式`
        : `請確認是否已填入所有${
              isFillAllAuthorOtherName ? "譯者顯示名稱" : "作者發表名稱"
          }或者請退出搜尋模式`;

    useEffect(() => {
        // 處理過後，不再執行
        if (!isEmpty(cloneLocalCreateState)) return;
        // 處理初次新增翻譯書或該翻譯書沒有作者、譯者的情況
        if (
            createState &&
            !createState?.hasAuthor &&
            !createState?.hasTranslator
        ) {
            const updates = [
                ["hasAuthor", ["create"]],
                ["authorName", ["新增@zh", "新增@en"]],
                ["hasTranslator", ["create"]],
                ["translatorName", ["新增@zh", "新增@en"]]
            ];

            const updatedCreateState = updateMultipleValues(
                createState,
                updates
            );

            setCloneLocalCreateState(updatedCreateState);

            dispatch({
                type: Act.DATA_SET_CLONE_CREATE_STATE,
                payload: updatedCreateState
            });

            setPerPageNum({
                hasAuthor: 5,
                hasTranslator: 5
            });

            setCurPage({
                hasAuthor: 1,
                hasTranslator: 1
            });
            return;
        }
        if (createState && !createState?.hasAuthor) {
            const updates = [
                ["hasAuthor", ["create"]],
                ["authorName", ["新增@zh", "新增@en"]]
            ];

            const updatedCreateState = updateMultipleValues(
                createState,
                updates
            );

            setCloneLocalCreateState(updatedCreateState);

            dispatch({
                type: Act.DATA_SET_CLONE_CREATE_STATE,
                payload: updatedCreateState
            });

            setPerPageNum({
                hasAuthor: 5,
                hasTranslator: 5
            });

            setCurPage({
                hasAuthor: 1,
                hasTranslator: 1
            });
            return;
        }
        if (createState && !createState?.hasTranslator) {
            const updates = [
                ["hasTranslator", ["create"]],
                ["translatorName", ["新增@zh", "新增@en"]]
            ];

            const updatedCreateState = updateMultipleValues(
                createState,
                updates
            );

            setCloneLocalCreateState(updatedCreateState);

            dispatch({
                type: Act.DATA_SET_CLONE_CREATE_STATE,
                payload: updatedCreateState
            });

            setPerPageNum({
                hasAuthor: 5,
                hasTranslator: 5
            });

            setCurPage({
                hasAuthor: 1,
                hasTranslator: 1
            });
            return;
        }
        // 為達成拖拉功能需以下state，僅用於作者及譯者區塊
        setCloneLocalCreateState(createState);
    }, [createState]);

    useEffect(() => {
        // 在更新時，需有拖拉後的資料進行更新比對，故額外存在redux內
        dispatch({
            type: Act.DATA_SET_CLONE_CREATE_STATE_FOR_UPDATE,
            payload: cloneLocalCreateState
        });
    }, [cloneLocalCreateState]);

    useEffect(() => {
        setCurrentId(createState?.srcId);
    }, [createState?.srcId]);

    useEffect(() => {
        const filterData = cloneLocalCreateState2.filter(
            el => el.srcId === currentId
        )[0];
        if (isEmpty(filterData)) return;
        setCloneLocalCreateState(filterData);
    }, [cloneLocalCreateState2]);

    function compareStringLengths(otherNameArr, ids) {
        if (isEmpty(otherNameArr)) return false;
        const firstString = otherNameArr[0].split("@")[0].split("、");
        const secondString = otherNameArr[1].split("@")[0].split("、");

        if (ids.length * 2 !== firstString.length + secondString.length) {
            return false;
        }

        if (ids.includes("create")) {
            return false;
        }

        return firstString.length === secondString.length;
    }

    useEffect(() => {
        if (
            isEmpty(cloneLocalCreateState?.authorName) ||
            isEmpty(cloneLocalCreateState?.translatorName) ||
            isEmpty(cloneLocalCreateState?.hasAuthor) ||
            isEmpty(cloneLocalCreateState?.hasTranslator)
        )
            return;

        const isFillAuthorOtherName = compareStringLengths(
            cloneLocalCreateState.authorName,
            cloneLocalCreateState.hasAuthor
        );

        const isFillTranslatorOtherName = compareStringLengths(
            cloneLocalCreateState.translatorName,
            cloneLocalCreateState.hasTranslator
        );

        if (!isFillAuthorOtherName) {
            setDisabledAddAuthor(true);
        } else {
            setDisabledAddAuthor(false);
        }
        if (!isFillTranslatorOtherName) {
            setDisabledAddTranslator(true);
        } else {
            setDisabledAddTranslator(false);
        }
    }, [cloneLocalCreateState]);
    const setCloneLocalCreateStateFct = data => {
        dispatch({
            type: Act.DATA_SET_UPDATE_CLONE_LOCAL_CREATE_STATE,
            payload: data
        });
        setCloneLocalCreateState(data);
    };

    const handleInputClick = async () => {
        const tmpObj = {};
        // const copyCreateState = JSON.parse(JSON.stringify(createState));
        const copyCreateState = JSON.parse(
            JSON.stringify(cloneLocalCreateState)
        );
        const tmpCurPage = JSON.parse(JSON.stringify(curPage));
        const tmpTotalPage = JSON.parse(JSON.stringify(totalPages));
        const isObjectEmpty = obj =>
            !Object.values(obj).some(
                value => value !== "" && value !== null && value !== undefined
            );

        // 當有搜尋值時，禁止使用者拖拉
        if (!isObjectEmpty(inputValueObj)) {
            dispatch({ type: Act.DATA_SET_IS_SEARCHING_OVER, payload: false });
        } else {
            dispatch({ type: Act.DATA_SET_IS_SEARCHING_OVER, payload: true });
        }

        // eslint-disable-next-line no-restricted-syntax
        for (const prop of Object.keys(inputValueObj)) {
            if (inputValueObj[prop]) {
                const apiStr = Api.getAuthOrTransInSinglePub
                    .replace("{ds}", dataset)
                    .replace("{pubId}", cloneLocalCreateState.srcId)
                    .replace("{aORt}", prop)
                    .replace("{keyword}", inputValueObj[prop]);

                // eslint-disable-next-line no-await-in-loop
                const res = await readNmtlData(apiStr);
                setSearchIds(res.data.map(el => el.perId));
                tmpObj[prop] = res.data.map(el => el.perId);
                tmpCurPage[prop] = 1;
                tmpTotalPage[prop] = Math.ceil(
                    tmpObj[prop].length / perPageNum[prop]
                );
            } else {
                setSearchIds([]);
                tmpCurPage[prop] = 1;
                tmpTotalPage[prop] = Math.ceil(
                    cloneLocalCreateState[prop].length / perPageNum[prop]
                );
            }
        }

        setCurPage(tmpCurPage);
        setTotalPages(tmpTotalPage);
        // 存放搜尋到的ID
        setLocCreateState({ ...copyCreateState, ...tmpObj });
    };

    const handleInputKeyPress = event => {
        if (event.key === "Enter") {
            handleInputClick();
        }
    };

    const handleInputChange = (value, inputProp) => {
        setInputValueObj({ ...inputValueObj, [inputProp]: value });
    };
    const handleClickOpen = (props, key) => {
        setOpen(prevState => ({ ...prevState, [key]: !props.active }));
    };

    const handleAdd = target => {
        if (
            !cloneLocalCreateState ||
            !cloneLocalCreateState[target] ||
            cloneLocalCreateState[target].indexOf(CREATE_TAG) > -1
        ) {
            return;
        }
        // 新增在最前面
        // createState[target].unshift(CREATE_TAG);
        cloneLocalCreateState[target].unshift(CREATE_TAG);

        // 更新頁碼
        setTotalPages(prevState => ({
            ...prevState,
            [target]: Math.ceil(
                cloneLocalCreateState[target]?.length / perPageNum[target]
            )
        }));

        // 新增項目時，搜尋回到初始
        setLocCreateState(preState => ({
            ...preState,
            [target]: cloneLocalCreateState[target]
        }));
        // 清空搜尋框
        setInputValueObj({ ...inputValueObj, [target]: "" });
        const updateCloneLocalCreateState = updateObjectValue(
            cloneLocalCreateState,
            target,
            cloneLocalCreateState[target]
        );

        setCloneLocalCreateStateFct(updateCloneLocalCreateState);
        dispatch({
            type: Act.DATA_SET_CLONE_CREATE_STATE,
            payload: updateCloneLocalCreateState
        });
        // setCallback(target, createState[target], menuName);
    };
    return contentSetting.map((area, index) => {
        const {
            Accordion: useAcc,
            accList: keyName,
            accTitle,
            searchBar,
            content: ct
        } = area;

        const content =
            createState && !isEmpty(createState[keyName])
                ? JSON.parse(JSON.stringify(createState[keyName]))
                : null;

        if (useAcc) {
            if (!Object.hasOwn(open, keyName)) {
                setOpen(prevState => ({ ...prevState, [keyName]: true }));
            }

            if (content && !Object.hasOwn(curPage, keyName)) {
                const tmpObj = JSON.parse(JSON.stringify(curPage));
                tmpObj[keyName] = 1;
                setCurPage(tmpObj);
            }

            if (content && !Object.hasOwn(perPageNum, keyName)) {
                const tmpPerPage = JSON.parse(JSON.stringify(perPageNum));
                // eslint-disable-next-line prefer-destructuring
                tmpPerPage[keyName] = pageOption[0];
                setPerPageNum(tmpPerPage);
            }

            if (content && !Object.hasOwn(totalPages, keyName)) {
                const tmpTotalPage = JSON.parse(JSON.stringify(totalPages));
                tmpTotalPage[keyName] = Math.ceil(
                    content?.length / pageOption[0]
                );
                setTotalPages(tmpTotalPage);
            }

            if (
                createState &&
                Object.hasOwn(createState, keyName) &&
                !Object.hasOwn(locCreateState, keyName)
            ) {
                setLocCreateState(prevState => ({
                    ...prevState,
                    [keyName]: createState[keyName]
                }));
            }

            const draggingMode =
                keyName === "hasAuthor"
                    ? isAuthorInDraggingMode
                    : isTranslatorInDraggingMode;

            return (
                <Accordion
                    fluid
                    styled
                    style={{ margin: "2rem 0" }}
                    key={`CustomTableView-accordion-${index}-${keyName}`}
                >
                    <Accordion.Title
                        active={open[keyName]}
                        onClick={(e, props) => handleClickOpen(props, keyName)}
                    >
                        <Icon name="dropdown" />
                        {accTitle}
                    </Accordion.Title>
                    <Accordion.Content active={open[keyName]}>
                        {searchBar && (
                            <Input
                                fluid
                                type="text"
                                placeholder="請搜尋..."
                                value={inputValueObj[keyName] || ""}
                                onChange={(event, { value }) =>
                                    handleInputChange(value, keyName)
                                }
                                onKeyPress={handleInputKeyPress}
                                icon
                            >
                                <input />
                                <Icon
                                    link
                                    name="search"
                                    onClick={handleInputClick}
                                />
                            </Input>
                        )}
                        <Button
                            color={draggingMode ? "red" : "green"}
                            content={draggingMode ? "完成" : "編輯"}
                            floated="right"
                            style={{ margin: "1rem 0" }}
                            onClick={() => {
                                dispatch({
                                    type:
                                        keyName === "hasAuthor"
                                            ? Act.DATA_SET_IS_AUTHOR_IN_DRAGGINGMODE
                                            : Act.DATA_SET_IS_TRANSLATOR_IN_DRAGGINGMODE,
                                    payload:
                                        keyName === "hasAuthor"
                                            ? !isAuthorInDraggingMode
                                            : !isTranslatorInDraggingMode
                                });

                                setTotalPages(prevState => ({
                                    ...prevState,
                                    [keyName]: Math.ceil(
                                        cloneLocalCreateState[keyName]?.length /
                                            perPageNum[keyName]
                                    )
                                }));
                            }}
                        />
                        {draggingMode && (
                            <Button
                                color="green"
                                icon="plus"
                                content={`新增${accTitle}`}
                                floated="right"
                                style={{ margin: "1rem 1rem 0 1rem" }}
                                onClick={() => handleAdd(keyName)}
                                disabled={
                                    keyName === "hasAuthor"
                                        ? disabledAddAuthor
                                        : disabledAddTranslator
                                }
                            />
                        )}
                        {/* {createState && !isEmpty(createState[keyName]) ? ( */}
                        {cloneLocalCreateState &&
                        !isEmpty(cloneLocalCreateState[keyName]) ? (
                            <React.Fragment>
                                {!draggingMode ? (
                                    <>
                                        <CustomNoDragTableView
                                            keyName={keyName}
                                            createState={createState}
                                            content={content}
                                            sheetName={sheetName}
                                            setCallback={setCallback}
                                            setCreateState={setCreateState}
                                            menuName={menuName}
                                            header={header}
                                            ct={ct}
                                            // data={cloneLocalCreateState[keyName]}
                                            filterIds={searchIds}
                                            setLocCreateState={
                                                setLocCreateState
                                            }
                                            cloneLocalCreateState={
                                                cloneLocalCreateState
                                            }
                                            setCloneLocalCreateStateFct={
                                                setCloneLocalCreateStateFct
                                            }
                                            isInDraggingMode={draggingMode}
                                            pageOption={pageOption}
                                            curPage={curPage}
                                            setCurPage={setCurPage}
                                            totalPages={totalPages}
                                            setTotalPages={setTotalPages}
                                            perPageNum={perPageNum}
                                            setPerPageNum={setPerPageNum}
                                        />
                                    </>
                                ) : (
                                    <>
                                        <CustomDragTableView
                                            keyName={keyName}
                                            createState={createState}
                                            content={content}
                                            sheetName={sheetName}
                                            setCallback={setCallback}
                                            setCreateState={setCreateState}
                                            menuName={menuName}
                                            header={header}
                                            ct={ct}
                                            // data={createState[keyName]}
                                            filterIds={searchIds}
                                            setLocCreateState={
                                                setLocCreateState
                                            }
                                            cloneLocalCreateState={
                                                cloneLocalCreateState
                                            }
                                            setCloneLocalCreateStateFct={
                                                setCloneLocalCreateStateFct
                                            }
                                            isInDraggingMode={draggingMode}
                                        />
                                    </>
                                )}
                                <Modal open={setIsFillAllOtherNameModalOpen}>
                                    <ModalHeader>儲存失敗</ModalHeader>
                                    <ModalContent>
                                        <p>{modalWord}</p>
                                    </ModalContent>
                                    <ModalActions>
                                        <Button
                                            positive
                                            onClick={() => {
                                                dispatch({
                                                    type:
                                                        Act.DATA_SET_IS_FILL_ALL_OTHERNAME_MODAL_OPEN,
                                                    payload: false
                                                });
                                                dispatch({
                                                    type:
                                                        Act.DATA_SET_IS_DRAGGING,
                                                    payload: false
                                                });
                                            }}
                                        >
                                            確認
                                        </Button>
                                    </ModalActions>
                                </Modal>
                            </React.Fragment>
                        ) : (
                            // <CustomTableView
                            //     key={`CustomTableView-accordion-${0}-${keyName}`}
                            //     itemAt={0}
                            //     sheetName={sheetName}
                            //     header={header}
                            //     content={ct}
                            //     setCallback={setCallback}
                            //     setCreateState={setCreateState}
                            //     createState={createState}
                            //     menuName={menuName}
                            // />
                            <CustomNoDragTableView
                                keyName={keyName}
                                createState={createState}
                                content={content}
                                sheetName={sheetName}
                                setCallback={setCallback}
                                setCreateState={setCreateState}
                                menuName={menuName}
                                header={header}
                                ct={ct}
                                // data={createState[keyName]}
                                filterIds={searchIds}
                                setLocCreateState={setLocCreateState}
                                cloneLocalCreateState={cloneLocalCreateState}
                                setCloneLocalCreateStateFct={
                                    setCloneLocalCreateStateFct
                                }
                                isInDraggingMode={draggingMode}
                                pageOption={pageOption}
                                curPage={curPage}
                                setCurPage={setCurPage}
                                totalPages={totalPages}
                                setTotalPages={setTotalPages}
                                perPageNum={perPageNum}
                                setPerPageNum={setPerPageNum}
                            />
                        )}
                    </Accordion.Content>
                </Accordion>
            );
        }

        return (
            <CustomTableView
                key={`customTableView-${index}`}
                sheetName={sheetName}
                header={header}
                content={ct}
                setCallback={setCallback}
                setCreateState={setCreateState}
                createState={createState}
                menuName={menuName}
                isDisableSecondInput={isDisableSecondInput}
            />
        );
    });
};

export default CustomAccordion;

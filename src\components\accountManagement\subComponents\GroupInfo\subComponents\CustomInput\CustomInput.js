import React, { useEffect, useState } from "react";

// plugins
import { useDispatch, useSelector } from "react-redux";
import { Input, Header, Icon } from "semantic-ui-react";
import textConfig from "../../../Utils/textConifg";

// scss
import "./CustomInput.scss";

//
import useDebounce from "../../../../../common/hooks/useDebounce";
import accMngAct from "../../../../../../reduxStore/accManage/accManageAction";

function CustomInput() {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { groupData } = state;
    const [groupName, setGroupName] = useState(null);
    const debSearchValue = useDebounce(groupName, 800);

    const onChange = (evt, data) => {
        setGroupName(data.value);
    };

    useEffect(() => {
        if (groupName === null) return;
        const tmpGroupData = JSON.parse(JSON.stringify(groupData));
        tmpGroupData.name = debSearchValue;
        dispatch({
            type: accMngAct.SET_GROUPDATA,
            payload: tmpGroupData
        });
    }, [debSearchValue]);

    return (
        <div className="CustomInput">
            <Header as="h2" textAlign="center">
                <Icon name="users" circular />
                <Header.Content>
                    {textConfig.GROUPINFO_Input_Title}
                </Header.Content>
            </Header>
            <Input
                onChange={onChange}
                className="CustomInput__Input"
                placeholder={textConfig.GROUPINFO_Input_PLH}
                defaultValue={groupData.name}
            />
        </div>
    );
}

export default CustomInput;

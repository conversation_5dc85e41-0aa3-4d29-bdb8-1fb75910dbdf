import React, { useState } from "react";
import PropTypes from "prop-types";
import { useDispatch } from "react-redux";

// semantic ui
import { Icon, Image, Label } from "semantic-ui-react";

// images
import noImage from "../../../../images/No_image_200x200.png";

// components
import ImagePicker from "../../../dataset/subComponents/content/CustomImageInput/ImagePicker";

// uitls
import FileAct from "../../../../reduxStore/file/fileAction";
import { getImgFolderPattern } from "../../../common/imageCommon/FolderList/folderListHelper";
import uploadConfig from "../../../toolPages/components/upload/uploadConfig";

const noImgPlaceholderUrl =
    "https://dummyimage.com/200x200/fff/000&text=No_image";

const fsRootUrl = process.env.REACT_APP_FILE_SERVER_URL;

function ImageInput({ imagePath, imageName, getSelectedFile }) {
    const dispatchRedux = useDispatch();
    const [isHover, setIsHover] = useState({ zIndex: "auto", scale: 1 });
    const [open, setOpen] = useState(false);

    const style = {
        zIndex: isHover.zIndex,
        transform: `scale(${isHover.scale})`,
        borderRadius: "unset"
    };

    const imageFullPath = resize => {
        if (!imagePath || !imageName) return "";
        if (resize) {
            return `${fsRootUrl}/read/upload/${imagePath}/600x600_${imageName}`;
        }
        return `${fsRootUrl}/read/upload/${imagePath}/${imageName}`;
    };

    const onMouseEnter = () => {
        // 沒有圖片，不需要做放大效果
        if (!imagePath || !imageName) {
            return;
        }
        setIsHover({ zIndex: "100", scale: 10 });
    };
    const onMouseLeave = () => {
        setIsHover({ zIndex: "auto", scale: 1 });
    };

    const openImgPicker = () => {
        dispatchRedux({
            type: FileAct.SET_DEFAULT_VALUE,
            payload: imageFullPath(false) || ""
        });
        dispatchRedux({
            type: FileAct.SELECT_FILE,
            payload: ""
        });
        getImgFolderPattern(dispatchRedux, uploadConfig.ApiGetImages);
        setOpen(true);
    };

    const closeImgPicker = selectFile => {
        getSelectedFile(selectFile);
        setOpen(false);
    };

    return (
        <div>
            <Label image basic style={{ display: "flex" }}>
                <Image
                    src={imageFullPath(true) || noImage || noImgPlaceholderUrl}
                    alt={imageName}
                    style={style}
                    onMouseEnter={onMouseEnter}
                    onMouseLeave={onMouseLeave}
                />
                <div style={{ flex: "1 1 auto" }}>{imageName}</div>
                <Label.Detail onClick={openImgPicker}>
                    <Icon name="add" />
                </Label.Detail>
            </Label>
            <ImagePicker
                open={open}
                setOpen={setOpen}
                defaultValue={imageFullPath(false)}
                onValueChange={closeImgPicker}
            />
        </div>
    );
}

ImageInput.propTypes = {
    /** 從資料庫讀到imagePath值 */
    imagePath: PropTypes.string,
    /** 從資料庫讀到imageName值 */
    imageName: PropTypes.string,
    /** get select file path callback */
    getSelectedFile: PropTypes.func
};

ImageInput.defaultProps = {
    /** 從資料庫讀到imagePath值 */
    imagePath: "",
    /** 從資料庫讀到imageName值 */
    imageName: "",
    /** get select file path callback */
    getSelectedFile: () => null
};

export default ImageInput;

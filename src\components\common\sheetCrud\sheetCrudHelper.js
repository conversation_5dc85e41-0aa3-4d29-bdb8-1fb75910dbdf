import { isEmpty } from '../../../commons';
import { CHECK_CELL_VALUE_LANG } from '../../dataset/subComponents/CreateComp/createConfig';
import { ApiField, checkLangTag, isCorrectSuffix } from '../../../api/nmtl/ApiField';
import { specialConvertToOption } from './utils';
import { classPrefix } from '../../../api/nmtl/classPrefix';
import { graphOptions, literaryOptions } from './config';
import datasetSheet from '../../dataset/datasetSheet';

const { isObjectEqual } = require('../../../commons/index');

const mergeGraphLiteraryOpts = graphOptions.concat(literaryOptions);

const propertyConvert = {
    // BasicInfo
    hasIntroductionWriter: 'hasIntroductionWriter__Person',
    hasOccupation: 'hasOccupation__Occupation',
    hasLiteraryAreaIn: 'hasLiteraryAreaIn__AreaOfTaiwan',
    hasAncestralHome: 'hasAncestralHome__Location',
    hasEthnicGroup: 'hasEthnicGroup__EthnicGroup',
    hasFamilyOrigin: 'hasFamilyOrigin__Location',
    hasCountryOfOrigin: 'hasCountryOfOrigin__Location',
    hasPlaceOfBirth: 'hasPlaceOfBirth__Location',
    hasPlaceOfDeath: 'hasPlaceOfDeath__Location',
    hasGender: 'hasGender__Gender',
    hasSource: 'hasSource',
    hasCopyrightStatus: 'hasCopyrightStatus',
    // Education
    isEducationOf: 'isEducationOf',
    hasEducatedAt: 'hasEducatedAt__Organization',
    hasExamination: 'hasExamination__Examination',
    hasAcademicDiscipline: 'hasAcademicDiscipline__AcademicDiscipline',
    hasAcademicDegree: 'hasAcademicDegree__AcademicDegree',
    hasSocialProcedure: 'hasSocialProcedure__SocialProcedure',
    // Organization
    isOrganizationOf: 'isOrganizationOf',
    hasFoundedIds: 'hasFounded__Organization',
    hasParticipant: 'hasParticipant__Organization',
    hasPosition: 'hasPosition__Position',
    // Speciality
    isSpecialtyOf: 'isSpecialtyOf',
    hasSpecialty: 'hasSpecialty__Specialty',
    // Relation
    isRelationOf: 'isRelationOf',
    // Event
    isEventOf: 'isEventOf',
    hasSetInPeriod: 'hasSetInPeriod__TaiwanPeriod',
    hasResidence: 'hasResidence__Location',
    hasEventLocation: 'hasEventLocation__Location',
    hasImageCopyright: 'hasCopyrightStatus__CopyrightStatus',
    hasRelatedWorks: 'hasRelatedWorks',
    hasTlvmPeriod: 'hasTlvmPeriod__TlvmPeriod',
    hasRelatedPerson: 'hasRelatedPerson__Person',
    hasCopyrightStatus__Event: 'hasCopyrightStatus__Event',
    // Foundation
    hasFoundation: 'hasFoundation',
    hasFoundationRelated: 'hasFoundationRelated',
    // Publication
    publication: 'label_Publication',
    hasPlaceOfPublication: 'hasPlaceOfPublication__Location',
    hasNarrativeLocation: 'hasNarrativeLocation__Location',
    hasInceptionDate: 'hasInceptionDate__DateEvent',
    hasWritingLocation: 'hasWritingLocation__Location',
    hasStartDate: 'hasStartDate__DateEvent',
    hasEndDate: 'hasEndDate__DateEvent',
    hasPublishedIn: 'hasPublishedIn',
    hasTranslationBook: 'hasTranslationBook',
    isTranslationBookOf: 'isTranslationBookOf',
    hasLanguageOfWorkOrName: 'hasLanguageOfWorkOrName__Language',
    MainSubjectTypes: 'MainSubject',
    LiteraryGenres: 'LiteraryGenre',
    hasDerivateWork: 'hasDerivateWork',
    hasCopyrightStatus__Publication: 'hasCopyrightStatus__Publication',
    // Article
    hasDescribedTarget: 'hasDescribedTarget',
    hasDescribedPoet: 'hasDescribedPoet',
    hasDescribedPoetClub: 'hasDescribedPoetClub',
    hasDataWriter: 'hasDataWriter__Person',
    hasCopyrightStatus__Article: 'hasCopyrightStatus__Article',
    // Award
    isAwardReceivedOf: 'isAwardReceivedOf',
    hasAwardReceived: 'hasAwardReceived__Award',
    hasAwardedForWork: 'hasAwardedForWork',
    // Project
    hasLocation: 'hasLocation__Location',
    // Location
    isAreaOf: 'isAreaOf',
    hasDynasty: 'hasDynasty',
    hasCountry: 'hasCountry',
    hasProvince: 'hasProvince',
    hasCity: 'hasCity',
    hasTownship: 'hasTownship',
    hasCurrentCountry: 'hasCurrentCountry',
    hasCurrentProvince: 'hasCurrentProvince',
    hasCurrentCity: 'hasCurrentCity',
    hasCurrentTownship: 'hasCurrentTownship',
    // 新舊組織
    hasReplaced: 'hasReplaced',
    isReplacedBy: 'isReplacedBy',
    // OrganizationInfo
    organization: 'label_Organization',
    hasLocationOfFormation: 'hasLocationOfFormation__Location',
    hasWorkLocation: 'hasWorkLocation__Location',
    hasInteractive: 'hasInteractive__Organization',
    // Collectible
    hasCollection: 'hasCollection',
    hasUser: 'hasUser',
    hasCollector: 'hasCollector',
    hasAppraiser: 'hasAppraiser',
    hasContributor: 'hasContributor',
    hasOwnedBy: 'hasOwnedBy',
    hasEntryDate: 'hasEntryDate__DateEvent',
    hasAdmissionDate: 'hasAdmissionDate__DateEvent',
    hasCreator: 'hasCreator',
    isCollectedBy: 'isCollectedBy',
    hasCopyrightStatus__Collectible: 'hasCopyrightStatus__Collectible',
    // DerivateWork
};

export const multiProperties = [
    'CollectibleType',
    'tPersonID',
    'relationType',
    'hasNarrativeLocation',
    'hasAuthor',
    'hasEditor',
    'hasModifier',
    'hasTranslator',
    'hasOraler',
    'hasPlaceOfBirth',
    'hasPlaceOfDeath',
    'hasCollector',
    'hasCollator',
    'hasInterviewer',
    'hasIllustrator',
    'hasInscriptioner',
    'hasReviewer',
    'hasWriter',
    'hasPrefaceAuthor',
    'hasSponsor',
    'hasCharacters',
    'hasPublisher',
    'hasLanguageOfWorkOrName',
    'hasPublishedIn',
    'hasTranslationBook',
    'isTranslationBookOf',
    'hasLiteraryAreaIn',
    'hasDerivateWork',
    'hasSetInPeriod',
    'MainSubject',
    'LiteraryGenre',
    'hasPrincipalInvestigator',
    'hasPlaceOfPublication',
    'hasRequester',
    'hasDynasty',
    'hasFoundationType',
    'isCollectedBy',
    'isEventOf',
    'isAwardReceivedOf',
    'isOrganizationOf',
    'hasIntroductionWriter',
    'hasEthnicGroup',
    'hasDescribedTarget',
    'hasDescribedPoet',
    'hasDescribedPoetClub',
    'hasDataWriter',
    'hasWritingLocation',
    'hasSpecialty',
    'hasRelatedWorks',
    'hasRelatedPerson',
    'hasResidence',
    'hasEventLocation',
    'hasCreator',
    'hasUser',
    'hasAppraiser',
    'hasContributor',
    'hasOwnedBy',
    'hasLocationOfFormation',
    'hasWorkLocation',
    'hasInteractive',
    'hasLocation',
    'hasFoundationRelated',
];

// Multiple values with restrict (cannot create new)
export const MULTI_RESTRICT = [
    'hasLiteraryAreaIn',
    'MainSubject',
    'LiteraryGenre',
    'hasDynasty',
    'hasDerivateWork',
    'hasEthnicGroup',
    'hasNanziLanguage',
    'literary',
    'graph',
    'Dataset',
];

// Single values (can create new)
export const SINGLE_CREATABLE = [
    'hasFamilyOrigin',
    'hasCountryOfOrigin',
    'hasAncestralHome',
    'hasEducatedAt',
    'hasExamination',
    'hasAcademicDiscipline',
    'hasAcademicDegree',
    'hasSocialProcedure',
    'isEducationOf',
    'isRelationOf',
    'hasFounded',
    'hasParticipant',
    'hasPosition',
    'hasOccupation',
    'isSpecialtyOf',
];

// 使用 Markdown 編輯器
export const MD_EDITOR = ['introduction', 'fullWorkAvailableAt', 'desc'];

// Date
export const DATE_EVENTS = [
    'hasAdmissionDate',
    'hasBirthDate',
    'hasDeathDate',
    'hasStartDate',
    'hasEndDate',
    'hasEntryDate',
    'hasInceptionDate',
    'hasDescribeEventDate',
    'hasInceptionDate',
];

// rich information so we have to provide a textarea for modifying.
export const RICH_INFO = [
    'label_Source',
    'introduction',
    'comment',
    'preface',
    'prologue',
    'references',
    'describeCategory',
    'collectionDesc',
    'eraDesc',
    'sourceDesc',
    'collectionStatusDesc',
    'culturalRelicsComment',
    'keywords',
    'tableOfContents',
];

export const SHOW_ID = [
    'hasPublishedIn',
    'hasTranslationBook',
    'isTranslationBookOf',
    'hasDescribedTarget',
    'hasCollection',
];

export const EDIT_IN_MODAL = [
    'hasTlvmPeriod',
];

export const contentTypeToClassType = {
    [datasetSheet.BasicInfo.api]: 'Person',
    [datasetSheet.Education.api]: 'EducationEvent',
    [datasetSheet.Organization.api]: 'OrganizationEvent',
    [datasetSheet.Specialty.api]: 'SpecialtyEvent',
    [datasetSheet.Event.api]: 'Event',
    [datasetSheet.Foundation.api]: 'FoundationEvent',
    [datasetSheet.Publication.api]: 'Publication',
    [datasetSheet.Article.api]: 'Article',
    [datasetSheet.Award.api]: 'AwardEvent',
    [datasetSheet.Project.api]: 'Project',
    [datasetSheet.Location.api]: 'Location',
    [datasetSheet.ReplaceOrg.api]: 'Organization',
    [datasetSheet.OrganizationInfo.api]: 'Organization',
    [datasetSheet.Collectible.api]: 'Collectible',
    [datasetSheet.Relationship.api]: 'RelationEvent',
    [datasetSheet.DerivateWork.api]: 'DerivateWork',
    [datasetSheet.TlvmPeriod.api]: 'TlvmPeriod',
    // 'fictionalcharacter/1.0': 'FictionalCharacter',
    [datasetSheet.Nanzi.api]: 'Nanzi',

    // specialTable
    [datasetSheet.Person.api]: 'Person',
    [datasetSheet.PublicationInfo.api]: 'Publication',
};

export const forCreateDropdown = ['PersonID', 'OrganizationID', 'LocationID'];

// srcId 可能隱藏在這些地方
// 這些 ID 都可以新建資料
export const idParams = [
    'EducationEvent',
    'OrganizationEvent',
    'SpecialtyEvent',
    'RelationEvent',
    'replaceOrgLabel',
    // "replaceOrgComment",
    // "ReplaceOrgEvent",
    'Event',
    'FoundationEvent',
    'PublicationID',
    'ArticleID',
    'AwardEvent',
    'ProjectID',
    'CollectibleID',
    'DerivateWorkID',
    'TlvmPeriodID',
    'FictionalCharacterID',
    'NanziID',
].concat(forCreateDropdown);

// default graph for creating instance
// for example, if class is Person, then the default graph is authority
export const graph4CreateInstance = {
    Person: 'authority',
    // TODO: 未來若要新增其他類別，請在此處新增
};

// graphs for copying instance with only instanceId
// for example:
// if Class is Person && dataset(string) is in mergeGraphLiteraryOpts.map(g=>g.value)
// then copy only instanceId (nmtl:PER999999 a nmtl:Person) from srcGraph to dstGraph
export const dstGraph4CopyOnlyInstanceId = {
    Person: mergeGraphLiteraryOpts.map((g) => g.value),
    // TODO: 未來若要新增其他類別，請在此處新增
};

export const LABEL_PREFIX = 'label_';
export const CREATED_PREFIX = 'ToBeCreated_';
// Special case: label_Event should be shown in all tables (Add/Edit/Read).
// export const LABEL_EVENT = "label_Event";
export const TO_CREATE_REPLACE_ORG = 'replaceOrgComment';
export const REPLACE_ORG_LABEL = 'replaceOrgLabel';
// export const NOT_SHOWN_IN_TABLE = idParams;
export const NOT_SHOWN_IN_TABLE = idParams.filter((el) => el !== TO_CREATE_REPLACE_ORG);
export const NOT_SHOWN_IN_ADD = ['seqid'];
export const NOT_SHOWN_IN_EDIT = NOT_SHOWN_IN_TABLE.concat(NOT_SHOWN_IN_ADD);
export const LAST_MODIFIED = 'lastModified';
export const PEICES_INFO = 'peicesInfo';
export const CASE_INFO = 'caseInfo';
export const TranslationBookName = 'translationBookName';
export const IsTranslationBookOf = 'isTranslationBookOf';
export const LabelPublication = 'label_Publication';
export const PublicationInfo = 'PublicationInfo';

export const TranslationDefault = 'default';

export const markToAdd = 'addLabel';
export const specialHeaderRemove = [LAST_MODIFIED];

const skipProperty = [
    // "graph",
    'PersonID',
    'id',
    'replacedOrganization',
    'person',
    LAST_MODIFIED,
].concat(idParams);

// 唯讀欄位
export const DISABLED_CHANGE = ['srcId', LAST_MODIFIED];
export const EDIT_BUTTON = ['edit'];

// special cases
const RelationEvent = 'RelationEvent';
export const hasRelationship = 'relationType';
export const tPersonID = 'tPersonID';

// Foundation
const FoundationEvent = 'FoundationEvent';
export const hasFoundationType = 'hasFoundationType';
export const hasFoundationRelated = 'hasFoundationRelated';

// Special case: 新舊組織
const hasReplaced = 'hasReplaced';
const isReplacedBy = 'isReplacedBy';
export const SET_TIMEOUT = 120000;
export const MAX_OPTION = 100;
export const CREATE_ID = 101;
// Special key in header, just use in create case, have to remove it when export to excel.
export const REMOVE_FROM_HEADER = 'forCreate';
// imageURL
export const IMAGE_URL = 'imageURL_hasURL';
export const IMAGE_KEY = 'hasURL';
export const LOCATION_LABEL = 'srcId_hasPlaceOfPublication';
export const LOCATION_KEY = 'hasPlaceOfPublication';

export const LOCATION_TYPE = 'Location';
export const TMP_LOCATION_KEY = 'labelLocation';
export const LOCATION_NAME = `${TMP_LOCATION_KEY}_hasPlaceOfPublication`;
export const LOCATION_LAT = 'geoLatitude_hasPlaceOfPublication';
export const LOCATION_LONG = 'geoLongitude_hasPlaceOfPublication';
export const GLOBAL_DEFAULT = 'default';
// 全文上傳
export const FULLTEXT_URL = 'fileAvailableAt';
// 需要繁簡轉換的key
export const textArray = [
    'all',
    'hasLanguageOfWorkOrName',
    'label_Publication',
    'hasAuthor',
    'translationBookName',
    'label_Person',
    'hasTranslationLanguage',
];

// keywords, have to set both keyword and keywords
const KEYWORDS = 'keywords';
const KEYWORD = 'keyword';

// SubValue
export const SubValues = {
    // property 的第一個值必須有值，不然會造成 Event 在後台操作被移除
    hasSource: { type: 'Source', property: ['label'] },
    [IMAGE_KEY]: {
        type: 'URLEvent',
        property: ['imageName', 'imagePath', 'imageFrom', 'hasCopyrightStatus'],
    },
    // only for specialTable create/edit
    [LOCATION_KEY]: {
        type: LOCATION_TYPE,
        property: [TMP_LOCATION_KEY, 'geoLatitude', 'geoLongitude'],
    },
    format: {
        type: 'Format',
        property: ['length', 'width', 'height', 'thickness', 'diameter', 'weight'],
    },
    // 新舊組織
    // 因更新組織基本資訊的label，type會被轉為DUMMY，需暫時註解，後續須與新舊組織表單一起修正
    // [hasReplaced]: {
    //     type: "Organization",
    //     replaceParentType: "DUMMY",
    //     property: ["isReplacedBy"]
    // }
};
const REMOVE_PROPERTIES = [
    'srcId',
    'hasSourceId',
    hasRelationship,
    tPersonID,
    'hasCopyrightStatus',
    'hasURLId',
    isReplacedBy,
    hasFoundationRelated,
    TranslationBookName,
    'geoLatitude_hasPlaceOfPublication',
    'geoLongitude_hasPlaceOfPublication',
    LOCATION_LABEL,
    // 由 API 自動更新，使用者無法修改
    LAST_MODIFIED,
];
// const REPLACE_PROPERTIES = {
//     [TranslationBookName]: LabelPublication
// };
export const checkPropertyData = (newArr, oriArr) =>
    newArr.every((property) => oriArr.indexOf(property) > -1) && newArr.length === oriArr.length;

// Split values but some properties should not be split.
export const splitMultiValues = (property, value) => {
    if (!value) {
        // 沒有值，代表要刪除，null 會通知 API 做刪除動作
        return null;
    }

    // 字串
    if (typeof value === 'string' || typeof value === 'boolean') {
        return [value];
    }

    // Property should not be split.
    if (RICH_INFO.indexOf(property) > -1) {
        return Array.isArray(value) ? value : Object.values(value);
    }

    // 已經是 Array 了
    if (Array.isArray(value)) {
        return value;
    }

    // { 0: "Male" }
    return Object.values(value);
};

// 配合 label_Source, label 在前面
export const bindSP = (s, p) => `${s}_${p}`;

// hasSource: [
// {srcId, classType, value: {label: "label1"}},
// {srcId, classType, value: {label: "label2"}}
// ]
const arrangeSubValue = (dataJson, sv, isDstJson = false) => {
    // no such sub key
    if (Object.keys(dataJson).indexOf(sv) < 0) {
        return '';
    }

    const resSubVal = [];
    // 特殊情況: specialTable create SubValue
    if (isEmpty(dataJson[sv])) {
        const subVal = {
            srcId: '',
            classType: SubValues[sv].type,
            value: {},
        };

        const subProperty = SubValues[sv].property;

        subProperty.forEach((sp) => {
            const prefixSP = sp === 'label' ? bindSP(sp, subVal.classType) : bindSP(sp, sv);
            if (Object.keys(dataJson).includes(prefixSP)) {
                subVal.value[sp] = splitMultiValues(prefixSP, dataJson[prefixSP]);
            }
        });
        resSubVal.push(subVal);
    } else {
        Object.keys(dataJson[sv]).forEach((kIdx) => {
            // hasSource: ["id1", "id2", "id3"]
            const subVal = {
                srcId: dataJson[sv][kIdx],
                classType: SubValues[sv].type,
                value: {},
            };

            const subProperty = SubValues[sv].property;
            subProperty.forEach((sp) => {
                const prefixSP = sp === 'label' ? bindSP(sp, subVal.classType) : bindSP(sp, sv);

                if (Object.keys(dataJson).includes(prefixSP)) {
                    if (prefixSP === LOCATION_NAME) {
                        subVal.value.label_Location = splitMultiValues(
                            prefixSP,
                            dataJson[prefixSP] && typeof dataJson[prefixSP] !== 'string'
                                ? dataJson[prefixSP][kIdx]
                                : dataJson[prefixSP],
                        );
                        return;
                    }
                    subVal.value[sp] = splitMultiValues(
                        prefixSP,
                        dataJson[prefixSP] && typeof dataJson[prefixSP] !== 'string'
                            ? dataJson[prefixSP][kIdx]
                            : dataJson[prefixSP],
                    );
                }
            });
            resSubVal.push(subVal);
        });
    }
    return resSubVal;
};

const convertStrToObject = (dataset, keyName, strValue) => {
    const infoArr = strValue.split('\n');
    return infoArr.reduce((acc, cur) => {
        if (cur === markToAdd) return acc;

        const [id, label] = cur.split('___');

        const { newClass, newValue } = isCorrectSuffix(keyName, label);
        const typePrefix = classPrefix.find((item) => item.eventType === newClass)?.prefix || '';

        const isValidId = typePrefix.length > 0 && id.startsWith(typePrefix);
        const isSpecialCase = ['hasEditor', 'hasPublisher'].includes(keyName);
        if (
            isValidId &&
            (!isSpecialCase || keyName === 'hasEditor' || keyName === 'hasPublisher')
        ) {
            acc.push({
                graph: dataset,
                classType: newClass,
                srcId: id,
                value: {
                    [`label_${newClass}`]: isSpecialCase ? newValue : `${newValue}@zh`,
                },
            });
        }
        return acc;
    }, []);
};

export const convertToGeneric = (srcJson, dstJson, contentType, dataset, isTltc = true) => {
    const newSrcJson = {
        graph: dataset,
        srcId: srcJson.srcId,
        classType: contentTypeToClassType[contentType],
        value: {},
    };
    const newDstJson = {
        graph: dataset,
        srcId: dstJson.srcId,
        classType: contentTypeToClassType[contentType],
        value: {},
    };

    // 特殊處理，必須所有的值都設定了才能處理的情況
    // RelationEvent, Foundation
    if (newSrcJson && newSrcJson?.classType === RelationEvent) {
        // src
        const { [hasRelationship]: srcRelation, [tPersonID]: srcToPerson } = srcJson;

        // srcRelation:{0: "hasAdoptedMother"} srcToPerson:{0: "PER7776"}
        if (srcRelation && srcToPerson) {
            const multiSrcRelation = splitMultiValues(hasRelationship, srcRelation);
            const multiSrcToPerson = splitMultiValues(tPersonID, srcToPerson);

            multiSrcRelation.forEach((rel) => {
                newSrcJson.value[rel] = multiSrcToPerson;
            });

            // eslint-disable-next-line no-param-reassign
            delete srcJson[hasRelationship];
            // eslint-disable-next-line no-param-reassign
            delete srcJson[tPersonID];
        }

        // dst
        const { [hasRelationship]: dstRelation, [tPersonID]: dstToPerson } = dstJson;

        // srcRelation:{0: "hasAdoptedMother"} srcToPerson:{0: "PER7776"}
        if (dstRelation && dstToPerson) {
            const multiDstRelation = splitMultiValues(hasRelationship, dstRelation);
            const multiDstToPerson = splitMultiValues(tPersonID, dstToPerson);

            multiDstRelation.forEach((rel) => {
                newDstJson.value[rel] = multiDstToPerson;
            });

            // eslint-disable-next-line no-param-reassign
            delete dstJson[hasRelationship];
            // eslint-disable-next-line no-param-reassign
            delete dstJson[tPersonID];
        }
    } else if (newSrcJson && newSrcJson?.classType === FoundationEvent) {
        // src
        const { [hasFoundationType]: srcRelation, [hasFoundationRelated]: srcToPerson } = srcJson;

        if (srcRelation && srcToPerson) {
            const multiSrcRelation = splitMultiValues(hasFoundationType, srcRelation);
            const multiSrcToPerson = splitMultiValues(hasFoundationRelated, srcToPerson);

            multiSrcRelation.forEach((rel) => {
                newSrcJson.value[rel] = multiSrcToPerson;
            });

            // eslint-disable-next-line no-param-reassign
            delete srcJson[hasFoundationType];
            // eslint-disable-next-line no-param-reassign
            delete srcJson[hasFoundationRelated];
        }

        // dst
        const { [hasFoundationType]: dstRelation, [hasFoundationRelated]: dstToPerson } = dstJson;

        if (dstRelation && dstToPerson) {
            const multiDstRelation = splitMultiValues(hasFoundationType, dstRelation);
            const multiDstToPerson = splitMultiValues(hasFoundationRelated, dstToPerson);

            multiDstRelation.forEach((rel) => {
                newDstJson.value[rel] = multiDstToPerson;
            });

            // eslint-disable-next-line no-param-reassign
            delete dstJson[hasFoundationType];
            // eslint-disable-next-line no-param-reassign
            delete dstJson[hasFoundationRelated];
        }
    }

    // SubValues
    // hasSource: [{label: "label1"}, {label: "label2}]
    Object.keys(SubValues).forEach((sv) => {
        // 符合 sub value properties
        if (
            Object.keys(dstJson).indexOf(sv) > -1 ||
            SubValues[sv].property.find((svp) => Object.keys(dstJson).indexOf(svp) > -1)
        ) {
            newDstJson.value[sv] = arrangeSubValue(dstJson, sv, true);

            // src
            if (
                !Object.keys(srcJson).indexOf(sv) > -1 &&
                SubValues[sv].property.find((svp) => Object.keys(dstJson).indexOf(svp) > -1)
            ) {
                return;
            }

            newSrcJson.value[sv] = arrangeSubValue(srcJson, sv, false);
        }

        // 更新時可能以string[] || {hasPlaceOfPublication: {...}}方式新增
        if (sv === LOCATION_KEY && isTltc) {
            if (
                Object.hasOwn(dstJson, LOCATION_KEY)
                // Object.hasOwn(srcJson, LOCATION_KEY)
            ) {
                const dstLoc = dstJson[LOCATION_KEY] || [];
                const srcLoc = srcJson[LOCATION_KEY] || [];
                const checkSame = checkPropertyData(Object.values(dstLoc), Object.values(srcLoc));

                // 若srcId都相同，表示變更LOC內容物: geoLat/geoLong...
                // 不相同時帶入String[]
                if (!checkSame) {
                    newDstJson.value[sv] = dstLoc;
                    newSrcJson.value[sv] = srcLoc;
                }
            }
        }

        // DUMMY Type：只使用 subValue 的部份
        const parentType = SubValues[sv].replaceParentType;
        if (Object.keys(dstJson).indexOf(sv) > -1 && parentType) {
            newDstJson.classType = parentType;
            newSrcJson.classType = parentType;
        }

        // Skip sub values if all sub values are the same.
        const isSame = isObjectEqual(newSrcJson.value[sv], newDstJson.value[sv]);
        if (isSame) {
            // remove the unnecessary sub values.
            delete newSrcJson.value[sv];
            delete newDstJson.value[sv];
        }
    });

    // 將所有的值代入 value，以符合 API 格式
    Object.keys(dstJson).forEach((name) => {
        if (skipProperty.indexOf(name) > -1) {
            return;
        }
        // 必須保留 RelationShip 與 tPersonID 的值
        if (srcJson[name] === dstJson[name]) {
            return;
        }
        // Sub values 的值不能覆蓋掉，上方已經設定好
        // ех: hasURL: {srcId:xxx, classType:URLEvent, value:{hasCopyRightStatus:[xxx,ooo]}}
        if (Object.keys(SubValues).indexOf(name) > -1) {
            if (name === LOCATION_KEY && !isTltc) {
                // FIXME: 因為多了外譯房的 location 編輯，所以 hasPlaceOfPublication 變得麻煩
            } else {
                return;
            }
        }

        if (Object.keys(propertyConvert).indexOf(name) > -1) {
            if (Object.keys(srcJson).indexOf(name) > -1) {
                newSrcJson.value[propertyConvert[name]] = splitMultiValues(name, srcJson[name]);
            }

            newDstJson.value[propertyConvert[name]] = splitMultiValues(name, dstJson[name]);
        } else {
            if (Object.keys(srcJson).indexOf(name) > -1) {
                newSrcJson.value[name] = splitMultiValues(name, srcJson[name]);
            }

            newDstJson.value[name] = splitMultiValues(name, dstJson[name]);
        }

        // console.log(dstJson);
        // 特殊處理: id 和 label 都帶入
        if (typeof dstJson[name] === 'string' && dstJson[name]?.startsWith(markToAdd)) {
            newDstJson.value[name] = convertStrToObject(dataset, name, dstJson[name]);
        }

        // Special property: keywords
        // to set keyword as split lines of keywords.
        if (KEYWORD === name) {
            // dst
            if (Object.keys(newDstJson.value).indexOf(KEYWORD) > -1) {
                //
                const deleteNull = newDstJson.value[KEYWORD]?.filter((el) => el);
                newDstJson.value[KEYWORDS] = deleteNull.join('\n');
            }
        }
    });

    return [newSrcJson, newDstJson];
};

export const convertToGenericSingle = (srcJson, dataset, contentType) => {
    if (!srcJson) {
        return null;
    }

    // console.log(srcJson, dataset, contentType);
    const newIdOption = srcJson;
    const newSrcJson = {
        graph: dataset,
        // 如果 option 是 PersonIDs，其值為 srcId, "" 為新建
        srcId: srcJson.srcId || '',
        classType: contentTypeToClassType[contentType],
        value: {},
    };

    // 特殊處理，必須所有的值都設定了才能處理的情況
    // RelationEvent, Foundation
    if (newSrcJson && newSrcJson.classType === RelationEvent) {
        const { [hasRelationship]: relation, [tPersonID]: toPerson } = newIdOption;

        if (!relation || !toPerson) {
            console.error('ERROR relation should relate to person.');
            return null;
        }

        const multiDstRelation = splitMultiValues(hasRelationship, relation);
        const multiDstToPerson = splitMultiValues(tPersonID, toPerson);

        multiDstRelation.forEach((rel) => {
            newSrcJson.value[rel] = multiDstToPerson;
        });

        delete newIdOption[hasRelationship];
        delete newIdOption[tPersonID];
    } else if (newSrcJson && newSrcJson.classType === FoundationEvent) {
        const { [hasFoundationType]: relation, [hasFoundationRelated]: toPerson } = newIdOption;

        if (!relation || !toPerson) {
            console.error('ERROR foundation relation should relate to person.');
            return null;
        }

        const multiDstRelation = splitMultiValues(hasRelationship, relation);
        const multiDstToPerson = splitMultiValues(tPersonID, toPerson);

        multiDstRelation.forEach((rel) => {
            newSrcJson.value[rel] = multiDstToPerson;
        });

        delete newIdOption[hasFoundationType];
        delete newIdOption[hasFoundationRelated];
    }

    // SubValues
    Object.keys(SubValues).forEach((sv) => {
        // 符合 sub value properties
        if (
            Object.keys(srcJson).indexOf(sv) > -1 ||
            SubValues[sv].property.find((svp) => Object.keys(srcJson).indexOf(svp) > -1)
        ) {
            const arrangedVal = arrangeSubValue(srcJson, sv, false);
            if (arrangedVal === '') {
                return;
            }

            newSrcJson.value[sv] = arrangedVal;

            // 新增時都以string[]方式新增
            if (sv === LOCATION_KEY) {
                if (
                    Object.hasOwn(srcJson, LOCATION_KEY)
                    // Object.hasOwn(srcJson, LOCATION_KEY)
                ) {
                    newSrcJson.value[sv] = srcJson[LOCATION_KEY] || [];
                }
            }
        }

        // DUMMY Type
        const parentType = SubValues[sv].replaceParentType;
        if (Object.keys(srcJson).includes(sv) && parentType) {
            newSrcJson.classType = parentType;
        }
    });

    // // Property Convert
    // Object.keys(REPLACE_PROPERTIES).forEach(sv => {
    //     // 符合 sub value properties
    //     if (Object.keys(srcJson).indexOf(sv) > -1) {
    //         newSrcJson.value[REPLACE_PROPERTIES[sv]] = srcJson[sv];
    //     }
    // });

    Object.keys(srcJson).forEach((name) => {
        if ([hasFoundationType].indexOf(name) > -1) {
            return;
        }
        if (skipProperty.indexOf(name) > -1) {
            return;
        }
        if (srcJson[name] === '') {
            return;
        }
        // Sub values.
        const subValueIds = Object.keys(SubValues)
            .concat(Object.keys(SubValues).map((s) => SubValues[s].property))
            .flat();
        if (subValueIds.indexOf(name) > -1) {
            return;
        }

        if (Object.keys(propertyConvert).indexOf(name) > -1) {
            newSrcJson.value[propertyConvert[name]] = splitMultiValues(name, srcJson[name]);
        } else {
            newSrcJson.value[name] = splitMultiValues(name, srcJson[name]);
        }

        // 特殊處理: id 和 label 都帶入
        if (typeof srcJson[name] === 'string' && srcJson[name]?.startsWith(markToAdd)) {
            newSrcJson.value[name] = convertStrToObject(dataset, name, srcJson[name]);
        }

        // Special property: keywords
        // to set keyword as split lines of keywords.
        if (KEYWORD === name) {
            // src
            if (Object.keys(newSrcJson.value).indexOf(KEYWORD) > -1) {
                newSrcJson.value[KEYWORDS] = newSrcJson.value[KEYWORD].join('\n');
            }
        }
    });

    // Remove un-used properties.
    REMOVE_PROPERTIES.forEach((rp) => {
        if (Object.keys(newSrcJson.value).indexOf(rp) > -1) {
            delete newSrcJson.value[rp];
        }
    });

    return newSrcJson;
};

const reduceData = (dataValue, data) =>
    dataValue.reduce((cur, next) => {
        const { dbPropName: dbPropName2, prop, lang } = next;

        const tmpCur = Object.assign({}, cur);
        if (!Object.hasOwn(cur, dbPropName2)) {
            tmpCur[dbPropName2] = [];
        }
        if (data[prop]) {
            const tmpVal = lang ? `${data[prop]}@${lang}` : data[prop];
            if (Array.isArray(tmpVal)) {
                tmpCur[dbPropName2] = [...tmpCur[dbPropName2], ...tmpVal];
            } else {
                // string
                tmpCur[dbPropName2] = [...tmpCur[dbPropName2], tmpVal];
            }
        }
        return tmpCur;
    }, {});

export const convertSimpleEntrySD = (srcData, dstData, entryCol) => {
    if (isEmpty(dstData) || isEmpty(entryCol)) {
        return {};
    }

    const { keyIdName, dataValue, graph, classType } = entryCol;
    // dataValue如果是陣列但是子選項沒有帶prop、dbPropName資料，直接return
    if (dataValue.some((el) => !Object.hasOwn(el, 'prop') || !Object.hasOwn(el, 'dbPropName'))) {
        return {};
    }

    /**
     * {
     *   [dbPropName] : [val1, val2, ....]
     * }
     */
    let entrySrc = {};
    if (!isEmpty(srcData)) {
        const entrySrcVal = reduceData(dataValue, srcData);

        // 儲存資料不處理lastModified
        delete entrySrcVal.lastModified;
        entrySrc = {
            graph,
            srcId: srcData[keyIdName] || '',
            classType,
            value: entrySrcVal,
        };
    }

    const entryDstVal = reduceData(dataValue, dstData);

    // 儲存資料不處理lastModified
    delete entryDstVal.lastModified;
    const entryDst = {
        graph,
        srcId: dstData[keyIdName] || '',
        classType,
        value: entryDstVal,
    };

    return { entrySrc, entryDst };
};

export const checkCellValue = (head, data, bookName, sheetName) => {
    // 特殊情況：原文書的新增
    if (bookName === GLOBAL_DEFAULT && sheetName === PublicationInfo) {
        return {
            incorrectData: null,
        };
    }

    // 挑出必填的欄位
    const markHeader = head.filter((el) => el?.mark);

    let errorMsg = '';
    //
    const emptyHeader = markHeader.filter((h) => {
        // FIXME::
        if (h.id === 'label_Publication') {
            return false;
        }

        // 如果header不存在 createState 直接回傳
        if (!Object.hasOwn(data, h.id)) {
            errorMsg += `${h.label} 不可為空。\n`;
            return true;
        }

        // 如果陣列為空直接回傳
        if (isEmpty(data[h.id])) {
            errorMsg += `${h.label} 不可為空。\n`;
            return true;
        }

        // 必須包含一個中文，一個外文
        if (CHECK_CELL_VALUE_LANG.includes(h.id)) {
            // 有中文
            const hasZhLang = data[h.id].filter((el) => el?.endsWith('@zh'));

            if (isEmpty(hasZhLang)) {
                errorMsg += `${h.label} 請填入中文版與外文版，並填入正確後綴，如：@zh, @en 或 @ja。\n`;
                return true;
            }

            // 有外文
            const hasOtherLang = data[h.id]
                .filter((el) => !hasZhLang.includes(el))
                .filter((el) => checkLangTag(el));

            if (isEmpty(hasOtherLang)) {
                errorMsg += `${h.label} 請填入中文版與外文版，並填入正確後綴，如：@zh, @en 或 @ja。\n`;
                return true;
            }
            return false;
        }

        return false;
    });

    // emptyHeader存在，表示有必填欄位為空，回傳該空欄位的label
    // if (emptyHeader) {
    return {
        incorrectData: emptyHeader?.filter((h) => h?.label) || null,
        errorMsg,
    };
    // }
};

export const mappingLabel = (dstData, headField) => {
    const dt = JSON.parse(JSON.stringify(dstData));

    Object.keys(dt).forEach((key) => {
        if (
            ApiField.getPersonlist.indexOf(key) > -1 ||
            ApiField.getOrganizationList.indexOf(key) > -1
        ) {
            if (!dt[key]) return;

            const option = specialConvertToOption(key, headField);

            const strValue = dt[key].reduce((acc, curId) => {
                const foundArr = option.find((el) => el.id === curId);

                if (foundArr) {
                    // 三個底線，因為有些id是帶有兩個底線的亂碼
                    // eslint-disable-next-line no-param-reassign
                    acc += `\n${curId}___${foundArr.label}`;
                }

                return acc;
            }, markToAdd);

            if (strValue && strValue !== markToAdd) {
                dt[key] = strValue;
            }
        }
    });

    return dt;
};

/**
 * 檢查是否僅複製 instanceId 而不copy instance其他property
 * @param classType {string}
 * @param graph {string}
 * @returns {boolean}
 */
export const shouldCopyOnlyInstanceId = (classType, graph) => {
    if (classType && classType in dstGraph4CopyOnlyInstanceId) {
        return dstGraph4CopyOnlyInstanceId[classType].includes(graph);
    }
    return false;
};

// 針對特定欄位不顯示ExistedModal
export const notShowingExistedModalHeader = [
    'hasEducatedAt',
    'hasExamination',
    'hasAcademicDiscipline',
    'hasAcademicDegree',
    'hasSocialProcedure',
    'hasPosition',
    'hasOccupation',
    'relationType',
    'hasSetInPeriod',
    'hasFoundationType',
    'hasLanguageOfWorkOrName',
    'CollectibleType',
    'hasCharacters',
];

import React, { useState } from "react";
//
import FltAnaAct from "../../../../../reduxStore/fltAnalysis/FltAnaAct";
import Api from "../../../../../api/nmtl/Api";
import { readNmtlData } from "../../../../../api/nmtl";
import { columnDefault, dataFormatter } from "../config";
import {
    getSingleLayerCollectionWhere,
    getSingleLayerCollectionDocs
} from "../../../../../api/firebase/cloudFirestore";
import firestoreConfig from "../../../../../config/config-firestore";
import { ALERT_MSG_TYPE } from "../../../../common/AlertMsg";
import { getErrorString } from "../../../../../commons";
// eslint-disable-next-line import/no-named-as-default-member
import arrayMerge from "../../../../../commons/arrayMerge";
import { getPropDomainRange } from "../crudHelper";
//
const { timeline } = arrayMerge;
//
const { FIRESTORE_FULLTEXT_COL } = firestoreConfig;

const CurAct = { ...FltAnaAct };

/**
 * 取得 某類別的 的 所有資料
 * @param graph {string}
 * @param onStart {function}
 * @param onEnd {function}
 * @returns {Promise<unknown>}
 */
const getData = async ({ url, onStart, onEnd }) => {
    if (typeof onStart === "function") {
        onStart({ url });
    }

    return new Promise(resolve => {
        readNmtlData(url)
            .then(res => {
                resolve({
                    data: res.data,
                    error: null
                });
                // resolve(res.data);
            })
            .catch(err => {
                resolve({
                    data: [],
                    error: err
                });
                // reject(err);
            })
            .finally(() => {
                if (typeof onEnd === "function") {
                    onEnd({ url });
                }
            });
    });
};

const safeQueryStrVal = str => (!str && "none") || str;

const DOC_FLT_KEY = "fltId";
const DOC_SERVERFROM_KEY = "serverFrom";

const useFetchData = props => {
    const {
        curClassType,
        dataset,
        keyword,
        curDispatch
        // notHasFullText, // 僅撈出沒有連結到 fltId 的資料
        // notFileAvailableAt // 僅撈出沒有連結到全文檔案連結的資料
        // mainDispatch
    } = props;
    //
    const [getDetailData, setGetDetailData] = useState(null);
    const [fltCollection, setFltCollection] = useState([]); // firestore 的 fullText-events collection

    // 整理 api 傳回來的資料
    const formatApiData = React.useCallback(
        data => {
            if (!Array.isArray(data)) return [];
            return dataFormatter(data, curClassType);
        },
        [curClassType]
    );

    // handle error
    const handleError = err => {
        curDispatch({
            type: CurAct.setMessage,
            payload: {
                type: ALERT_MSG_TYPE.error,
                title: getErrorString(err)
                // text: ""
            }
        });
    };

    // 限制搜尋比數
    const QRY_LIMIT = 5000;

    // 整理成 MultiLangCell 需要的格式
    const multiLangMod = React.useCallback(
        (thisData, labelKey, valueKey, mergedName, getLabel) =>
            timeline(
                thisData,
                ["id", "graph"],
                [labelKey, valueKey],
                mergedName
            ).map(item => ({
                ...item,
                [mergedName]: Array.from(
                    new Set(item?.[mergedName].map(o => JSON.stringify(o)))
                )
                    .map(o => JSON.parse(o))
                    .map(o => {
                        const lblKeys = Array.isArray(labelKey)
                            ? labelKey
                            : [labelKey];
                        // 串接 key value :e.g. FLT105_en
                        let thisLbl = lblKeys
                            .reduce((acc, cur) => {
                                if (cur in o) {
                                    return [...acc].concat([o[cur]]);
                                }
                                return acc;
                            }, [])
                            .join("_");
                        if (typeof getLabel === "function") {
                            thisLbl = getLabel(o);
                        }
                        return {
                            ...o,
                            label: thisLbl || "",
                            value: o?.[valueKey] || ""
                        };
                    })
                // 注意：fetch basic data 時,部分 key value 為空,所以不要過濾無 o.value
                // .filter(o => !!o.value)
            })),
        []
    );

    // 適用於 basic data 及 detail data 的 merge 資料功能
    const commonMerge = React.useCallback(
        data =>
            timeline(
                data,
                ["id", "graph"],
                [
                    "id",
                    "graph",
                    "label",
                    "_type",
                    "type",
                    "source",
                    "fileLang",
                    "author",
                    "fileAvailableAt",
                    "fullWorkLang",
                    "fullWorkAvailableAt",
                    "fltIdLang",
                    "fltId"
                ],
                "multiLang"
            ).map(o => ({
                ...o,
                // 融合資料並整理資料格式: fileAvailableAts
                fileAvailableAts: multiLangMod(
                    o.multiLang,
                    "fileLang",
                    "fileAvailableAt",
                    "fileAvailableAts"
                )?.[0]?.fileAvailableAts,
                // 融合資料並整理資料格式: fullWorkAvailableAts
                fullWorkAvailableAts: multiLangMod(
                    o.multiLang,
                    "fullWorkLang",
                    "fullWorkAvailableAt",
                    "fullWorkAvailableAts"
                )?.[0]?.fullWorkAvailableAts,
                // 融合資料並整理資料格式: fltIds
                fltIds: multiLangMod(
                    o.multiLang,
                    ["fltId", "fltIdLang"],
                    "fltId",
                    "fltIds"
                )?.[0]?.fltIds
            })),
        []
    );

    // 從 nmtl-api取得基本資料
    const getBasicData = async () => {
        const getBasicDataUrl = {
            fulltext: () =>
                Api.getFullTxtAnalysis
                    .replace("{keyword}", (keyword || "").trim())
                    .replace("{dataset}", dataset || "")
                    .replace("{limit}", QRY_LIMIT)
        };

        // 必須限制 graph,因為相同 id 可能存在多個 graph, 而不同 graph 該 id 的property 會有差異(e.g.有無作者)
        const getDetailDataUrl = {
            fulltext: ids =>
                Api.getFullTxtAnalysisDetail
                    .replace("{ids}", safeQueryStrVal(ids))
                    .replace("{dataset}", dataset || "")
        };

        /**
         * 從 nmtl-api 取得 詳細資料
         * @param ids {string[]}
         * @returns {Promise<unknown>}
         */
        // eslint-disable-next-line consistent-return
        const fetchDetail = async ids => {
            let detailUrl;
            if (!(Array.isArray(ids) && ids.length > 0)) return;
            try {
                const tmpIds = Array.from(new Set(ids));
                detailUrl = getDetailDataUrl[curClassType.toLowerCase()](
                    tmpIds.join(",")
                );

                const res = await getData({
                    url: detailUrl,
                    onStart: () => {
                        curDispatch({
                            type: CurAct.pushQuery,
                            payload: detailUrl
                        });
                    },
                    onEnd: () => {
                        curDispatch({
                            type: CurAct.popQuery,
                            payload: detailUrl
                        });
                    }
                });

                const { data, error } = res;
                // 優先捕捉錯誤
                if (error) {
                    handleError(error);
                    // eslint-disable-next-line consistent-return
                    return {
                        data: []
                    };
                }
                if (Array.isArray(data)) {
                    let mergeData = data;
                    mergeData = commonMerge(mergeData);

                    // eslint-disable-next-line consistent-return
                    return {
                        data: formatApiData([...mergeData])
                    };
                }
            } catch (err) {
                // catch error
                handleError(err);
                // eslint-disable-next-line consistent-return
                return {
                    data: []
                };
            }
        };

        // 儲存 function for fetchDetail
        setGetDetailData(() => fetchDetail);

        try {
            const basicUrl = getBasicDataUrl[curClassType.toLowerCase()]();

            // 取得基本資料 start
            const resBasic = await getData({
                url: basicUrl,
                onStart: () => {
                    curDispatch({
                        type: CurAct.pushQuery,
                        payload: basicUrl
                    });
                },
                onEnd: () => {
                    curDispatch({
                        type: CurAct.popQuery,
                        payload: basicUrl
                    });
                }
            });
            const { data, error } = resBasic;

            if (error) {
                handleError(error);
            }

            const sortData = list =>
                list
                    .map(o => ({
                        ...o,
                        idNum: o.id.replace("ART", "").replace("PUB", "")
                    }))
                    .sort(
                        (a, b) =>
                            Number.parseInt(a.idNum, 10) -
                            Number.parseInt(b.idNum, 10)
                    );

            if (data) {
                let mergeData = data;
                mergeData = commonMerge(mergeData);

                // 同時儲存至 content.created 及content.filtered
                curDispatch({
                    type: CurAct.setBasicData,
                    // payload: formatApiData(Object.assign([], sortData(data)))
                    payload: formatApiData(
                        Object.assign([], sortData(mergeData))
                    )
                });
                curDispatch({
                    type: CurAct.setFetchDetailFunc,
                    payload: fetchDetail
                });
                // 發送過濾 資料訊息
                curDispatch({
                    type: FltAnaAct.fetchSignalFilter
                });
                // 回傳 data, 方便做其他運用
                return {
                    data
                };
            }
            return {
                data: []
            };
        } catch (err) {
            handleError(err);
            return {
                data: []
            };
        } finally {
            // do nothing
        }
    };

    // 取得 firestore/fullText-events 的所有資料
    const getFrStoreFltCol = async (docIds = []) => {
        try {
            // 減少搜尋次數(因為隨著儲存量增多,每次讀取數也增多):依據使用者處理的 docIds 來取得最新資料
            const data =
                Array.isArray(docIds) && docIds.length > 0
                    ? await getSingleLayerCollectionDocs(
                        FIRESTORE_FULLTEXT_COL,
                        docIds
                    )
                    : await getSingleLayerCollectionWhere(
                        FIRESTORE_FULLTEXT_COL,
                        ["serverFrom", "==", process.env.REACT_APP_WEB_MODE]
                    );
            // console.log("docIds", docIds);

            if (data.error) {
                handleError(data.error);
                return "finish";
            }
            // console.log("data.length", data.length);
            // 過濾 prodoction or development
            const filterData = data
                .filter(
                    dt =>
                        dt[DOC_SERVERFROM_KEY] ===
                        process.env.REACT_APP_WEB_MODE
                )
                .map(dt => {
                    // doc.id 為 firestore 的 doc id
                    // 將 doc id 改存在 docId, 然後刪除原本的 id,避免與 dbData 的 id 重疊
                    // firestore doc.fileAvailableAt => 生成 pdf 前的原始檔案路徑,e.g. /xxx/filename.md
                    // 將 doc.fileAvailableAt 改存在 doc_fileAvailableAt
                    const tmpDt = {
                        ...(dt || {}),
                        docId: dt.id,
                        doc_fileAvailableAt: dt.fileAvailableAt
                    };
                    delete tmpDt.id;
                    delete tmpDt.fileAvailableAt;
                    return tmpDt;
                });
            let sliceData = JSON.parse(JSON.stringify(filterData));
            setFltCollection(prev =>
                (prev || [])
                    .map(dt => {
                        const find = sliceData.find(o => o.docId === dt.docId);
                        const findIdx = sliceData.findIndex(
                            o => o.docId === dt.docId
                        );
                        if (find) {
                            sliceData = sliceData
                                .slice(0, findIdx)
                                .concat(sliceData.slice(findIdx + 1));
                        }
                        return find || dt;
                    })
                    .concat(sliceData || [])
            );
            return "finish";
        } catch (e) {
            handleError(e);
            return "finish";
        }
    };

    // 將 database 取得的資料與 collection data 融合
    const mergeDbAndCollData = dbData => {
        if (!Array.isArray(dbData)) return [];
        if (!Array.isArray(fltCollection)) return dbData;
        // 利用 classType 找到定義的 columnDefault(預設欄位)
        const { classColumns } =
            columnDefault.find(
                def =>
                    def.classType.toLowerCase() === curClassType.toLowerCase()
            ) || {};
        const statusKey = classColumns
            .filter(o => o.group === "status")
            .map(o => {
                const { prop } = getPropDomainRange(o.accessor) || {};
                return prop;
            });

        const mergeData = dbData.map(dt => {
            const fltIds = Array.isArray(dt.fltIds) ? dt.fltIds : [dt.fltIds];
            const status = statusKey.reduce((acc, cur) => {
                acc[cur] = fltIds
                    .map(fltIdObj => {
                        const find = fltCollection.find(
                            fl => fl?.[DOC_FLT_KEY] === fltIdObj?.[DOC_FLT_KEY]
                        );
                        return (
                            find && {
                                label: fltIdObj?.[DOC_FLT_KEY],
                                value: find?.[cur] || {}
                            }
                        );
                    })
                    .filter(o => !!o);
                return acc;
            }, {});

            return {
                ...dt,
                docIds: fltIds
                    .map(fltIdObj => {
                        const find = fltCollection.find(
                            fl => fl?.[DOC_FLT_KEY] === fltIdObj?.[DOC_FLT_KEY]
                        );
                        return {
                            fltId: fltIdObj?.[DOC_FLT_KEY],
                            docId: find?.docId,
                            label: fltIdObj?.[DOC_FLT_KEY],
                            value: find?.docId
                        };
                    })
                    .filter(o => !!o.value),
                fltIds: fltIds.map(fltIdObj => {
                    const find = fltCollection.find(
                        fl => fl?.[DOC_FLT_KEY] === fltIdObj?.[DOC_FLT_KEY]
                    );
                    return {
                        ...fltIdObj,
                        // fullWorkLang: "en,zh" (串接,允許一個docId.fullWorkLang可以紀錄多個語系)
                        // 使用 fullWorkLang 當作 fltIdLang
                        fltIdLang: find?.fullWorkLang || [],
                        ...(find || {})
                    };
                }),
                ...status
            };
        });
        return formatApiData(mergeData);
    };

    return {
        getBasicData,
        getDetailData,
        getFrStoreFltCol,
        mergeDbAndCollData
    };
};

export default useFetchData;

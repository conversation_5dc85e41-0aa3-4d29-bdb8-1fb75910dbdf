import RPAct from "./reportIssueAction";
import { statusKey } from "./common/statusConfig";

const INITIAL_STATE = {
    rpSubject: "",
    tabStatus: statusKey.all, // tab選擇要看的issue處理狀態分類
    allData: [], // 從firebase取得的所有report issue，用在跟newAllData比對差異
    newAllData: [], // 記錄另一份firebase的report issue data，主要用在更新資料會用到，可以跟原始資料比對用
    openRSModal: false, // 控制result modal顯示
    hisData: {}, // single report issue history data
    cntData: {}, // single report issue data
    cntModal: false // content modal control
};

const RPReducer = (state = INITIAL_STATE, action) => {
    switch (action.type) {
        case RPAct.SET_RPSUBJECT:
            return { ...state, rpSubject: action.payload };
        case RPAct.SET_RPTABSTATUS:
            return { ...state, tabStatus: action.payload };
        case RPAct.SET_RPAllDATA:
            return { ...state, allData: action.payload };
        case RPAct.SET_RPNEWAllDATA:
            return { ...state, newAllData: action.payload };
        case RPAct.SET_RPOPENRSMODAL:
            return { ...state, openRSModal: action.payload };
        case RPAct.SET_RPHISDATA:
            return { ...state, hisData: action.payload };
        case RPAct.SET_RPCNTDATA:
            return { ...state, cntData: action.payload };
        case RPAct.SET_RPCNTMODAL:
            return { ...state, cntModal: action.payload };
        default:
            return state;
    }
};

export default RPReducer;

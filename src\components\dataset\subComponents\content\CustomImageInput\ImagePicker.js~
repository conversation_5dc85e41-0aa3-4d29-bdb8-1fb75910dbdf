import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
    Modal,
    Button,
    Grid,
    Segment,
    Container,
    Dimmer,
    Loader
} from "semantic-ui-react";

// component
import FolderMenu from "./uploadImage/FolderMenu";
import CurrentFolder from "./uploadImage/curFolder";
import FilePicker from "./uploadImage/FilePicker";
import CurrentImage from "./CurrentImage";
import CropImage from "./CropImage";

// store
import FileAct from "../../../../../reduxStore/file/fileAction";

// helper
import uploadConfig from "../../../../toolPages/components/upload/uploadConfig";
import { uuidv4 } from "../../../../../commons/utility";

// 資料管理 從標題裡新增單張圖片
const ImagePicker = ({
    open,
    setOpen,
    defaultValue: oriValue,
    onValueChange
}) => {
    const {
        files: { loading, defaultValue, selectFile }
    } = useSelector(state => state);
    const dispatchRedux = useDispatch();

    const [pickMethod, setPickMethod] = useState("store"); // ENUM("store", "upload")

    const handleModalClose = () => {
        // 最後上傳圖片清空
        dispatchRedux({
            type: FileAct.UPLOAD_IMAGES_LATEST,
            payload: []
        });

        // 初始資料夾載入重設
        dispatchRedux({
            type: FileAct.INIT_FILE_SETTINGS,
            payload: false
        });

        dispatchRedux({ type: FileAct.INIT_FOLDERPATTERN });

        dispatchRedux({
            type: FileAct.CUR_FOLDER_FILES_STATUS,
            payload: { original: [], checked: [] }
        });

        dispatchRedux({
            type: FileAct.FOLDER_FILES_URL,
            payload: []
        });
        console.log('oriValue', oriValue, 'selectFile', selectFile);
        if (oriValue !== selectFile) {
            if (selectFile && selectFile.length > 0) {
                onValueChange(selectFile);
            } else {
                onValueChange(defaultValue);
            }
        }
        setOpen(false);
    };

    const selectMode = {
        store: {
            name: "store",
            label: "從圖片庫中挑選",
            onClick: () => setPickMethod("store"),
            leftComp: <FolderMenu />,
            rightComp: <CurrentFolder type={uploadConfig.image} />
        },
        upload: {
            name: "upload",
            label: "上傳圖片",
            onClick: () => setPickMethod("upload"),
            leftComp: <FolderMenu />,
            rightComp: <FilePicker type={uploadConfig.image} />
        }
    };

    // 刪除連結
    // const handleClearLink = () => {
    //     dispatchRedux({
    //         type: FileAct.SET_DEFAULT_VALUE,
    //         payload: ""
    //     });
    // };

    return (
        <Modal
            size="large"
            // closeOnDimmerClick={false}
            open={open}
            closeOnDocumentClick={false}
            onClose={() => handleModalClose()}
        >
            <Dimmer active={loading.state} inverted>
                <Loader size="large">{loading.message}</Loader>
            </Dimmer>
            <Modal.Header>圖片設定</Modal.Header>
            <Modal.Content image scrolling>
                <Container>
                    {/* display current image */}
                    <CurrentImage
                        defaultValue={defaultValue}
                        currentValue={selectFile}
                    />
                    {/* switcher: 使用圖片庫 or 上傳圖片 */}
                    {/* <Button.Group>
                        {Object.values(selectMode).map(btn => (
                            <Button
                                key={uuidv4()}
                                positive={pickMethod === btn.name}
                                onClick={() => btn.onClick()}
                            >
                                {btn.label}
                            </Button>
                        ))}
                    </Button.Group>
                    <Button
                        style={{ marginLeft: "20px" }}
                        onClick={handleClearLink}
                    >
                        刪除圖片連結
                    </Button> */}
                    <Grid textAlign="center" celled stackable>
                        <Grid.Row>
                            <Grid.Column width={3}>
                                <Segment basic compact>
                                    {/* left side */}
                                    {pickMethod in selectMode &&
                                        selectMode[pickMethod].leftComp}
                                </Segment>
                            </Grid.Column>
                            <Grid.Column width={13}>
                                {/* right side */}
                                {pickMethod in selectMode &&
                                    selectMode[pickMethod].rightComp}
                            </Grid.Column>
                        </Grid.Row>
                    </Grid>
                </Container>
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={() => handleModalClose()} primary>
                    關閉
                </Button>
            </Modal.Actions>
            <CropImage />
        </Modal>
    );
};

export default ImagePicker;

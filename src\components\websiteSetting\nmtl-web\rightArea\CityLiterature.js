import React, { useContext, useEffect, useRef, useState } from "react";

// components
import Selector from "../../components/Selector";
import LanguageSelect from "../../components/LanguageSelect";
import UpdateText from "../../components/UpdateText";
import SaveButton from "../../components/SaveButton";

// general
import Act from "../../../../store/actions";
import { StoreContext } from "../../../../store/StoreProvider";
import Api from "../../../../api/nmtl/Api";
import { readNmtlData } from "../../../../api/nmtl";
import { returnDefaultValue } from "../../commons";
import CustomQuill from "../components/CustomQuill";

function CityLiterature() {
    const [state, dispatch] = useContext(StoreContext);
    const editorRef = useRef(null);
    const { originData, menuActiveItem } = state.websiteSetting;
    const [language, setLanguage] = useState("zh");
    const [dropDown, setDropDown] = useState({});

    useEffect(() => {
        // 下拉選單選項清空
        dispatch({
            type: Act.SET_SELECTOPTION,
            payload: ""
        });
    }, []);

    useEffect(() => {
        if (originData.length !== 0) {
            const apiStr = Api.getCityLiteratureList;
            const tmpOriginData = JSON.parse(JSON.stringify(originData));
            const tmpData = tmpOriginData.find(
                element => element.id === menuActiveItem.key
            );
            readNmtlData(apiStr)
                .then(result => {
                    let tmpObj = {};
                    const keys = Object.keys(tmpData).filter(
                        key => key !== "id"
                    );
                    result.data.forEach(dataObj => {
                        const findKey = keys.find(key => key === dataObj.id);
                        // 找出firestore還沒存的縣市
                        if (!findKey) {
                            tmpObj[dataObj.id] = returnDefaultValue(
                                "CityLiterature"
                            );
                            tmpObj[dataObj.id].name = dataObj.name;
                        }
                    });
                    tmpObj = Object.assign(tmpData, tmpObj);
                    setDropDown(tmpObj);
                    dispatch({
                        type: Act.SET_UPDATEDDATA,
                        payload: tmpOriginData
                    });
                })
                .catch(error => {
                    console.log(error);
                });
        }
    }, [originData]);

    return (
        <div className="CityLiterature">
            <div className="Selector">
                <Selector dropDown={dropDown} />
            </div>
            <div className="updateArea">
                <div className="updateAreaTop">
                    <h1>概述</h1>
                    <LanguageSelect
                        language={language}
                        setLanguage={setLanguage}
                    />
                </div>
                <div className="updateAreaButtom" style={{ height: "80%" }}>
                    <UpdateText
                        language={language}
                        dropDown={dropDown}
                        option={{
                            column: "overview"
                        }}
                    />
                </div>
                <div className="updateAreaTop">
                    <h1>總述</h1>
                </div>
                <div className="updateAreaButtom">
                    {/* <UpdateText */}
                    {/*    language={language} */}
                    {/*    dropDown={dropDown} */}
                    {/*    option={{ */}
                    {/*        column: "introduction" */}
                    {/*    }} */}
                    {/* /> */}
                    <CustomQuill
                        quillId="CustomEditorCtyLt"
                        tmpRef={editorRef}
                        language={language}
                        dropDown={dropDown}
                        option={{
                            column: "introduction"
                        }}
                    />
                </div>
                <div className="btnArea">
                    <SaveButton language={language} />
                </div>
            </div>
        </div>
    );
}

export default CityLiterature;

import React, { useState } from "react";

// ui
import { Checkbox } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import VillagesAct from "../../../VillagesAction";

// store

const SpecialAllCheckBox = ({
    rowId,
    isChecked,
    onClick,
    // setActiveCheckedIds,
    // setHeaderChecked,
    // headerChecked,
    ...rest
}) => {
    const dispatch = useDispatch();
    const { rows } = useSelector(state => state);
    // handle checkbox
    const [checked, setChecked] = useState(isChecked);

    // handle checkbox for delete
    const handleCheckbox = () => {
        // checkbox state
        setChecked(!checked);

        // record rowId if checked === true
        if (!checked) {
            // customCheckBox
            const data = Object.keys(rows).map(key => ({ rowId: key })) || [];

            // record checkbox for delete
            dispatch({
                type: VillagesAct.DATA_CONTENT_ROW_CHECKED_ALL,
                payload: data
            });

            // setActiveCheckedIds(data.map(item => item.rowId));
        } else {
            // cancel record checkbox for delete
            dispatch({ type: VillagesAct.DATA_CONTENT_ROW_CHECKED_CLEAN });
            // setActiveCheckedIds([]);
        }
    };

    return <Checkbox {...rest} checked={checked} onClick={handleCheckbox} />;
};

export default SpecialAllCheckBox;

import React, { useCallback, useContext, useEffect, useState } from 'react';

import { Container, Divider, Grid, Segment, Tab, Menu, Loader } from 'semantic-ui-react';

import SearchBar from './subComponents/searchBar';
import SideBar from './subComponents/sideBar';
import ContentView from './subComponents/content/ContentView';
import EventButton from './subComponents/eventButton';
import AlertMessage from './subComponents/alertMessage';
import { StoreContext } from '../../store/StoreProvider';
import SpecialContentView from './subComponents/content/SpecialContentView';
import SpecialSearchBar from './subComponents/searchBar/specialSearchBar';
import SpecialEventButton from './subComponents/specialEventButton';
import CustomPagination from './subComponents/pagination/CustomPagination/CustomPagination';
import { isEmpty } from '../../commons';
import { uuidv4 } from '../../commons/utility';
import Api from '../../api/nmtl/Api';
import { readNmtlData } from '../../api/nmtl';
import Act from '../../store/actions';
import ToastMessage from '../ToastMessage';

const SPECIAL_MAINSUBJECT = ['tltc'];
const SPECIAL_SHEET = ['PublicationInfo', 'Person'];
const DatasetManagement = () => {
  const [state, dispatch] = useContext(StoreContext);
  const { mainSubject, sheet } = state.data;
  const { dataset } = mainSubject.selected;
  const { hasTab, contentTabPath } = sheet.selected;
  const { tabCount } = sheet;
  const { tabIndex } = sheet.tabKey;

  const [isLoading, setIsLoading] = useState(false);

  const getTabCount = useCallback(
    async (key) => {
      if (dataset && contentTabPath[key]) {
        const urlPath = `${Api.getBaseUrl}/${contentTabPath[key]}`;
        // activePage 起始值為 1，但方法必須從 0 開始，因此減 1
        const parameter = `limit=-1&offset=0&ds=${dataset}`;

        const tabApiUrl = `${urlPath}?${parameter}`;
        if (!tabApiUrl) {
          // setIsLoading(false);
          return { data: [], total: null, error: 'Not ready' };
        }

        return readNmtlData(tabApiUrl);
      }
      return undefined;
    },
    [dataset, contentTabPath],
  );

  const handleGetTabContent = async () => {
    if (!contentTabPath) {
      return;
    }

    const tabKeys = Object.keys(contentTabPath);
    const promises = tabKeys.map((key) => getTabCount(key));

    const results = await Promise.allSettled(promises).then((res) => res);

    const apiResults = results.reduce((acc, cur, curIndex) => {
      // console.log("cur", cur.value.data);
      acc[tabKeys[curIndex]] = cur.value.data.map((item) => item.count).join('');

      return acc;
    }, {});

    if (apiResults) {
      dispatch({
        type: Act.DATA_TAB_COUNT_SHEET,
        payload: apiResults,
      });

      dispatch({
        type: Act.DATA_TAB_KEY,
        payload: {
          tabClass: hasTab[tabIndex || 0].tabClass,
          tabIndex: tabIndex || 0,
        },
      });
    }
  };

  useEffect(() => {
    dispatch({ type: Act.DATA_MAINSUBJECT_CLEAN });
    dispatch({ type: Act.DATA_SHEET_CLEAN });
    dispatch({ type: Act.DATA_SHEET_ACTIVATE_HEADER_CLEAN, payload: {} });

    // 離開DataSet也要把Sheet資料清乾淨，避免影響Authority頁面使用
    return () => {
      dispatch({ type: Act.DATA_MAINSUBJECT_CLEAN });
      dispatch({ type: Act.DATA_SHEET_CLEAN });
      dispatch({ type: Act.DATA_SHEET_ACTIVATE_HEADER_CLEAN, payload: {} });
    };
  }, []);

  useEffect(() => {
    setIsLoading(true);
    handleGetTabContent();
    setIsLoading(false);
  }, [contentTabPath]);

  const basicContent = () => (
    <React.Fragment>
      <Grid>
        <Grid.Column floated="left" width={6}>
          {/* search content from topic and sheet  */}
          <SearchBar />
        </Grid.Column>
        <Grid.Column floated="right" width={10}>
          {/* handle update and delete event  */}
          <EventButton />
        </Grid.Column>
      </Grid>

      <Divider hidden style={{ margin: '.4rem 0' }} />
      {/* show topic and sheet content */}
      <ContentView />

      {/* show page */}
      {/* <Pagination /> */}
      <CustomPagination />
    </React.Fragment>
  );

  const specialContent = (curState) => (
    <React.Fragment>
      <Grid width={14}>
        <Grid.Row
          style={{
            padding: '1.2rem',
          }}
        >
          {/* search content from topic and sheet  */}
          <SpecialSearchBar curState={curState} />
        </Grid.Row>
        <Grid.Row
          style={{
            justifyContent: 'end',
            padding: '0 1.2rem',
            marginBottom: '1.2em',
          }}
        >
          {/* handle update and delete event  */}
          <SpecialEventButton />
        </Grid.Row>
      </Grid>

      <Divider hidden style={{ margin: '.4rem 0' }} />
      {/* show topic and sheet content */}

      <SpecialContentView curState={curState} />

      {/* show page */}
      <CustomPagination />
    </React.Fragment>
  );

  const specialView = () => {
    if (isEmpty(hasTab)) return specialContent(sheet?.selected);

    if (isLoading)
      return (
        <Loader active inline="centered" size="large">
          Loading...
        </Loader>
      );

    return (
      <Tab
        menu={{ secondary: true, pointing: true }}
        panes={hasTab.map((ct) => ({
          menuItem: (
            <Menu.Item key={uuidv4()} style={{ height: '4rem' }}>
              {`${ct.label}（${tabCount[ct.tabClass] || ''}）`}
            </Menu.Item>
          ),
          render: () => (
            <Tab.Pane attached={false} loading={isLoading}>
              {specialContent(ct)}
            </Tab.Pane>
          ),
        }))}
        onTabChange={(event, data) => {
          dispatch({
            type: Act.DATA_TAB_KEY,
            payload: {
              tabClass: hasTab[data.activeIndex].tabClass,
              tabIndex: data.activeIndex,
            },
          });

          dispatch({ type: Act.DATA_PAGINATION_ACTIVE_PAGE_INIT });
          dispatch({ type: Act.DATA_SORTED_CLEAN });
          dispatch({ type: Act.DATA_SEARCH_KEYWORD_CLEAN });
          dispatch({ type: Act.DATA_CONTENT_ROWS_CLEAN });
        }}
        activeIndex={tabIndex}
      />
    );
  };

  return (
    <Container style={{ width: '95%' }} className="Dataset">
      <Segment basic compact>
        <h2>資料管理</h2>
      </Segment>
      <Divider />
      <ToastMessage />
      <Grid celled>
        <Grid.Row>
          <Grid.Column width={2}>
            {/* SideBar for MainSubject and Sheet */}
            <SideBar />
          </Grid.Column>
          <Grid.Column width={14}>
            {SPECIAL_MAINSUBJECT.includes(mainSubject?.selected?.key) &&
            SPECIAL_SHEET.includes(sheet?.selected?.key)
              ? specialView()
              : basicContent()}
          </Grid.Column>
        </Grid.Row>
      </Grid>
      <br />
    </Container>
  );
};

export default DatasetManagement;

import React, { useEffect, useState } from "react";
import {
    <PERSON>ton,
    <PERSON>dal,
    <PERSON>dal<PERSON><PERSON>,
    ModalContent,
    ModalHeader
} from "semantic-ui-react";
import DatePicker from "react-datepicker";
import axios from "axios";
import { fileServerAPI } from "../../../../../api/fileServer";

const DownloadModal = ({ avaliableDownloadLogTime }) => {
    const [startDate, setStartDate] = useState(new Date());
    const [open, setOpen] = useState(false);
    const [includedDates, setIncludedDates] = useState([]);

    function formatToYearMonth(dateString) {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        return `${year}-${month}`;
    }

    useEffect(() => {
        // 為符合格式，須加日期01
        const allowedDates = avaliableDownloadLogTime.map(
            month => new Date(`${month}-01`)
        );
        setIncludedDates(allowedDates);
    }, [avaliableDownloadLogTime]);

    const MonthPickerForDownload = () => (
        <DatePicker
            selected={startDate}
            onChange={date => setStartDate(date)}
            dateFormat="yyyy/MM"
            showMonthYearPicker
            includeDates={includedDates}
            // locale={zhTW}
        />
    );

    const handleDownload = async () => {
        const yearMonth = formatToYearMonth(startDate);

        try {
            const response = await axios.post(
                fileServerAPI.historyLog,
                {
                    yearMonth,
                    filePath: process.env.REACT_APP_HISTORY_LOG_FILESERVER_PATH
                },
                { responseType: "blob" } // 需指定 responseType 為 'blob'
            );

            const url = window.URL.createObjectURL(response.data);
            const a = document.createElement("a");
            a.href = url;
            a.download = `歷史紀錄_${yearMonth}.xlsx`;
            document.body.appendChild(a);
            a.click();
            a.remove();
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error("Download error:", error);
        }

        setOpen(false);
    };

    return (
        <Modal
            onClose={() => setOpen(false)}
            onOpen={() => setOpen(true)}
            open={open}
            trigger={<Button>下載歷史訊息</Button>}
        >
            <ModalHeader>下載歷史訊息</ModalHeader>
            <ModalContent image>
                <p>下載月份：</p>
                <MonthPickerForDownload />
            </ModalContent>
            <ModalActions>
                <Button color="black" onClick={() => setOpen(false)}>
                    取消
                </Button>
                <Button
                    content="下載"
                    labelPosition="right"
                    icon="checkmark"
                    onClick={() => handleDownload()}
                    positive
                />
            </ModalActions>
        </Modal>
    );
};

export default DownloadModal;

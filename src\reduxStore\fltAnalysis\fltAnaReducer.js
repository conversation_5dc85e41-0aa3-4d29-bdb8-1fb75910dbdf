/* eslint-disable no-case-declarations */

import FltAnaAct from "./FltAnaAct";

const INITIAL_STATE = {
    classType: "FullText",
    // dataset
    mainSubject: { selected: {} },
    table: {
        read: null, // react-table instance
        edit: null // react-table instance
    },
    refreshTable: { signal: Date.now() },
    content: {
        created: [], // Object[] fetch API取得的所有基本資料
        filtered: [], // 從 created 過濾後(後分類)的資料
        updated: [], // Object[] [{id:"",label:"",_original:{...原本的資料}}]
        checked: [], // Object[]
        fetchDetail: () => null
    },
    fetchSignal: {
        basic: null,
        filter: null,
        detail: null,
        firestore: null,
        dbAndFirestore: Date.now() // 先取 firestore 資料 再取 db 資料
    },
    memoFirestoreColDocIds: [], // 用來記憶該使用者處理的 firestore docId
    columns: [], // 欄位設定
    search: { keyword: "" },
    query: [], // 用來記憶 fetch url,開始 fetch 時會push,fetch完後會pop
    // fixme: 若有多種不同功能的 modal時,要有其他別名,e.g. editModal
    editModal: {
        open: false,
        context: {
            columns: [], // 欄位設定
            data: [] // 要編輯的資料
        }
    },
    byPassModal: {
        open: false,
        context: {
            rowData: null,
            httpMethod: null // string["POST","PUT"]
        }
    },
    message: null // type: info, success, error
};

const fltAnaReducer = (state = INITIAL_STATE, action) => {
    switch (action.type) {
        case FltAnaAct.clearCache:
            return {
                ...INITIAL_STATE
            };
        // dataset
        case FltAnaAct.setMainSubject:
            return {
                ...state,
                mainSubject: { selected: action.payload }
            };
        case FltAnaAct.setReadTable:
            return {
                ...state,
                table: {
                    ...state.table,
                    read: action.payload
                }
            };
        case FltAnaAct.setEditTable:
            return {
                ...state,
                table: {
                    ...state.table,
                    edit: action.payload
                }
            };
        case FltAnaAct.setRefreshTableSignal:
            return {
                ...state,
                refreshTable: {
                    signal: Date.now()
                }
            };
        case FltAnaAct.setBasicData:
            return {
                ...state,
                content: {
                    ...INITIAL_STATE.content,
                    created: action.payload,
                    filtered: action.payload
                }
            };
        case FltAnaAct.setBasicDataFilt:
            return {
                ...state,
                content: {
                    ...state.content,
                    filtered: action.payload
                }
            };
        case FltAnaAct.setDetailData:
            if (!Array.isArray(action.payload))
                return {
                    ...state
                };
            const mapFunc = src => {
                const find = action.payload.find(dst => dst.id === src.id);
                if (find) {
                    const { author, source, type, fullWorkAvailableAts } = find;
                    return {
                        ...src,
                        author,
                        source,
                        type,
                        // 僅需要置換掉部分資訊
                        fullWorkAvailableAts__MultiLang: fullWorkAvailableAts
                    };
                }
                return {
                    ...src
                };
            };
            return {
                ...state,
                content: {
                    ...state.content,
                    created: state.content.created.map(mapFunc),
                    filtered: state.content.filtered.map(mapFunc)
                }
            };
        case FltAnaAct.setInitUpdatedData:
            return {
                ...state,
                content: {
                    ...state.content,
                    updated: action.payload
                }
            };
        case FltAnaAct.setUpdatedData:
            // action.payload 接受的格式: {rowIdx,colId?,data}|{rowIdx,colId?,data}[]
            // eslint-disable-next-line no-case-declarations
            const { updated } = state.content;
            if (!action.payload)
                return {
                    ...state
                };
            // eslint-disable-next-line no-case-declarations
            let tmpUpdate = [...(updated || [])];
            // eslint-disable-next-line no-case-declarations
            const payloadList = Array.isArray(action.payload)
                ? action.payload.filter(o => !!o)
                : [action.payload];
            //
            tmpUpdate = updated.map(o => {
                let newData = o;
                payloadList.forEach(payload => {
                    // fixme: 確認 uniq key 為 id
                    const { rowIdx, colId, data: pData } = payload || {};
                    // 有 rowIdx, colId => 為 cell
                    if (rowIdx != null && colId != null) {
                        if (o.rowIdx === rowIdx) {
                            newData = { ...(o || {}), [colId]: pData };
                        }
                        // 有 rowIdx 沒有 colId => 為 row
                    } else if (rowIdx != null && pData && o.rowIdx === rowIdx) {
                        newData = { ...o, ...pData };
                    }
                });
                return newData;
            });
            return {
                ...state,
                content: {
                    ...state.content,
                    updated: tmpUpdate
                }
            };
        case FltAnaAct.setFetchDetailFunc:
            return {
                ...state,
                content: {
                    ...state.content,
                    fetchDetail: action.payload
                }
            };
        case FltAnaAct.fetchSignalBasic:
            return {
                ...state,
                fetchSignal: {
                    ...state.fetchSignal,
                    basic: Date.now()
                }
            };
        case FltAnaAct.fetchSignalFilter:
            return {
                ...state,
                fetchSignal: {
                    ...state.fetchSignal,
                    filter: Date.now()
                }
            };
        case FltAnaAct.fetchSignalDetail:
            return {
                ...state,
                fetchSignal: {
                    ...state.fetchSignal,
                    detail: Date.now()
                }
            };
        case FltAnaAct.fetchSignalFrstore:
            return {
                ...state,
                fetchSignal: {
                    ...state.fetchSignal,
                    firestore: Date.now()
                }
            };
        case FltAnaAct.fetchSignalDbAndFr:
            return {
                ...state,
                fetchSignal: {
                    ...state.fetchSignal,
                    dbAndFirestore: Date.now()
                }
            };
        //
        case FltAnaAct.putMemoFirestoreColDocIds:
            return {
                ...state,
                memoFirestoreColDocIds: Array.from(
                    new Set(
                        state.memoFirestoreColDocIds.concat(
                            Array.isArray(action.payload)
                                ? action.payload
                                : [action.payload]
                        )
                    )
                )
            };
        case FltAnaAct.clearMemoFirestoreColDocIds:
            return {
                ...state,
                memoFirestoreColDocIds: INITIAL_STATE.memoFirestoreColDocIds
            };
        case FltAnaAct.pushQuery:
            return {
                ...state,
                query: Array.isArray(action.payload)
                    ? [...state.query].concat(action.payload)
                    : [...state.query, action.payload]
            };
        case FltAnaAct.popQuery:
            return {
                ...state,
                query: state.query.filter(q => q !== action.payload)
            };
        // set editModal open or close
        case FltAnaAct.setEditModalOpen:
            return {
                ...state,
                editModal: { ...state.editModal, open: action.payload }
            };
        // set editModal data
        case FltAnaAct.setEditModalContext:
            const { columns: editColumns, data: editData } =
                action.payload || {};
            return {
                ...state,
                editModal: {
                    ...state.editModal,
                    context: {
                        ...state.editModal.context,
                        columns: editColumns || state.editModal.context.columns,
                        data: editData || state.editModal.context.data
                    }
                }
            };
        // set byPassModal open or close
        case FltAnaAct.setBypassModalOpen:
            return {
                ...state,
                byPassModal: { ...state.byPassModal, open: action.payload }
            };
        // set byPassModal data
        case FltAnaAct.setBypassModalContext:
            return {
                ...state,
                byPassModal: {
                    ...state.byPassModal,
                    context: action.payload
                }
            };
        case FltAnaAct.setBypassModalContextInit:
            return {
                ...state,
                byPassModal: {
                    ...INITIAL_STATE.byPassModal
                }
            };
        case FltAnaAct.clearEditModalContext:
            return {
                ...state,
                editModal: {
                    ...state.editModal,
                    context: { ...INITIAL_STATE.editModal.context }
                }
            };
        case FltAnaAct.setMessage:
            return { ...state, message: action.payload };
        // 把所有的 state 合併在一起儲存
        case FltAnaAct.setData:
            return { ...state, ...action.payload };
        default:
            return state;
    }
};

export default fltAnaReducer;

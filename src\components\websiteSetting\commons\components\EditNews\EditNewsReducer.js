import { createStore } from "redux";
import NewsAct from "./EditNewsAction";

const initialState = {
    newsBriefInfo: {}, // 紀錄點選news的資訊，透過裡面的"newsIdStr"找詳細資訊
    newsFullInfo: {}, // 存放編輯前的最新消息資料
    updateNewsInfo: {}, // 存放編輯時的最新消息資料
    isEdited: false,
    modalCaller: "",
    modalSelect: null,
    modalMsg: "",
    openModal: false,
    isExternal: false,
    tbBdData: [] // 最新消息列表，table body資料
};

// Create a "reducer" function that determines what the new state
// should be when something happens in the app
function EditNewsReducer(state = initialState, action) {
    // Reducers usually look at the type of action that happened
    // to decide how to update the state
    switch (action.type) {
        case NewsAct.SET_NEWSBRIEFINFO:
            return { ...state, newsBriefInfo: action.payload };
        case NewsAct.SET_NEWSFULLINFO:
            return { ...state, newsFullInfo: action.payload };
        case NewsAct.SET_UPDATENEWSINFO:
            return { ...state, updateNewsInfo: action.payload };
        case NewsAct.SET_ISEDITED:
            return { ...state, isEdited: true };
        case NewsAct.SET_NOTEDITED:
            return { ...state, isEdited: false };
        case NewsAct.SET_MODALCALLER:
            return { ...state, modalCaller: action.payload };
        case NewsAct.SET_MODALSELECT:
            return { ...state, modalSelect: action.payload };
        case NewsAct.SET_MODALMSG:
            return { ...state, modalMsg: action.payload };
        case NewsAct.SET_OPENMODAL:
            return { ...state, openModal: action.payload };
        case NewsAct.SET_ISEXTERNAL:
            return { ...state, isExternal: action.payload };
        case NewsAct.SET_TBBDDATA:
            return { ...state, tbBdData: action.payload };
        default:
            // If the reducer doesn't care about this action type,
            // return the existing state unchanged
            return state;
    }
}

const editNewsStore = createStore(EditNewsReducer);

export default editNewsStore;

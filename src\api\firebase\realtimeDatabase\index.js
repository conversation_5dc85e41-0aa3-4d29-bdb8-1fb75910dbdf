// firebase
import firebase from "firebase/app";
import "firebase/database";
import { isEmpty } from "../../../commons";

/*
    component: accountManagement
    desc: get all user
    path: nmtl-web/users
 */
const getUsers = () =>
    firebase
        .database()
        .ref("/users")
        .once("value")
        .then(snapshot => Object.values(snapshot.val()).map(item => item));

/*
    component: accountManagement
    desc: findUser 
    path: nmtl-web/users
 */
const getUser = uid => {
    // make sure uid is correct
    const safeUid = isEmpty(uid) ? "unknownUid" : uid;
    return firebase
        .database()
        .ref(`/users/${safeUid}`)
        .once("value")
        .then(snapshot => {
            const data = {};
            // return result list if it is exist
            // eslint-disable-next-line no-restricted-syntax
            if (snapshot.val()) {
                for (const [key, value] of Object.entries(snapshot.val())) {
                    data[key] = value;
                }
            }
            return data;
        });
};

const updateUser = (uid, data) => {
    // make sure uid is correct
    const safeUid = isEmpty(uid) ? "unknownUid" : uid;
    return firebase
        .database()
        .ref(`users/${safeUid}`)
        .update(data);
};

const deleteUser = uid => {
    // make sure uid is correct
    const safeUid = isEmpty(uid) ? "unknownUid" : uid;
    return firebase
        .database()
        .ref(`users/${safeUid}`)
        .remove();
};

/*
    component: SignIn
    desc: save user detail
    path: nmtl-web/user/{uid}
 */
const saveUser = (uid, data) => {
    // make sure uid is correct
    const safeUid = isEmpty(uid) ? "unknownUid" : uid;
    firebase
        .database()
        .ref(`users/${safeUid}`)
        .update(data);
};

const getEarliestDate = async () => {
    try {
        const snapshot = await firebase
            .firestore()
            .collection("daily-count")
            .orderBy("date", "asc")
            .limit(1)
            .get();

        if (!snapshot.empty) {
            const doc = snapshot.docs[0];
            const data = doc.data();
            return data.date;
        } else {
            return "";
        }
    } catch (e) {
        console.log("Error fetching earliest date:", e);
        return "";
    }
};

const getPageView = async (pageViewArr, startDate, endDate) => {
    if (!pageViewArr || pageViewArr.length === 0) return;

    try {
        const previousDay = new Date(startDate);
        previousDay.setDate(previousDay.getDate() - 1);
        const formattedStartDate = previousDay.toISOString().split("T")[0];

        const startSnapshot = await firebase
            .firestore()
            .collection("daily-count")
            .where("date", "==", formattedStartDate)
            .get();

        const endSnapshot = await firebase
            .firestore()
            .collection("daily-count")
            .where("date", "==", endDate)
            .get();

        const startSums = {};
        const endSums = {};

        startSnapshot.forEach(doc => {
            const data = doc.data();
            Object.keys(data).forEach(key => {
                if (typeof data[key] === "number") {
                    startSums[key] = data[key];
                }
            });
        });

        endSnapshot.forEach(doc => {
            const data = doc.data();
            Object.keys(data).forEach(key => {
                if (typeof data[key] === "number") {
                    endSums[key] = data[key];
                }
            });
        });

        const getDateValue = (date, id) =>
            process.env.REACT_APP_WEB_MODE === "production"
                ? date[id] || 0
                : date[`${id}2`] || 0;

        const resultArray = pageViewArr.map(item => {
            const startValue = getDateValue(startSums, item.id);
            const endValue = getDateValue(endSums, item.id);
            const difference = endValue - startValue;

            return {
                id: item.id,
                title: item.title,
                stat: `${difference.toLocaleString()} 人`,
                url: ""
            };
        });

        return resultArray;
    } catch (e) {
        console.log("Error fetching page views:", e);
        return pageViewArr.map(el => ({
            id: el.id,
            title: el.title,
            stat: "0 人",
            url: ""
        }));
    }
};

export {
    getUser,
    getUsers,
    updateUser,
    deleteUser,
    saveUser,
    getPageView,
    getEarliestDate
};

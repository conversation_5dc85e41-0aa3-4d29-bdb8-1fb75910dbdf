import React from "react";

import { Icon, Pagination } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import PgDD from "./PgDD";
import PerPgNumDd from "./PerPgNumDD";
import accMngAct from "../../../../../../reduxStore/accManage/accManageAction";

// scss
import "./Pagination.scss";

function CustomPagination() {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { totalPage, currentPage } = state;

    const handlePage = (evt, { activePage }) => {
        dispatch({
            type: accMngAct.SET_CURRENTPAGE,
            payload: activePage
        });
    };

    return (
        <div className="Pagination">
            <div className="chooseRange">
                <PerPgNumDd />
            </div>
            <Pagination
                activePage={currentPage}
                ellipsisItem={{
                    content: <Icon name="ellipsis horizontal" />,
                    icon: true
                }}
                firstItem={{
                    content: <Icon name="angle double left" />,
                    icon: true
                }}
                lastItem={{
                    content: <Icon name="angle double right" />,
                    icon: true
                }}
                prevItem={{ content: <Icon name="angle left" />, icon: true }}
                nextItem={{ content: <Icon name="angle right" />, icon: true }}
                totalPages={totalPage}
                onPageChange={handlePage}
            />
            <div className="selectPage">
                <PgDD />
            </div>
        </div>
    );
}

export default CustomPagination;

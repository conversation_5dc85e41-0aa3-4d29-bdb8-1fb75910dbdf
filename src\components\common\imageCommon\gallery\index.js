import React, { useEffect, useState, useContext } from "react";
import { Checkbox, Popup } from "semantic-ui-react";
import PropTypes from "prop-types";
import { InView } from "react-intersection-observer";
import { useSelector, useDispatch } from "react-redux";
import options from "../options";
import { getImgFilenameFromUrl, formatDateTimeZoneTW } from "./galleryHelper";
import { uuidv4 } from "../../../../commons/utility";
import Api from "../../../../api/nmtl/Api";
import { readNmtlData } from "../../../../api/nmtl";
import { fsFrontEdit } from "../../../../api/fileServer";
import { isEmpty } from "../../../../commons";
import folderConfig from "../folder/folderConfig";
import CustomPagination from "../../CustomPagination/CustomPagination";
import { StoreContext } from "../../../../store/StoreProvider";
import FileAct from "../../../../reduxStore/file/fileAction";
import imgCheckedConfig from "./imgCheckedConfig";

const { SELECT_MODE } = options;

const Gallery = props => {
    const {
        images,
        withCheckbox,
        onChange,
        onImageClick,
        selectMode,
        firstChild,
        currentFolder,
        filterCriteria
    } = props;

    const {
        files: { uploadImagesLatest, uploadImages }
    } = useSelector(state => state);

    const dispatchRedux = useDispatch();

    const [state] = useContext(StoreContext);

    const [urlSelect, setUrlSelect] = useState([]); // for single select
    const [imagesList, setImagesList] = useState(images); // 增加其他參數

    const [filterList, setFilterList] = useState([]);

    // page control
    const pageOption = [50, 100, 200];
    const [curPage, setCurPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [perPageNum, setPerPageNum] = useState(pageOption[0]);

    useEffect(() => {
        setCurPage(1);
    }, [totalPages]);

    // 找到上傳照片的最後一張
    useEffect(() => {
        if (
            uploadImages &&
            uploadImages.length > 0 &&
            state.common.headerActiveName === "資料管理"
        ) {
            const latestImage = [uploadImages[uploadImages.length - 1]];
            dispatchRedux({
                type: FileAct.UPLOAD_IMAGES_LATEST,
                payload: latestImage
            });
        }
    }, [uploadImages]);

    // 上傳的最後一張自動打勾且到預覽區
    useEffect(() => {
        if (uploadImagesLatest.length > 0) {
            setUrlSelect(uploadImagesLatest[0].url);
            imgCheckedConfig.checked[0].value = uploadImagesLatest[0].url;
            imgCheckedConfig.checked[0]["data-img-url"] =
                uploadImagesLatest[0].url;
            imgCheckedConfig.checked[0]["data-img-original-url"] =
                uploadImagesLatest[0].imgUrl;
            imgCheckedConfig.checked[0]["data-img-info"] =
                uploadImagesLatest[0].imgInfo;
            imgCheckedConfig.checked[0]["data-img-file-name"] =
                uploadImagesLatest[0].imgFileName;
            dispatchRedux({
                type: FileAct.CUR_FOLDER_FILES_STATUS,
                payload: imgCheckedConfig
            });
        }
    }, [uploadImagesLatest]);

    const handleClick = (e, data) => {
        dispatchRedux({
            type: FileAct.UPLOAD_IMAGES_LATEST,
            payload: []
        });

        if (selectMode === SELECT_MODE.single.name) {
            if (data.checked) {
                setUrlSelect([data.value]);
            } else {
                setUrlSelect([]);
            }
        } else if (urlSelect.includes(data.value)) {
            // setUrlSelect("");
            const newUrlSelect = urlSelect.filter(url => url !== data.value);
            setUrlSelect(newUrlSelect);
        } else {
            setUrlSelect(prev => [...(prev || []), data.value]);
        }
    };

    useEffect(() => {
        setTotalPages(Math.ceil(filterList.length / perPageNum));
    }, [filterList, perPageNum]);

    useEffect(() => {
        const imgUrls = images.map(img => img.url);
        const newUrlSelect = [];
        imgUrls.forEach(url => {
            if (urlSelect.includes(url)) {
                newUrlSelect.push(url);
            }
        });
        setUrlSelect(newUrlSelect);

        if (!isEmpty(currentFolder)) {
            // "從圖片庫中挑選"的預覽
            // images增加判斷是否顯示已使用的參數，前台編輯資料找settings資料集
            const dataSet =
                currentFolder.path.indexOf(fsFrontEdit) !== -1
                    ? "settings"
                    : currentFolder?.folderName;

            // 選到最後一層資料夾才會增加"已使用"判斷
            if (dataSet) {
                const apiStr = Api.getImageList.replace("{ds}", dataSet);
                readNmtlData(apiStr).then(res => {
                    const imageNames = res?.data.map(
                        ({ imageName }) => imageName
                    );
                    const tmpImages = images.map(img =>
                        // const oriFileName = img.originalUrl.split("/").pop();
                        ({
                            ...img,
                            hasUsed: imageNames.includes(img.imgFileName)
                        })
                    );
                    setImagesList(tmpImages);
                });
            }
        } else {
            // "上傳圖片"的預覽
            const tmpImages = images.map(img => ({
                ...img,
                hasUsed: false
            }));
            setImagesList(tmpImages);
        }
    }, [images]);

    useEffect(() => {
        if (isEmpty(filterCriteria)) {
            setFilterList(imagesList);
            return;
        }
        const tmpFilterList = imagesList
            .filter(
                ({ imgFileName }) =>
                    imgFileName.indexOf(filterCriteria.keyword) !== -1
            )
            .filter(img => {
                switch (filterCriteria.radio) {
                    case folderConfig.radioValue.link:
                        return img.hasUsed;
                    case folderConfig.radioValue.noLink:
                        return !img.hasUsed;
                    default:
                        return true;
                }
            });
        setFilterList(tmpFilterList);
    }, [filterCriteria, imagesList]);

    const handleShow = _images => _images && _images.length > 0;

    const popupHeader = (imgFileName, url) => (
        <>
            {/* {url && <img src={url} alt="" style={{ width: "150px" }} />} */}
            <h3>{imgFileName || getImgFilenameFromUrl(url)}</h3>
        </>
    );

    const popupContent = contentStr => {
        if (!(typeof contentStr === "string")) return "";
        const content = contentStr.split("\n");
        const dtReg = /(?<prefix>.+)(?<datetime>\d{4}-\d{2}-\d{2}[A-Z\s]\d{2}:\d{2}:\d{2}\+\d{2}:\d{2})/;
        return content.map(ct => {
            // 若符合時間格式, 則轉換成特定時區格式
            const dtMatch = ct.match(dtReg);
            if (dtMatch) {
                const { prefix, datetime } = dtMatch.groups;
                return (
                    <div key={uuidv4()}>
                        {`${prefix}${formatDateTimeZoneTW(datetime)}`}
                    </div>
                );
            }
            return <div key={uuidv4()}>{ct}</div>;
        });
    };

    const handlePage = (evt, { activePage }) => {
        setCurPage(activePage);
    };

    const handleDDPage = (evt, data) => {
        setCurPage(data.value);
    };

    const handlePerPageNum = (evt, data) => {
        setCurPage(1);
        setPerPageNum(data.value);
    };

    return (
        <>
            {/* firstChild:可以是 新增檔案的 icon "+"...等 */}
            {firstChild}

            {handleShow(filterList) &&
                filterList
                    .sort((a, b) => {
                        const dateA = new Date(
                            a.imgInfo.match(/建立日期：(.+)/)[1]
                        );
                        const dateB = new Date(
                            b.imgInfo.match(/建立日期：(.+)/)[1]
                        );
                        return dateB - dateA;
                    })
                    .slice((curPage - 1) * perPageNum, curPage * perPageNum)
                    .map((img, index) => {
                        const {
                            url,
                            imgInfo,
                            imgFileName,
                            originalUrl,
                            type,
                            hasUsed
                        } = img;
                        return (
                            <InView
                                key={uuidv4()}
                                triggerOnce
                                rootMargin="500px"
                            >
                                {/* eslint-disable-next-line no-unused-vars */}
                                {({ inView, ref, entry }) => (
                                    <div>
                                        <div
                                            ref={ref}
                                            className="thumb"
                                            data-cur-thumb-id={index}
                                            onClick={() => onImageClick(img)}
                                            style={{
                                                border:
                                                    hasUsed && "5px red solid"
                                            }}
                                        >
                                            <Popup
                                                header={popupHeader(
                                                    imgFileName,
                                                    url
                                                )}
                                                content={popupContent(imgInfo)}
                                                trigger={
                                                    <div className="thumbInner">
                                                        <img
                                                            src={
                                                                inView
                                                                    ? url
                                                                    : ""
                                                            }
                                                            alt=""
                                                        />
                                                    </div>
                                                }
                                                size="small"
                                                hoverable
                                                popperModifiers={{
                                                    preventOverflow: {
                                                        boundariesElement:
                                                            "window"
                                                    }
                                                }}
                                            />
                                            {withCheckbox && (
                                                <Checkbox
                                                    className="img-checkbox"
                                                    name={
                                                        selectMode ===
                                                        SELECT_MODE.multiple
                                                            .name
                                                            ? ""
                                                            : "checkboxRadioGroup"
                                                    }
                                                    value={url}
                                                    checked={(
                                                        urlSelect || []
                                                    ).includes(url)}
                                                    data-img-url={url}
                                                    data-img-original-url={
                                                        originalUrl
                                                    }
                                                    data-img-info={imgInfo}
                                                    data-img-file-name={
                                                        imgFileName
                                                    }
                                                    onClick={(e, data) =>
                                                        handleClick(e, data)
                                                    }
                                                    // 判斷是「圖片」image或是「檔案」docs
                                                    actiontype={type}
                                                    onChange={(e, data) => {
                                                        onChange(e, data);
                                                    }}
                                                />
                                            )}
                                        </div>
                                    </div>
                                )}
                            </InView>
                        );
                    })}
            <div style={{ width: "100%" }}>
                <CustomPagination
                    currentPage={curPage}
                    totalPages={totalPages}
                    handlePage={handlePage}
                    handlePerPageNum={handlePerPageNum}
                    handleDDPage={handleDDPage}
                    pageOption={pageOption}
                />
            </div>
        </>
    );
};

Gallery.defaultProps = {
    images: [],
    withCheckbox: false,
    selectMode: options.SELECT_MODE.multiple.name,
    onChange: () => null,
    onImageClick: () => null,
    /** 圖片顯示篩選條件 */
    filterCriteria: {},
    /** 儲存檔案圖片的路徑資料 */
    currentFolder: {}
};

Gallery.propTypes = {
    images: PropTypes.arrayOf(PropTypes.any),
    withCheckbox: PropTypes.bool,
    selectMode: PropTypes.oneOf(Object.keys(options.SELECT_MODE)),
    onChange: PropTypes.func,
    onImageClick: PropTypes.func,
    /** 圖片顯示篩選條件 */
    filterCriteria: PropTypes.shape({
        keyword: PropTypes.string,
        radio: PropTypes.string
    }),
    /** 儲存檔案圖片的路徑資料 */
    currentFolder: PropTypes.shape({
        path: PropTypes.string,
        path_ch: PropTypes.string,
        folderName: PropTypes.string,
        folderName_ch: PropTypes.string,
        type: PropTypes.string
    })
};

export default Gallery;

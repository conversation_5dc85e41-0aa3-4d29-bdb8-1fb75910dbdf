import React from "react";

// ui
import { Table } from "semantic-ui-react";

// common
import { isEmpty } from "../../../../../../commons";

const CustomTableRowCell = ({ type, label, rawData, newData }) => {
    switch (type) {
        case 'create':
            return (
                <Table.Row >
                    <Table.Cell>{label}</Table.Cell>
                    <Table.Cell positive>{rawData}</Table.Cell>
                    <Table.Cell>{newData}</Table.Cell>
                </Table.Row>
            );
        case 'update':
            if (!isEmpty(rawData) && !isEmpty(newData)) {
                const isDiff = rawData !== newData;
                return (
                    <Table.Row warning={isDiff}>
                        <Table.Cell>{label}</Table.Cell>
                        <Table.Cell negative={isDiff}>{rawData}</Table.Cell>
                        <Table.Cell positive={isDiff}>{newData}</Table.Cell>
                    </Table.Row>
                );
            } else if (isEmpty(rawData) && !isEmpty(newData)) {
                return (
                    <Table.Row warning>
                        <Table.Cell>{label}</Table.Cell>
                        <Table.Cell negative>{rawData}</Table.Cell>
                        <Table.Cell positive>{newData}</Table.Cell>
                    </Table.Row>
                );
            } else if (!isEmpty(rawData) && isEmpty(newData)) {
                return (
                    <Table.Row warning>
                        <Table.Cell>{label}</Table.Cell>
                        <Table.Cell negative>{rawData}</Table.Cell>
                        <Table.Cell positive>{newData}</Table.Cell>
                    </Table.Row>
                );
            } else {
                return (
                    <Table.Row>
                        <Table.Cell>{label}</Table.Cell>
                        <Table.Cell>{rawData}</Table.Cell>
                        <Table.Cell>{newData}</Table.Cell>
                    </Table.Row>
                );
            }
        case 'delete':
            return (
                <Table.Row >
                    <Table.Cell>{label}</Table.Cell>
                    <Table.Cell negative>{rawData}</Table.Cell>
                    <Table.Cell>{newData}</Table.Cell>
                </Table.Row>
            );
        default:
            return (
                <Table.Row >
                    <Table.Cell>{label}</Table.Cell>
                    <Table.Cell>{rawData}</Table.Cell>
                    <Table.Cell>{newData}</Table.Cell>
                </Table.Row>
            );
    }
};

export default CustomTableRowCell;
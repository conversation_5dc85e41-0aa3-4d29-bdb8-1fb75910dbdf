import React, { useContext, useState } from 'react';
import { Button } from 'semantic-ui-react';
import { StoreContext } from '../../../store/StoreProvider';
import Act from '../../../store/actions';
import { objectCompare, saveData, keyPath } from '../commons';
import { isEmpty } from '../../../commons';
import SettingResultModal from './SettingResultModal';
import { createHistoryEvent } from '../../downloadData/components/history/common/common';
import CustomModal from './CustomModal';

// configs
import Api from '../../../api/nmtl/Api';
import NMTL_WEB_CONFIGS from '../nmtl-web/config';

// functions
import { updateNmtlData } from '../../../api/nmtl';

const { VRMUSEUM } = NMTL_WEB_CONFIGS.MENU_ACTIVE_ITEM;

function showAlertMsg(objRealtimeData, language) {
    // 語系
    const languageOption = language === 'zh' ? '中文' : '英文';
    // 內容
    let content = objRealtimeData;
    keyPath.forEach((key) => {
        content = content[key];
    });

    // clear keyPath
    while (keyPath.length !== 0) {
        keyPath.shift();
    }

    const realtimeMsg = `語系: ${languageOption} \n\r
                       內容: ${content} \n`;

    return realtimeMsg;
}

function SaveButton({ language }) {
    const [state, dispatch] = useContext(StoreContext);
    const {
        menuActiveItem,
        originData,
        realtimeData,
        updatedData,
        selectOption,
        openModal,
        fusekiData: {
            vrMuseum: {
                logo: { oriData, tempData },
            },
        },
    } = state.websiteSetting;
    const [showMessage, setShowMessage] = useState('');
    const [openSettingResultModal, setOpenSettingResultModal] = useState(false);
    const [loading, setLoading] = useState(false);

    const { headerActiveName } = state.common;
    const { displayName } = state.user;
    const columns = [headerActiveName, menuActiveItem.name];
    const { role } = state.user;

    /*
     * selectOption === ""(表示該頁有下拉選單，但還沒有點選項目)
     * selectOption === null(表示該頁沒有下拉選單)
     * */

    // for update logo data to Apache Jena Fuseki
    const updateNmtlLogoData = async ({ user, api, dataset, sheetName }) => {
        const { graph, srcId, label, imageName, type, classType } = oriData;
        const entrySrc = {
            graph,
            srcId,
            classType,
            value: { label, imageName, type },
        };
        const entryDst = {
            ...entrySrc,
            value: {
                label: tempData?.text,
                imageName,
                type: tempData?.type,
            },
        };

        await updateNmtlData(user, api, dataset, sheetName, entrySrc, entryDst)
            .then(() => {
                dispatch({
                    type: Act.SET_VRMUSEUM_LOGO_ORI_DATA,
                    payload: {
                        ...oriData,
                        label: tempData?.text,
                        imageName,
                        type: tempData?.type,
                    },
                });

                const historyMsg = `${JSON.stringify(entrySrc)}\n變動後：\n${JSON.stringify(
                    entryDst,
                )}`;
                // 建立歷史紀錄
                createHistoryEvent(displayName, '更新', `${columns.join('/')}：${historyMsg}`);

                setLoading(false);
                setOpenSettingResultModal(true);
            })
            .catch((error) => console.error(`Update Nmtl Data Failed: ${error}`));
    };

    const handleClick = () => {
        if (selectOption === '') {
            alert('請選擇下拉選單選項');
            return;
        }
        setLoading(true);

        // for update logo data to Apache Jena Fuseki
        if (menuActiveItem?.key === VRMUSEUM && oriData && tempData) {
            // update logo data
            updateNmtlLogoData({
                user: state.user,
                api: Api.getGeneric,
                dataset: selectOption,
                sheetName: 'frontEdit Data',
            });
        }
        let objOriginData = originData.find((element) => element.id === menuActiveItem.key)[
            selectOption
        ];
        let objRealtimeData = {};
        if (selectOption && selectOption.length !== 0 && objOriginData) {
            objRealtimeData = realtimeData.find((element) => element.id === menuActiveItem.key)[
                selectOption
            ];
        } else {
            objOriginData = originData.find((element) => element.id === menuActiveItem.key);
            objRealtimeData = realtimeData.find((element) => element.id === menuActiveItem.key);
        }

        if (!(isEmpty(objOriginData) || isEmpty(objRealtimeData))) {
            if (!objectCompare(objOriginData, objRealtimeData)) {
                dispatch({
                    type: Act.SET_OPENMODAL,
                    payload: true,
                });
                dispatch({
                    type: Act.SET_MODALMESSAGE,
                    payload: showAlertMsg(objRealtimeData, language),
                });
            } else {
                // 資料庫資料沒有變動，直接更新
                saveData(
                    menuActiveItem,
                    updatedData,
                    setShowMessage,
                    setOpenSettingResultModal,
                    setLoading,
                    originData,
                    displayName,
                    columns,
                );
                //
                dispatch({
                    type: Act.SET_ORIGINDATA,
                    payload: updatedData,
                });
                createHistoryEvent(displayName, '更新', columns.join('/'));
            }
        }
    };

    return (
        <div
            style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
            }}
        >
            <Button primary onClick={handleClick} loading={loading} disabled={role === 'reader'}>
                保存
            </Button>
            <SettingResultModal
                openSettingResultModal={openSettingResultModal}
                setOpenSettingResultModal={setOpenSettingResultModal}
                showMessage={showMessage}
            />

            {/* DB data有更改時，跳提示視窗 */}
            {openModal && (
                <CustomModal
                    setOpenSettingResultModal={setOpenSettingResultModal}
                    setShowMessage={setShowMessage}
                    setLoading={setLoading}
                />
            )}
        </div>
    );
}

export default SaveButton;

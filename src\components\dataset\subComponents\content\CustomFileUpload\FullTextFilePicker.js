import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
    Modal,
    Button,
    Grid,
    Segment,
    Container,
    Dimmer,
    Loader
} from "semantic-ui-react";

// component
import FolderMenu from "../CustomImageInput/uploadImage/FolderMenu";
import CurrentFolder from "./CurrentFolder";
import FilePicker from "../CustomImageInput/uploadImage/FilePicker";
import CurrentFile from "./CurrentFile";

// store
import FileAct from "../../../../../reduxStore/file/fileAction";

// helper
import uploadConfig from "../../../../toolPages/components/upload/uploadConfig";
import { uuidv4 } from "../../../../../commons/utility";

const FullTextFilePicker = ({
    open,
    setOpen,
    defaultValue: oriValue,
    onValueChange
}) => {
    const {
        files: { loading, defaultValue, selectFile }
    } = useSelector(state => state);
    const dispatchRedux = useDispatch();
    const [pickMethod, setPickMethod] = useState("store");

    const handleModalClose = () => {
        if (oriValue !== selectFile) {
            if (selectFile && selectFile.length > 0) {
                onValueChange(selectFile);
            } else {
                onValueChange(defaultValue);
            }
        }
        // console.log(oriValue, selectFile);

        dispatchRedux({
            type: FileAct.INIT_FILE_SETTINGS,
            payload: false
        });

        dispatchRedux({ type: FileAct.INIT_FOLDERPATTERN });

        dispatchRedux({
            type: FileAct.FOLDER_FILES_URL,
            payload: []
        });

        setOpen(false);
    };

    const selectMode = {
        store: {
            name: "store",
            label: "從檔案庫中挑選",
            onClick: () => setPickMethod("store"),
            leftComp: <FolderMenu />,
            rightComp: <CurrentFolder type={uploadConfig.file} />
        },
        upload: {
            name: "upload",
            label: "上傳檔案",
            onClick: () => setPickMethod("upload"),
            leftComp: <FolderMenu />,
            rightComp: <FilePicker type={uploadConfig.file} />
        }
    };

    // 刪除連結
    const handleClearLink = () => {
        dispatchRedux({
            type: FileAct.SET_DEFAULT_VALUE,
            payload: ""
        });
    };

    // console.log(defaultValue, selectFile);
    return (
        <Modal
            size="large"
            open={open}
            closeOnDocumentClick
            onClose={handleModalClose}
        >
            <Dimmer active={loading.state} inverted>
                <Loader size="large">{loading.message}</Loader>
            </Dimmer>
            <Modal.Header>檔案設定</Modal.Header>
            <Modal.Content image scrolling>
                <Container>
                    {/* display current image */}
                    <CurrentFile
                        defaultValue={defaultValue}
                        currentValue={selectFile}
                    />
                    {/* switcher: 使用圖片庫 or 上傳圖片 */}
                    <Button.Group>
                        {Object.values(selectMode).map(btn => (
                            <Button
                                key={uuidv4()}
                                positive={pickMethod === btn.name}
                                onClick={() => btn.onClick()}
                            >
                                {btn.label}
                            </Button>
                        ))}
                    </Button.Group>
                    <Button
                        style={{ marginLeft: "20px" }}
                        onClick={handleClearLink}
                    >
                        刪除檔案連結
                    </Button>
                    <Grid textAlign="center" celled stackable>
                        <Grid.Row>
                            <Grid.Column width={3}>
                                <Segment basic compact>
                                    {/* left side */}
                                    {pickMethod in selectMode &&
                                        selectMode[pickMethod].leftComp}
                                </Segment>
                            </Grid.Column>
                            <Grid.Column width={13}>
                                {/* right side */}
                                {pickMethod in selectMode &&
                                    selectMode[pickMethod].rightComp}
                            </Grid.Column>
                        </Grid.Row>
                    </Grid>
                </Container>
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={() => handleModalClose()} primary>
                    關閉
                </Button>
            </Modal.Actions>
        </Modal>
    );
};

export default FullTextFilePicker;

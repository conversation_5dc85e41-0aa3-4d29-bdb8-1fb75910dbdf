import TurndownService from "turndown";

// html convert to markdown
const htmlToMarkDown = str => {
    // cancel turndownJS escape rule
    TurndownService.prototype.escape = string => string;
    const turndownService = new TurndownService();

    turndownService
        .addRule("keep", {
            filter: ["iframe", "img", "span", "u", "sub", "sup"],
            replacement(content, node) {
                return node.outerHTML;
            }
        })
        .addRule("h1", {
            filter: ["h1"],
            replacement(content) {
                return `# ${content}\n`;
            }
        })
        .addRule("h2", {
            filter: ["h2"],
            replacement(content) {
                return `## ${content}\n`;
            }
        })
        .addRule("s", {
            filter: ["s"],
            replacement(content) {
                return `~~${content}~~`;
            }
        })
        .addRule("em", {
            filter: ["em"],
            replacement(content) {
                return ` _${content}_ `;
            }
        });
    return turndownService.turndown(str);
};

export default htmlToMarkDown;

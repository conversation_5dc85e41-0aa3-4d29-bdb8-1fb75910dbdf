import Api from "../../../../../../api/nmtl/Api";
import { readNmtlData } from "../../../../../../api/nmtl";
import { pubStatusSelect } from "./fixedSelect";
import { setTbBDData } from "./utils";

const getNewsTB = (dispatch, websiteSubject) => {
    const tmpSubject = websiteSubject === "nmtl-web" ? "nmtl" : websiteSubject;
    const apiStr = Api.getNewsList.replace("{subject}", tmpSubject);
    readNmtlData(apiStr).then(res => {
        const showStatus = pubStatusSelect
            .filter(el => el.enable)
            .map(el => el.value);

        const data = res.data.filter(({ status }) =>
            showStatus.includes(status)
        );
        setTbBDData(dispatch, data);
    });
};

export default getNewsTB;

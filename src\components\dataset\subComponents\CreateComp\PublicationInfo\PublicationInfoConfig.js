import CustomSegmentReadOnly from "../../commonComp/specialTableComp/CustomSegmentReadOnly";
import CustomSingleInput from "../../commonComp/specialTableComp/CustomSingleInput";
import CustomSingleDropdown from "../../commonComp/specialTableComp/CustomSingleDropdown";
import CustomSingleDropdownCreatable from "../../commonComp/specialTableComp/CustomSingleDropdownCreatable";
import CustomDoubleInput from "../../commonComp/specialTableComp/CustomDoubleInput";
import CustomDoubleTextArea from "../../commonComp/specialTableComp/CustomDoubleTextArea";
import CustomMultiDropdownCreatable from "../../commonComp/specialTableComp/CustomMulitDropdownCreatable";
import CustomMultiDropdown from "../../commonComp/specialTableComp/CustomMulitDropdown";
import CustomSingleTextArea from "../../commonComp/specialTableComp/CustomSingleTextArea";
import CustomFileUpload from "../../commonComp/specialTableComp/CustomFileUpload";
import CustomImageInput from "../../commonComp/specialTableComp/CustomImageInput";
import CustomCheckBox from "../../commonComp/specialTableComp/CustomCheckBox";
import CustomLocationInput from "./CustomLocationInput/CustomLocationInput";
import CustomSingleDropdownForAcc from "./CustomTabContent/CustomSingleDropdownForAcc/CustomSingleDropdownForAcc";
import CustomDoubleDropdownForAcc from "./CustomTabContent/CustomDoubleDropdownForAcc/CustomDoubleDropdownForAcc";

export const hasTranslationBook = "hasTranslationBook";
const PublicationInfoConfig = {
    oriBookArea: [
        [
            {
                header: "srcId",
                comp: CustomSegmentReadOnly,
                defaultValue: "資料新增完成後系統將自動產生ID"
            },
            {
                header: "label_Publication",
                comp: CustomSingleInput
            }
        ]
    ],
    transBookArea: [
        {
            Accordion: false,
            content: [
                [
                    {
                        header: "hasTranslationBook",
                        comp: CustomSegmentReadOnly
                    },
                    {
                        header: "translationBookName",
                        comp: CustomSingleInput
                    }
                ]
            ]
        },
        {
            Accordion: true,
            searchBar: true,
            accTitle: "作者",
            accList: "hasAuthor",
            content: [
                [
                    {
                        header: "hasAuthor",
                        comp: CustomSingleDropdownForAcc,
                        compProp: {
                            linkToHeader: "authorName"
                        }
                    },
                    {
                        header: "authorName",
                        comp: CustomDoubleDropdownForAcc,
                        compProp: {
                            subTitle: `顯示名稱`,
                            linkToHeader: "hasAuthor"
                        }
                    }
                ]
            ]
        },
        {
            Accordion: true,
            searchBar: true,
            accTitle: "譯者",
            accList: "hasTranslator",
            content: [
                [
                    {
                        header: "hasTranslator",
                        comp: CustomSingleDropdownForAcc,
                        compProp: {
                            linkToHeader: "translatorName"
                        }
                    },
                    {
                        header: "translatorName",
                        comp: CustomDoubleDropdownForAcc,
                        compProp: {
                            subTitle: "顯示名稱",
                            linkToHeader: "hasTranslator"
                        }
                    }
                ]
            ]
        },
        {
            Accordion: false,
            content: [
                [
                    {
                        header: "hasEditor",
                        comp: CustomMultiDropdownCreatable
                    },
                    {
                        header: "hasPublisher",
                        comp: CustomMultiDropdownCreatable
                    }
                ],
                [
                    {
                        header: "hasInceptionDate",
                        colSpan: 3,
                        comp: CustomSingleInput
                    }
                ],
                [
                    {
                        header: "srcId_hasPlaceOfPublication",
                        rowSpan: 2,
                        comp: CustomSingleDropdownCreatable
                    },
                    {
                        header: "geoLatitude_hasPlaceOfPublication",
                        comp: CustomLocationInput
                    }
                ],
                [
                    {
                        header: "geoLongitude_hasPlaceOfPublication",
                        comp: CustomLocationInput
                    }
                ],
                [
                    {
                        header: "LiteraryGenre",
                        comp: CustomMultiDropdown
                    },
                    {
                        header: "hasLanguageOfWorkOrName",
                        comp: CustomMultiDropdown
                    }
                ],
                [
                    {
                        header: "totalPage",
                        comp: CustomSingleInput
                    },
                    {
                        header: "ISBN",
                        comp: CustomSingleInput
                    }
                ],
                [
                    {
                        header: "references",
                        colSpan: 3,
                        comp: CustomDoubleInput,
                        compProp: {
                            titleFirst:
                                "中文版網站顯示名稱，請於內容最後填上語系@zh",
                            titleSecond: "外文版網站顯示名稱"
                        },
                        errorMsg:
                            "不可為空，且欄位請填入正確後綴，如：@zh, @en, @Ja ..."
                    }
                ],
                [
                    {
                        header: "introduction",
                        colSpan: 3,
                        comp: CustomDoubleTextArea,
                        compProp: {
                            subTitle: `顯示摘要`
                        }
                    }
                ],
                [
                    {
                        header: "tableOfContents",
                        colSpan: 3,
                        comp: CustomSingleTextArea
                    }
                ]
            ]
        },
        {
            Accordion: false,
            content: [
                [
                    {
                        header: "fileAvailableAt",
                        comp: CustomFileUpload,
                        compProp: {
                            subTitle: "顯示名稱"
                        }
                    },
                    {
                        header: "hasFullWorkCopyright",
                        comp: CustomSingleDropdown
                    }
                ],
                [
                    {
                        header: "imageURL_hasURL",
                        subHeader: "建議上傳尺寸建議為：852px * 852px",
                        comp: CustomImageInput
                    },
                    {
                        header: "hasCopyrightStatus_hasURL",
                        comp: CustomSingleDropdown
                    }
                ],
                [
                    {
                        header: "comment",
                        colSpan: 3,
                        comp: CustomSingleInput
                    }
                ],
                [
                    {
                        header: "lift",
                        comp: CustomCheckBox
                    },
                    {
                        header: "peak",
                        comp: CustomCheckBox
                    }
                ],
                [
                    {
                        header: "friendlyLink",
                        colSpan: 3,
                        comp: CustomSingleInput
                    }
                ]
            ]
        }
    ]
};

export default PublicationInfoConfig;

import { useContext, useEffect } from "react";

// firebase
import firebase from "firebase/app";
import "firebase/auth";

// store
import { StoreContext } from "../../store/StoreProvider";
import act from "../../store/actions";

// commons code
import { getFormatUser } from "../../commons";
import { getUser } from "../../api/firebase/realtimeDatabase";
import role from "../../App-role";

import Api from "../../api/nmtl/Api";
import getAllGPInfo from "./utils";

const ALLOW_TEST_USER_ONLY = false;
const AuthListener = () => {
    // eslint-disable-next-line no-unused-vars
    const [_, dispatch] = useContext(StoreContext);
    // const firebaseAuth = firebase.auth();

    useEffect(() => {
        if (!firebase.auth()) {
            return;
        }

        if (ALLOW_TEST_USER_ONLY === true) {
            const userInfo = {
                uid: "AAAAAAAAAAAAAAAAAAAAAAAAAAAA",
                displayName: "測試帳號",
                email: "<EMAIL>",
                emailVerified: true,
                isAnonymous: false,
                providerId: "email",
                creationTimestamp: "1576230808124",
                lastSignInTimestamp: "1612159891292",
                creationTime: "Fri, 13 Dec 2019 09:53:28 GMT",
                lastSignInTime: "Mon, 01 Feb 2021 06:11:31 GMT",
                role: "reader"
            };
            dispatch({
                type: act.FIREBASE_LOGIN_USER,
                payload: userInfo
            });
            localStorage.setItem("isLogin", JSON.stringify(true));
            return;
        }

        firebase.auth().onAuthStateChanged(async user => {
            if (user) {
                const userInfo = getFormatUser(user);
                const { uid, displayName, email } = userInfo;
                const userData = uid && (await getUser(uid));
                userInfo.role = (userData && userData.role) || role.anonymous;

                if (uid && (displayName || email)) {
                    dispatch({
                        type: act.FIREBASE_LOGIN_USER,
                        payload: userInfo
                    });
                    // 加user所屬的group資訊
                    getAllGPInfo(userInfo, dispatch);
                }
                // localStorage
                localStorage.setItem("isLogin", JSON.stringify(true));
            } else {
                dispatch({
                    type: act.FIREBASE_LOGOUT_USER
                });
                // localStorage
                localStorage.removeItem("isLogin");
            }
        });

        // refresh 認證
        const idTokenNextObserver = async user => {
            if (user) {
                // token: expiration time: 60 mins
                const token = await user.getIdToken();
                // localStorage
                localStorage.setItem("isLogin", JSON.stringify(true));
                Api.setAxiosAuth(token);
            }
        };
        const onIdTokenChangeError = () => {
            alert("連線逾時,請重新整理網頁");
        };
        const onIdTokenChangeComplete = () => {
            // do nothing
        };
        // observe for token change
        firebase
            .auth()
            .onIdTokenChanged(
                idTokenNextObserver,
                onIdTokenChangeError,
                onIdTokenChangeComplete
            );
    }, [dispatch]);

    return null;
};

export default AuthListener;

import { splitTag } from "../config/config";
import { noZHTag } from "../../../../../../../commons/utility";

const checkZHTag = (cell, propLabel) => {
    let tmpResStr = "";
    if (cell.value) {
        if (typeof cell.value === "string") {
            const allLabel = cell.value.split(splitTag);
            if (allLabel.every(elStr => noZHTag(elStr))) {
                tmpResStr += `${cell.address}, [${cell.value}], 欄位:${propLabel}，須至少有一個${propLabel}最後帶'@zh'(半形符號)。\n`;
            }
        } else if (Object.hasOwn(cell.value, "hyperlink")) {
            if (typeof cell.value.text === "string") {
                const allLabel = cell.value.text.split(splitTag);
                if (allLabel.every(elStr => noZHTag(elStr))) {
                    tmpResStr += `${cell.address}, [${cell.value}], 欄位:${propLabel}，須至少有一個${propLabel}最後帶'@zh'(半形符號)。\n`;
                }
            } else {
                tmpResStr += `${cell.address}, [${cell.value}], 欄位:${propLabel}，須至少有一個${propLabel}最後帶'@zh'(半形符號)。\n`;
            }
        }
    }

    return tmpResStr;
};

export default checkZHTag;

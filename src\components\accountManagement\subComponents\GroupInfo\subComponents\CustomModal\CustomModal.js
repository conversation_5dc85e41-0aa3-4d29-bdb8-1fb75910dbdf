import React, { useEffect, useState } from "react";

// plugins
import { <PERSON><PERSON>, <PERSON><PERSON> } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";

//
import accMngAct from "../../../../../../reduxStore/accManage/accManageAction";
import { BtnName, TypeName } from "../../../Utils/compoConfig";
import MemberAddCnt from "./ModalContent/MemberAddCnt";
import GroupDelCnt from "./ModalContent/GroupDelCnt";
import ModalConfirmBtn from "../CustomButton/ModalConfirmBtn";
import MemberDelCnt from "./ModalContent/MemberDelCnt";
import textConfig from "../../../Utils/textConifg";
import SaveDataCnt from "./ModalContent/SaveDataCnt";
import { getGroupData } from "../../../Utils/utils";

function CustomModal() {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { modalCaller } = state;
    const [headerStr, setHeaderStr] = useState("");
    const confirmBtnCaller = [TypeName.GroupDelete, TypeName.GroupMemberDelete];
    const saveCaller = [
        textConfig.SuccessMessage.callerName,
        textConfig.ErrorMessage.callerName
    ];
    const colChkCaller = [textConfig.ColumnCheck.callerName];

    const showSaveData = () =>
        saveCaller.indexOf(modalCaller) !== -1 ||
        colChkCaller.indexOf(modalCaller) !== -1;

    const closeModal = () => {
        // close after checking save result modal
        if (saveCaller.indexOf(modalCaller) !== -1) {
            dispatch({
                type: accMngAct.SET_EDITGROUPMODE,
                payload: false
            });
            // 再從firebase抓最新groupData
            getGroupData(dispatch);
        }

        dispatch({
            type: accMngAct.SET_MODALCALLER,
            payload: ""
        });
    };

    useEffect(() => {
        if (modalCaller === "") return;

        switch (modalCaller) {
            case textConfig.SuccessMessage.callerName:
                setHeaderStr(textConfig.SuccessMessage.SuccessTitle);
                break;
            case textConfig.ColumnCheck.callerName:
            case textConfig.ErrorMessage.callerName:
                setHeaderStr(textConfig.ErrorMessage.ErrorTitle);
                break;
            default:
                setHeaderStr(
                    BtnName.find(el => el.typeName === modalCaller).btnName
                );
                break;
        }
    }, [modalCaller]);

    return (
        <Modal onClose={closeModal} open={modalCaller !== ""}>
            <Modal.Header>{headerStr}</Modal.Header>
            <Modal.Content>
                {modalCaller === TypeName.GroupMemberADD && <MemberAddCnt />}
                {modalCaller === TypeName.GroupDelete && <GroupDelCnt />}
                {modalCaller === TypeName.GroupMemberDelete && <MemberDelCnt />}
                {showSaveData() && <SaveDataCnt />}
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={closeModal} positive>
                    關閉
                </Button>
                {confirmBtnCaller.indexOf(modalCaller) !== -1 && (
                    <ModalConfirmBtn />
                )}
            </Modal.Actions>
        </Modal>
    );
}

export default CustomModal;

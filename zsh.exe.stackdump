Stack trace:
Frame         Function      Args
0000005FF3A0  00021005FEBA (00021026AD60, 00021026AB6E, 000000000000, 0000005FB000) msys-2.0.dll+0x1FEBA
0000005FF3A0  0002100467F9 (0000005FD320, 0000005FF3A0, 0000005FFAD0, 000210070DF0) msys-2.0.dll+0x67F9
0000005FF3A0  000210046832 (000000000000, 0000005FBDF8, 000000000000, 000100402000) msys-2.0.dll+0x6832
0000005FF3A0  000210046DCB (000000000002, 000210312760, 000000000060, 000000000000) msys-2.0.dll+0x6DCB
0000005FF3A0  00021004831F (00007FFE0384, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x831F
0000005FF3A0  000210070EAC (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x30EAC
0000005FF630  7FFC8DCA020E (000000000000, 000000000000, 000000000000, 000000000000) ntdll.dll+0x16020E
0000005FF630  7FFC8DBB29CE (000210040000, 000000000001, 7FFC8DB4F164, 7FFC8DCBD430) ntdll.dll+0x729CE
0000005FF630  7FFC8DBB193C (7FFC8DB7AE00, 7FFC00000000, 000000654B60, 0000005FF634) ntdll.dll+0x7193C
0000005FF630  7FFC8DB7BC7A (000000656D90, 000000654C00, 7FFC8DD13710, 000000000040) ntdll.dll+0x3BC7A
0000005FF630  7FFC8DB7BCA6 (7FFC8DCDBF08, 7FFC8DCB9298, 0000002F2000, 7FFC8DCDC280) ntdll.dll+0x3BCA6
000000000000  7FFC8DBA56AC (000000000000, 000000000000, 0000002F2000, 000000000000) ntdll.dll+0x656AC
000000000000  7FFC8DBA3864 (7FFC8DB40000, 0000002F2000, 0000002F3000, 7FFC8DB40000) ntdll.dll+0x63864
000000000000  7FFC8DBA364E (000000000000, 000000000000, 000000000000, 000000000000) ntdll.dll+0x6364E
000000000000  7FFC8DB75FCE (000000000000, 000000000000, 000000000000, 000000000000) ntdll.dll+0x35FCE
End of stack trace
Loaded modules:
000100400000 zsh.exe
7FFC8DB40000 ntdll.dll
7FFC8C170000 KERNEL32.DLL
7FFC8AE80000 KERNELBASE.dll
000539B50000 msys-zsh-5.9.dll
000210040000 msys-2.0.dll
0005FCB10000 msys-ncursesw6.dll
7FFC8BB80000 advapi32.dll
7FFC8C5D0000 msvcrt.dll
7FFC8BD40000 sechost.dll
7FFC8D690000 RPCRT4.dll
7FFC8A1A0000 CRYPTBASE.DLL
7FFC8B840000 bcryptPrimitives.dll

import React, { useEffect, useState } from "react";
import { Popup, Icon, Label } from "semantic-ui-react";
import { sortedByStroke } from "twchar";

const MultiLangCell = ({
    // 所有 cell 共用的 props
    colId,
    rowIdx,
    cellValue,
    column,
    rowData,
    domain,
    range,
    graph,
    classType
    // 特定 cell 才有的 props

    // 除了 table 之外的使用,可以自定義其他的 property 擴充功能
}) => {
    //

    // 複製文字的 Icon
    const CopyEl = ({ value }) => {
        const [show, setShow] = useState(false);
        const colorDef = "grey";
        const [color, setColor] = useState(colorDef);
        const copy = async () => {
            try {
                await navigator.clipboard.writeText(value);
                setShow(true);
                setColor("blue");
                setTimeout(() => {
                    setShow(false);
                    setColor(colorDef);
                }, [2000]);
            } catch (e) {
                //
            }
        };

        const iconProps =
            (color && {
                color
            }) ||
            {};

        if (!value) return null;

        return (
            <React.Fragment>
                <div
                    onClick={() => copy()}
                    style={{ cursor: "pointer" }}
                    onMouseOver={e => {
                        e.stopPropagation();
                    }}
                >
                    <Icon name="copy outline" {...iconProps} />
                    {show && <div style={{ color: "#2185d0" }}>Copied!</div>}
                </div>
            </React.Fragment>
        );
    };

    const valueList = (Array.isArray(cellValue)
        ? cellValue
        : [cellValue]
    ).filter(o => !!o && o.value);

    const labelStr = value => (value?.label ? `${value.label}` : `無語系`);

    const sortValueList = sortedByStroke(valueList, "label");

    return (
        <div>
            {sortValueList.map((o, idx) => (
                <Popup
                    key={idx.toString()}
                    // on={["hover"]}
                    // hoverable
                    on="click"
                    wide
                    position="right center"
                    trigger={
                        <Label
                            style={{ marginBottom: "8px", cursor: "pointer" }}
                        >
                            {labelStr(o)}
                        </Label>
                    }
                >
                    <Popup.Content>
                        <div style={{ maxHeight: "300px", overflow: "auto" }}>
                            <Label style={{ marginBottom: "8px" }}>
                                {labelStr(o)}
                            </Label>
                            <div>{o.value}</div>
                        </div>
                        <CopyEl value={o.value} />
                    </Popup.Content>
                </Popup>
            ))}
        </div>
    );
};

export default MultiLangCell;

import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

// semantic ui
import { Button, Container, Grid, Segment } from "semantic-ui-react";

// components
import CurrentFile from "./CurrentFile";
import FolderMenu from "../CustomImageInput/uploadImage/FolderMenu";
import CurrentFolder from "./CurrentFolder";
import FilePicker from "../CustomImageInput/uploadImage/FilePicker";

// utils
import { uuidv4 } from "../../../../../commons/utility";
import { getFileFolderPattern } from "../../../../common/imageCommon/FolderList/folderListHelper";

// config
import uploadConfig from "../../../../toolPages/components/upload/uploadConfig";
import FileAct from "../../../../../reduxStore/file/fileAction";

/**
 * fixme: split code from FullTextFilePicker，之後再整合到FullTextFilePicker裡面，目前先用在SpecialImportButton裡面(Bennis 20231018)
 * */
function FilePickerContent() {
    const {
        files: { defaultValue, selectFile }
    } = useSelector(state => state);
    const dispatchRedux = useDispatch();
    const [pickMethod, setPickMethod] = useState("store");

    useEffect(() => {
        dispatchRedux({
            type: FileAct.SET_DEFAULT_VALUE,
            payload: ""
        });
        dispatchRedux({
            type: FileAct.SELECT_FILE,
            payload: ""
        });
        // dispatchRedux({
        //     type: FileAct.SET_FIRSTLAYERFILENAME,
        //     payload: []
        // });
        getFileFolderPattern(dispatchRedux, uploadConfig.ApiGetFiles);
        return () => {
            dispatchRedux({ type: FileAct.INIT_FOLDERPATTERN });
            dispatchRedux({
                type: FileAct.FOLDER_FILES_URL,
                payload: []
            });
            dispatchRedux({ type: FileAct.INIT_SELECT_FOLDER });
        };
    }, []);

    const selectMode = {
        store: {
            name: "store",
            label: "從檔案庫中挑選",
            onClick: () => setPickMethod("store"),
            leftComp: <FolderMenu />,
            rightComp: <CurrentFolder type={uploadConfig.file} />
        },
        upload: {
            name: "upload",
            label: "上傳檔案",
            onClick: () => setPickMethod("upload"),
            leftComp: <FolderMenu />,
            rightComp: <FilePicker type={uploadConfig.file} />
        }
    };

    // 刪除連結
    const handleClearLink = () => {
        dispatchRedux({
            type: FileAct.SET_DEFAULT_VALUE,
            payload: ""
        });
    };

    return (
        <Container>
            {/* display current image */}
            <CurrentFile
                defaultValue={defaultValue}
                currentValue={selectFile}
            />
            {/* switcher: 使用圖片庫 or 上傳圖片 */}
            <Button.Group>
                {Object.values(selectMode).map(btn => (
                    <Button
                        key={uuidv4()}
                        positive={pickMethod === btn.name}
                        onClick={() => btn.onClick()}
                    >
                        {btn.label}
                    </Button>
                ))}
            </Button.Group>
            <Button style={{ marginLeft: "20px" }} onClick={handleClearLink}>
                刪除檔案連結
            </Button>
            <Grid textAlign="center" celled stackable>
                <Grid.Row>
                    <Grid.Column width={3}>
                        <Segment basic compact>
                            {/* left side */}
                            {pickMethod in selectMode &&
                                selectMode[pickMethod].leftComp}
                        </Segment>
                    </Grid.Column>
                    <Grid.Column width={13}>
                        {/* right side */}
                        {pickMethod in selectMode &&
                            selectMode[pickMethod].rightComp}
                    </Grid.Column>
                </Grid.Row>
            </Grid>
        </Container>
    );
}

export default FilePickerContent;

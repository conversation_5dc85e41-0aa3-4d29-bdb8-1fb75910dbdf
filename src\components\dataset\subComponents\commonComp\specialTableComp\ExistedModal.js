import React, { useContext, useState } from "react";

import { Button, Modal, Radio, Table } from "semantic-ui-react";
import axios from "axios";
import { StoreContext } from "../../../../../store/StoreProvider";

import { isEmpty } from "../../../../../commons";
import { createNmtlData } from "../../../../../api/nmtl";
import { isCorrectSuffix } from "../../../../../api/nmtl/ApiField";
import Api from "../../../../../api/nmtl/Api";
import getKeyBySingle from "../../../../../api/nmtl/ApiKey";
import getSingleByApi from "../../../../../api/nmtl/ApiSingle";
import { updateObjectValue } from "../../CreateComp/helper";
import Act from "../../../../../store/actions";

/**
 * 此組件用於將資料已存在其他dataset但不在tltc的進行複製移動至tltc
 * 目前此組件僅對以下五個欄位進行優化支援
 */

const CMDC = ["hasPublisher", "hasEditor"];
const CSDFA = ["hasAuthor", "hasTranslator"];
const srcIdHasPlaceOfPublication = ["srcId_hasPlaceOfPublication"];

const ExistedModal = ({
    equalValue,
    open,
    setOpen,
    cellId,
    newOptions,
    setCallback,
    menuName,
    createState,
    itemAt,
    headerFields,
    cloneLocalCreateState,
    setCloneLocalCreateStateFct
}) => {
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { mainSubject, sheet } = state.data;
    // const { headerFields } = sheet;
    const { dataset } = mainSubject.selected;
    const { key: sheetName } = sheet.selected;
    const [checkItem, setCheckItem] = useState(null);
    const [isLoading, setIsLoading] = useState(false);

    const modalConfirm = async () => {
        setIsLoading(true);

        const isPersonOrOrg =
            checkItem.id.substring(0, 3) === "PER" ||
            checkItem.id.substring(0, 3) === "ORG";
        const tmpZhVal = isPersonOrOrg
            ? checkItem.label.concat("@zh", "@", checkItem.id.substring(0, 3))
            : checkItem.label;

        const { newClass, newApi } = isCorrectSuffix(cellId, tmpZhVal);

        const itemKey = getKeyBySingle(getSingleByApi(newApi));

        const apiUrl = Api.getGeneric;
        const copyApiUrl = Api.getCopy;

        // 將資料複製到外譯房，並且api會將原本沒有lang tag的label移除，如果該資料並沒有zh lang tag label，
        // 會再自動放上zh lang tag label
        await axios.put(copyApiUrl, {
            entrySrc: {
                graph: checkItem.ds,
                classType: newClass || itemKey,
                value: checkItem.ds,
                srcId: checkItem.id
            },
            entryDst: {
                graph: checkItem.ds,
                classType: newClass || itemKey,
                value: "tltc", // 要移動到的資料集
                srcId: checkItem.id
            }
        });

        // label加上en
        const enObj = {
            graph: dataset,
            classType: newClass || itemKey,
            srcId: checkItem.id || "",
            value: {
                label: checkItem.label.concat("@en")
            }
        };

        await createNmtlData(user, apiUrl, dataset, sheetName, enObj).then(
            res => res === "OK"
        );

        // 新增otherName
        const otherNameObj = {
            graph: dataset,
            classType: newClass || itemKey,
            srcId: checkItem.id || "",
            value: {
                otherName: checkItem.label.concat("@zh")
            }
        };
        await createNmtlData(
            user,
            apiUrl,
            dataset,
            sheetName,
            otherNameObj
        ).then(res => res === "OK");

        const optVal = {
            id: checkItem.id,
            label: isPersonOrOrg
                ? checkItem.label.concat("@", checkItem.id.substring(0, 3))
                : checkItem.label,
            value: checkItem.id,
            graph: dataset
        };
        // 將更新的資料放到headerFields，才可以在更新翻譯書的時候拿到值
        if (Object.keys(headerFields).indexOf(newApi) > -1) {
            headerFields[newApi].push(optVal);
        }
        const isInCMDC = CMDC.includes(cellId);
        const isInCSDFA = CSDFA.includes(cellId);
        const isInsrcIdHasPlaceOfPublication = srcIdHasPlaceOfPublication.includes(
            cellId
        );

        // 針對不同欄位做相對應處理
        if (isInCMDC) {
            const tmpValue =
                createState && createState[cellId]
                    ? [...createState[cellId], checkItem.id]
                    : [checkItem.id];

            newOptions.push({
                id: checkItem?.id,
                label: isPersonOrOrg
                    ? checkItem?.label.concat("@", checkItem.id.substring(0, 3))
                    : checkItem?.label,
                value: checkItem?.id,
                graph: dataset
            });

            setCallback(cellId, tmpValue, menuName);
        } else if (isInCSDFA) {
            newOptions.push({
                id: checkItem?.id,
                label: isPersonOrOrg
                    ? checkItem?.label.concat("@", checkItem.id.substring(0, 3))
                    : checkItem?.label,
                value: checkItem?.id,
                graph: dataset
            });

            const list =
                cloneLocalCreateState && cloneLocalCreateState[cellId]
                    ? cloneLocalCreateState[cellId]
                    : [];
            list[itemAt] = checkItem?.id;

            const updateData = updateObjectValue(
                cloneLocalCreateState,
                cellId,
                list
            );

            setCloneLocalCreateStateFct(updateData);
            dispatch({
                type: Act.DATA_SET_CLONE_CREATE_STATE,
                payload: updateData
            });
        } else if (isInsrcIdHasPlaceOfPublication) {
            const tmpValue =
                createState && createState[cellId]
                    ? [checkItem.id]
                    : [checkItem.id];

            newOptions.push({
                id: checkItem?.id,
                label: isPersonOrOrg
                    ? checkItem?.label.concat("@", checkItem.id.substring(0, 3))
                    : checkItem?.label,
                value: checkItem?.id,
                graph: dataset
            });

            const list =
                createState && createState[cellId] ? createState[cellId] : [];
            list[itemAt] = checkItem?.id;

            setCallback(cellId, tmpValue, menuName);
        }

        setIsLoading(false);
        setOpen(false);
    };

    const modalCancel = () => {
        setCheckItem(null);
        setOpen(false);
    };

    const modalTableBody = () =>
        equalValue.map((el, idx) => (
            <Table.Row key={`ModalTable-${idx}`}>
                <Table.Cell>
                    <Radio
                        value={el.id}
                        checked={checkItem?.index === idx}
                        onChange={(event, data) => {
                            if (data.checked) {
                                setCheckItem({
                                    id: el.id,
                                    ds: el.Graph,
                                    label: el.label,
                                    index: idx
                                });
                            } else {
                                setCheckItem(undefined);
                            }
                        }}
                    />
                </Table.Cell>
                <Table.Cell>{el.id}</Table.Cell>
                <Table.Cell>{el.label}</Table.Cell>
                <Table.Cell>{el.Graph}</Table.Cell>
            </Table.Row>
        ));
    const modal = (
        <React.Fragment>
            <Modal.Header>名稱確認</Modal.Header>

            <Modal.Content image scrolling>
                <Modal.Description>
                    <span>
                        名稱已存在，請選擇ID匯入當前資料集，或是取消，重新修改名稱。
                    </span>
                    <Table celled striped>
                        <Table.Header>
                            <Table.Row>
                                {!isEmpty(equalValue) && (
                                    <React.Fragment>
                                        <Table.HeaderCell collapsing />
                                        <Table.HeaderCell>ID</Table.HeaderCell>
                                        <Table.HeaderCell>
                                            名稱
                                        </Table.HeaderCell>
                                        <Table.HeaderCell>
                                            資料集
                                        </Table.HeaderCell>
                                    </React.Fragment>
                                )}
                            </Table.Row>
                        </Table.Header>
                        <Table.Body>
                            {!isEmpty(equalValue) && modalTableBody()}
                        </Table.Body>
                    </Table>
                </Modal.Description>
            </Modal.Content>

            <Modal.Actions>
                <Button onClick={modalCancel} color="black">
                    取消
                </Button>
                <Button
                    disabled={isEmpty(checkItem)}
                    onClick={modalConfirm}
                    loading={isLoading}
                    positive
                >
                    確定
                </Button>
            </Modal.Actions>
        </React.Fragment>
    );

    return (
        <Modal
            onClose={() => setOpen(false)}
            onOpen={() => setOpen(true)}
            open={open}
        >
            {modal}
        </Modal>
    );
};
export default ExistedModal;

import FileAct from "./fileAction";

const INITIAL_STATE = {
    tmpImages: [], // 上傳 images 的暫存區
    // images: images uploaded or dropped (not save to db yet)
    // images: [{url:""},{url:""}]
    uploadImages: [],
    files: [],
    imageFormData: null,
    // folderPattern:跟 API 取得的資料夾結構
    folderPattern: null,
    // UPLOAD_CONFIG: 跟 API 取得的檔案上傳設定
    UPLOAD_CONFIG: {
        maxSize: "50000000",
        maxCount: 20
    },
    // ACCEPTABLE_EXTENSIONS: 跟 API 取得的可接受的延伸檔名
    ACCEPTABLE_EXTENSIONS: [],
    // currentFolder: 目前鎖定的資料夾
    currentFolder: {
        path: "",
        path_ch: "",
        folderName: "",
        folderName_ch: ""
    },
    // curFolderFilesUrl: 目前(drop area 或 current folder)顯示的圖片 [{url:""},{url:""}]
    curFolderFilesUrl: [],
    // curFolderFiles: 目前選定的資料夾的圖片, checked 代表 "已勾選"
    curFolderFiles: { original: [], checked: [] },
    // dropFolderFiles: 目前 drop area 的圖片, checked 代表 "已勾選"
    dropFolderFiles: { original: [], checked: [] },
    // fileServerUrl: file server base url
    fileServerUrl: "",
    loading: {
        state: false, // e.g. true/false
        message: "" // e.g. loading, uploading
    },
    defaultValue: "",
    selectFile: "",
    // common
    pickConfig: {
        uploadPage: {
            selectMode: "multiple", // ENUM:["single", "multiple"]
            withCheckbox: true, // use checkbox for user to check
            maxFiles: 0, // 0 means there is no limit for uploading files count
            openSwalAlert: true
        },
        datasetPage: {
            selectMode: "single", // ENUM:["single", "multiple"]
            withCheckbox: true, // use checkbox for user to check
            maxFiles: 1,
            openSwalAlert: false
        }
    },
    headerActiveName: "", // 紀錄當下畫面所在的header name
    systemDataActiveItem: "", // 紀錄"系統相關"頁面選擇的項目名稱
    toolPagesActiveName: "", // 紀錄"工具相關"頁面選擇的項目名稱
    downloadDataActiveName: "", // 紀錄"數據資料"頁面選擇的項目名稱
    // image
    imageEditor: {
        displayModal: ""
    },
    imageEditorData: {
        currentData: {}
    },
    imgPickerMessage: {
        curFolder: {
            type: "",
            title: "",
            text: ""
        },
        drop: {
            type: "",
            title: "",
            text: ""
        }
    },
    // 記錄第一層資料夾名稱
    firstLayerFileName: [],
    uploadImagesLatest: [],
    initFileSettings: false,
    selectedCropImg: "",
    openCropImgModal: false
};

const files = (state = INITIAL_STATE, action) => {
    switch (action.type) {
        case FileAct.FILES_FILE_SELECTED:
            return {
                ...state,
                selectedFile: JSON.parse(JSON.stringify(action.payload))
            };
        case FileAct.SET_FILES_FOLDER_SETTINGS:
            return { ...state, ...JSON.parse(JSON.stringify(action.payload)) };
        case FileAct.SELECT_FOLDER:
            return {
                ...state,
                currentFolder: action.payload
            };
        case FileAct.INIT_SELECT_FOLDER:
            return { ...state, currentFolder: INITIAL_STATE.currentFolder };
        case FileAct.FOLDER_FILES_URL:
            return {
                ...state,
                curFolderFilesUrl: JSON.parse(JSON.stringify(action.payload))
            };
        case FileAct.CLEAR_CUR_FOLDER_FILES_STATUS:
            return {
                ...state,
                curFolderFiles: JSON.parse(
                    JSON.stringify(INITIAL_STATE.curFolderFiles)
                )
            };

        case FileAct.CUR_FOLDER_FILES_STATUS:
            return {
                ...state,
                curFolderFiles: JSON.parse(JSON.stringify(action.payload))
            };

        case FileAct.SET_DEFAULT_VALUE:
            return {
                ...state,
                defaultValue: JSON.parse(JSON.stringify(action.payload))
            };
        case FileAct.SELECT_FILE:
            return {
                ...state,
                selectFile: JSON.parse(JSON.stringify(action.payload))
            };
        case FileAct.UPLOAD_IMAGE:
            return {
                ...state,
                uploadImages: JSON.parse(JSON.stringify(action.payload))
            };
        case FileAct.SET_FORM_DATA:
            return { ...state, imageFormData: action.payload };
        case FileAct.UPLOAD_TMP_IMAGES:
            return {
                ...state,
                tmpImages: JSON.parse(JSON.stringify(action.payload))
            };
        case FileAct.CLEAR_DROP_FOLDER_FILES_STATUS:
            return {
                ...state,
                dropFolderFiles: INITIAL_STATE.dropFolderFiles
            };
        case FileAct.DROP_FOLDER_FILES_STATUS:
            return {
                ...state,
                dropFolderFiles: JSON.parse(JSON.stringify(action.payload))
            };
        case FileAct.UPLOAD_LOADING:
            return {
                ...state,
                loading: action.payload
            };
        case FileAct.IMAGE_EDITOR_DISPLAY:
            return {
                ...state,
                imageEditor: {
                    ...state.imageEditor,
                    displayModal: action.payload
                }
            };
        // content: image editor display
        case FileAct.IMAGE_EDITOR_CURRENT_DATA:
            return {
                ...state,
                imageEditorData: {
                    ...state.imageEditorData,
                    currentData: action.payload
                }
            };
        case FileAct.IMG_PICKER_INITIAL_STATE:
            return {
                ...state,
                tmpImages: [],
                images: [],
                dropFolderFiles: INITIAL_STATE.dropFolderFiles
            };
        case FileAct.SET_FILE_SERVER_URL:
            return {
                ...state,
                fileServerUrl: action.payload
            };
        case FileAct.UPLOAD_CLEAR_CACHE:
            return INITIAL_STATE;
        case FileAct.PICKER_CUR_FOLDER_MESSAGE:
            return {
                ...state,
                imgPickerMessage: {
                    ...state.imgPickerMessage,
                    curFolder: action.payload
                }
            };
        case FileAct.CLEAN_PICKER_CUR_FOLDER_MESSAGE:
            return {
                ...state,
                imgPickerMessage: {
                    ...state.imgPickerMessage,
                    curFolder: INITIAL_STATE.imgPickerMessage.curFolder
                }
            };
        case FileAct.PICKER_DROP_MESSAGE:
            return {
                ...state,
                imgPickerMessage: {
                    ...state.imgPickerMessage,
                    drop: action.payload
                }
            };
        case FileAct.CLEAN_PICKER_DROP_MESSAGE:
            return {
                ...state,
                imgPickerMessage: {
                    ...state.imgPickerMessage,
                    drop: INITIAL_STATE.imgPickerMessage.drop
                }
            };
        case FileAct.SET_FIRSTLAYERFILENAME:
            return {
                ...state,
                firstLayerFileName: action.payload
            };
        case FileAct.INIT_FOLDERPATTERN:
            return { ...state, folderPattern: INITIAL_STATE.folderPattern };
        case FileAct.UPLOAD_IMAGES_LATEST:
            return {
                ...state,
                uploadImagesLatest: action.payload
            };
        case FileAct.INIT_FILE_SETTINGS:
            return {
                ...state,
                initFileSettings: action.payload
            };
        case FileAct.SELECTED_CROP_IMG:
            return {
                ...state,
                selectedCropImg: action.payload
            };
        case FileAct.OPEN_CROP_IMG_MODAL:
            return { ...state, openCropImgModal: action.payload };
        default:
            return state;
    }
};

export default files;

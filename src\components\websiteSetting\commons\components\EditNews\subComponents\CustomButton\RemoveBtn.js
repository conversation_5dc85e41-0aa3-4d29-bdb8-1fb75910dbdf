import React, { useEffect, useState } from "react";

// plugins
import { Button, Icon } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";

//
import clsName from "../../Utils/clsName";
import { setUpdateNewsInfo } from "../../Utils/utils";
import initColumnDef from "../../Utils/initColumnDef";
import { isEmpty } from "../../../../../../../commons";
import openModalControl from "../../Utils/openModalControl";

function RemoveBtn({ className, index }) {
    const newsDispatch = useDispatch();
    const { updateNewsInfo, modalSelect, modalCaller } = useSelector(
        state => state
    );

    const [cusStyle, setCusStyle] = useState({});
    // localIndex用來紀錄點選Item的index，避免Modal close後，移除錯誤item
    const [localIndex, setLocalIndex] = useState(-1);

    useEffect(() => {
        switch (className) {
            case clsName.carouselRM:
                setCusStyle({
                    background: "inherit",
                    fontSize: "large"
                });
                break;
            case clsName.AttachmentRM:
                setCusStyle({
                    background: "inherit",
                    padding: "0 0 0 1rem"
                });
                break;
            default:
                break;
        }
    }, []);

    const removeItem = () => {
        const tmpNewsEvents = JSON.parse(JSON.stringify(updateNewsInfo));
        switch (className) {
            case clsName.carouselRM:
                if (!isEmpty(tmpNewsEvents[initColumnDef.hasURL])) {
                    tmpNewsEvents[initColumnDef.hasURL] = tmpNewsEvents[
                        initColumnDef.hasURL
                    ].filter((el, tmpIndex) => tmpIndex !== index);
                }
                break;
            case clsName.AttachmentRM:
                if (!isEmpty(tmpNewsEvents[initColumnDef.fileAvailableAt])) {
                    tmpNewsEvents[
                        initColumnDef.fileAvailableAt
                    ] = tmpNewsEvents[initColumnDef.fileAvailableAt].filter(
                        (el, tmpIndex) => tmpIndex !== index
                    );
                }
                break;
            default:
                break;
        }

        // 寫回到newsEvents
        setUpdateNewsInfo(newsDispatch, tmpNewsEvents);
    };

    useEffect(() => {
        if (modalSelect === null) return;
        if (modalSelect) {
            if (modalCaller === className && index === localIndex) {
                removeItem();

                // 每次移除Item都要重設localIndex，避免移除動作第2次會移除錯誤問題
                setLocalIndex(-1);
            }
        }
    }, [modalSelect, modalCaller]);

    return (
        <Button
            icon
            circular
            style={cusStyle}
            onClick={() => {
                setLocalIndex(index);
                openModalControl(newsDispatch, className);
            }}
        >
            <Icon name="cancel" style={{ color: "red" }} />
        </Button>
    );
}

export default RemoveBtn;

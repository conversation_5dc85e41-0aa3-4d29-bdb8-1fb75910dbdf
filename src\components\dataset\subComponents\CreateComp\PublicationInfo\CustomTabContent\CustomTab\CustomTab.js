import React, { useContext, useEffect, use<PERSON>emo, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "semantic-ui-react";

import "./CustonTab.scss";
import { useHistory } from "react-router-dom";
import axios from "axios";
import { isEmpty } from "../../../../../../../commons";
import Act from "../../../../../../../store/actions";
import { deleteNmtlData, readNmtlData } from "../../../../../../../api/nmtl";
import Api from "../../../../../../../api/nmtl/Api";
import { StoreContext } from "../../../../../../../store/StoreProvider";
import {
    contentTypeToClassType,
    GLOBAL_DEFAULT,
    PEICES_INFO
} from "../../../../../../common/sheetCrud/sheetCrudHelper";
import { createHistoryEvent } from "../../../../../../downloadData/components/history/common/common";
import WarningContent from "../../../../eventButton/buttons/DeleteButton/WarningContent";
import warningAction from "../../../../eventButton/buttons/DeleteButton/utils/deletePubData";

const CustomTab = ({
    panes,
    setPanes,
    curMenu,
    setCurMenu,
    createState,
    setCreateState,
    setCallback
}) => {
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { displayName } = user;
    const { mainSubject, sheet } = state.data;
    const { dataset } = mainSubject.selected;
    const { headerActiveName } = state.common;
    const { tabIndex } = sheet.tabKey;
    const { key: sheetName, contentWritePath, hasTab } = sheet.selected;

    const history = useHistory();

    const [open, setOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [warnData, setWarnData] = useState({});
    const [allLang, setAllLang] = useState([]);

    const columns = [
        headerActiveName,
        mainSubject.selected.value,
        sheet.selected.value
    ];

    useEffect(() => {
        (async () => {
            try {
                const allLangRes = await axios.get(Api.getLanguage);
                setAllLang(allLangRes.data.data);
            } catch (e) {
                console.log(e);
            }
        })();
    }, []);

    const handleAddTab = () => {
        setPanes(prevState => [...prevState, `default${panes.length}`]);
    };

    const handleDelete = async deleteBook => {
        if (isEmpty(dataset) || isEmpty(sheetName)) {
            return { deleteResult: false, historyMsg: "delete failed" };
        }

        const apiWritePath =
            contentWritePath || hasTab[tabIndex]?.contentWritePath || null;

        // 新增警告會更新其他表單資料流程
        if (!isEmpty(warnData)) {
            const res = await warningAction(
                warnData,
                dataset,
                apiWritePath,
                sheetName,
                PEICES_INFO,
                user
            );

            if (res.status !== "OK") {
                return { deleteResult: false, historyMsg: "delete failed" };
            }
        }

        // 如果沒有srcId，表示該資料還沒有被新增到資料庫中，直接刪除即可
        if (
            Object.hasOwn(createState, deleteBook) &&
            !Object.hasOwn(createState[deleteBook], "srcId")
        ) {
            const historyMsg = `delete ${deleteBook}`;

            return { deleteResult: true, historyMsg };
        }

        const entry = {
            graph: dataset,
            srcId: createState[deleteBook].srcId,
            classType: contentTypeToClassType[apiWritePath],
            value: {}
        };

        const apiUrl = Api.getGeneric;

        if (isEmpty(entry))
            return { deleteResult: false, historyMsg: "delete failed" };

        const deleteResult = deleteNmtlData(
            user,
            apiUrl,
            dataset,
            sheetName,
            entry
        );

        const historyMsg = JSON.stringify(entry);

        return { deleteResult, historyMsg };
    };

    const handleDeleteModal = deleteBook => {
        // 只有一本原文書對應一本翻譯書時，連同原文書一起刪除
        const onlyOneBook = Object.keys(createState).every(key =>
            [deleteBook, GLOBAL_DEFAULT].includes(key)
        );

        if (onlyOneBook) {
            const oriId = createState.default?.srcId || "";
            if (oriId) {
                const apiStr = Api.getTransBookByOri(dataset, oriId);

                readNmtlData(apiStr).then(res => {
                    setWarnData({ transPubArr: res });
                });
            }
        }

        setOpen(true);
    };

    const modalConfirm = async deleteBook => {
        setIsLoading(true);
        let successCount = 0;
        let errorCount = 0;
        const deleteInfo = [];

        if (Object.hasOwn(createState, deleteBook)) {
            const { deleteResult, historyMsg } = await handleDelete(deleteBook);

            if (deleteResult && historyMsg) {
                deleteInfo.push(deleteResult);
                successCount += 1;
            } else {
                errorCount += 1;
            }

            if (errorCount === 0) {
                dispatch({
                    type: Act.DATA_INFO_MESSAGE,
                    payload: {
                        title: `已刪除 ${deleteBook}`,
                        success: successCount,
                        renderSignal: `delete-${new Date().getTime()}`
                    }
                });

                const tmpState = JSON.parse(JSON.stringify(createState));

                delete tmpState[deleteBook];
                setCreateState(tmpState);

                setPanes(prevState =>
                    prevState.filter(name => name !== deleteBook)
                );

                createHistoryEvent(
                    displayName,
                    "刪除",
                    `${columns.join("/")}：${deleteInfo}`
                );
            } else {
                dispatch({
                    type: Act.DATA_INFO_MESSAGE,
                    payload: {
                        title: `刪除 ${deleteBook}失敗`,
                        error: 1,
                        renderSignal: `update-${new Date().getTime()}`
                    }
                });

                createHistoryEvent(
                    displayName,
                    "刪除失敗",
                    `${columns.join("/")}：${deleteInfo}`
                );
            }
        }

        setIsLoading(false);
        setOpen(false);

        const onlyOneBook = Object.keys(createState).every(key =>
            [deleteBook, "default"].includes(key)
        );
        if (onlyOneBook) {
            setWarnData({});
            history.push("/Dataset");
        }
    };
    const modalCancel = () => {
        setOpen(false);
    };

    return useMemo(
        () => (
            <Menu secondary className="tab__wrapped">
                <Menu.Item
                    key="tab__plus"
                    className="tab__wrapped__firstMenu"
                    onClick={handleAddTab}
                >
                    <Icon
                        className="tab__wrapped__firstMenu__iconPlus"
                        name="plus"
                    />
                </Menu.Item>
                {panes.map((menuItem, idx) => {
                    const result =
                        menuItem.lastIndexOf("_") > -1
                            ? menuItem.slice(0, menuItem.lastIndexOf("_"))
                            : menuItem;

                    return (
                        <Menu.Item
                            key={`tab__${menuItem}__${idx}`}
                            className={
                                curMenu === menuItem
                                    ? "tab__wrapped__menuItemFocus"
                                    : "tab__wrapped__menuItem"
                            }
                            name={menuItem}
                            active={curMenu === menuItem}
                            onClick={(e, { name }) => {
                                setCurMenu(name);
                                if (isEmpty(createState[name]?.srcId)) {
                                    setCallback("srcId", name, name);
                                }
                            }}
                        >
                            <div
                                style={{
                                    display: "flex",
                                    flexDirection: "column"
                                }}
                            >
                                <p
                                    style={{
                                        margin: "0",
                                        fontSize: "14px",
                                        fontWeight: "500",
                                        lineHeight: "20.27px"
                                    }}
                                >
                                    {result}
                                </p>
                                {allLang.length > 0 &&
                                    allLang.map((allLangItem, index) => {
                                        if (
                                            createState[menuItem] &&
                                            createState[menuItem]
                                                .hasLanguageOfWorkOrName &&
                                            allLangItem.id ===
                                                createState[menuItem]
                                                    .hasLanguageOfWorkOrName[0]
                                        ) {
                                            return (
                                                <p
                                                    key={index}
                                                    style={{
                                                        margin: "0",
                                                        fontSize: "12px",
                                                        fontWeight: "400",
                                                        lineHeight: "17.38px"
                                                    }}
                                                >
                                                    {allLangItem.label}
                                                </p>
                                            );
                                        }
                                    })}
                            </div>
                            {/* <Modal
                                open={open}
                                trigger={
                                    <Icon
                                        className={
                                            curMenu === menuItem
                                                ? "tab__wrapped__menuItemFocus__deleteIcon"
                                                : "tab__wrapped__menuItem__deleteIcon"
                                        }
                                        name="delete"
                                        onClick={() =>
                                            handleDeleteModal(menuItem)
                                        }
                                    />
                                }
                            >
                                <Modal.Header>刪除翻譯書</Modal.Header>
                                <Header as="h5">{`是否確定要刪除翻譯書「${menuItem}」?`}</Header>

                                警告訊息
                                {Object.values(warnData).some(
                                    ({ data }) => !isEmpty(data)
                                ) && <WarningContent warnData={warnData} />}

                                <Modal.Actions>
                                    <Button
                                        onClick={() => modalConfirm(menuItem)}
                                        loading={isLoading}
                                        positive
                                    >
                                        確定
                                    </Button>
                                    <Button onClick={modalCancel} color="black">
                                        取消
                                    </Button>
                                </Modal.Actions>
                            </Modal> */}
                        </Menu.Item>
                    );
                })}
            </Menu>
        ),
        [panes, curMenu, open, warnData, allLang]
    );
};

export default CustomTab;

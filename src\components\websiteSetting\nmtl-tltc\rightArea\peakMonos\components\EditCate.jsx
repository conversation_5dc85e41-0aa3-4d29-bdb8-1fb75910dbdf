import React, { useContext, useEffect, useState } from "react";
import "./EditCate.scss";
import { Button, Segment } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import PeakAct from "../PeakMonosAction";
import { areObjectsEqual } from "../utils/utils";
import Api from "../../../../../../api/nmtl/Api";
import { isEmpty } from "../../../../../../commons";
import CustomButton from "./subComponents/CustomButton";
import FormArea from "./subComponents/chapterComponents/area/FormArea";
import QuillArea from "./subComponents/chapterComponents/area/QuillArea";
import { switchPropertyName } from "./subComponents/chapterComponents/config";
import PeakArea from "./subComponents/chapterComponents/area/PeakArea";
import useFetchData from "./subComponents/chapterComponents/useFetchData";
import CustomRequiredModal from "./subComponents/chapterComponents/components/CustomRequiredModal";
import useGetSubjectOPs from "../../../../../common/hooks/useGetSubjectOPs";
import { StoreContext } from "../../../../../../store/StoreProvider";
import { createHistoryEvent } from "../../../../../downloadData/components/history/common/common";

const EditCate = ({ websiteSubject }) => {
    const {
        editingPeakId,
        editingPchId,
        chapterListLength,
        isEditedChapterList,
        editingDropdownIsSaving,
        introductionZhForQuill,
        introductionEnForQuill,
        judgesCommentaryZhForQuill,
        judgesCommentaryEnForQuill,
        tlaPersonIntroZhForQuill,
        tlaPersonIntroEnForQuill
    } = useSelector(state => state);
    const [globalState] = useContext(StoreContext);
    const [stateContext] = useContext(StoreContext);
    const { user } = stateContext;
    const { displayName } = user;
    const { menuActiveItem } = globalState.websiteSetting;
    const { headerActiveName } = stateContext.common;
    const { groupInfo } = stateContext.data;
    const dropOptions = useGetSubjectOPs(groupInfo);

    const [peakChapterData, setPeakChapterData] = useState(null);
    const [peakChapterUpdateData, setPeakChapterUpdateData] = useState(null);
    const [requiredModalOpen, setRequiredModalOpen] = useState(false);

    const sheetSelectedValue = dropOptions.find(it => it.key === websiteSubject)
        ?.text;
    const columns = [headerActiveName, sheetSelectedValue, menuActiveItem.name];

    const dispatch = useDispatch();

    const cleanQuillArea = () => {
        dispatch({ type: PeakAct.SET_INTRODUCTIONZHFORQUILL, payload: "" });
        dispatch({ type: PeakAct.SET_INTRODUCTIONENFORQUILL, payload: "" });
        dispatch({ type: PeakAct.SET_JUDGESCOMMENTARYZHFORQUILL, payload: "" });
        dispatch({ type: PeakAct.SET_JUDGESCOMMENTARYENFORQUILL, payload: "" });
        dispatch({ type: PeakAct.SET_TLAPERSONINTROZHFORQUILL, payload: "" });
        dispatch({ type: PeakAct.SET_TLAPERSONINTROENFORQUILL, payload: "" });
    };

    const convertObject = (list, obj) => {
        const newObj = { ...obj };
        list.forEach(item => {
            const key = Object.keys(item)[0];
            const tmpValue = item[key];
            if (newObj[tmpValue] !== undefined) {
                newObj[key] = newObj[tmpValue].split(",");
                delete newObj[tmpValue];
            }
        });
        return newObj;
    };

    const handleCancel = () => {
        cleanQuillArea();
        dispatch({
            type: PeakAct.SET_ISEDITEDCATE,
            payload: false
        });
        dispatch({
            type: PeakAct.SET_EDITDROPDOWNISSAVING,
            payload: false
        });
    };

    const handleUpdate = async updatedData => {
        function combineZhEnKeys(data) {
            const result = {};
            // eslint-disable-next-line no-restricted-syntax
            for (const key in data) {
                if (key.endsWith("Zh") || key.endsWith("En")) {
                    const baseKey = key.slice(0, -2);
                    if (!result[baseKey]) {
                        result[baseKey] = [];
                    }
                    if (key.endsWith("Zh")) {
                        result[baseKey].unshift(
                            data[key] ? `${data[key]}@zh` : ""
                        );
                    } else {
                        result[baseKey].push(
                            data[key] ? `${data[key]}@en` : ""
                        );
                    }
                } else {
                    result[key] = data[key];
                }
            }
            return result;
        }
        const tmpUpdatedData = updatedData;
        if (isEmpty(tmpUpdatedData?.order)) {
            tmpUpdatedData.order = chapterListLength.toString();
        }

        const combinedSrcData = combineZhEnKeys(peakChapterData);
        const combinedDstData = combineZhEnKeys(tmpUpdatedData);

        const entrySrc = {
            graph: websiteSubject,
            classType: "Publication",
            value: combinedSrcData,
            srcId: editingPchId
        };

        const entryDst = {
            graph: websiteSubject,
            classType: "Publication",
            value: combinedDstData,
            srcId: editingPchId
        };

        const updateHistoryMsg = `${JSON.stringify(
            entrySrc
        )}\n變動後：\n${JSON.stringify(entryDst)}`;

        const createHistoryMsg = `${JSON.stringify(entryDst)}`;

        // 建立歷史紀錄
        createHistoryEvent(
            displayName,
            isEmpty(combinedSrcData) ? "創建" : "更新",
            `${columns.join("/")}：${
                isEmpty(combinedSrcData) ? createHistoryMsg : updateHistoryMsg
            }`
        );

        await axios.put(Api.getGeneric, { entrySrc, entryDst });
    };

    const handleConnectPeakChapter = async () => {
        const entry = {
            classType: "PeakMono",
            graph: "tltc",
            srcId: editingPeakId,
            value: {
                hasPeakChapter: editingPchId
            }
        };
        await axios.post(Api.getGeneric, { entry });
    };

    const handleSave = async () => {
        // 檢查必填欄位是否已填寫完成
        if (
            !peakChapterUpdateData?.labelZh ||
            !peakChapterUpdateData?.labelEn
        ) {
            setRequiredModalOpen(true);
            return;
        }

        const quillDataSwitch = {
            introductionZh: introductionZhForQuill,
            introductionEn: introductionEnForQuill,
            judgesCommentaryZh: judgesCommentaryZhForQuill,
            judgesCommentaryEn: judgesCommentaryEnForQuill,
            tlaPersonIntroZh: tlaPersonIntroZhForQuill,
            tlaPersonIntroEn: tlaPersonIntroEnForQuill
        };

        const updatedData = Object.assign(
            {},
            peakChapterUpdateData,
            quillDataSwitch
        );

        if (isEmpty(peakChapterData) && !isEmpty(updatedData)) {
            await handleConnectPeakChapter();
            await handleUpdate(updatedData);
        } else if (!areObjectsEqual(peakChapterData, updatedData)) {
            await handleUpdate(updatedData);
        }

        dispatch({
            type: PeakAct.SET_EDITDROPDOWNISSAVING,
            payload: false
        });
        dispatch({
            type: PeakAct.SET_ISEDITEDCHAPTERLIST,
            payload: !isEditedChapterList
        });
        dispatch({
            type: PeakAct.SET_ISEDITEDCATE,
            payload: false
        });
        dispatch({
            type: PeakAct.SET_ISMODALOPEN,
            payload: true
        });
    };

    const peakChapterUpdateDataHandler = (d, key, val) => {
        const updateObjectValue = (obj, tmpKey, tmpValue) => {
            if (!key) return [];
            const tmpObj = JSON.parse(JSON.stringify(obj));
            tmpObj[tmpKey] = tmpValue;
            return tmpObj;
        };
        const updatedData = updateObjectValue(d, key, val);
        setPeakChapterUpdateData(isEmpty(updatedData) ? d : updatedData);
    };

    useEffect(() => {
        setPeakChapterUpdateData(peakChapterData);
    }, [peakChapterData]);

    useEffect(() => {
        const getData = async () => {
            const api = Api.getPeakMonoChapterNewContent.replace(
                "{pchId}",
                editingPchId
            );
            await axios.get(api).then(res => {
                const result = convertObject(
                    switchPropertyName,
                    res?.data?.data[0]
                );
                setPeakChapterData(result);
            });
        };
        getData();
    }, [editingPchId]);

    const optionLists = {
        writerList: useFetchData(Api.getWriterList, [
            peakChapterData,
            editingDropdownIsSaving
        ]),
        tlaCategoryList: useFetchData(Api.getTLACategoryList, [
            peakChapterData,
            editingDropdownIsSaving
        ]),
        tlaAwardsList: useFetchData(Api.getTLAAwardsList, [
            peakChapterData,
            editingDropdownIsSaving
        ]),
        publisherList: useFetchData(Api.getPublisherList, [
            peakChapterData,
            editingDropdownIsSaving
        ])
    };

    return (
        <>
            <div
                style={{
                    display: "flex",
                    width: "100%",
                    alignItems: "center",
                    // overflow: "scroll",
                    padding: "20px",
                    height: "100%",
                    flexDirection: "column"
                }}
            >
                <Segment className="EditCate">
                    <div
                        style={{
                            display: "flex",
                            justifyContent: "space-between"
                        }}
                    >
                        <div className="topArea">
                            <h1>新增書目</h1>
                        </div>
                        <div className="topArea">
                            <Button
                                color="grey"
                                content="取消"
                                onClick={handleCancel}
                            />
                            <CustomButton
                                onClick={handleSave}
                                message="儲存成功"
                                content="儲存"
                                color="green"
                            />
                        </div>
                    </div>
                    {peakChapterData && (
                        <>
                            <FormArea
                                updateFct={peakChapterUpdateDataHandler}
                                data={peakChapterData}
                                updatedData={peakChapterUpdateData}
                                optionLists={optionLists}
                                editingPchId={editingPchId}
                            />
                            <QuillArea
                                updateFct={peakChapterUpdateDataHandler}
                                data={peakChapterData}
                                updatedData={peakChapterUpdateData}
                            />
                            <PeakArea
                                updateFct={peakChapterUpdateDataHandler}
                                data={peakChapterData}
                                updatedData={peakChapterUpdateData}
                                optionLists={optionLists}
                            />
                        </>
                    )}
                </Segment>
                <div
                    style={{
                        display: "flex",
                        width: "100%",
                        justifyContent: "flex-end"
                    }}
                />
                <CustomRequiredModal
                    onClick={setRequiredModalOpen}
                    open={requiredModalOpen}
                />
            </div>
        </>
    );
};

export default EditCate;

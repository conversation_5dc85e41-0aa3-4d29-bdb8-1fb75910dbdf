import React, { useEffect, useState } from "react";

// plugins
import { <PERSON><PERSON>, <PERSON><PERSON>, Segment } from "semantic-ui-react";
import { useSelector } from "react-redux";

// config
import NewsInfoData from "./NewsInfoData";

// components
import CustomButton from "../CustomButton/CustomButton";

// utils
import { isEmpty } from "../../../../../../../commons";
import clsName from "../../Utils/clsName";

function NewsInfoArea({ className }) {
    const { isExternal, updateNewsInfo } = useSelector(state => state);
    const [lv2Form, setLv2Form] = useState([]);

    useEffect(() => {
        if (isEmpty(updateNewsInfo)) return;
        if (isExternal) {
            setLv2Form(NewsInfoData.filter(el => !el.title)[0].isLink);
        } else {
            setLv2Form(NewsInfoData.filter(el => !el.title)[0].notLink);
        }
    }, [updateNewsInfo, isExternal]);

    const createRow = el => {
        const { compProps, title, gridCol2Width } = el;
        const { label, fusekiCol } = compProps || "";
        return (
            <Grid.Row key={title}>
                <Grid.Column width={3} verticalAlign="middle">
                    <Header size="medium">{title}</Header>
                </Grid.Column>
                <Grid.Column width={gridCol2Width || 6}>
                    {label ? (
                        <el.components
                            className={label}
                            fusekiCol={fusekiCol}
                        />
                    ) : (
                        <el.components />
                    )}
                </Grid.Column>
            </Grid.Row>
        );
    };

    return (
        <Segment className={className}>
            <Grid celled container>
                {NewsInfoData.filter(el => el.title).map(el => createRow(el))}

                {/*  顯示LV2  */}
                {!isEmpty(lv2Form) && lv2Form.map(el => createRow(el))}

                <Grid.Row>
                    <Grid.Column
                        style={{ display: "flex", justifyContent: "flex-end" }}
                    >
                        <CustomButton className={clsName.SendBtn} />
                    </Grid.Column>
                </Grid.Row>
            </Grid>
        </Segment>
    );
}

export default NewsInfoArea;

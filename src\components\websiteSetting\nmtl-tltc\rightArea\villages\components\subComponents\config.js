import CustomInput from "./components/CustomInput";
import CustomFile from "./components/CustomFile";
import CustomTextarea from "./components/CustomTextarea";

const switchPropertyName = [
    { hasTLAAwardName: "TLAAwardNames" },
    { hasTLACategory: "TLACategorys" },
    { hasAuthor: "authors" },
    { hasPublisher: "publishers" },
    { hasDescribedTarget: "describedTargets" }
];

/**
 * - component: The component to render.
 * - type: Unique identifier for the input.
 * - rowName: The label for the input field.
 * - optionListKey: The data to be displayed in the dropdown.
 * - valueKeys: The key to extract the value from the data object.
 * - titles: The titles for the double input fields.
 * - subRowName: The sub label for the input fields.
 * - required: Whether the input is required.
 */
const formConfig = [
    {
        section: "section1",
        component: CustomFile,
        props: {
            type: "hasURL",
            rowName: "封面圖片",
            valueKey: "hasURL"
        }
    },
    {
        section: "section2",
        component: CustomInput,
        props: {
            type: "labelZh",
            rowName: "中文標題",
            valueKey: "labelZh"
        }
    },
    {
        section: "section2",
        component: CustomInput,
        props: {
            type: "labelEn",
            rowName: "英文標題",
            valueKey: "labelEn"
        }
    },
    {
        section: "section3",
        component: CustomTextarea,
        props: {
            type: "villageContentZh",
            rowName: "中文說明",
            valueKey: "villageContentZh"
        }
    },
    {
        section: "section3",
        component: CustomTextarea,
        props: {
            type: "villageContentEn",
            rowName: "英文說明",
            valueKey: "villageContentEn"
        }
    }
];

// 活動列表
const modalFormConfig = [
    {
        section: "section1",
        component: CustomInput,
        props: {
            type: "activityDateZh",
            rowName: "中文活動時間",
            valueKey: "activityDateZh"
        }
    },
    {
        section: "section1",
        component: CustomInput,
        props: {
            type: "activityDateEn",
            rowName: "英文活動時間",
            valueKey: "activityDateEn"
        }
    },
    {
        section: "section2",
        component: CustomInput,
        props: {
            type: "activityTitleZh",
            rowName: "中文活動名稱",
            valueKey: "activityTitleZh"
        }
    },
    {
        section: "section2",
        component: CustomInput,
        props: {
            type: "activityTitleEn",
            rowName: "英文活動名稱",
            valueKey: "activityTitleEn"
        }
    },
    {
        section: "section3",
        component: CustomInput,
        props: {
            type: "activityPlaceZh",
            rowName: "中文地點",
            valueKey: "activityPlaceZh"
        }
    },
    {
        section: "section3",
        component: CustomInput,
        props: {
            type: "activityPlaceEn",
            rowName: "英文地點",
            valueKey: "activityPlaceEn"
        }
    }
];

export { switchPropertyName, formConfig, modalFormConfig };

// auth
import authority from './App-authority';

// components
import Home from './components/home';
import SignIn from './components/account/SignIn';
import SignOut from './components/account/SignOut';
import AccountManagement from './components/accountManagement';
import DatasetManagement from './components/dataset/DatasetManagement';
import AuthorityManagement from './components/AuthorityManagement';
import SystemData from './components/systemData';
import ToolPages from './components/toolPages';
import DownloadData from './components/downloadData';
import WebsiteSetting from './components/websiteSetting';
import ReportIssue from './components/reportIssue';
import AtaiDataManagement from './components/ataiDataManagement';

// config
import systemDataMenu from './components/systemData/menuConfig';
import toolPagesMenu from './components/toolPages/config';
import downloadDataMenu from './components/downloadData/menuConfig';
import CreateContentView from './components/dataset/subComponents/CreateComp/CreateContentView';

/**
 * example
 id: "route-01",
 label: "Index",
 path: "/",
 public: true,
 authority: authority.Index,
 component: Index,
 childMenu: 子選單
 accPageSelect: 決定權限管理頁可不可以控制
 */

// HTML Body: URL Redirect
const routes = [
  {
    id: 'route-01',
    label: 'Home',
    path: '/',
    public: true,
    authority: authority.Home,
    component: Home,
    childMenu: [],
    accPageSelect: false,
  },
  {
    id: 'route-02',
    label: 'SignIn',
    path: '/SignIn',
    public: true,
    authority: authority.SignIn,
    component: SignIn,
    childMenu: [],
    accPageSelect: false,
  },
  {
    id: 'route-03',
    label: 'SignOut',
    path: '/SignOut',
    public: true,
    authority: authority.SignOut,
    component: SignOut,
    childMenu: [],
    accPageSelect: false,
  },
  {
    id: 'route-04',
    label: 'Account',
    path: '/Account',
    public: false,
    authority: authority.Account,
    component: AccountManagement,
    childMenu: [],
    accPageSelect: false,
  },
  {
    id: 'route-06',
    label: 'Dataset',
    path: '/Dataset',
    public: false,
    authority: authority.Dataset,
    component: DatasetManagement,
    childMenu: [],
    accPageSelect: true,
  },
  {
    id: 'route-11',
    label: 'Dataset',
    path: `/Dataset/Information/:id`,
    public: false,
    authority: authority.Dataset,
    component: CreateContentView,
    childMenu: [],
    accPageSelect: false,
  },
  // {
  //     id: "route-12",
  //     label: "Dataset",
  //     path: `/Dataset/create`,
  //     public: false,
  //     authority: authority.Dataset,
  //     component: CreateContentView,
  //     childMenu: [],
  //     accPageSelect: false
  // },
  {
    id: 'route-13',
    label: 'Authority',
    path: '/Authority',
    public: false,
    authority: authority.Authority,
    component: AuthorityManagement,
    childMenu: [],
    accPageSelect: true,
  },
  {
    id: 'route-05',
    label: 'Database',
    path: '/SystemData',
    public: false,
    authority: authority.SystemData,
    component: SystemData,
    childMenu: systemDataMenu,
    accPageSelect: true,
  },
  {
    id: 'route-07',
    label: 'ToolPages',
    path: '/ToolPages',
    public: false,
    authority: authority.ToolPages,
    component: ToolPages,
    childMenu: toolPagesMenu,
    accPageSelect: true,
  },
  {
    id: 'route-08',
    label: 'WebsiteSetting',
    path: '/WebsiteSetting',
    public: false,
    authority: authority.WebsiteSetting,
    component: WebsiteSetting,
    childMenu: [],
    accPageSelect: true,
  },
  {
    id: 'route-09',
    label: 'DownloadData',
    path: '/DownloadData',
    public: false,
    authority: authority.DownloadData,
    component: DownloadData,
    childMenu: downloadDataMenu,
    accPageSelect: true,
  },
  {
    id: 'route-10',
    label: 'ReportIssue',
    path: '/ReportIssue',
    public: false,
    authority: authority.ReportIssue,
    component: ReportIssue,
    childMenu: [],
    accPageSelect: true,
  },
  {
    id: 'route-12',
    label: 'AtaiDataManagement',
    path: '/AtaiDataManagement',
    public: false,
    authority: authority.AtaiDataManagement,
    component: AtaiDataManagement,
    childMenu: [],
    accPageSelect: true,
  },
];

export default routes;

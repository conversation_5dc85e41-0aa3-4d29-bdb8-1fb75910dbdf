import React from "react";
import { Header, Table } from "semantic-ui-react";
import CustomHeaderInformation from "../headerInformation";

import "./CustomTableView.scss";
import { PublicationInfo } from "../../../common/sheetCrud/sheetCrudHelper";

const CustomTableView = ({
    itemAt,
    sheetName,
    header,
    content,
    createState,
    setCallback,
    setCreateState,
    menuName,
    isDisableSecondInput,
    isInDraggingMode
}) => {
    const tableCellValue = (row, rowIdx) =>
        row.map((rowCell, cellIdx) => {
            const {
                comp: Component,
                header: rowHeader,
                defaultValue,
                subHeader,
                colSpan,
                rowSpan,
                compProp
            } = rowCell;

            const headerInfo = header.find(el => el.id === rowHeader);

            let hdLabel = headerInfo?.label || rowHeader;

            if (sheetName === PublicationInfo && rowHeader === "srcId") {
                hdLabel = "原文書ID";
            }

            if (rowHeader === "hasTranslationBook") {
                hdLabel = "翻譯書ID";
            }

            const cellValue =
                createState &&
                menuName !== undefined &&
                Object.hasOwn(createState, rowHeader)
                    ? createState[rowHeader]
                    : defaultValue;

            return (
                <React.Fragment
                    key={`CreateContent-${sheetName}-${rowIdx}-${cellIdx}`}
                >
                    <Table.Cell
                        className="TableBody__Row__header"
                        rowSpan={rowSpan || 1}
                    >
                        <Header as="h5">
                            {headerInfo?.mark ? `*${hdLabel}` : hdLabel}
                            <CustomHeaderInformation id={rowCell.header} />
                            {subHeader && (
                                <Header.Subheader content={subHeader} />
                            )}
                        </Header>
                    </Table.Cell>
                    <Table.Cell
                        className="TableBody__Row__cellInfo"
                        colSpan={colSpan || 1}
                        rowSpan={rowSpan || 1}
                    >
                        {Component ? (
                            <Component
                                key={`CreateContent-${sheetName}-${rowIdx}-${rowCell.header}-Component`}
                                itemAt={itemAt}
                                rowIdx={rowIdx}
                                cellId={rowCell.header}
                                setCallback={setCallback}
                                setCreateState={setCreateState}
                                createState={createState}
                                defaultValue={cellValue || defaultValue}
                                menuName={menuName}
                                isDisableSecondInput={isDisableSecondInput}
                                isInDraggingMode={isInDraggingMode}
                                {...compProp}
                            />
                        ) : (
                            ""
                        )}
                    </Table.Cell>
                </React.Fragment>
            );
        });

    return (
        <Table celled structured>
            <Table.Body>
                {content.map((row, idx) => (
                    <Table.Row key={`CustomTableView-${sheetName}-${idx}-row`}>
                        {tableCellValue(row, idx)}
                    </Table.Row>
                ))}
            </Table.Body>
        </Table>
    );
};

export default CustomTableView;

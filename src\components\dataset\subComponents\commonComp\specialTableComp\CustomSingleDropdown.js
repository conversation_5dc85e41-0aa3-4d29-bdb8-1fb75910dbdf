import React, { useContext, useState, useMemo, useEffect } from "react";
import Select, { createFilter } from "react-select";
import { StoreContext } from "../../../../../store/StoreProvider";
import { createConfig } from "../../CreateComp/createConfig";
// store

import MenuList from "../MenuList";
import { specialConvertToOption } from "../../../../common/sheetCrud/utils";
import { getMenuPlacement } from "../../../datasetConfig";
import { MAX_OPTION } from "../../../../common/sheetCrud/sheetCrudHelper";

const CustomSingleDropdown = ({
    cellId,
    rowIdx,
    defaultValue,
    setCallback,
    option,
    menuName
}) => {
    const [state] = useContext(StoreContext);
    const { sheet } = state.data;
    const { headerFields } = sheet;
    const [selectedValue, setSelectedValue] = useState(
        Array.isArray(defaultValue) ? defaultValue[0] : defaultValue
    );

    // console.log(menuName, defaultValue);
    // change input value
    const handleChange = selectValue => {
        if (!selectValue) setSelectedValue(null);
        setCallback(cellId, selectValue?.id, menuName);
        setSelectedValue(selectValue?.id);
    };

    const customStyles = {
        control: styles => ({
            ...styles,
            // boxShadow: "none",
            borderRadius: "4px",
            border: "solid 0.5px #e0e1e2"
        })
    };

    const customPlacement = getMenuPlacement(rowIdx);

    const newOptions = useMemo(() => {
        if (option) return option;
        if (!headerFields) {
            return [];
        }
        return specialConvertToOption(cellId, headerFields) || [];
    }, [cellId, headerFields, option]);

    // useEffect(() => {
    //     setSelectedValue(defaultValue);
    // }, [menuName]);

    return useMemo(
        () => (
            <Select
                isClearable
                placeholder={createConfig.dropdownHint}
                styles={customStyles}
                options={
                    newOptions
                        ? newOptions
                            .filter(el => el.id !== selectedValue)
                            .slice(0, MAX_OPTION)
                        : null
                }
                value={
                    newOptions && selectedValue
                        ? newOptions
                            .filter(el => el.id === selectedValue)
                            .slice(0, MAX_OPTION)
                        : null
                }
                onChange={handleChange}
                components={{ MenuList }}
                menuPlacement={customPlacement}
                // [fixed] select lag issue: https://github.com/JedWatson/react-select/issues/3128 by M1K3Yio commented on 19 Oct 2018
                filterOption={createFilter({ ignoreAccents: false })}
            />
        ),
        [cellId, newOptions, selectedValue, menuName]
    );
};

export default CustomSingleDropdown;

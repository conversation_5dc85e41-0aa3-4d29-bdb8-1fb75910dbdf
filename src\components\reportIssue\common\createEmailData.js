// utils
import { getFileName } from "../../../commons/utility";
import handleSendEmail from "../../../commons/sendMail";
import getLocalISOString from "../../../commons/getLocalISOString";

// config
import fbConfig from "./fbConfig";
import Api from "../../../api/nmtl/Api";
import { statusConfig } from "./statusConfig";

/**
 *
 * @param {Object} mailData 要帶在信件裡面的內容
 * @returns string
 */

const createMailContent = mailData => {
    const { issueID, name, email, status, desc } = mailData;
    const statusText = statusConfig.find(el => el.status === status).text;
    const nmtlBKURL = "https://nmtl-backend.daoyidh.com/";
    return `
        問題回報編號: ${issueID} <br />
        回報者姓名: ${name} <br />
        回報者信箱: ${email} <br />
        處理狀態: ${statusText} <br />
        <br />
        問題描述: ${desc} <br />
        
        <br />
        若附件下載有誤，請至好臺誌管理平台網站問題回報頁面確認。<br />
        好臺誌管理平台網站: ${nmtlBKURL}
    `;
};

/**
 * @param {string} subject 主題中文名稱
 * @param {Object} mailData issue content data
 */
const createEmailData = (subject, mailData) => {
    // detect wrong mail format
    const mailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    const mailArr = mailData[fbConfig.staffEmail]
        .filter(str => str)
        .filter(str => mailRegex.test(str));

    // prepare send email data
    const mailDataArr = mailArr.map(mailStr => {
        const date = new Date();
        const sendMail = {
            from: "<EMAIL>",
            to: mailStr,
            subject: `${subject}問題回報-${getLocalISOString(date)}`,
            html: createMailContent(mailData),
            attachments: mailData.file.map(url => ({
                filename: getFileName(url),
                content: url
            }))
        };

        return handleSendEmail(sendMail, Api.mail, res => res);
    });

    return new Promise((resolve, reject) => {
        Promise.all(mailDataArr)
            .then(res => {
                const check = res
                    .map(({ state }) => state)
                    .every(el => el === true);
                resolve(!!check);
            })
            .catch(err => {
                reject(err);
            });
    });
};

export default createEmailData;

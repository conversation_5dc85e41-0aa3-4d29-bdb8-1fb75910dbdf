// read Elastic data
// const readElasticData = async api => {
//     console.log("I am readElasticData", api);
//
//     // abort if it tyr too long
//     const controller = new AbortController();
//     const { signal } = controller;
//
//     // to stop fetch if timeout
//     setTimeout(() => controller.abort(), 5000);
//
//     // replace api prefix for production
//     if (process.env.NODE_ENV === "production") {
//         api = api.replace(process.env.REACT_APP_ELASTIC_NODE_URL, "");
//     }
//
//     console.log(api);
//
//     // fetch nmtl api
//     return await fetch(api, { signal })
//         // handle data to json
//         .then(res => res.json())
//         // return data
//         .then(data => data.data || data)
//         // handle catch
//         .catch(err => {
//             // handle AbortController event and return error message
//             if (err.name === "AbortError") {
//                 console.error(
//                     "api:elastic:fetch:get:aborted:connection:timeout"
//                 );
//                 return { error: err.message };
//             }
//             // handle other error event and return error message
//
//             console.error("api:elastic:fetch:get:error:", err.message);
//             return { error: err.message };
//         });
// };
//
// const createElasticData = async (api, entry) => {
//     console.log("I am createElasticData", api, entry);
//
//     // abort if it tyr too long
//     const controller = new AbortController();
//     const { signal } = controller;
//
//     // to stop fetch if timeout
//     setTimeout(() => controller.abort(), 5000);
//
//     // replace api prefix for production
//     if (process.env.NODE_ENV === "production") {
//         api = api.replace(process.env.REACT_APP_ELASTIC_NODE_URL, "");
//     }
//
//     console.log(api);
//
//     const options = {
//         signal,
//         method: "POST",
//         headers: {
//             "content-type": "application/json",
//             "Accept-Encoding": "gzip"
//         },
//         body: JSON.stringify(entry)
//     };
//
//     // replace api prefix for production
//     if (process.env.NODE_ENV === "production") {
//         api = api.replace(process.env.REACT_APP_ELASTIC_NODE_URL, "");
//     }
//
//     console.log(api);
//
//     // fetch nmtl api
//     return await fetch(api, options)
//         // handle data to json
//         .then(res => res.json())
//         // get data
//         .then(data => data.data || data)
//         // handle catch
//         .catch(err => {
//             // handle AbortController event
//             if (err.name === "AbortError") {
//                 console.error(
//                     "api:elastic:fetch:post:create:aborted:connection:timeout"
//                 );
//                 return { error: err.message };
//             }
//             // handle other error event
//
//             console.error("api:elastic:fetch:post:create:error:", err.message);
//             return { error: err.message };
//         });
// };

const readElasticData = async api => ({ error: "feature removed" });
const createElasticData = async (api, entry) => ({ error: "feature removed" });

export { createElasticData, readElasticData };

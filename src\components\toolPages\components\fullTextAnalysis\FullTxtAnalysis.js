/* eslint-disable camelcase */
import React, { useEffect, useState, useContext } from "react";
import {
    Button,
    Container,
    Divider,
    Grid,
    Segment,
    Checkbox,
    Popup
} from "semantic-ui-react";
import queryString from "query-string";
import { useHistory } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
// component
import EventButton from "./crud/EventButton";
import Content from "./content/Content";
import PaginationHOC from "./content/PaginationHOC";
import SearchBar from "./search/SearchBar";
import AlertMsg, { ALERT_MSG_TYPE } from "../../../common/AlertMsg";
import DropDownMainSubject from "./search/DropDownMainSubject";
import ByPassModal from "./crud/ByPassModal";
// hook
import useFetchData from "./hook/useFetchData";
import useColumnsDef from "./hook/useColumnsDef";

// utils,config
import { columnDefault } from "./config";
import FltAnaAct from "../../../../reduxStore/fltAnalysis/FltAnaAct";
import {
    getTotalPage,
    getDataByActivePage
} from "../../../../commons/pageHelper";
import { StoreContext } from "../../../../store/StoreProvider";
import { getErrorString } from "../../../../commons";

const FullTxtAnalysis = () => {
    // route
    const history = useHistory();
    const {
        location: { search }
    } = history;
    const { keyword } = queryString.parse(search);
    // store
    const curDispatch = useDispatch();
    const { fltAna } = useSelector(state => state);
    const {
        content: curClassData,
        // search,
        fetchSignal,
        query,
        classType,
        message,
        mainSubject,
        memoFirestoreColDocIds
    } = fltAna;
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    // const { mainDiary } = main;
    const pageSizeOptions = [5, 10, 15, 20].map(o => ({
        key: o,
        text: `顯示 ${o} 筆`,
        value: o
    }));
    const [pageSize, setPageSize] = useState(10);
    const totalPages = React.useMemo(
        () => getTotalPage(curClassData?.filtered || [], pageSize),
        [curClassData?.filtered, pageSize]
    );
    const [activePage, setActivePage] = useState(1); // 頁碼從 1 開始
    const [curPageData, setCurPageData] = useState([]);
    const curClassType = classType;

    const [notHasFullText, setNotHasFullText] = useState(true);
    const [notFileAvailableAt, setNotFileAvailableAt] = useState(true);
    const checkBoxItems = [
        {
            label: "未字詞分析",
            checked: notHasFullText,
            onChange: (e, { checked }) => setNotHasFullText(checked)
        },
        {
            label: "未連結pdf檔案",
            checked: notFileAvailableAt,
            onChange: (e, { checked }) => setNotFileAvailableAt(checked)
        }
    ];

    const hookProps = {
        curClassType,
        dataset: mainSubject?.selected?.dataset,
        keyword: keyword || "",
        curDispatch,
        state
    };
    // hook for fetch data
    const {
        getBasicData,
        getDetailData,
        getFrStoreFltCol,
        mergeDbAndCollData
    } = useFetchData({
        ...hookProps
    });
    // hook for table 基本設定
    const {
        readColumns,
        readColumnVisibilityDef,
        readTableMetaDef
    } = useColumnsDef({
        ...hookProps
    });

    /**
     * 取得資料流程:
     * 1.依據 graph(dataset) 及keyword 取得基本資料,放進 content.created.然後通知執行步驟 2.
     * 2.後分類: 依據 過濾條件,過濾 content.created,放進 content.filtered.然後通知執行步驟 3.
     * 3.取得該頁的詳細資料: 從 content.filtered 取的該頁的資料,再fetch API取得詳細資訊
     *
     */

    const fetchCurPageData = async () => {
        try {
            // 先清空
            setCurPageData([]);
            // 取得目前頁面的 data
            // eslint-disable-next-line no-underscore-dangle
            const _curPageData = getDataByActivePage(
                curClassData?.filtered,
                activePage,
                pageSize
            );
            // 取得詳細資料
            if (
                typeof getDetailData === "function" &&
                Array.isArray(_curPageData) &&
                _curPageData.length > 0
            ) {
                const res = await getDetailData(_curPageData.map(o => o.id));

                // const freezeData = JSON.parse(JSON.stringify(res.data));
                const freezeData = res.data;
                if (Array.isArray(res?.data)) {
                    const tmpData = _curPageData.map(cur => {
                        const find = freezeData.find(
                            r => r.id === cur.id && r.graph === cur.graph
                        );
                        if (find) {
                            const {
                                author,
                                source,
                                type,
                                // eslint-disable-next-line camelcase
                                fullWorkAvailableAts__MultiLang
                            } = find;

                            return {
                                ...cur,
                                author,
                                source,
                                type,
                                // 置換掉部分資訊
                                fullWorkAvailableAts__MultiLang: [
                                    ...fullWorkAvailableAts__MultiLang
                                ]
                            };
                        }
                        return {
                            ...cur
                        };
                    });

                    setCurPageData(
                        mergeDbAndCollData(JSON.parse(JSON.stringify(tmpData)))
                    );
                    // 傳遞訊息使 <Content /> 的react-table 重新渲染
                    dispatch({
                        type: FltAnaAct.setRefreshTableSignal
                    });
                }
            }
        } catch (err) {
            curDispatch({
                type: FltAnaAct.setMessage,
                payload: {
                    type: ALERT_MSG_TYPE.error,
                    title: getErrorString(err)
                    // text: ""
                }
            });
        }
    };

    useEffect(() => {
        setActivePage(1);
    }, [mainSubject?.selected?.dataset]);

    // 搜尋時, set activePage 1
    useEffect(() => {
        setActivePage(1);
        curDispatch({
            type: FltAnaAct.fetchSignalBasic
        });
        // 先清空訊息
        curDispatch({
            type: FltAnaAct.setMessage,
            payload: null
        });
    }, [search]);

    useEffect(() => {
        // 當 notHasFullText 或 notFileAvailableAt變更時, 觸發 fetchSignalFilter 訊號
        curDispatch({
            type: FltAnaAct.fetchSignalFilter
        });
    }, [notHasFullText, notFileAvailableAt]);

    // 取得 firestore/fullText-events 的所有資料
    // 時機：進到頁面
    useEffect(() => {
        if (fetchSignal.firestore) {
            getFrStoreFltCol();
        }
    }, [fetchSignal.firestore]);

    // 先取得所有 instance 基本資料(依 keyword 及篩選機制搜尋相關資料)
    // 時機：進到頁面,keyword 變更, 篩選機制變更
    useEffect(() => {
        if (fetchSignal.basic) {
            getBasicData();
        }
    }, [fetchSignal.basic]);

    useEffect(() => {
        // 當更新整頁資訊(接收到 dbAndFirestore 訊號)時, 先取得 firestore 的資訊, 再 fetch nmtl-api 的資料
        if (fetchSignal.dbAndFirestore) {
            // 先取得 firestore 資料,在取 nmtl-api 的資要
            getFrStoreFltCol(memoFirestoreColDocIds).then(() => {
                // 重新取得 database 資料
                curDispatch({
                    type: FltAnaAct.fetchSignalBasic
                });
            });
        }
    }, [fetchSignal.dbAndFirestore]);

    useEffect(() => {
        const mustHasVal = list => list.filter(l => l.value);
        // 當 filter 訊號變更時, 則依據 notHasFullText 或 notFileAvailableAt 過濾 data
        if (fetchSignal.filter) {
            // 後分類的結果放進 curClassData.created
            curDispatch({
                type: FltAnaAct.setBasicDataFilt,
                payload: (curClassData?.created || []).filter(o => {
                    if (notHasFullText || notFileAvailableAt) {
                        // todo:
                        return (
                            (notHasFullText &&
                                !(
                                    Array.isArray(o.fltIds) &&
                                    mustHasVal(o.fltIds).length > 0
                                )) ||
                            (notFileAvailableAt &&
                                !(
                                    Array.isArray(o.fileAvailableAts) &&
                                    mustHasVal(o.fileAvailableAts).length > 0
                                ))
                        );
                    }
                    return true;
                })
            });
            curDispatch({
                type: FltAnaAct.fetchSignalDetail
            });
        }
    }, [fetchSignal?.filter]);

    // 取得詳細資訊
    // 時機：關鍵字搜尋 api 取得所有基本資訊、每頁比數變更、目前頁碼變更
    useEffect(() => {
        fetchCurPageData();
    }, [fetchSignal?.detail, pageSize, activePage]);

    // 離開頁面,清空 state
    useEffect(
        () => () => {
            curDispatch({
                type: FltAnaAct.clearCache
            });
        },
        []
    );

    const title =
        columnDefault.find(
            col => col.classType.toLowerCase() === curClassType.toLowerCase()
        )?.title || "";

    const isLoading = Array.isArray(query) && query.length > 0;

    return (
        <Container>
            <Segment basic compact style={{ marginBottom: "0px" }}>
                <h2>{title}</h2>
            </Segment>

            <AlertMsg message={message} />

            {/* 建立其他 modal */}
            <ByPassModal />

            <Grid celled>
                <Grid.Row>
                    <Grid.Column width={16}>
                        <Grid>
                            <Grid.Row>
                                <Grid.Column floated="left" width={3}>
                                    <DropDownMainSubject />
                                </Grid.Column>
                                <Grid.Column floated="left" width={5}>
                                    {/* search content */}
                                    <SearchBar />
                                </Grid.Column>

                                <Grid.Column floated="right" width={5}>
                                    <div
                                        style={{
                                            display: "flex",
                                            justifyContent: "flex-end"
                                        }}
                                    >
                                        <Popup
                                            content="更新狀態"
                                            trigger={
                                                <Button
                                                    icon="refresh"
                                                    onClick={() => {
                                                        // 先取得 firestore 資料,在取 nmtl-api 的資要
                                                        curDispatch({
                                                            type:
                                                                FltAnaAct.fetchSignalDbAndFr
                                                        });
                                                    }}
                                                    disabled={isLoading}
                                                    basic
                                                    color="blue"
                                                    style={{
                                                        marginRight: "20px"
                                                    }}
                                                />
                                            }
                                        />

                                        {/* other actions */}
                                        <EventButton
                                            exclude={["save", "recovery"]}
                                        />
                                    </div>
                                </Grid.Column>
                            </Grid.Row>
                            <Grid.Row>
                                <Grid.Column width={5} verticalAlign="middle">
                                    <div style={{ display: "flex" }}>
                                        篩選：{" "}
                                        {checkBoxItems.map((props, idx) => (
                                            <Checkbox
                                                {...props}
                                                key={idx.toString()}
                                                style={{ marginRight: "15px" }}
                                                disabled={isLoading}
                                            />
                                        ))}
                                    </div>
                                </Grid.Column>
                            </Grid.Row>
                        </Grid>

                        <Divider hidden style={{ margin: ".4rem 0" }} />

                        {/* table: list */}
                        <div>
                            <Content
                                data={curPageData}
                                columns={readColumns}
                                columnVisibility={readColumnVisibilityDef}
                                tableMeta={readTableMetaDef}
                                contentType="read"
                                loading={isLoading}
                            />
                        </div>

                        <Divider hidden />

                        {/* pagination */}
                        <PaginationHOC
                            totalPages={totalPages}
                            activePage={activePage}
                            onPageChange={val => {
                                setActivePage(val);
                            }}
                            pageSize={pageSize}
                            onPageSizeChange={val => setPageSize(val)}
                            loading={query.length > 0}
                            pageSizeOptions={pageSizeOptions}
                        />
                    </Grid.Column>
                </Grid.Row>
            </Grid>
            <Divider hidden />
        </Container>
    );
};

export default FullTxtAnalysis;

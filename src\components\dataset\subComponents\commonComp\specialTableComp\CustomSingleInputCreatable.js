import React, { useEffect, useMemo, useState } from "react";

// ui
import { createFilter } from "react-select";
import CreatableSelect from "react-select/creatable";
import { isEmpty } from "../../../../../commons";
import MenuList from "../MenuList";

const CustomSingleInputCreatable = ({
    cellId,
    // rowIdx,
    disabled,
    defaultValue,
    setCallback,
    menuName
}) => {
    const [inputValue, setInputValue] = useState(defaultValue);
    const [createOpt, setCreateOpt] = useState([]);

    useEffect(() => {
        if (defaultValue && Array.isArray(defaultValue)) {
            setCreateOpt(
                defaultValue.map((item, idx) => ({
                    id: idx,
                    value: item,
                    label: item
                }))
            );
        }
    }, [menuName]);

    // const handleInputKeyPress = event => {
    //     console.log("handleInputKeyPress");
    //     if (event.key === "Enter") {
    //         const existValue = createOpt?.find(el => el.value === inputValue);
    //
    //         if (existValue) return;
    //
    //         const curOption = [
    //             ...createOpt,
    //             {
    //                 id: createOpt.length,
    //                 value: inputValue,
    //                 label: inputValue
    //             }
    //         ];
    //
    //         setCreateOpt(curOption);
    //         // setCallback(
    //         //     cellId,
    //         //     curOption.map(el => el.value)
    //         // );
    //     }
    // };

    const handleInputChange = value => {
        if (disabled) {
            return;
        }

        setInputValue(value);
    };

    const handleChange = selectValue => {
        // selectValue: [{ id: "PER96766", label: "施仁思@PER", value: "PER96766" }...]
        const cellValue = isEmpty(selectValue) ? [] : selectValue;

        setCreateOpt(cellValue);
        setCallback(
            cellId,
            cellValue.map(el => el.value)
        );
    };

    return useMemo(
        () => (
            <CreatableSelect
                isMulti
                isClearable
                placeholder="請輸入"
                options={createOpt}
                value={createOpt}
                onChange={handleChange}
                onInputChange={handleInputChange}
                components={{
                    MenuList,
                    DropdownIndicator: () => null,
                    IndicatorSeparator: () => null
                }}
                // onKeyDown={handleInputKeyPress}
                // onCreateOption={handleInputKeyPress}
                filterOption={createFilter({ ignoreAccents: false })}
            />
        ),
        [inputValue, createOpt]
    );
};

export default CustomSingleInputCreatable;

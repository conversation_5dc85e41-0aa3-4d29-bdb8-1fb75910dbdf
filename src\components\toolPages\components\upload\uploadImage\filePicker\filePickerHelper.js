import axios from "axios";
import { verifyFileName, generateUID } from "../../../../../../commons/utility";
import {
    fileServerAPI,
    fileServerMethod
} from "../../../../../../api/fileServer";
import { handleFolderClick } from "../../../../../common/imageCommon/FolderList/folderListHelper";
import uploadConfig from "../../uploadConfig";
import FileAct from "../../../../../../reduxStore/file/fileAction";

const helpers = {};

const handleImgUrl = (imageName, imageSize = uploadConfig.ImageSize) => {
    const pathMatch = imageName.match(
        // eslint-disable-next-line no-useless-escape
        /(?<directory>.+)?[\\\/](?<filename>[^\\\/]+)\.(?<extension>.+)$/
    );

    if (!pathMatch) return imageName;
    const { directory, filename, extension } = pathMatch.groups;
    return `${directory}/${imageSize}_${filename}.${extension}`;
};

const normalizeFileName = name => {
    const fileMatch = name.match(/(?<filename>[^\\\/]+)\.(?<extension>.+)$/);
    const { filename, extension } = fileMatch.groups;
    return `${filename || ""}_${generateUID()}.${extension}`;
};

// 偵測到 drop 圖片,立即 put API, 上傳成功後才顯示圖片於 Gallery
helpers.handleDropCallback = (
    files,
    dispatch,
    folderPattern
) => acceptedFiles => {
    const {
        uploadImages,
        currentFolder,
        ACCEPTABLE_EXTENSIONS,
        UPLOAD_CONFIG
    } = files;

    const verifyFiles =
        (Array.isArray(acceptedFiles) &&
            acceptedFiles.filter(af =>
                verifyFileName(af, ACCEPTABLE_EXTENSIONS)
            )) ||
        [];

    const illegalFileCount = acceptedFiles.length - verifyFiles.length;
    const alertMsgText = `已接收 ${verifyFiles.length} 個檔案, 拒絕 ${illegalFileCount} 個檔案`;
    const alertMsgTitle =
        illegalFileCount > 0 ? "部分檔案不符合規範" : "檔案符合規範";
    dispatch({
        type: FileAct.PICKER_DROP_MESSAGE,
        payload: {
            type: illegalFileCount > 0 ? "warning" : "success",
            title: alertMsgTitle,
            text: alertMsgText
        }
    });

    if (verifyFiles.length === 0) return;
    if (currentFolder.path.length === 0) {
        dispatch({
            type: FileAct.PICKER_DROP_MESSAGE,
            payload: {
                type: "warning",
                title: "尚未選取要上傳的資料夾"
            }
        });
        return;
    }

    // Make sure to revoke the data uris to avoid memory leaks
    if (acceptedFiles && acceptedFiles.length > 0) {
        acceptedFiles
            .filter(af => !verifyFileName(af, ACCEPTABLE_EXTENSIONS))
            .forEach(file => URL.revokeObjectURL(file.preview));
    }

    if (verifyFiles.length > UPLOAD_CONFIG.maxCount) {
        acceptedFiles.forEach(file => URL.revokeObjectURL(file.preview));
        const alertTitle = "超過上傳數量";
        dispatch({
            type: FileAct.PICKER_DROP_MESSAGE,
            payload: {
                type: "warning",
                title: alertTitle
            }
        });
        return;
    }

    const formData = new FormData();
    if (verifyFiles && verifyFiles.length > 0) {
        verifyFiles.forEach(file => {
            formData.append(uploadConfig.ImageFormName, file);
        });
    }

    const reqUrl = `${fileServerAPI.uploadFile.replace(
        "[type]",
        currentFolder.type
    )}${
        (currentFolder.path || "").startsWith("/")
            ? currentFolder.path
            : `/${currentFolder.path}`
    }`;

    if (!formData) return;
    if (formData) {
        // change uploading state
        dispatch({
            type: FileAct.UPLOAD_LOADING,
            payload: {
                state: true,
                message: "uploading"
            }
        });
        axios({
            method: fileServerMethod.uploadFile,
            url: reqUrl,
            headers: {
                "Access-Control-Allow-Origin": "*"
            },
            data: formData
        })
            .then(res => {
                if (res.status === 200 && res.data.status === "success") {
                    const { images: apiImages } = res.data;

                    if (apiImages) {
                        // 更新 drop area message
                        dispatch({
                            type: FileAct.PICKER_DROP_MESSAGE,
                            payload: {
                                type: "success",
                                title: "檔案上傳成功"
                            }
                        });
                        const imagesUrl =
                            (apiImages &&
                                apiImages.length > 0 &&
                                apiImages.map(fn => ({
                                    imgUrl: fn.imgUrl,
                                    imgInfo: fn.imgInfo,
                                    imgFileName: fn.imgFileName,
                                    url:
                                        currentFolder.type ===
                                        uploadConfig.image
                                            ? handleImgUrl(`${fn.imgUrl}`)
                                            : null
                                }))) ||
                            [];

                        // 顯示上傳的圖片於 drop 區 的 gallery
                        dispatch({
                            type: FileAct.UPLOAD_IMAGE,
                            payload: [...(uploadImages || []), ...imagesUrl]
                        });
                        // }

                        // 使用 handleFolderClick, 重新抓取該資料夾的檔案路徑
                        handleFolderClick(
                            null,
                            currentFolder,
                            folderPattern,
                            dispatch
                        );
                    }
                } else {
                    dispatch({
                        type: FileAct.PICKER_DROP_MESSAGE,
                        payload: {
                            type: "error",
                            title: "檔案上傳失敗，請稍後再試"
                        }
                    });
                }
            })
            .catch(err => {
                dispatch({
                    type: FileAct.PICKER_DROP_MESSAGE,
                    payload: {
                        type: "error",
                        title: "檔案上傳失敗，請稍後再試",
                        text: `error: ${err.message}`
                    }
                });
                console.log(err);
            })
            .finally(() => {
                dispatch({
                    type: FileAct.SET_FORM_DATA,
                    payload: null
                });
                // change uploading state
                dispatch({
                    type: FileAct.UPLOAD_LOADING,
                    payload: {
                        state: false,
                        message: ""
                    }
                });
            });
    }
};

export default helpers;

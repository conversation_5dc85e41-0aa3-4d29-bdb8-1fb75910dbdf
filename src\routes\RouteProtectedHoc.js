import React, { useContext } from "react";
import { Route, Redirect } from "react-router-dom";
import { bool, any, object } from "prop-types";

// store
import { StoreContext } from "../store/StoreProvider";

// common
import { isEmpty } from "../commons";

const RouteProtectedHoc = ({ component: Component, ...rest }) => {

    // handle page refresh go to home if user in other page and in login status
    const isLogin = JSON.parse(localStorage.getItem("isLogin"));

    // eslint-disable-next-line no-unused-vars
    const [state, _] = useContext(StoreContext);

    const { user } = state;

    if (isLogin || !isEmpty(user) || rest.public) {
        return (
            <Route
                {...rest}
                render={ props => <Component {...props} /> }
            />
        );
    }
    return <Redirect to={{ pathname: "SignIn" }} />;
};

RouteProtectedHoc.propTypes = {
    component: any,
    isLoggedIn: bool,
    rest: object,
    props: object
};

export default RouteProtectedHoc;

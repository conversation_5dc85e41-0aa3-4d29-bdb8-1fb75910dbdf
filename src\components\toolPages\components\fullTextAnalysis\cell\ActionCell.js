import React, { useEffect, useState } from "react";
import { Button, Icon, Popup } from "semantic-ui-react";

//
const ActionCell = ({
    // 所有 cell 共用的 props
    colId,
    rowIdx,
    cellValue,
    column,
    rowData,
    domain,
    range,
    graph,
    classType,
    // 特定 cell 才有的 props
    onClickStartAnalysis,
    onClickReAnalysis
    // 除了 table 之外的使用,可以自定義其他的 property 擴充功能
}) => {
    // 流程中必要的步驟
    const checkKeys = [
        "pdf2png__Status",
        "png2ocrJson__Status",
        "service2segApi__Status",
        "service2nmtlApi__Status"
    ];

    const { fltId, fileAvailableAt, docId } = rowData;

    // 有連結 fltId
    const hasFullText = !!fltId;
    const hasPdf = !!fileAvailableAt;

    /**
     * 開始分析時機:
     * - 從未分析過,包含 未連結 fltId or 沒有建立或連結 pdf 檔案
     * 重新分析時機:
     * - 已連結 fltId
     * - 已分析過,但中途失敗
     * - 已分析過,且完成
     *
     */

    // 是否顯示開始分析
    const showStart = () => {
        if (docId) return false;
        // 已連結fltId 且 連結pdf, 則 show 為 false
        if (hasFullText && hasPdf) return false;
        let show = false;
        // firestore 尚未有紀錄, 則 show 改為 true
        // 只要有一個步驟未完成,則顯示 start
        checkKeys.forEach(key => {
            // 若沒有狀態的 key or 有 key 但是 value 為空
            if (!(key in rowData)) {
                show = true;
                return;
            }
            if (
                rowData[key].length === 0 ||
                rowData[key].find(
                    o => Object.values(o?.value || "").length === 0
                )
            ) {
                show = true;
            }
        });
        return show;
    };

    // 是否顯示 開始分析 button
    const showStartBtn = showStart();

    // 是否正在跑流程中
    const isInProgress = () => {
        // 初始值: 假設沒有正在跑流程中
        let inProgress = false;
        checkKeys.forEach(key => {
            if (key in rowData) {
                const { finishTime, requestTime, errorTime, errorMessage } =
                    rowData[key] || {};
                // 流程中有出現錯誤,即代表流程中止
                if (errorTime || errorMessage) {
                    inProgress = false;
                    return;
                }
                // 只要有一個中間流程尚未紀錄 requestTime & finishTime, 則代表正在跑流程中
                if (!requestTime && !finishTime) {
                    // fixme:
                    // 注意:fs-api如果在跑流程時掛掉,firestore的紀錄只紀錄到一半,inProgress是true
                    // inProgress = true;
                }
            }
        });
        return inProgress;
    };

    // 是否從頭到尾跑完流程
    const isFinishedAna = () => {
        let isFinished = true;
        checkKeys.forEach(key => {
            if (key in rowData) {
                const { finishTime, requestTime, errorTime } =
                    rowData[key] || {};
                if (!requestTime && !(finishTime || errorTime))
                    isFinished = false;
            }
        });
        return isFinished;
    };

    // 處理過程是否發生錯誤
    const isErrorOccur = () => {
        let isError = true;
        checkKeys.forEach(key => {
            if (key in rowData) {
                const { errorTime, errorMessage } = rowData[key] || {};
                if (errorTime || errorMessage) isError = true;
            }
        });
        return isError;
    };

    // 是否顯示 重新分析
    const showReAnalysis = () => {
        // 如果 有顯示 「開始分析」, 則不顯示 「重新分析」
        if (showStartBtn) return false;
        return hasFullText || isFinishedAna() || isErrorOccur();
    };

    const [anchorEle, setAnchorEle] = useState(null);

    const menuName = {
        start: "start",
        reAna: "reAna"
    };
    const menus = [
        {
            name: menuName.start,
            icon: "play",
            label: "開始分析",
            action: payload => {
                onClickStartAnalysis(payload);
                setAnchorEle(menuName.start);
            },
            show: showStartBtn,
            disabled:
                !showStartBtn || anchorEle === menuName.start || isInProgress()
        },
        {
            name: menuName.reAna,
            icon: "redo",
            label: "重新分析",
            action: payload => {
                onClickReAnalysis(payload);
                setAnchorEle(menuName.reAna);
            },
            show: showReAnalysis(),
            disabled: anchorEle === menuName.reAna
            // disabled: !isFinishedAna() || anchorEle === menuName.reAna
        }
    ];

    const handleItemClick = (e, { name }) => {
        const payload = {
            data: rowData,
            rowIdx,
            colId
        };
        const find = menus.find(menu => menu.name === name);
        if (find?.action && typeof find.action === "function") {
            find.action(payload);
        }
    };

    return (
        <div>
            {menus
                .filter(o => o.show)
                // eslint-disable-next-line no-unused-vars
                .map(({ name, icon, label, disabled }, idx) => (
                    <Popup
                        key={idx.toString()}
                        content={label}
                        trigger={
                            <Button
                                key={idx.toString()}
                                name={name}
                                color="blue"
                                size="small"
                                basic
                                onClick={handleItemClick}
                                icon={icon}
                                style={{ margin: "10px auto" }}
                                disabled={disabled}
                            />
                        }
                    />
                ))}
        </div>
    );
};

export default ActionCell;

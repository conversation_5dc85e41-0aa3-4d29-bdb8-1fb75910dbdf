import MainIntroduction from './rightArea/MainIntroduction';
import MainCarousel from './rightArea/MainCarousel';
import MainCardImage from './rightArea/MainCardImage';
import LinkingPage from './rightArea/LinkingPage';
import PrivacyPage from './rightArea/PrivacyPage';
import ReferencePage from './rightArea/ReferencePage';
import RegionLiterature from './rightArea/RegionLiterature';
import SubjectLiterature from './rightArea/SubjectLiterature';
import CityLiterature from './rightArea/CityLiterature';
import VrMuseum from './rightArea/VrMuseum';
import LiteralProducer from './rightArea/LiteralProducer';
import LiteralData from './rightArea/LiteralData';
import ManualPage from './rightArea/ManualPage';
import DeveloperPage from './rightArea/DeveloperPage';
import EditNewsProvider from '../commons/components/EditNews/EditNewsProvider';
import CardImageUpdater from './rightArea/CardImageUpdater';

// key對應到firestore的document命名
const menuItem = [
    {
        name: '首頁網站簡介及圖片更換',
        component: MainIntroduction,
        key: 'MainIntroduction',
    },
    { name: '首頁輪播圖更換', component: MainCarousel, key: 'MainCarousel' },
    {
        name: '首頁主題文學小卡圖更換',
        component: MainCardImage,
        key: 'MainCardImage',
    },
    { name: '相關連結頁', component: LinkingPage, key: 'LinkingPage' },
    { name: '隱私權及安全政策頁', component: PrivacyPage, key: 'PrivacyPage' },
    { name: '資料來源頁', component: ReferencePage, key: 'ReferencePage' },
    {
        name: '區域文學簡介',
        component: RegionLiterature,
        key: 'RegionLiterature',
    },
    {
        name: '各主題文學',
        component: SubjectLiterature,
        key: 'SubjectLiterature',
    },
    { name: '各城市文學', component: CityLiterature, key: 'CityLiterature' },
    { name: '各VR文學館', component: VrMuseum, key: 'VrMuseum' },
    {
        name: '文學素材生成器使用說明',
        component: LiteralProducer,
        key: 'LiteralProducer',
    },
    {
        name: '各文學數據標題、使用說明',
        component: LiteralData,
        key: 'LiteralData',
    },
    { name: '無障礙頁面文字', component: ManualPage, key: 'ManualPage' },
    { name: '開發者修改選項', component: DeveloperPage, key: 'DeveloperPage' },
    {
        name: '最新消息',
        component: EditNewsProvider,
        key: 'EditNews',
    },
    {
        name: '卡片預設圖更換',
        component: CardImageUpdater,
        key: 'CardImageUpdater',
    },
];

export default menuItem;

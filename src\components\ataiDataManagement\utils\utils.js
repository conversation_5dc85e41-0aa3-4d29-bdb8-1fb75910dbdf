// import { useDispatch } from "react-redux";
import axios from "axios";
import originalAtaiData from "../originalAtaiData";
import ataiMngAct from "../../../reduxStore/ataiManage/ataiManageAction";
import Api from "../../../api/nmtl/Api";

const getAtaiData = async dispatch => {
    try {
        const res = await axios.post(Api.getAtaiData);

        const ataiData = [...res.data.files, ...originalAtaiData];

        const newAtaiData = ataiData.map(ataiDataEl => {
            const matchedOriginalDataEl = originalAtaiData.find(
                originalDataEl =>
                    ataiDataEl.filename === originalDataEl.filename
            );

            if (!matchedOriginalDataEl) {
                return { ...ataiDataEl, checked: false };
            }

            return ataiDataEl;
        });

        dispatch({
            type: ataiMngAct.SET_ATAI_DATA,
            payload: newAtaiData
        });
    } catch (e) {
        alert("取得資料失敗: ", e);

        dispatch({
            type: ataiMngAct.SET_ATAI_DATA,
            payload: []
        });
    }
};

export default getAtaiData;

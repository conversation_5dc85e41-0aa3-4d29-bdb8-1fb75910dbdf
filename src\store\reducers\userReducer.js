import Act from "../actions";

/** 說明:
 * userAllGroupInfo: Filter all group which has current user without combining data.
 * */
const initState = { userAllGroupInfo: {} };

const userReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.FIREBASE_LOGIN_USER:
            return { ...state, ...action.payload };
        case Act.FIREBASE_LOGOUT_USER:
            return {};

        case Act.FIREBASE_GROUP_INFO_BY_USER:
            return { ...state, userAllGroupInfo: action.payload };
        default:
            return state;
    }
};

export default userReducer;

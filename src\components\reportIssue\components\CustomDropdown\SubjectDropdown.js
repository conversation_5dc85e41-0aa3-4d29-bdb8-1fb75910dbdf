import React, { useEffect, useState } from "react";

// plugins
import { useDispatch } from "react-redux";

// semantic ui
import { Dropdown } from "semantic-ui-react";

// utils
import comTextConfig from "../../../../commons/comTextConfig";
import { isEmpty } from "../../../../commons";
import RPAct from "../../reportIssueAction";

// hooks
import useCusContext from "../../../common/hooks/useCusContext";
import useGetSubjectOPs from "../../../common/hooks/useGetSubjectOPs";

function SubjectDropdown() {
    const [state] = useCusContext();
    const { groupInfo } = state.data;

    const dispatch = useDispatch();

    const dropOptions = useGetSubjectOPs(groupInfo);
    const [opVal, setOpVal] = useState("");

    useEffect(() => {
        if (isEmpty(dropOptions)) return;
        setOpVal(dropOptions[0].value);
    }, [dropOptions]);

    useEffect(() => {
        dispatch({
            type: RPAct.SET_RPSUBJECT,
            payload: opVal
        });
    }, [opVal]);

    const handleChange = (evt, data) => {
        setOpVal(data.value);
    };

    return (
        <Dropdown
            selection
            options={dropOptions}
            style={{ border: "none" }}
            placeholder={comTextConfig.Header_Selector}
            onChange={handleChange}
            value={opVal}
        />
    );
}

export default SubjectDropdown;

import React, { useMemo, useState, useContext } from "react";
import { Container, Header } from "semantic-ui-react";

import CustomTab from "./CustomTab/CustomTab";
import CustomAccordion from "./CustomAccordion/CustomAccordion";
import { StoreContext } from "../../../../../../store/StoreProvider";

const CustomTabContent = ({
    sheetName,
    content,
    header,
    setCallback,
    setCreateState,
    createState,
    isDisableSecondInput
}) => {
    const [state, dispatch] = useContext(StoreContext);
    const { content: ct } = state.data;
    const [curMenu, setCurMenu] = useState(ct.curBookLang);
    const [panes, setPanes] = useState(
        createState
            ? Object.keys(createState).filter(el => el !== "default")
            : null
    );
    const [cloneLocalCreateState, setCloneLocalCreateState] = useState([]);

    return useMemo(
        () => (
            <Container style={{ width: "100%" }} className="TabContent">
                <Header className="TabContent__Header">翻譯書</Header>
                <CustomTab
                    contentSetting={content}
                    className="TabContent__Menu"
                    panes={panes}
                    setPanes={setPanes}
                    curMenu={curMenu}
                    setCurMenu={setCurMenu}
                    createState={createState}
                    setCreateState={setCreateState}
                    isDisableSecondInput={isDisableSecondInput}
                    setCallback={setCallback}
                />
                {curMenu &&
                    panes.map((item, index) => {
                        if (curMenu === item) {
                            return (
                                <CustomAccordion
                                    key={`customAccordion-${curMenu}-${index}`}
                                    sheetName={sheetName}
                                    contentSetting={content}
                                    header={header}
                                    setCallback={setCallback}
                                    setCreateState={setCreateState}
                                    createState={
                                        (createState && createState[curMenu]) ||
                                        null
                                    }
                                    menuName={curMenu}
                                    isDisableSecondInput={isDisableSecondInput}
                                    cloneLocalCreateState={
                                        cloneLocalCreateState
                                    }
                                    setCloneLocalCreateState={
                                        setCloneLocalCreateState
                                    }
                                />
                            );
                        }
                        return null;
                    })}
            </Container>
        ),
        [sheetName, panes, curMenu, createState]
    );
};

export default CustomTabContent;

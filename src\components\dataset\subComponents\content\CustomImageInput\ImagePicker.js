import React, { useContext } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Tab, TabPane } from 'semantic-ui-react';

// component
import UploadImage from './UploadImage';
import UploadLink from './UploadLink';

// 裁切元件
import CropImage from './CropImage';

// store
import FileAct from '../../../../../reduxStore/file/fileAction';
import { StoreContext } from '../../../../../store/StoreProvider';

const CustomTabPane = ({ children }) => (
    <TabPane style={{ height: '70vh', overflowY: 'auto' }}>{children}</TabPane>
);

// 資料管理 從標題裡新增單張圖片
const ImagePicker = ({ open, setOpen, defaultValue: oriValue, onValueChange }) => {
    const {
        files: { loading, defaultValue, selectFile },
    } = useSelector((state) => state);
    const dispatchRedux = useDispatch();

    const [state] = useContext(StoreContext);
    const { mainSubject, sheet } = state.data;

    const useLink = !!(mainSubject?.selected?.key === 'tlvm' && sheet?.selected?.key === 'Event');

    const handleModalClose = () => {
        // 最後上傳圖片清空
        dispatchRedux({ type: FileAct.UPLOAD_IMAGES_LATEST, payload: [] });
        // 初始資料夾載入重設
        dispatchRedux({ type: FileAct.INIT_FILE_SETTINGS, payload: false });
        dispatchRedux({ type: FileAct.INIT_FOLDERPATTERN });
        dispatchRedux({
            type: FileAct.CUR_FOLDER_FILES_STATUS,
            payload: { original: [], checked: [] },
        });
        dispatchRedux({ type: FileAct.FOLDER_FILES_URL, payload: [] });

        if (oriValue !== selectFile) {
            if (selectFile && selectFile.length > 0) {
                onValueChange(selectFile);
            } else {
                onValueChange(defaultValue);
            }
        }

        setOpen(false);
    };

    const panes = [
        {
            menuItem: { key: 'upload', content: '圖片上傳' },
            render: () => (
                <CustomTabPane>
                    <UploadImage defaultValue={defaultValue} currentValue={selectFile} />
                </CustomTabPane>
            ),
        },
        {
            menuItem: { key: 'link', content: '使用連結', disabled: !useLink },
            render: () => (
                <CustomTabPane>
                    <UploadLink defaultValue={defaultValue} currentValue={selectFile} />
                </CustomTabPane>
            ),
        },
    ];

    return (
        <Modal size="large" open={open} closeOnDocumentClick={false} onClose={handleModalClose}>
            <Dimmer active={loading.state} inverted>
                <Loader size="large">{loading.message}</Loader>
            </Dimmer>

            <Modal.Header>圖片設定</Modal.Header>

            <Modal.Content>
                <Tab panes={panes} />
            </Modal.Content>

            <Modal.Actions>
                <Button onClick={handleModalClose} primary>
                    關閉
                </Button>
            </Modal.Actions>

            <CropImage />
        </Modal>
    );
};

export default ImagePicker;

@mixin rightComponentMain {
  width: 100%;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

@mixin btnArea {
  display: flex;
  justify-content: flex-end;
}

.EditPdf {
  @include rightComponentMain;
  //height: 25%;
  .topArea{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px;
    &__left{
      display: flex;
      width:100%;
      justify-content: center;
      align-items: center;
      height: 100%;
      font-size: 12px;
    }
    &__right{
      display: flex;
      gap: 5px;
      width: 100%;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      &--first{
        display: flex;
        align-items: center;
        justify-content: center;
        flex:1;
      }
      &--second{
        margin: auto;
        flex:8;
        padding:1rem;
        border:1px solid #e0e1e2;
      }
      &--third{
        display: flex;
        align-items: center;
        justify-content: center;
        margin: auto;
        flex:1;
        cursor: pointer;
      }
    }
  }
  .uploadArea{
    margin-bottom: 5px;
    height: 40%;
    display: flex;
  }
  .btnArea{
    @include btnArea;
    button {
      margin: 0;
    }
  }
  .bookArea{
    height:80%;
    overflow-y: scroll;
    margin-top: 2rem;
  }
}

.topEditArea{
  width: 100%;
  height: 20%;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.midEditArea{
  width: 100%;
  height: 20%;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.botEditArea{
  width: 100%;
  height: 20%;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.EditInfo{
  @include rightComponentMain;
  //height:40%;
  //min-height: 400px;
  &__content{
    margin-top:2rem;
      .quill {
        display: flex;
        flex-direction: column;
        height: 100%;
        .ql-container {
          flex: 1 1 auto;
          .ql-editor {
            flex: 1;
          }
        }
      }
    }
  &__buttonBox{
    display:flex;
    gap:1rem;
    margin-top:1rem;
    justify-content: flex-end;
  }
}

.EditChapters{
  @include rightComponentMain;
  //min-height: 450px;
  //height:35%;
  .topArea{
    display:flex;
    justify-content: space-between;
  }
  .bookArea{
    height:80%;
    margin-top: 2rem;
  }
  .btnArea{
    margin-top: 2rem;
    display: flex;
    justify-content: flex-end;
    width: 100%;
  }
}


.EditPeakYear {
  @include rightComponentMain;
  //height: 25%;
  .topArea{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px;
    &__left{
      display: flex;
      width:100%;
      justify-content: center;
      align-items: center;
      height: 100%;
      font-size: 12px;
    }
    &__right{
      display: flex;
      gap: 5px;
      width: 100%;
      justify-content: left;
      align-items: center;
      font-size: 12px;
    }
  }
}
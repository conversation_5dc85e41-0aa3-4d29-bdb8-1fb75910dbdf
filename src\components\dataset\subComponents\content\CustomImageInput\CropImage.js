import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { <PERSON><PERSON>, But<PERSON> } from "semantic-ui-react";
import <PERSON><PERSON><PERSON> from "react-easy-crop";
import FileAct from "../../../../../reduxStore/file/fileAction";
import getCroppedImg from "./utils/getCroppedImg";
import uploadConfig from "../../../../toolPages/components/upload/uploadConfig";
import { fileServerAPI, fileServerMethod } from "../../../../../api/fileServer";
import { handleFolderClick } from "../../../../common/imageCommon/FolderList/folderListHelper";
import axios from "axios";

const CropImage = () => {
    const dispatchRedux = useDispatch();
    const {
        files: { openCropImgModal, selectedCropImg, folderPattern },
        files
    } = useSelector(state => state);

    const { currentFolder } = files;

    const [crop, setCrop] = useState({ x: 0, y: 0 });
    const [rotation, setRotation] = useState(0);
    const [zoom, setZoom] = useState(1);
    const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);

    const handleImgUrl = (imageName, imageSize = uploadConfig.ImageSize) => {
        const pathMatch = imageName.match(
            // eslint-disable-next-line no-useless-escape
            /(?<directory>.+)?[\\\/](?<filename>[^\\\/]+)\.(?<extension>.+)$/
        );

        if (!pathMatch) return imageName;
        const { directory, filename, extension } = pathMatch.groups;
        return `${directory}/${imageSize}_${filename}.${extension}`;
    };

    const onCropComplete = (croppedArea, croppedAreaPixels) => {
        setCroppedAreaPixels(croppedAreaPixels);
    };

    const uploadCroppedImg = async croppedImage => {
        const formData = new FormData();
        formData.append(uploadConfig.ImageFormName, croppedImage);

        const reqUrl = `${fileServerAPI.uploadFile.replace(
            "[type]",
            currentFolder.type
        )}${
            (currentFolder.path || "").startsWith("/")
                ? currentFolder.path
                : `/${currentFolder.path}`
        }`;

        if (!formData) return;
        try {
            if (formData) {
                const res = await axios({
                    method: fileServerMethod.uploadFile,
                    url: reqUrl,
                    headers: {
                        "Access-Control-Allow-Origin": "*"
                    },
                    data: formData
                });
                if (res.status === 200 && res.data.status === "success") {
                    const { images: apiImages } = res.data;
                    if (apiImages) {
                        const imagesUrl =
                            (apiImages &&
                                apiImages.length > 0 &&
                                apiImages.map(fn => ({
                                    imgUrl: fn.imgUrl,
                                    imgInfo: fn.imgInfo,
                                    imgFileName: fn.imgFileName,
                                    url:
                                        currentFolder.type ===
                                        uploadConfig.image
                                            ? handleImgUrl(`${fn.imgUrl}`)
                                            : null
                                }))) ||
                            [];

                        dispatchRedux({
                            type: FileAct.UPLOAD_IMAGES_LATEST,
                            payload: imagesUrl
                        });

                        handleFolderClick(
                            null,
                            currentFolder,
                            folderPattern,
                            dispatchRedux
                        );
                    }
                }
            }
        } catch (e) {
            console.log("上傳失敗: ", e);
        } finally {
            handleCloseCropImgModal();
        }
    };

    const handleGetAndUploadCroppedImg = async () => {
        try {
            const croppedImage = await getCroppedImg(
                selectedCropImg,
                croppedAreaPixels,
                rotation
            );
            uploadCroppedImg(croppedImage);
        } catch (e) {
            console.error(e);
        }
    };

    const handleCloseCropImgModal = () => {
        dispatchRedux({
            type: FileAct.OPEN_CROP_IMG_MODAL,
            payload: false
        });

        dispatchRedux({
            type: FileAct.SELECTED_CROP_IMG,
            payload: ""
        });
    };

    return (
        <Modal
            onClose={handleCloseCropImgModal}
            open={openCropImgModal}
            size="large"
        >
            <Modal.Header>圖片裁切</Modal.Header>
            <Modal.Content
                style={{ height: "70vh", backgroundColor: "#ffffff" }}
            >
                <div
                    style={{
                        position: "relative",
                        width: "100%",
                        height: "100%"
                    }}
                >
                    <Cropper
                        image={selectedCropImg}
                        crop={crop}
                        zoom={zoom}
                        aspect={1 / 1}
                        onCropChange={setCrop}
                        onCropComplete={onCropComplete}
                        onRotationChange={setRotation}
                        onZoomChange={setZoom}
                        style={{ width: "100%", height: "100%" }}
                    />
                </div>
            </Modal.Content>
            <Modal.Actions>
                <Button
                    content="取消"
                    onClick={handleCloseCropImgModal}
                    style={{ margin: "0 3.5px 0 10px", color: "#000000" }}
                />
                <Button
                    content="儲存"
                    color="blue"
                    onClick={handleGetAndUploadCroppedImg}
                    style={{ margin: "0 3.5px 0 10px" }}
                />
            </Modal.Actions>
        </Modal>
    );
};

export default CropImage;

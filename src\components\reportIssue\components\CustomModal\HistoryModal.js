import React from "react";
import PropTypes from "prop-types";

// semantic ui
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "semantic-ui-react";

// config
import textConfig from "../../common/textConfig";

// components
import HistoryTable from "../CustomTable/HistoryTable";

function HistoryModal({ openModal, onClose }) {
    return (
        <Modal
            size="small"
            open={openModal}
            onClose={onClose}
            className="HistoryModal"
        >
            <Modal.Header className="HistoryModal__header">
                <Header as="h2">{textConfig.label.history}</Header>
            </Modal.Header>
            <Modal.Content>
                <HistoryTable />
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={onClose}>{textConfig.label.close}</Button>
            </Modal.Actions>
        </Modal>
    );
}

HistoryModal.propTypes = {
    /** 顯示儲存資料後結果的視窗 */
    openModal: PropTypes.bool,
    /** 關閉視窗動作 */
    onClose: PropTypes.func
};

HistoryModal.defaultProps = {
    /** 顯示儲存資料後結果的視窗 */
    openModal: false,
    /** 關閉視窗動作 */
    onClose: () => {}
};

export default HistoryModal;

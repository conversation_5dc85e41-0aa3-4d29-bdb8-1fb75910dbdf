@import "../../Style/commonStyle.scss";

@mixin btnArea {
  display: flex;
  justify-content: flex-end;
}

@mixin textLanguageSelect {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 5px;
}

.ReportIssue {
  width: 100%;
  height: 90vh;
  padding: 10px;
  display: flex;
  flex-direction: column;
  &__topArea {
    width: 100%;
  }
  &__contentArea {
    flex: 1 1 auto;
    border: solid 0.5px #e0e1e2;
    // max-height: 100%;
    // @include center;
  }
}
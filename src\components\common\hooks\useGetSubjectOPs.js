import { useState, useEffect } from "react";

import { getMainSubject } from "../../../api/firebase/cloudFirestore";
import { customSubjectOption } from "../../../commons/utility";
import { isEmpty } from "../../../commons/index";
import { filterDataSet } from "../../../commons/filterGroup";

// get subject options
function useGetSubjectOPs(groupInfo) {
    const [msListData, setMsListData] = useState([]);
    const [dropOptions, setDropOptions] = useState([]); // 下拉選單選項

    useEffect(() => {
        getMainSubject()
            .then(result => {
                setMsListData(result);
            })
            .catch(error => {
                console.log(error);
            });
    }, []);

    useEffect(() => {
        if (isEmpty(msListData) || isEmpty(groupInfo)) return;
        const nmtlSet = customSubjectOption();
        const msDataOption = [...msListData, ...nmtlSet]
            .filter(ms => filterDataSet(ms, groupInfo))
            .sort((pre, next) => pre.seq - next.seq)
            .map(({ id, label }) => ({
                key: id,
                text: label,
                value: id
            }));
        setDropOptions(msDataOption);
    }, [msListData, groupInfo]);

    return dropOptions;
}

export default useGetSubjectOPs;

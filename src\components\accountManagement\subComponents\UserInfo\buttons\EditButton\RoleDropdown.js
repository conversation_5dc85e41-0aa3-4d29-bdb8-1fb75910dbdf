import React, { useContext, useState } from "react";

// ui
import { Dropdown } from "semantic-ui-react";

// role config
import role from "../../../../../../App-role";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";
import Act from "../../../../../../store/actions";
import level from "../../../../../../App-level";

const RoleDropdown = ({ userId, userRole }) => {
    // eslint-disable-next-line no-unused-vars
    const [_, dispatch] = useContext(StoreContext);
    const [isDiff, setIsDiff] = useState(false);

    const defaultValue = userRole || "unknown";

    const roles = Object.keys(role).map(r => ({ key: r, text: r, value: r }));

    const handleChange = (event, { value }) => {
        if (value !== defaultValue) {
            setIsDiff(true);
            dispatch({
                type: Act.FIREBASE_USER_ROLE_CHANGED,
                payload: { uid: userId, role: value }
            });
        } else {
            setIsDiff(false);
            dispatch({ type: Act.FIREBASE_USER_ROLE_CHANGED_CLEAN });
        }
    };

    return (
        <Dropdown
            error={isDiff}
            pointing="bottom"
            fluid
            selection
            options={roles}
            defaultValue={defaultValue}
            onChange={handleChange}
        />
    );
};

export default RoleDropdown;

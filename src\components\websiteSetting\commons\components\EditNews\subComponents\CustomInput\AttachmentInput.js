import React, { useEffect, useState } from "react";

// plugins
import { Segment, List } from "semantic-ui-react";
import { useSelector } from "react-redux";

//
import CustomInput from "./CustomInput";
import textConfig from "../../Utils/textConfig";
import RemoveBtn from "../CustomButton/RemoveBtn";
import clsName from "../../Utils/clsName";
import { isEmpty } from "../../../../../../../commons";
import { getFileName } from "../../Utils/saveDataUtils";

function AttachmentInput() {
    const { updateNewsInfo } = useSelector(state => state);
    const [allFiles, setAllFiles] = useState([]);

    useEffect(() => {
        if (updateNewsInfo.allfileAvailableAt) {
            setAllFiles(
                updateNewsInfo.allfileAvailableAt.map(url => getFileName(url))
            );
        }
    }, [updateNewsInfo]);

    return (
        <React.Fragment>
            <CustomInput className={clsName.Attachment} />
            <Segment>
                {isEmpty(allFiles) ? (
                    textConfig.Attachment_NO
                ) : (
                    <List bulleted>
                        {!isEmpty(allFiles) &&
                            allFiles.map((item, index) => (
                                <List.Item key={item}>
                                    {item}
                                    <RemoveBtn
                                        index={index}
                                        className={clsName.AttachmentRM}
                                    />
                                </List.Item>
                            ))}
                    </List>
                )}
            </Segment>
        </React.Fragment>
    );
}

export default AttachmentInput;

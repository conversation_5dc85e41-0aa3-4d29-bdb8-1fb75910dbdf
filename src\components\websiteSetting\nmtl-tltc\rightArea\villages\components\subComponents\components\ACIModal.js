import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import { <PERSON><PERSON>, Modal } from "semantic-ui-react";
import { isEmpty } from "../../../../../../../../commons";
import Api from "../../../../../../../../api/nmtl/Api";
import VillagesAct from "../../../VillagesAction";
import "../../EditVillagesDetail.scss";
import ModalFormArea from "../area/ModalFormArea";

const ACIModal = ({ onClick }) => {
    const dispatch = useDispatch();
    const {
        editingACIId,
        aciIsEdited,
        editingACIData,
        notifyUpdateActivityInfo,
        editingVillageId,
        aciListLength,
        aciList
    } = useSelector(state => state);
    const [aciUpdateData, setACIUpdateData] = useState([]);

    const combineZhEnKeys = data => {
        const result = {};
        // eslint-disable-next-line no-restricted-syntax
        for (const key in data) {
            if (key.endsWith("Zh") || key.endsWith("En")) {
                const baseKey = key.slice(0, -2);
                if (!result[baseKey]) {
                    result[baseKey] = [];
                }
                if (key.endsWith("Zh")) {
                    result[baseKey].unshift(data[key] ? `${data[key]}@zh` : "");
                } else {
                    result[baseKey].push(data[key] ? `${data[key]}@en` : "");
                }
            } else {
                result[key] = data[key];
            }
        }
        return result;
    };

    const handleConnectVillage = async () => {
        const aciIdList = aciList.map(el => el.id);
        const entrySrc = {
            classType: "VillageEvent",
            graph: "settings",
            srcId: editingVillageId,
            value: {
                hasActivityInfo: [...aciIdList]
            }
        };

        const entryDst = {
            classType: "VillageEvent",
            graph: "settings",
            srcId: editingVillageId,
            value: {
                hasActivityInfo: [...aciIdList, editingACIId]
            }
        };

        await axios.put(Api.getGeneric, { entrySrc, entryDst });
    };

    const onCancel = () => {
        dispatch({ type: VillagesAct.SET_IS_EDITED_ACI, payload: false });
        dispatch({ type: VillagesAct.SET_EDITING_ACI_DATA, payload: {} });
        dispatch({ type: VillagesAct.SET_EDITING_ACI_ID, payload: "" });
    };

    const onSave = async () => {
        const combinedSrcData = combineZhEnKeys(editingACIData);
        const combinedDstData = combineZhEnKeys(aciUpdateData);

        delete combinedSrcData.id;
        delete combinedDstData.id;

        const entrySrc = {
            graph: "settings",
            classType: "ActivityInfo",
            value: combinedSrcData,
            srcId: editingACIId
        };

        const entryDst = {
            graph: "settings",
            classType: "ActivityInfo",
            value: combinedDstData,
            srcId: editingACIId
        };

        await axios.put(Api.getGeneric, { entrySrc, entryDst });
        // 關閉modal
        dispatch({ type: VillagesAct.SET_IS_EDITED_ACI, payload: false });
        // 通知list更新Data
        dispatch({
            type: VillagesAct.SET_NOTIFY_UPDATE_ACVIVITY_INFO,
            payload: !notifyUpdateActivityInfo
        });
        dispatch({ type: VillagesAct.SET_EDITING_ACI_DATA, payload: {} });
    };

    const onCreate = async () => {
        const combinedDstData = combineZhEnKeys(aciUpdateData);
        combinedDstData.order = (aciListLength + 1).toString();
        delete combinedDstData.id;

        const entry = {
            graph: "settings",
            classType: "ActivityInfo",
            value: combinedDstData,
            srcId: editingACIId
        };

        await handleConnectVillage();
        await axios.post(Api.getGeneric, { entry });
        // 關閉modal
        dispatch({ type: VillagesAct.SET_IS_EDITED_ACI, payload: false });
        // 通知list更新Data
        dispatch({
            type: VillagesAct.SET_NOTIFY_UPDATE_ACVIVITY_INFO,
            payload: !notifyUpdateActivityInfo
        });
    };

    const aciUpdateDataHandler = (d, key, val) => {
        const updateObjectValue = (obj, tmpKey, tmpValue) => {
            if (!key) return [];
            const tmpObj = JSON.parse(JSON.stringify(obj));
            tmpObj[tmpKey] = tmpValue;
            return tmpObj;
        };
        const updatedData = updateObjectValue(d, key, val);
        setACIUpdateData(isEmpty(updatedData) ? d : updatedData);
    };

    useEffect(() => {
        setACIUpdateData(editingACIData);
    }, [editingACIData]);

    return (
        <Modal
            onClose={onClick}
            onOpen={onClick}
            open={aciIsEdited}
            size="large"
        >
            <Modal.Header>
                {isEmpty(editingACIId) ? "新增活動" : "編輯活動"}
            </Modal.Header>
            <Modal.Content
                style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "1rem"
                }}
            >
                <ModalFormArea
                    updateFct={aciUpdateDataHandler}
                    data={editingACIData}
                    updatedData={aciUpdateData}
                    editingPchId=""
                />
                <div />
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={onCancel} negative>
                    取消
                </Button>
                <Button
                    onClick={() =>
                        isEmpty(editingACIData) ? onCreate() : onSave()
                    }
                    positive
                >
                    儲存
                </Button>
            </Modal.Actions>
        </Modal>
    );
};
export default ACIModal;

{"name": "nmtl-backend-web-v2", "version": "2.0.0", "private": true, "dependencies": {"@botom/quill-resize-module": "^2.0.0", "@elastic/elasticsearch": "^7.9.1", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "axios": "^0.23.0", "babel-eslint": "^10.1.0", "base64url": "^3.0.1", "cross-env": "^7.0.3", "d3": "^7.1.1", "date-fns": "^3.3.1", "elasticsearch": "^16.7.1", "env-cmd": "^10.1.0", "exceljs": "^4.3.0", "file-saver": "^2.0.5", "firebase": "^7.17.0", "firebaseui": "^4.6.0", "jszip": "^3.10.1", "lodash": "^4.17.21", "markdown-it": "^13.0.1", "memoize-one": "^6.0.0", "prop-types": "^15.7.2", "query-string": "^7.1.1", "quill-html-edit-button": "^2.2.12", "react": "^16.13.1", "react-autosize-textarea": "^7.1.0", "react-beautiful-dnd": "^13.1.1", "react-data-export": "^0.6.0", "react-datepicker": "^4.25.0", "react-dom": "^16.13.1", "react-dropdown-tree-select": "^2.7.1", "react-dropzone": "^11.2.4", "react-easy-crop": "^5.0.7", "react-firebaseui": "^5.0.2", "react-hot-toast": "^2.5.2", "react-input-files": "^1.2.0", "react-intersection-observer": "^8.30.3", "react-query": "^3.39.3", "react-quill": "^2.0.0", "react-redux": "7.2.4", "react-router": "^5.2.1", "react-router-dom": "^5.3.0", "react-scripts": "^3.4.3", "react-select": "^5.1.0", "react-select-virtualized": "^5.6.0", "react-table": "^7.8.0", "react-virtualized-auto-sizer": "^1.0.24", "react-window": "^1.8.10", "react-windowed-select": "^5.2.0", "redux": "4.1.0", "semantic-ui-css": "2.4.1", "semantic-ui-react": "1.0.0", "styled-components": "^5.3.11", "swagger-ui-react": "4.12.0", "sweetalert2": "^10.10.1", "systeminformation": "^5.9.7", "turndown": "^7.1.1", "twchar": "1.1.2", "typescript": "^4.0.3", "xlsx": "^0.16.9"}, "scripts": {"start": "cross-env PORT=3030 react-scripts start", "build": "env-cmd -f .env.production react-scripts build", "build:tlsg": "env-cmd -f .env.production_tlsg react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"dotenv": "^16.0.1", "eslint": "^6.6.0", "eslint-config-airbnb": "^17.1.0", "eslint-config-prettier": "^3.6.0", "eslint-formatter-pretty": "^1.3.0", "eslint-loader": "^2.1.1", "eslint-plugin-import": "^2.14.0", "eslint-plugin-jsx-a11y": "^6.2.1", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-react": "^7.12.4", "prettier": "^1.16.4", "sass": "^1.54.9"}}
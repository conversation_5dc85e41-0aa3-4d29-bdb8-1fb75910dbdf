import React from 'react';
import { Form, Checkbox } from 'semantic-ui-react';

function ImageTypeSelect({ imageWebOrMobile, setImageWebOrMobile }) {
    return (
        <div
            style={{
                display: 'flex',
                alignItems: 'center',
                marginLeft: '15px',
            }}
        >
            {/* 選擇上傳圖片樣式 */}
            <Form style={{ display: 'flex', alignItems: 'center' }} className="ImageTypeSelectForm">
                <Checkbox
                    radio
                    label="網頁版"
                    checked={imageWebOrMobile === 'web'}
                    onClick={() => {
                        setImageWebOrMobile('web');
                    }}
                    style={{ marginRight: '3px' }}
                />
                <Checkbox
                    radio
                    label="手機版"
                    checked={imageWebOrMobile === 'mobile'}
                    onClick={() => {
                        setImageWebOrMobile('mobile');
                    }}
                />
            </Form>
        </div>
    );
}

export default ImageTypeSelect;

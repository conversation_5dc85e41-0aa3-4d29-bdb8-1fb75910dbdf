import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";

// semantic ui
import { Grid, Input, Icon } from "semantic-ui-react";

// components
import SaveBtn from "../CustomButton/SaveBtn";
import ResultModal from "../../../common/CustomModal/ResultModal";

// config
import fbConfig from "../../common/fbConfig";
import RPAct from "../../reportIssueAction";
import textConfig from "../../common/textConfig";

// utils
import { isEmpty } from "../../../../commons";
import createEmailData from "../../common/createEmailData";
import {
    createHistoryDesc,
    getAllData,
    saveSingleRPIssue
} from "../../common/utils";

// hooks
import useCusContext from "../../../common/hooks/useCusContext";
import useGetSubjectOPs from "../../../common/hooks/useGetSubjectOPs";

// For ContentTable use only
function ReportField() {
    const [oldState] = useCusContext();
    const { displayName } = oldState.user;
    const { groupInfo } = oldState.data;
    const subOptions = useGetSubjectOPs(groupInfo);

    const dispatch = useDispatch();
    const { rpSubject, cntData, newAllData } = useSelector(
        state => state.report
    );

    const [locData, setLocData] = useState(cntData);
    const [subObj, setSubObj] = useState({});

    // SaveBtn
    const [loading, setLoading] = useState(false);

    // Result modal
    const [openModal, setOpenModal] = useState(false);
    const [modalMessage, setModalMessage] = useState("");

    useEffect(() => {
        // init staffEmail column
        setLocData({ ...locData, [fbConfig.staffEmail]: [""] });
    }, []);

    useEffect(() => {
        if (isEmpty(subOptions)) return;
        const findObj = subOptions.find(el => el.value === rpSubject);
        if (findObj) {
            setSubObj(findObj);
        }
    }, [subOptions]);

    const handleAdd = () => {
        const tmpArr = locData[fbConfig.staffEmail];
        tmpArr.push("");
        setLocData({ ...locData, [fbConfig.staffEmail]: tmpArr });
    };

    const handleDel = idx => {
        const tmpArr = locData[fbConfig.staffEmail].filter(
            (el, tmpIdx) => tmpIdx !== idx
        );
        setLocData({ ...locData, [fbConfig.staffEmail]: tmpArr });
    };

    const handleInput = (value, idx) => {
        const tmpArr = locData[fbConfig.staffEmail];
        tmpArr[idx] = value;
        setLocData({ ...locData, [fbConfig.staffEmail]: tmpArr });
    };

    const saveMailCallBack = msg => {
        setLoading(false);
        setOpenModal(true);
        setModalMessage(msg);
    };

    const storeMailHistory = () => {
        const tmpAllData = JSON.parse(JSON.stringify(newAllData));
        const tmpCurData = tmpAllData.find(el => el.id === locData.id);
        if (!tmpCurData) return;

        // detect wrong mail format
        const mailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        tmpCurData[fbConfig.staffEmail] = locData[fbConfig.staffEmail]
            .filter(str => str)
            .filter(str => mailRegex.test(str));

        const tmpHisObj = {
            [fbConfig.historyProp.time]: Date.now(),
            [fbConfig.historyProp.status]: createHistoryDesc(
                {},
                tmpCurData,
                displayName
            )
        };
        tmpCurData[fbConfig.history].push(tmpHisObj);

        // save history data to firestore
        saveSingleRPIssue(tmpCurData.id, tmpCurData)
            .then(res => {
                if (res.status === "OK") {
                    getAllData(rpSubject, dispatch);
                }
            })
            .catch(err => console.log(err));
    };

    const handleSave = () => {
        setLoading(true);
        createEmailData(subObj.text, locData)
            .then(res => {
                const msg = res
                    ? textConfig.SuccessMessage.SENDMAIL
                    : textConfig.ErrorMessage.OTHER;
                saveMailCallBack(msg);

                if (res) {
                    // 發送信件成功再新增歷史紀錄
                    storeMailHistory();
                }
            })
            .catch(err => {
                const msg = `${textConfig.ErrorMessage.SENDMAIL}\n${err}`;
                saveMailCallBack(msg);
            });
    };

    const closeResModal = () => {
        setOpenModal(false);
        dispatch({
            type: RPAct.SET_RPCNTMODAL,
            payload: false
        });
    };

    return (
        <Grid columns={2}>
            {Object.hasOwn(locData, fbConfig.staffEmail) &&
                locData[fbConfig.staffEmail].map((emailStr, idx) => (
                    <Grid.Row
                        style={{
                            display: "flex",
                            justifyContent: "space-between",
                            margin: "0.5rem auto",
                            padding: "0"
                        }}
                        key={`${idx.toString()}`}
                    >
                        <Grid.Column
                            style={{
                                display: "flex",
                                flex: "1 1 auto",
                                alignItems: "center"
                            }}
                        >
                            <Icon
                                name="plus"
                                onClick={handleAdd}
                                style={{ cursor: "pointer" }}
                            />
                            <Input
                                style={{ flex: "1 1 auto" }}
                                placeholder="處理回報者Email"
                                type="email"
                                onChange={(evt, data) =>
                                    handleInput(data.value, idx)
                                }
                                value={locData[fbConfig.staffEmail][idx]}
                            />
                        </Grid.Column>
                        <Grid.Column
                            width={3}
                            style={{ display: "flex", alignItems: "center" }}
                        >
                            {idx !== locData[fbConfig.staffEmail].length - 1 ? (
                                <Icon
                                    color="red"
                                    name="minus"
                                    onClick={() => handleDel(idx)}
                                    style={{ cursor: "pointer" }}
                                />
                            ) : (
                                <SaveBtn
                                    label={textConfig.label.send}
                                    loading={loading}
                                    handleClick={handleSave}
                                />
                            )}
                        </Grid.Column>
                    </Grid.Row>
                ))}
            <ResultModal
                openModal={openModal}
                modalMessage={modalMessage}
                onClose={closeResModal}
                onClick={closeResModal}
            />
        </Grid>
    );
}

export default ReportField;

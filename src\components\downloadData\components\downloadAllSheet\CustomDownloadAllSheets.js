import React, { useState, useEffect } from 'react';

// ui
import { Button, Progress } from 'semantic-ui-react';
// firebase api
import { getSheets } from '../../../../api/firebase/cloudFirestore';
// common
import { isEmpty } from '../../../../commons';
import CustomMultiSheetsExcelJS from '../../../dataset/subComponents/commonComp/CustomMultiSheetsExcelJS';
// nmtl api
import { createHistoryEvent } from '../history/common/common';
import { exportExcel } from '../../../common/sheetCrud/utils';
import { filterColumn, filterSheet } from '../../../../commons/filterGroup';
import { readNmtlData } from '../../../../api/nmtl';

const getYMDNow = () => {
    const dt = new Date();
    return `${dt.getFullYear()}_${dt.getMonth() + 1}_${dt.getDate()}`;
};

// dataset: wikipedia
// datasetName: 維基百科
// displayName: username
const CustomDownloadAllSheets = ({ dataset, datasetName, displayName, groupInfo }) => {
    // force refresh CustomExcelFile component for download excel
    const [refreshKey, setRefreshKey] = useState(0);
    // isLoading
    const [isLoading, setIsLoading] = useState(false);
    // excel data
    // const [excelData, setExcelData] = useState(undefined);
    const [processIndex, setProcessIndex] = useState(0);
    const [error, setError] = useState(undefined);
    const [allSheets, setAllSheets] = useState([]);

    // get sheet header
    useEffect(() => {
        const handleGetSheets = async () => {
            let sheetsData = await getSheets();
            // 根據群組限制欄位下載表單
            if (groupInfo.sheets) {
                sheetsData = sheetsData
                    .map((el) => ({ ...el, key: el.id }))
                    .filter((sheetObj) => filterSheet(sheetObj, groupInfo))
                    .map((el) => ({
                        ...el,
                        headers: el.headers.filter((tmpH) => filterColumn(el.key, tmpH, groupInfo)),
                    }));
            }

            setAllSheets(sheetsData);
            if (sheetsData.error) {
                setError(sheetsData.error);
            }
        };

        setIsLoading(true);
        handleGetSheets();
        setIsLoading(false);
    }, [groupInfo]);

    const handleDownload = async () => {
        if (!dataset) {
            return;
        }
        if (!allSheets || allSheets.length < 1) {
            setError('表單下載中...');
            return;
        }

        // set loading status
        setIsLoading(true);

        const allData = {};

        for (let idx = 0; idx < allSheets.length; idx += 1) {
            setProcessIndex(idx);
            const sheetId = allSheets?.[idx]?.id;
            const st = allSheets[idx];

            // get sheet header
            const header = st.headers;

            // get list ids
            const getListApi = st.getClassList.replace('{ds}', dataset);

            // get content
            const getContentApi = st.getTable2;

            // get idList first because of Publication、Article data is too large
            const idList = ['Publication', 'Article'].includes(sheetId)
                ? await readNmtlData(getListApi, 100000).then(({ data }) =>
                      data.reduce((acc, { id }) => {
                        if (id) {
                            acc.push(id);
                        }
                        return acc;
                    }, []),
                )
                : '';

            // idList 為空字串，因為下載全部表單不會有搜尋關鍵字的情況
            const { data: contentData, exportHeader } = await exportExcel(
                dataset,
                idList,
                getContentApi,
                header,
            );

            allData[st.label] = {};
            allData[st.label].headers = exportHeader;
            // 表單的排序
            allData[st.label].order = st.order;

            if (!isEmpty(contentData) && !isEmpty(dataset)) {
                allData[st.label].data = contentData;
                setRefreshKey(refreshKey + 1);
            }
        }

        // history
        if (Object.keys(allData).length > 0) {
            createHistoryEvent(displayName, '下載全部表單', datasetName);
        } else {
            createHistoryEvent(displayName, '下載全部表單失敗', datasetName);
        }
        // setExcelData(allData);

        if (!isEmpty(dataset) && !isEmpty(allData)) {
            CustomMultiSheetsExcelJS({ allData, filename: `${dataset}_${getYMDNow()}` });
        }

        // set loading status
        setIsLoading(false);
    };

    const customStyle = {
        marginBottom: '1em',
        marginRight: '1em',
        minWidth: '200px',
    };

    return (
        <div style={{ marginTop: '20px' }}>
            <Button
                disabled={isLoading}
                loading={isLoading}
                color="orange"
                onClick={handleDownload}
                style={customStyle}
            >
                下載 Excel ({datasetName})
            </Button>
            {isLoading && (
                <div style={{ width: '20%' }}>
                    為確保下載順利完成，請勿關閉或離開此頁面!
                    <div style={{ width: '100%' }}>
                        <Progress
                            percent={((processIndex / allSheets.length) * 100).toFixed(1)}
                            indicating
                            progress
                        />
                    </div>
                </div>
            )}
            {error}
        </div>
    );
};

export default CustomDownloadAllSheets;

import React from "react";
import { But<PERSON>, Modal } from "semantic-ui-react";
import "../../../EditCate.scss";

const CustomRequiredModal = ({ onClick, open }) => {
    const handleClick = () => {
        onClick(!open);
    };
    return (
        <Modal onClose={onClick} onOpen={onClick} open={open}>
            <Modal.Header>警告</Modal.Header>
            <Modal.Content>
                <Modal.Description>
                    請確認是否已填寫完成所有必填欄位
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={handleClick} positive>
                    確認
                </Button>
            </Modal.Actions>
        </Modal>
    );
};
export default CustomRequiredModal;

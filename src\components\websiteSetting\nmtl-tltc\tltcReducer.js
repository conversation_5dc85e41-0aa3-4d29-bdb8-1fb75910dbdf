import tltcAct from "./tltcAction";

const tltcInitState = {
    rellinkTypes: [], // 相關連結頁，type選項
    rellinkTable: [], // 相關連結頁，表格
    rellinkEditId: "" // 相關連結頁，編輯ID
};

const tltcReducer = (state, action) => {
    try {
        if (!action.localType) {
            throw new Error("Please give localType params!");
        }
        switch (action.localType) {
            case tltcAct.SET_RELLINKTYPES:
                return { ...state, rellinkTypes: action.payload };
            case tltcAct.SET_RELLINKTABLE:
                return { ...state, rellinkTable: action.payload };
            case tltcAct.SET_RELLINKEDITID:
                return { ...state, rellinkEditId: action.payload };
            default:
                return state;
        }
    } catch (err) {
        console.error(err);
    }
    return state;
};

export { tltcReducer, tltcInitState };

import base64url from "base64url";
import axios from "axios";
import Api from "../../../api/nmtl/Api";
import { PREFIX_FLT, FULLTEXT_MD2PDF_DIR } from "./config";
import { fileServerAPI } from "../../../api/fileServer";
// import { createNmtlData } from "../../../api/nmtl";

/**
 *
 * @param count {number}
 * @returns {Promise<*[]>}
 */
// eslint-disable-next-line import/prefer-default-export
export const getFltId = async count => {
    try {
        const url = Api.getWords
            .replace("{type}", base64url.encode(PREFIX_FLT))
            .replace("{reserved}", base64url.encode("0"));
        const res = await axios.get(url);
        const curFltId = res?.data?.data;
        const url2 = Api.getWords
            .replace("{type}", base64url.encode(PREFIX_FLT))
            .replace(
                "{reserved}",
                base64url.encode(
                    count == null || !Number.isNaN(count) ? "1" : String(count)
                )
            );
        const res2 = await axios.get(url2);
        const lastFltId = res2?.data?.data;
        // // e.g. 1001,1002,1003
        const nums = [];
        for (
            let i = Number.parseInt(curFltId, 10) + 1;
            i <= Number.parseInt(lastFltId, 10);
            i += 1
        ) {
            nums.push(`${PREFIX_FLT}${i}`);
        }
        return nums;
    } catch (e) {
        console.log("e", e);
        //
        return [];
    }
};

/**
 * post to file-server api for 全文字詞分析
 * @param payload
 * @returns {Promise<AxiosResponse<unknown>|*>}
 */
export const postFullText = async payload => {
    try {
        const url = fileServerAPI.fullText;
        const res = await axios.post(url, payload);
        return res;
    } catch (e) {
        return e;
    }
};

/**
 * post to file-server api for 全文字詞分析
 * @param payload
 * @returns {Promise<AxiosResponse<unknown>|*>}
 */
export const putFullText = async payload => {
    try {
        const url = fileServerAPI.fullText;
        const res = await axios.put(url, payload);
        return res;
    } catch (e) {
        return e;
    }
};

// fileName 加入 時間戳記
const makeFileName = (label, fullWorkLang) => {
    const date = new Date();
    try {
        // 移除檔名中不合法自元
        const safeLabel = (label || "").replace(/[/\\?%*:|"<>]/g, "-");
        const dateInfo = [
            date.getFullYear(),
            date.getMonth(),
            date.getDate(),
            date.getMonth(),
            date.getHours(),
            date.getMinutes(),
            date.getSeconds()
        ];
        // 在檔名中加入 語系 資訊 e.g. abcde__en.pdf, abcde__zh.pdf
        const langTag = fullWorkLang ? `__${fullWorkLang}` : "";
        return `${safeLabel}__${dateInfo.join("-")}${langTag}`;
    } catch (e) {
        return label;
    }
};

export const ANALYSIS_TYPE = {
    start: "start", // 開始全文字詞分析流程
    reAnalysis: "reAnalysis" // 重新全文字詞分析流程
};

/**
 * 開始全文字詞分析流程 or 重新全文字詞分析流程
 * @param payload
 * @param type {'start'|'reAnalysis'} 區分為"開始分析" or "重新分析"
 * @param onFinish
 * @param onError
 * @returns {Promise<{error: string}>}
 */
export const mainFltAnalysis = async (payload, type, onFinish, onError) => {
    try {
        // byPass: 跳過哪些步驟
        const { data: rowData, byPass } = payload || {};

        const {
            id,
            _type,
            graph,
            fileAvailableAt,
            fltId,
            fullWorkAvailableAt,
            fullWorkLang, // 全文語系 e.g.["zh","en",""], 當前要處理的語系會放在最前面
            docId, // firestore 的 doc id
            label // 著作/文章名稱
        } = rowData;
        // check

        if (!(id && graph)) {
            const msg = "id and graph no exist";
            if (typeof onError === "function") {
                onError({ error: msg });
            }
            return { error: msg };
        }
        if (!(fileAvailableAt || fullWorkAvailableAt)) {
            const msg =
                "one of fileAvailableAt and fullWorkAvailableAt no exist";
            if (typeof onError === "function") {
                onError({ error: msg });
            }
            return { error: msg };
        }
        // 某些情況可能已字詞分析(標定狀態為reAnalysis),但沒有 docId,所以可以pass
        // eslint-disable-next-line camelcase
        if (type === ANALYSIS_TYPE.reAnalysis && !docId) {
            // const msg = "doc_fileAvailableAt no exist";
            // return onError({ error: msg });
        }

        let tmpFileAvailMd; // 原始格式檔案路徑
        let tmpFileAvailPdf; // 生成pdf檔案路徑(存到資料庫)

        // 取得 string
        const safeStr = srcStr => {
            let dstStr = "";
            if (Array.isArray(srcStr)) {
                dstStr = srcStr.length > 0 ? srcStr[0] : "";
            } else {
                dstStr = srcStr || "";
            }
            return dstStr;
        };
        // 有全文,但是尚未生成 pdf, 預先建立 filename
        if (fullWorkAvailableAt) {
            // 語系標籤文字
            const fullWorkLangStr = safeStr(fullWorkLang);

            // 建立檔案路徑 tmpFileAvailMd,tmpFileAvailPdf
            // 檔名使用 id, 不要使用 label, 避免讀取檔案失敗(pdf 轉 png)
            // 檔名有帶語系
            tmpFileAvailMd = `${FULLTEXT_MD2PDF_DIR}/${makeFileName(
                id,
                fullWorkLangStr
            )}.md`;
            const langTag = fullWorkLangStr ? `@${fullWorkLangStr}` : "";
            // 檔名有帶語系 & 寫入 stardog 的 value 也有帶語系標籤
            tmpFileAvailPdf = `${FULLTEXT_MD2PDF_DIR}/${makeFileName(
                id,
                fullWorkLangStr
            )}.pdf${langTag}`;
        }

        // get filtId
        let nextFltId = fltId;
        if (!fltId) {
            const nums = await getFltId(1);
            // eslint-disable-next-line prefer-destructuring
            nextFltId = nums[0];
        }

        if (!nextFltId) {
            const msg = "get next fltId failed";
            if (typeof onError === "function") {
                onError({ error: msg });
            }
            return { error: msg };
        }

        // eslint-disable-next-line consistent-return
        const start = async () => {
            // 語系標籤文字
            const fullWorkLangStr = safeStr(fullWorkLang);

            const reqPayload = {
                fileAvailableAt: fileAvailableAt || tmpFileAvailMd,
                fltId: nextFltId,
                fullWorkAvailableAt,
                firestoreColDocId: docId, // firestore 的 doc id
                byPass: byPass || [],
                fullWorkLang,
                title: fullWorkLangStr ? `${label}@${fullWorkLangStr}` : label, // 書名/文章名 e.g.望玉山記@zh, 望玉山記@en
                srcId: id, // 攜帶 srcId, 用於寫入全文
                graph, // 攜帶 graph, 用於寫入全文
                classType: _type // 攜帶 classType, 用於寫入全文
            };

            const fetchMethod =
                type === ANALYSIS_TYPE.start ? postFullText : putFullText;
            const res = await fetchMethod(reqPayload);
            //
            if (res instanceof Error) {
                // handle error
                if (typeof onError === "function") onError(res);
                return { error: res };
            }
            if ((res?.data?.status || "").toLowerCase() === "ok") {
                // 更新 firestore 狀態
                if (typeof onFinish === "function") onFinish(res);
                return { error: null };
            }
        };

        // 建立 文章或著作與 fltId 的連結
        if (!fltId || !fileAvailableAt) {
            const entry = {
                graph,
                // 如果 option 是 PersonIDs，其值為 srcId, "" 為新建
                srcId: id,
                classType: _type,
                value: {
                    hasFullText: nextFltId,
                    // 必須將 tmpFileAvailableAt 副檔名轉換成
                    fileAvailableAt: [fileAvailableAt || tmpFileAvailPdf]
                }
            };
            const apiUrl = Api.getGeneric;
            const res = await axios.post(apiUrl, { entry });
            if (res.data) {
                return start();
            }
        } else {
            return start();
        }
    } catch (e) {
        if (typeof onError === "function") onError(e);
        return { error: e };
    }
};

// 開始分析
export const postFltAnalysis = (payload, onFinish, onError) =>
    mainFltAnalysis(payload, ANALYSIS_TYPE.start, onFinish, onError);

// 重新分析
export const putFltAnalysis = (payload, onFinish, onError) =>
    mainFltAnalysis(payload, ANALYSIS_TYPE.reAnalysis, onFinish, onError);

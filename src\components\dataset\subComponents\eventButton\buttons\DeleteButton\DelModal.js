import React, { useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";

// semantic ui
import { <PERSON><PERSON>, Modal } from "semantic-ui-react";

// components
import WarningContent from "./WarningContent";
import BasicContent from "./BasicContent";

// context
import { StoreContext } from "../../../../../../store/StoreProvider";

// utils & config
import { isEmpty } from "../../../../../../commons";
import {
    convertToGenericSingle,
    PublicationInfo,
    PEICES_INFO
} from "../../../../../common/sheetCrud/sheetCrudHelper";
import { Person } from "../../../CreateComp/createConfig";
import warningAction, { getPubIds } from "./utils/deletePubData";

// api
import Api from "../../../../../../api/nmtl/Api";
import {
    createNmtlData,
    deleteNmtlData,
    readNmtlData
} from "../../../../../../api/nmtl";
import Act from "../../../../../../store/actions";
import { createHistoryEvent } from "../../../../../downloadData/components/history/common/common";

function DelModal({ open, setOpen }) {
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { content, sheet, mainSubject } = state.data;
    const { dataset } = mainSubject.selected;
    const { key: sheetName, contentWritePath, hasTab } = sheet.selected;
    const { checked, rows } = content;
    const { headerActiveName } = state.common;
    const { displayName } = state.user;
    const { tabClass, tabIndex } = sheet.tabKey;

    const [warnData, setWarnData] = useState({});

    // FIXME:: 針對人物表單與外譯房著作表單特殊處理
    useEffect(() => {
        if (sheetName === PublicationInfo) {
            const { ids } = getPubIds(sheetName, tabClass, checked, rows);

            if (!ids) return;
            const apiStr = Api.getTransBookByOri(dataset, ids);

            readNmtlData(apiStr).then(res => {
                setWarnData({ transPubArr: res });
            });
        } else if (sheetName === Person || sheetName === "BasicInfo") {
            const { ids } = getPubIds(sheetName, tabClass, checked, rows);

            if (!ids) return;
            const apiStr = Api.getPubByPerName(dataset, ids);

            // fetch checked id data for authorName and translatorName in dataset chose
            readNmtlData(apiStr).then(res => {
                // 再檢查一次，顯示名稱要完全對應perName才算
                const tmpRes = JSON.parse(JSON.stringify(res));
                tmpRes.data = tmpRes.data.filter(({ perName, checkValue }) => {
                    const spValArr = checkValue.split("、");
                    return spValArr.includes(perName);
                });
                setWarnData({ pubCntArr: tmpRes });
            });
        }
    }, []);

    // 記錄在歷史訊息的欄位資訊
    const columns = [
        headerActiveName,
        mainSubject.selected.value,
        sheet.selected.value
    ];

    const handleDelete = async () => {
        // console.log("I am EventButton:handleUpdate");

        const apiWritePath =
            contentWritePath || hasTab[tabIndex]?.contentWritePath || null;

        // 新增警告會更新其他表單資料流程
        if (!isEmpty(warnData)) {
            const res = await warningAction(
                warnData,
                dataset,
                apiWritePath,
                sheetName,
                tabClass,
                user
            );

            if (res.status !== "OK") {
                return;
            }
        }

        if (
            isEmpty(content) ||
            isEmpty(apiWritePath) ||
            isEmpty(dataset) ||
            isEmpty(sheetName)
        ) {
            return;
        }

        // merge rows and changed
        if (isEmpty(checked) || isEmpty(rows)) {
            return;
        }

        let historyMsg = "";
        // promise list
        const promises = checked.map(async item => {
            let targetData = rows[item.rowId];

            if (tabClass === PEICES_INFO) {
                const [rIdx, cIdx] = item.rowId.split("-");

                if (!rIdx || !cIdx) return null;

                targetData = Object.keys(rows[rIdx]).reduce((acc, cur) => {
                    if (cur === "transList") {
                        return Object.assign(acc, rows[rIdx][cur][cIdx]);
                    }
                    acc[cur] = rows[rIdx][cur];

                    return acc;
                }, {});
            }

            const apiUrl = Api.getGeneric;
            const entry = convertToGenericSingle(
                targetData,
                dataset,
                apiWritePath
            );

            if (!entry) {
                return null;
            }
            historyMsg += JSON.stringify(entry);

            // 權位檔未融合資料都刪除只留宣告
            if (
                entry.value.hasOwnProperty("unintegrated") &&
                entry.value.unintegrated
            ) {
                let newEntry = JSON.parse(JSON.stringify(entry));

                if (entry.value.hasOwnProperty("graph")) {
                    newEntry.graph = entry.value.graph[0];
                } else if (entry.value.hasOwnProperty("literary")) {
                    newEntry.graph = entry.value.literary[0];
                }

                const newItem = {
                    graph: newEntry.graph,
                    classType: "Person",
                    srcId: entry.srcId,
                    value: {}
                };

                const deleteResult = await deleteNmtlData(
                    user,
                    apiUrl,
                    dataset,
                    sheetName,
                    newEntry
                );
                await createNmtlData(user, apiUrl, item, sheetName, newItem);
                return deleteResult;
            }

            return deleteNmtlData(user, apiUrl, dataset, sheetName, entry);
        });

        // get all result
        Promise.allSettled(promises)
            .then(results => {
                // count
                let successCount = 0;
                let errorCount = 0;
                let msg = "Delete";
                results.forEach(res => {
                    if (res.value) {
                        successCount += 1;
                    } else {
                        errorCount += 1;
                    }
                });

                if (errorCount > 0) {
                    msg =
                        "Delete Error: should relate to person or organization";
                }
                const message = {
                    title: msg,
                    success: successCount,
                    error: errorCount,
                    renderSignal: `delete-${new Date().getTime()}`
                };
                // alert message
                dispatch({
                    type: Act.DATA_MESSAGE,
                    payload: message
                });
                // 歷史紀錄，刪除成功
                createHistoryEvent(
                    displayName,
                    "刪除",
                    `${columns.join("/")}：${historyMsg}`
                );
            })
            .catch(error => {
                // 歷史紀錄，刪除失敗
                createHistoryEvent(
                    displayName,
                    "刪除失敗",
                    `${columns.join("/")}：${historyMsg}`,
                    error
                );
            });

        // clean edit record after updated
        dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });

        if (checked.length === Object.values(rows).length) {
            dispatch({ type: Act.DATA_PAGINATION_ACTIVE_PAGE_INIT });
        }

        // close
        setOpen(false);
        setWarnData({});
    };

    return (
        <Modal
            open={open}
            onClose={() => setOpen(false)}
            onOpen={() => setOpen(true)}
        >
            <Modal.Header>刪除確認</Modal.Header>
            {/* 警告訊息 */}
            {Object.values(warnData).some(({ data }) => !isEmpty(data)) && (
                <WarningContent warnData={warnData} />
            )}

            {/* 資料訊息 */}
            <BasicContent />

            {/* Confirm & Cancel button */}
            <Modal.Actions>
                <Button onClick={handleDelete} color="green">
                    確認
                </Button>
                <Button
                    onClick={() => {
                        setWarnData({});
                        setOpen(false);
                    }}
                    color="red"
                >
                    取消
                </Button>
            </Modal.Actions>
        </Modal>
    );
}

DelModal.propTypes = {
    open: PropTypes.bool,
    setOpen: PropTypes.func
};

DelModal.defaultProps = {
    open: false,
    setOpen: () => {}
};

export default DelModal;

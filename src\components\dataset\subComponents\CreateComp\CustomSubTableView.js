import React, { useContext } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "semantic-ui-react";
import CustomHeaderInformation from "../headerInformation";

import "./CustomTableView.scss";
import { PublicationInfo } from "../../../common/sheetCrud/sheetCrudHelper";
import iconDrag from "../../../../images/icon_drag.svg";
import Act from "../../../../store/actions";
import { updateObjectValue } from "./helper";
import { StoreContext } from "../../../../store/StoreProvider";

const CustomSubTableView = ({
    itemAt,
    sheetName,
    header,
    content,
    createState,
    setCallback,
    setCreateState,
    menuName,
    isDisableSecondInput,
    rValue,
    options,
    cloneLocalCreateState,
    setCloneLocalCreateStateFct,
    isInDraggingMode,
    curPage
}) => {
    const [state, dispatch] = useContext(StoreContext);
    const tableCellValue = (row, rowIdx) =>
        row.map((rowCell, cellIdx) => {
            const {
                comp: Component,
                header: rowHeader,
                defaultValue,
                subHeader,
                colSpan,
                rowSpan,
                compProp
            } = rowCell;

            const headerInfo = header.find(el => el.id === rowHeader);

            let hdLabel = headerInfo?.label || rowHeader;

            if (sheetName === PublicationInfo && rowHeader === "srcId") {
                hdLabel = "原文書ID";
            }

            if (rowHeader === "hasTranslationBook") {
                hdLabel = "翻譯書ID";
            }

            const cellValue =
                createState &&
                menuName !== undefined &&
                Object.hasOwn(createState, rowHeader)
                    ? createState[rowHeader]
                    : defaultValue;

            const deleteHandler = () => {
                const key =
                    rowHeader === "hasAuthor" ? "authorName" : "translatorName";

                function processData(labels, labelToRemove, ids) {
                    const idToRemove = labelToRemove.id;

                    const labelToRemoveZh = labelToRemove.label
                        .replace(/@zh|@en/g, "")
                        .split("、")[0];
                    const labelToRemoveEn = labelToRemove.label
                        .replace(/@zh|@en/g, "")
                        .split("、")[1];

                    function getLanguageTag(namesString) {
                        if (namesString.endsWith("@zh")) {
                            return "@zh";
                        }
                        if (namesString.endsWith("@en")) {
                            return "@en";
                        }
                        return null;
                    }

                    const processedLabels = labels.map(labelStr => {
                        const langTag = getLanguageTag(labelStr);
                        const splitLabelArr = labelStr
                            .replace(/@zh|@en/g, "")
                            .split("、");
                        return splitLabelArr.filter(
                            label =>
                                label !==
                                (langTag === "@zh"
                                    ? labelToRemoveZh
                                    : labelToRemoveEn)
                        );
                    });
                    const languages = ["zh", "en"];

                    const outputLabels = processedLabels.map(
                        (subArray, index) =>
                            `${subArray.join("、")}@${languages[index]}`
                    );

                    const outputIds = ids.filter(id => id !== idToRemove);

                    return { outputLabels, outputIds };
                }

                const { outputLabels, outputIds } = processData(
                    cloneLocalCreateState[key],
                    rValue,
                    cloneLocalCreateState[rowHeader]
                );

                // 只剩最後一個的時候，僅清空值，保持畫面顯示
                if (cloneLocalCreateState[rowHeader].length === 1) {
                    // 先更新id，再更新otherName
                    const updatedIdObj = updateObjectValue(
                        cloneLocalCreateState,
                        rowHeader,
                        ["create"]
                    );
                    const updatedData = updateObjectValue(updatedIdObj, key, [
                        "新增@zh",
                        "新增@en"
                    ]);

                    dispatch({
                        type: Act.DATA_SET_CLONE_CREATE_STATE,
                        payload: updatedData
                    });
                    setCloneLocalCreateStateFct(updatedData);
                } else {
                    // 先更新id，再更新otherName
                    const updatedIdObj = updateObjectValue(
                        cloneLocalCreateState,
                        rowHeader,
                        outputIds
                    );
                    const updatedData = updateObjectValue(
                        updatedIdObj,
                        key,
                        outputLabels
                    );

                    dispatch({
                        type: Act.DATA_SET_CLONE_CREATE_STATE,
                        payload: updatedData
                    });
                    setCloneLocalCreateStateFct(updatedData);
                }
            };

            return (
                <React.Fragment
                    key={`CreateContent-${sheetName}-${rowIdx}-${cellIdx}`}
                >
                    {isInDraggingMode &&
                        (rowHeader === "hasAuthor" ||
                            rowHeader === "hasTranslator") && (
                        <>
                            <Table.Cell
                                colSpan={1}
                                width={1}
                                textAlign="center"
                            >
                                <img src={iconDrag} alt="drag" />
                            </Table.Cell>
                            <Table.Cell
                                colSpan={1}
                                width={1}
                                textAlign="center"
                                onClick={deleteHandler}
                            >
                                <Button icon="trash" color="red" />
                            </Table.Cell>
                        </>
                    )}
                    <Table.Cell
                        className="TableBody__Row__header"
                        rowSpan={rowSpan || 1}
                    >
                        <Header as="h5">
                            {headerInfo?.mark ? `*${hdLabel}` : hdLabel}
                            <CustomHeaderInformation id={rowCell.header} />
                            {subHeader && (
                                <Header.Subheader content={subHeader} />
                            )}
                        </Header>
                    </Table.Cell>
                    <Table.Cell
                        className="TableBody__Row__cellInfo"
                        colSpan={colSpan || 1}
                        rowSpan={rowSpan || 1}
                    >
                        {Component ? (
                            <Component
                                key={`CreateContent-${sheetName}-${rowIdx}-${rowCell.header}-Component`}
                                itemAt={itemAt}
                                rowIdx={rowIdx}
                                cellId={rowCell.header}
                                setCallback={setCallback}
                                setCreateState={setCreateState}
                                createState={createState}
                                defaultValue={cellValue || defaultValue}
                                menuName={menuName}
                                isDisableSecondInput={isDisableSecondInput}
                                rValue={rValue}
                                options={options}
                                cloneLocalCreateState={cloneLocalCreateState}
                                setCloneLocalCreateStateFct={
                                    setCloneLocalCreateStateFct
                                }
                                isInDraggingMode={isInDraggingMode}
                                curPage={curPage}
                                {...compProp}
                            />
                        ) : (
                            ""
                        )}
                    </Table.Cell>
                </React.Fragment>
            );
        });

    return (
        <Table celled structured className="CustomTableView">
            <Table.Body>
                {content.map((row, idx) => (
                    <Table.Row key={`CustomTableView-${sheetName}-${idx}-row`}>
                        {tableCellValue(row, idx)}
                    </Table.Row>
                ))}
            </Table.Body>
        </Table>
    );
};

export default CustomSubTableView;

import React, { useEffect } from "react";
import PropTypes from "prop-types";
import { useDispatch, useSelector } from "react-redux";

// semantic ui
import { Button, Modal, Grid } from "semantic-ui-react";

// components
// import ImportSteps from "./ImportSteps";

// config
import { stepConfig, stepStrConf } from "../config/stepConfig";
import ImportAct from "../ImportAction";

// utils
import { isEmpty } from "../../../../../../../commons";

function ImportModal({ open, setOpen }) {
    const dispatch = useDispatch();
    const { curStep, activeItem } = useSelector(state => state.import);

    useEffect(() => {
        dispatch({ type: ImportAct.INIT_ACTIVEITEM });
    }, []);

    // eslint-disable-next-line no-unused-vars
    const clickNextStep = () => {
        const findIdx = Object.keys(stepStrConf).findIndex(
            key => key === curStep
        );

        // avoid exceed range
        if (findIdx + 1 < Object.keys(stepStrConf).length) {
            const tmpCurStep = Object.values(stepStrConf)[findIdx + 1];
            dispatch({
                type: ImportAct.SET_CURSTEP,
                payload: tmpCurStep
            });

            const tmpObj = stepConfig[tmpCurStep];
            if (!isEmpty(tmpObj)) {
                dispatch({
                    type: ImportAct.SET_ACTIVEITEM,
                    payload: tmpObj
                });
            }
        }
    };

    const closeModal = () => {
        dispatch({ type: ImportAct.INIT_CURSTEP });
        dispatch({ type: ImportAct.INIT_ACTIVEITEM });
        setOpen(false);
    };

    return (
        <Modal open={open} size="large">
            <Modal.Header>匯入資料</Modal.Header>
            <Modal.Content>
                <Modal.Description>
                    {/* <Grid centered style={{ marginBottom: "0" }}> */}
                    {/*    <ImportSteps /> */}
                    {/* </Grid> */}
                    {!isEmpty(activeItem) && (
                        <Grid centered>
                            <activeItem.component />
                        </Grid>
                    )}
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <Button content="關閉" onClick={closeModal} />
                {/* <Button content="下一步" onClick={clickNextStep} primary /> */}
            </Modal.Actions>
        </Modal>
    );
}

ImportModal.propTypes = {
    /** open callback */
    setOpen: PropTypes.func,
    /** open */
    open: PropTypes.bool
};

ImportModal.defaultProps = {
    /** open callback */
    setOpen: () => {},
    /** open */
    open: false
};

export default ImportModal;

import React, { useEffect, useMemo, useState } from "react";
import { Checkbox } from "semantic-ui-react";

const CustomCheckBox = ({
    cellId,
    setCallback,
    label,
    disabled,
    defaultValue,
    menuName
}) => {
    const [check, setCheck] = useState(
        defaultValue ? defaultValue[0] === "true" : false
    );

    const handleChange = () => {
        setCheck(!check);

        setCallback(cellId, !check ? ["true"] : ["false"], menuName);
    };

    useEffect(() => {
        if (disabled) {
            setCheck(false);
            setCallback(cellId, ["false"], menuName);
        }
    }, [disabled]);

    // useEffect(() => {
    //     setCheck(defaultValue ? defaultValue[0] === "true" : false);
    // }, [menuName]);

    return useMemo(
        () => (
            <Checkbox
                label={label}
                disabled={disabled}
                checked={check}
                onChange={handleChange}
            />
        ),
        [cellId, disabled, check, menuName]
    );
};

export default CustomCheckBox;

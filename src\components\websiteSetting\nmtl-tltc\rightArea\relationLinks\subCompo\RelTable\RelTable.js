import React, { useContext, useEffect } from "react";
import PropTypes from "prop-types";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";

// semantic ui
import { Table, Checkbox, Button, Ref } from "semantic-ui-react";

// utils
import { handleEdit, loadTBData, showDate } from "../../utils/utils";
import Act from "../../../../../../../store/actions";
import tltcAct from "../../../../tltcAction";
import { StoreContext } from "../../../../../../../store/StoreProvider";

// images
import iconGrap from "../../../../../../../images/icon_grab.svg";
import iconEdit from "../../../../../../../images/icon_edit.svg";

// components
import SortedHeader from "./SortedHeader";
import EmptyView from "../../../../../../../commons/components/empty/EmptyView";

function RelTable({ setIsEdited, type, sorted }) {
    const [state, dispatch] = useContext(StoreContext);
    const { rellinkTable } = state.websiteSetting;
    /* table Head 資料 */
    const tableHeadData = ["", "名稱", "Logo圖", "最後修改時間", "編輯"];

    useEffect(() => {
        if (!type) return;
        loadTBData(type, dispatch);
    }, [type]);

    const handleCheck = (data, idx) => {
        const tmpTable = [...rellinkTable];
        tmpTable[idx].check = data.checked;
        dispatch({
            type: Act.FRONTEDIT_TLTC,
            localType: tltcAct.SET_RELLINKTABLE,
            payload: tmpTable
        });
    };

    const allCheck = () => rellinkTable.every(elTB => elTB.check);
    const someCheck = () => rellinkTable.some(elTB => elTB.check);
    const checkAll = checked => {
        let tmpTable = [...rellinkTable];
        tmpTable = tmpTable.map(el => ({ ...el, check: checked }));
        dispatch({
            type: Act.FRONTEDIT_TLTC,
            localType: tltcAct.SET_RELLINKTABLE,
            payload: tmpTable
        });
    };

    const onDragEnd = result => {
        const startIndex = result.source.index;
        const endIndex = result.destination.index;

        const tmpTable = [...rellinkTable];
        const tmpObj = tmpTable[startIndex];
        tmpTable[startIndex] = tmpTable[endIndex];
        tmpTable[endIndex] = tmpObj;

        dispatch({
            type: Act.FRONTEDIT_TLTC,
            localType: tltcAct.SET_RELLINKTABLE,
            payload: tmpTable
        });
    };

    return (
        <EmptyView data={rellinkTable}>
            <DragDropContext onDragEnd={onDragEnd}>
                <Table celled structured size="small">
                    <Table.Header>
                        <Table.Row>
                            {tableHeadData.map((el, idx) => (
                                <Table.HeaderCell
                                    key={idx}
                                    width={
                                        idx === 0 ||
                                        idx === tableHeadData.length - 1
                                            ? 1
                                            : 4
                                    }
                                    style={{ textAlign: "center" }}
                                >
                                    {idx === 0 && !sorted && (
                                        <Checkbox
                                            checked={allCheck()}
                                            indeterminate={
                                                someCheck() && !allCheck()
                                            }
                                            onClick={(evt, data) => {
                                                checkAll(data.checked);
                                            }}
                                        />
                                    )}
                                    {idx === 3 && (
                                        <SortedHeader sorted={sorted}>
                                            {el}
                                        </SortedHeader>
                                    )}
                                    {![0, 3].includes(idx) && el}
                                </Table.HeaderCell>
                            ))}
                        </Table.Row>
                    </Table.Header>
                    <Droppable droppableId="droppableId">
                        {provided => (
                            <Ref
                                innerRef={provided.innerRef}
                                {...provided.droppableProps}
                            >
                                <Table.Body>
                                    {rellinkTable.map((el, idx) => (
                                        <Draggable
                                            isDragDisabled={!sorted}
                                            draggableId={`draggable_${idx}`}
                                            index={idx}
                                            key={idx}
                                        >
                                            {dragRow => (
                                                <Ref
                                                    innerRef={dragRow.innerRef}
                                                >
                                                    <Table.Row
                                                        key={idx}
                                                        {...dragRow.draggableProps}
                                                        {...dragRow.dragHandleProps}
                                                    >
                                                        <Table.Cell
                                                            style={{
                                                                textAlign:
                                                                    "center"
                                                            }}
                                                        >
                                                            {sorted ? (
                                                                <img
                                                                    src={
                                                                        iconGrap
                                                                    }
                                                                    alt="grap"
                                                                />
                                                            ) : (
                                                                <Checkbox
                                                                    checked={
                                                                        el.check
                                                                    }
                                                                    onChange={(
                                                                        evt,
                                                                        data
                                                                    ) =>
                                                                        handleCheck(
                                                                            data,
                                                                            idx
                                                                        )
                                                                    }
                                                                />
                                                            )}
                                                        </Table.Cell>
                                                        <Table.Cell>
                                                            {el.labelZH && (
                                                                <p>
                                                                    {`${el.labelZH}@zh`}
                                                                </p>
                                                            )}
                                                            {el.labelEN && (
                                                                <p>
                                                                    {`${el.labelEN}@en`}
                                                                </p>
                                                            )}
                                                        </Table.Cell>
                                                        <Table.Cell>
                                                            {el?.imgUrl ? (
                                                                <img
                                                                    src={
                                                                        el.imgUrl
                                                                    }
                                                                    alt="logo"
                                                                    style={{
                                                                        width:
                                                                            "100%"
                                                                    }}
                                                                />
                                                            ) : (
                                                                <p>無logo圖</p>
                                                            )}
                                                        </Table.Cell>
                                                        <Table.Cell>
                                                            {showDate(
                                                                el.lastModified
                                                            )}
                                                        </Table.Cell>
                                                        <Table.Cell
                                                            style={{
                                                                textAlign:
                                                                    "center"
                                                            }}
                                                        >
                                                            <Button
                                                                icon
                                                                style={{
                                                                    background:
                                                                        "initial",
                                                                    margin: "0"
                                                                }}
                                                                onClick={() =>
                                                                    handleEdit(
                                                                        setIsEdited,
                                                                        dispatch,
                                                                        el.urlId
                                                                    )
                                                                }
                                                                disabled={
                                                                    sorted
                                                                }
                                                            >
                                                                <img
                                                                    src={
                                                                        iconEdit
                                                                    }
                                                                    alt="edit"
                                                                />
                                                            </Button>
                                                        </Table.Cell>
                                                    </Table.Row>
                                                </Ref>
                                            )}
                                        </Draggable>
                                    ))}
                                    {provided.placeholder}
                                </Table.Body>
                            </Ref>
                        )}
                    </Droppable>
                </Table>
            </DragDropContext>
        </EmptyView>
    );
}

RelTable.propTypes = {
    /** 開啟編輯模式的callback */
    setIsEdited: PropTypes.func,
    /** 相關連結頁，選擇["合作對象"、"相關資源"]其中一個 */
    type: PropTypes.string,
    /** 排序功能狀態 */
    sorted: PropTypes.bool
};

RelTable.defaultProps = {
    /** 開啟編輯模式的callback */
    setIsEdited: () => null,
    /** 相關連結頁，選擇["合作對象"、"相關資源"]其中一個 */
    type: "",
    /** 排序功能狀態 */
    sorted: false
};

export default RelTable;

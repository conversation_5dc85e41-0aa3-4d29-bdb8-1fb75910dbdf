import React, { useEffect } from 'react';

// semantic ui
import { Form, Checkbox } from 'semantic-ui-react';

// utils
import textMsg from '../commons/textMsg';

function LanguageSelect({ language, setLanguage }) {
    const { languageList } = textMsg;

    useEffect(() => {
        setLanguage(Object.keys(languageList)[0]);
    }, []);

    return (
        <div
            style={{
                display: 'flex',
                alignItems: 'center',
                marginLeft: '15px',
            }}
        >
            {/* 修改語系 */}
            <Form style={{ display: 'flex', alignItems: 'center' }} className="LanguageSelectForm">
                {Object.keys(languageList).map((key) => (
                    <Checkbox
                        key={key}
                        radio
                        label={languageList[key]}
                        checked={language === key}
                        onClick={() => {
                            setLanguage(key);
                        }}
                        style={{ marginRight: '3px' }}
                    />
                ))}
            </Form>
        </div>
    );
}

export default LanguageSelect;

import React, { useContext, useEffect, useMemo, useState } from "react";

// store
import Select from "react-select";
import { StoreContext } from "../../../../../store/StoreProvider";
import { isEmpty } from "../../../../../commons";
import { getMenuPlacement } from "../../../datasetConfig";
import { specialConvertToOption } from "../../../../common/sheetCrud/utils";
import { MAX_OPTION } from "../../../../common/sheetCrud/sheetCrudHelper";

// custom ui

// common

// 可以新增
const CustomMultiDropdown = ({
    cellId,
    rowId,
    idx = 0,
    createState,
    setCallback,
    defaultValue,
    isShowId,
    menuName
}) => {
    const [state, dispatch] = useContext(StoreContext);
    const { sheet } = state.data;
    const { headerFields } = sheet;

    // set true when input and default vale are diff
    // const [isDiffValue, setDiffValue] = useState(false);
    const [inputValue, setInputValue] = useState(defaultValue);
    // change dropdown value
    const handleChange = selectValue => {
        const cellValue = isEmpty(selectValue) ? [] : selectValue;
        // extract and combine id, e.g. ORG1/ORG2
        const selectCombinedIds = cellValue.map(item => item.id).sort();

        // keep input value when it changed
        if (cellValue.length === 0) {
            // clear
            setCallback(cellId, null, menuName);
        } else {
            setCallback(cellId, selectCombinedIds, menuName);
        }
    };

    const handleInputChange = input => {
        setInputValue(input);
        // setCallback(cellId, rowId, idx, { isOption: true, input: inputValue });
    };

    const customStyles = {
        container: styles => ({
            ...styles,
            margin: "-9px",
            minWidth: "100%",
            width: "300px"
        }),
        control: (styles, { selectProps: { controlColor } }) => ({
            ...styles,
            borderStyle: "none",
            borderRadius: "unset",
            backgroundColor: controlColor
        })
    };

    const customPlacement = getMenuPlacement(rowId);
    // const customControlBgColor = isDiffValue ? "#f9c09a66" : "";

    const newOptions = useMemo(() => {
        if (!headerFields) {
            return [];
        }

        return specialConvertToOption(cellId, headerFields) || [];
    }, [cellId, headerFields]);

    // console.log(cellId, createState);
    return useMemo(
        () => (
            <Select
                isMulti
                isClearable
                styles={customStyles}
                options={
                    inputValue && newOptions
                        ? newOptions
                            .filter(o => o.label.includes(inputValue))
                            .slice(0, MAX_OPTION)
                        : newOptions.slice(0, MAX_OPTION)
                }
                value={
                    createState && createState[cellId]
                        ? newOptions.filter(
                            o =>
                                Object.values(createState[cellId]).indexOf(
                                    o.id
                                ) > -1
                        )
                        : null
                }
                onChange={handleChange}
                onInputChange={handleInputChange}
                // components={{ MenuList }}
                menuPlacement={customPlacement}
                // controlColor={customControlBgColor}
                autosize
            />
        ),
        [cellId, inputValue, createState, newOptions, menuName]
    );
};

export default CustomMultiDropdown;

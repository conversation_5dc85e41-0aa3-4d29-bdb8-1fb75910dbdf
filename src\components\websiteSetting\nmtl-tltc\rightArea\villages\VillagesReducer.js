import { createStore } from "redux";
import VillagesAct from "./VillagesAction";

const initialState = {
    villagesIsEdited: false,
    editingVillageId: "",
    translatorContentZhForQuill: "",
    translatorContentEnForQuill: "",
    aciIsEdited: false,
    editingACIId: "",
    editingACIData: {},
    notifyUpdateActivityInfo: false,
    aciListLength: "",
    aciList: [],
    isEditedActivityInfoList: false,
    isModalOpen: false,
    isDelModalOpen: false,
    deletingActivityInfo: [],

    updatePeakBrief: {},
    peakInfo: [],
    newPeakInfo: [],

    content: {
        checked: []
    },
    rows: {},

    curFolderFiles: { original: [], checked: [] },
    pickConfig: {
        uploadPage: {
            selectMode: "multiple", // ENUM:["single", "multiple"]
            withCheckbox: true, // use checkbox for user to check
            maxFiles: 0, // 0 means there is no limit for uploading files count
            openSwalAlert: true
        },
        datasetPage: {
            selectMode: "single", // ENUM:["single", "multiple"]
            withCheckbox: true, // use checkbox for user to check
            maxFiles: 1,
            openSwalAlert: false
        }
    },
    folderPattern: null,
    loading: {
        state: false, // e.g. true/false
        message: "" // e.g. loading, uploading
    },
    // currentFolder: 目前鎖定的資料夾
    currentFolder: {
        path: "",
        path_ch: "",
        folderName: "",
        folderName_ch: ""
    },
    curFolderFilesUrl: [],
    notifyUploadTidbits: false,
    villageList: []
};

function VillagesReducer(state = initialState, action) {
    switch (action.type) {
        case VillagesAct.SET_IS_EDITED_VILLAGES:
            return { ...state, villagesIsEdited: action.payload };

        case VillagesAct.SET_EDITING_VILLAGEID:
            return { ...state, editingVillageId: action.payload };

        case VillagesAct.DATA_CONTENT_ROW_CHECKED:
            return {
                ...state,
                content: {
                    ...state.content,
                    checked: [...state.content.checked, action.payload]
                }
            };

        case VillagesAct.DATA_CONTENT_ROW_CHECKED_ALL:
            return {
                ...state,
                content: {
                    ...state.content,
                    checked: action.payload
                }
            };

        case VillagesAct.DATA_CONTENT_ROW_NO_CHECKED:
            return {
                ...state,
                content: {
                    ...state.content,
                    checked: [
                        ...state.content.checked.filter(
                            item => item.rowId !== action.payload.rowId
                        )
                    ]
                }
            };
        // clean data when updated
        case VillagesAct.DATA_CONTENT_ROW_CHECKED_CLEAN:
            return {
                ...state,
                content: { ...state.content, checked: [] }
            };

        case VillagesAct.DATA_SET_ROWS:
            return { ...state, rows: action.payload };

        case VillagesAct.SET_TRANSLATOR_CONTENT_ZH_FOR_QUILL:
            return { ...state, translatorContentZhForQuill: action.payload };

        case VillagesAct.SET_TRANSLATOR_CONTENT_EN_FOR_QUILL:
            return { ...state, translatorContentEnForQuill: action.payload };

        case VillagesAct.SET_IS_EDITED_ACI:
            return { ...state, aciIsEdited: action.payload };

        case VillagesAct.SET_EDITING_ACI_ID:
            return { ...state, editingACIId: action.payload };

        case VillagesAct.SET_EDITING_ACI_DATA:
            return { ...state, editingACIData: action.payload };

        case VillagesAct.SET_NOTIFY_UPDATE_ACVIVITY_INFO:
            return { ...state, notifyUpdateActivityInfo: action.payload };

        case VillagesAct.SET_ACI_LIST_LENGTH:
            return { ...state, aciListLength: action.payload };

        case VillagesAct.SET_ACI_LIST:
            return { ...state, aciList: action.payload };

        case VillagesAct.SET_IS_EDITED_ACTIVITY_INFO_LIST:
            return { ...state, isEditedActivityInfoList: action.payload };

        case VillagesAct.SET_DELETING_ACTIVITY_INFO:
            return { ...state, deletingActivityInfo: action.payload };

        case VillagesAct.SET_IS_DEL_MODAL_OPEN:
            return { ...state, isDelModalOpen: action.payload };

        case VillagesAct.SET_IS_MODAL_OPEN:
            return { ...state, isModalOpen: action.payload };

        case VillagesAct.CLEAR_CUR_FOLDER_FILES_STATUS:
            return {
                ...state,
                curFolderFiles: JSON.parse(
                    JSON.stringify(initialState.curFolderFiles)
                )
            };

        case VillagesAct.CUR_FOLDER_FILES_STATUS:
            return {
                ...state,
                curFolderFiles: JSON.parse(JSON.stringify(action.payload))
            };
        case VillagesAct.UPLOAD_IMAGE:
            return {
                ...state,
                uploadImages: JSON.parse(JSON.stringify(action.payload))
            };
        case VillagesAct.CLEAN_PICKER_CUR_FOLDER_MESSAGE:
            return {
                ...state,
                imgPickerMessage: {
                    ...state.imgPickerMessage,
                    curFolder: initialState.imgPickerMessage.curFolder
                }
            };
        case VillagesAct.CLEAN_PICKER_DROP_MESSAGE:
            return {
                ...state,
                imgPickerMessage: {
                    ...state.imgPickerMessage,
                    drop: initialState.imgPickerMessage.drop
                }
            };
        case VillagesAct.FOLDER_FILES_URL:
            return {
                ...state,
                curFolderFilesUrl: JSON.parse(JSON.stringify(action.payload))
            };

        case VillagesAct.SET_NOTIFY_UPLOAD_TIDBITS:
            return { ...state, notifyUploadTidbits: action.payload };

        case VillagesAct.SET_VILLAGE_LIST:
            return { ...state, villageList: action.payload };

        default:
            return state;
    }
}

const VillagesStore = createStore(VillagesReducer);

export default VillagesStore;

import React, { useContext, useEffect, useState } from "react";
import { <PERSON><PERSON>, Container, Modal, Input } from "semantic-ui-react";
import { StoreContext } from "../../../../../store/StoreProvider";
import { isEmpty } from "../../../../../commons";
import Act from "../../../../../store/actions";
import { createNmtlData } from "../../../../../api/nmtl";
import Api from "../../../../../api/nmtl/Api";
import { getReservedNewId } from "../../../../common/sheetCrud/utils";

const CopyButton = () => {
    const [open, setOpen] = useState(false);
    const [copyData, setCopyData] = useState({});
    const [copyCount, setCopyCount] = useState(1);

    const [state, dispatch] = useContext(StoreContext);
    const { checked, rows } = state.data.content;
    const { user } = state;
    const { mainSubject, sheet } = state.data;
    const { dataset } = mainSubject.selected;
    const { key: sheetName } = sheet.selected;

    const handleClose = () => {
        setOpen(false);
    };

    const findCopiedMaxNumber = (ids, baseSrcId) => {
        // 過濾出以 baseSrcId 開頭的編號
        const matchingIds = ids.filter(id => id.startsWith(baseSrcId));

        // 找到下一層的最大編號，屬於該 baseSrcId 的直接子編號
        const subIds = matchingIds
            .map(id => {
                const parts = id.split("-");
                // 確保只考慮與 baseSrcId 同一層級的子編號
                return parts.length > baseSrcId.split("-").length
                    ? parseInt(parts[parts.length - 1], 10)
                    : NaN;
            })
            .filter(num => !isNaN(num)) // 過濾無效數字
            .sort((a, b) => a - b); // 按數字排序

        // 如果有子編號，返回最大值；否則返回 0
        return subIds.length > 0 ? Math.max(...subIds) : 0;
    };

    const createCopyDataToDb = async item => {
        const apiUrl = Api.getGeneric;
        const createResult = await createNmtlData(
            user,
            apiUrl,
            item.graph,
            sheetName,
            item
        ).then(res => res === "OK");
        return createResult;
    };

    const handleCopy = async () => {
        if (isEmpty(copyData) || copyCount === 0) return;
        const createFinishArr = [];

        const curPgPersonIds = Object.values(rows)
            .map(el => Object.values(el.label_Person))
            .flat();

        const identicalPersonIds = curPgPersonIds.filter(el =>
            Object.keys(copyData.label_Person).some(key =>
                el.includes(copyData.label_Person[key])
            )
        );

        const maxNumbers = Object.keys(copyData.label_Person).reduce(
            (acc, key) => {
                acc[key] = findCopiedMaxNumber(
                    identicalPersonIds,
                    copyData.label_Person[key]
                );
                return acc;
            },
            {}
        );

        // 複製 copyCount 份數
        for (let count = 1; count <= copyCount; count++) {
            let newCopyData = JSON.parse(JSON.stringify(copyData));

            const newSrcId = await getReservedNewId("Person");

            newCopyData = Object.keys(newCopyData).reduce((acc, key) => {
                if (["srcId", "graph", "literary"].includes(key)) return acc;
                if (
                    typeof newCopyData[key] === "object" &&
                    !Array.isArray(newCopyData[key])
                ) {
                    acc[key] = Object.values(newCopyData[key]);
                } else {
                    acc[key] = newCopyData[key];
                }
                return acc;
            }, {});

            Object.keys(newCopyData.label_Person).forEach(key => {
                const maxNumber = maxNumbers[key] || 0;
                newCopyData.label_Person[key] = `${
                    copyData.label_Person[key]
                }-${maxNumber + count}`;
            });

            delete newCopyData.lastModified;

            let baseItem = {
                graph: dataset,
                classType: "Person",
                srcId: newSrcId,
                value: newCopyData
            };

            createFinishArr.push(createCopyDataToDb(baseItem));

            if ("graph" in copyData) {
                const graphArr = Object.values(copyData.graph);
                const graphItems = graphArr.map(graph => ({
                    ...baseItem,
                    graph,
                    value: {}
                }));
                createFinishArr.push(...graphItems.map(createCopyDataToDb));
            }

            if ("literary" in copyData) {
                const literaryArr = Object.values(copyData.literary);
                const literaryItems = literaryArr.map(literary => ({
                    ...baseItem,
                    graph: literary,
                    value: {}
                }));
                createFinishArr.push(...literaryItems.map(createCopyDataToDb));
            }
        }

        const results = await Promise.all(createFinishArr);

        const successCount = results.filter(res => res).length;
        const errorCount = results.length - successCount;

        dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });

        const message = {
            title: "Copy",
            success: successCount,
            error: errorCount,
            renderSignal: `Copy-${new Date().getTime()}`
        };

        dispatch({
            type: Act.DATA_MESSAGE,
            payload: message
        });

        setCopyCount(1);
        setCopyData({});
        handleClose();
    };

    useEffect(() => {
        if (checked.length === 1 && Object.keys(rows).length > 0) {
            const checkedRows = rows[checked[0].rowId];

            if (!checkedRows.unintegrated) {
                setCopyData(checkedRows);
                return;
            }
        }

        setCopyData({});
    }, [checked, rows]);

    return (
        <Modal
            open={open}
            onOpen={() => setOpen(true)}
            onClose={handleClose}
            trigger={
                <Button
                    color="blue"
                    floated="right"
                    disabled={isEmpty(copyData)}
                >
                    複製
                </Button>
            }
        >
            <Modal.Header>複製確認</Modal.Header>

            <Modal.Content image>
                <Modal.Description style={{ width: "100%" }}>
                    <Container
                        style={{
                            display: "flex",
                            width: "60%",
                            justifyContent: "space-between",
                            alignItems: "center"
                        }}
                    >
                        <span>將</span>
                        <span>{!isEmpty(copyData) && copyData.srcId}</span>
                        <span>
                            {!isEmpty(copyData) &&
                                copyData.label_Person["0"].split("@")[0]}
                        </span>
                        <div
                            style={{
                                display: "flex",
                                gap: "8px",
                                alignItems: "center"
                            }}
                        >
                            <span>複製</span>
                            <Input
                                defaultValue={1}
                                type="number"
                                style={{ width: "100px" }}
                                input={{ min: 1 }}
                                onChange={e =>
                                    setCopyCount(parseInt(e.target.value, 10))
                                }
                                onKeyDown={e => {
                                    if (
                                        e.key === "-" ||
                                        e.key === "e" ||
                                        e.key === "0"
                                    ) {
                                        e.preventDefault();
                                    }
                                }}
                            />
                            <span>份</span>
                        </div>
                    </Container>
                </Modal.Description>
            </Modal.Content>

            <Modal.Actions>
                <Button onClick={handleCopy} color="green">
                    確認
                </Button>
                <Button onClick={handleClose} color="red">
                    取消
                </Button>
            </Modal.Actions>
        </Modal>
    );
};

export default CopyButton;

import React from "react";
import { useSelector } from "react-redux";

// semantic ui
import { Step } from "semantic-ui-react";

// config
import { stepConfig } from "../config/stepConfig";

function ImportSteps() {
    const { curStep } = useSelector(state => state.import);

    return (
        <Step.Group ordered style={{ padding: "0" }} size="mini">
            {Object.keys(stepConfig).map(key => {
                const { title, desc } = stepConfig[key];
                return (
                    <Step key={key} active={key === curStep}>
                        <Step.Content>
                            <Step.Title>{title}</Step.Title>
                            {desc && (
                                <Step.Description>{desc}</Step.Description>
                            )}
                        </Step.Content>
                    </Step>
                );
            })}
        </Step.Group>
    );
}

export default ImportSteps;

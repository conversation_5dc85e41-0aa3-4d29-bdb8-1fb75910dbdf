import PersonCreateContent from "./Person/PersonCreateContent";
import PublicationCreateContent from "./PublicationInfo/PublicationCreateContent";
import Api from "../../../../api/nmtl/Api";

const PICTURE_DISPLAY = "pictureDisplay";

const CHECK_CELL_VALUE_LANG = ["label_Person", "introduction"];
export const GLOBAL_CREATE_PATH = "create";

export const Person = "Person";
const createConfig = {
    editButton: "儲存",
    createButton: "新增",
    cancelButton: "取消",
    createTitle: "新增內容",
    editTitle: "編輯內容",
    subTitle_OriBook: "原文書",
    subTitle_TransBook: "翻譯書",
    dropdownHint: "請選擇",
    inputHint: "請輸入",
    nextStep: "下一步"
};

const sheetCreateView = {
    Person: {
        checkKey: "label_Person",
        keyErrorMsg:
            "人名為必填欄位,請重新確認是否已填寫，並填入正確後綴，如：@zh, @en 或 @ja。",
        apiPath: Api.getSameListWithGraph,
        components: PersonCreateContent
    },
    PublicationInfo: {
        checkKey: "label_Publication",
        keyErrorMsg: "原文書名為必填欄位，請重新確認是否已填寫",
        apiPath: Api.getSameListWithGraph,
        components: PublicationCreateContent
    }
};

export {
    PICTURE_DISPLAY,
    createConfig,
    sheetCreateView,
    // columnSetting,
    CHECK_CELL_VALUE_LANG
};

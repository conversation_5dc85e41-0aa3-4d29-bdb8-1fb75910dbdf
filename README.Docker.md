# Docker Usage Guide for nmtl-backend-web-v2

This document explains how to run the nmtl-backend-web-v2 application using Docker in both **development** and **production** modes.

---

## 📦 Prerequisites

- [Docker](https://www.docker.com/products/docker-desktop) installed and running
- Terminal with support for Unix-style commands (e.g., Git Bash, WSL, or PowerShell with proper escaping)
- Change to the nmtl-backend-web-v2 directory, where **the Dockerfile is located at the same level**.

---

## 🧪 Development Mode (Windows)

Use this command to run the application in development mode:

```sh
docker run -dp 3000:9999 `
    -w /opt/nmtl-backend-web-v2 -v ${PWD}:/opt/nmtl-backend-web-v2 `
    node:14-alpine `
    sh -c "npm install && npm start"
```

## 🚀 Production Mode

### Step 1: Build docker image

```sh
docker build -t nmtl-backend-web-v2 .
```

### Step 2:

Use this command to run the application in production mode:

```sh
docker run -dp 3001:80 nmtl-backend-web-v2
```

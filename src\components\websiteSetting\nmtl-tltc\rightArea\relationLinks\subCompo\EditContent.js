import React, { useContext, useEffect, useState } from "react";
import PropTypes from "prop-types";

// semantic ui
import { Button, Table, Select, Input } from "semantic-ui-react";

// utils
import { StoreContext } from "../../../../../../store/StoreProvider";
import { isEmpty } from "../../../../../../commons";
import Api from "../../../../../../api/nmtl/Api";
import { readNmtlData } from "../../../../../../api/nmtl";
import { createData, getNewOrder, transInstanceData } from "../utils/utils";
import { checkColumn } from "../utils/colMustHave";
import { getReservedNewId } from "../../../../../common/sheetCrud/utils";
import textMsg from "../../../../commons/textMsg";
import { hasFrontEditType } from "../../../../../../api/nmtl/classPrefix";

// components
import ImageInput from "../../../../commons/components/ImageInput";
import SaveButton from "../../../../commons/components/SaveButton";

function EditContent({ setIsEdited, type }) {
    const [state] = useContext(StoreContext);
    const { rellinkEditId } = state.websiteSetting;
    const [srcItem, setSrcItem] = useState({});
    const [editItem, setEditItem] = useState({});
    const [copyOptions, setCopyOptions] = useState([]);
    const headWth = 2;
    const { graph } = textMsg;
    const classType = "URLEvent";
    const [pass, setPass] = useState(true); // 檢查必填欄位

    useEffect(() => {
        const apiStr = Api.getCopyrightStatus;
        readNmtlData(apiStr).then(res => {
            if (!isEmpty(res.data)) {
                const tmpCopyOptions = res.data.map(({ id, label }) =>
                    createData(id, id, label)
                );
                setCopyOptions(tmpCopyOptions);
            }
        });

        // 沒帶rellinkEditId，表示要新增(須新增ID)
        if (isEmpty(rellinkEditId)) {
            getReservedNewId(classType).then(async newId => {
                let tmpObj = { id: newId, type };
                tmpObj = await getNewOrder(type, tmpObj);
                setEditItem({
                    ...tmpObj,
                    hasFrontEditType: hasFrontEditType.TLTCPartner
                });
            });
        }
    }, []);

    useEffect(() => {
        /** 進到EditContent有兩種情況
         * 1. 沒帶rellinkEditId，表示要新增(須新增ID)
         * 2. 有帶rellinkEditId，表示要修改
         * */
        if (!isEmpty(rellinkEditId)) {
            const apiStr = Api.getSubValue2
                .replace("{ds}", graph)
                .replace("{ids}", "")
                .replace("{type}", classType);

            const entry = { ids: rellinkEditId };
            const options = {
                method: "POST",
                headers: {
                    "content-type": "application/json",
                    "Accept-Encoding": "gzip"
                },
                body: JSON.stringify({ entry })
            };

            readNmtlData(apiStr, 60000, options).then(res => {
                const tmpItem = transInstanceData(res.data, classType);
                setSrcItem(tmpItem);
                setEditItem({
                    ...tmpItem,
                    hasFrontEditType: hasFrontEditType.TLTCPartner
                });
            });
        }
    }, [rellinkEditId]);

    const cancelEdit = () => {
        setIsEdited(false);
    };

    const handleChange = async (prop, value) => {
        let tmpItem = Object.assign({}, editItem);
        if (prop === "type") {
            // update order
            tmpItem = await getNewOrder(value, editItem);
        }
        tmpItem[prop] = value;
        setEditItem(tmpItem);
    };

    const getSelectedFile = selectFile => {
        const tmpItem = Object.assign({}, editItem);
        [tmpItem.imageName] = selectFile.split("/").slice(-1);
        const firUnScoreIdx = tmpItem.imageName.indexOf("_");
        tmpItem.imageName = tmpItem.imageName.slice(firUnScoreIdx + 1);
        tmpItem.imagePath = "FrontEdit/tltc";
        setEditItem(tmpItem);
    };

    return (
        <div className="EditContent">
            <div>
                <div
                    className="TopArea"
                    style={{ justifyContent: "start", alignSelf: "start" }}
                >
                    <h1>編輯內容</h1>
                </div>
                <div className="TableArea">
                    <Table celled structured size="small">
                        <Table.Body>
                            <Table.Row colSpan={4}>
                                {/* nmtl-tltc web相關連結不分區塊，先隱藏下拉選單-Bennis 20230511 */}
                                {/* <Table.Cell width={headWth}>*分類</Table.Cell> */}
                                {/* <Table.Cell width={8 - headWth}> */}
                                {/*    <Select */}
                                {/*        placeholder="請選擇合作相關" */}
                                {/*        options={rellinkTypes} */}
                                {/*        onChange={(evt, data) => { */}
                                {/*            handleChange("type", data.value); */}
                                {/*        }} */}
                                {/*        value={editItem.type || type} */}
                                {/*        fluid */}
                                {/*        error={!pass} */}
                                {/*    /> */}
                                {/* </Table.Cell> */}
                                <Table.Cell width={headWth} rowSpan={2}>
                                    *名稱
                                </Table.Cell>
                                <Table.Cell width={8 - headWth} rowSpan={2}>
                                    <div className="TableArea__CHName">
                                        <div>中文</div>
                                        <Input
                                            placeholder="請輸入中文名稱"
                                            value={editItem.labelZH || ""}
                                            onChange={(evt, data) =>
                                                handleChange(
                                                    "labelZH",
                                                    data.value
                                                )
                                            }
                                            error={!pass}
                                        />
                                    </div>
                                    <div className="TableArea__ENName">
                                        <div>外文</div>
                                        <Input
                                            placeholder="Please type translated name"
                                            value={editItem.labelEN || ""}
                                            onChange={(evt, data) =>
                                                handleChange(
                                                    "labelEN",
                                                    data.value
                                                )
                                            }
                                            error={!pass}
                                        />
                                    </div>
                                </Table.Cell>
                                <Table.Cell>Logo圖</Table.Cell>
                                <Table.Cell>
                                    <ImageInput
                                        imageName={editItem.imageName}
                                        imagePath={editItem.imagePath}
                                        getSelectedFile={getSelectedFile}
                                    />
                                </Table.Cell>
                            </Table.Row>
                            <Table.Row>
                                <Table.Cell>圖像權利標註</Table.Cell>
                                <Table.Cell>
                                    <Select
                                        placeholder="請選擇"
                                        options={copyOptions}
                                        fluid
                                        onChange={(evt, data) =>
                                            handleChange(
                                                "hasCopyrightStatus",
                                                data.value
                                            )
                                        }
                                        value={editItem.hasCopyrightStatus}
                                    />
                                </Table.Cell>
                            </Table.Row>
                            <Table.Row>
                                <Table.Cell width={headWth}>
                                    外部連結
                                </Table.Cell>
                                <Table.Cell colSpan="3">
                                    <Input
                                        fluid
                                        placeholder="請輸入外部連結"
                                        value={editItem.externalLinks || ""}
                                        onChange={(evt, data) =>
                                            handleChange(
                                                "externalLinks",
                                                data.value
                                            )
                                        }
                                    />
                                </Table.Cell>
                            </Table.Row>
                        </Table.Body>
                    </Table>
                </div>
            </div>
            <div className="BottomArea">
                <SaveButton
                    srcData={srcItem}
                    dstData={editItem}
                    closeCallBack={cancelEdit}
                    checkCallBack={checkColumn}
                    setPass={setPass}
                />
                <Button onClick={cancelEdit}>取消</Button>
            </div>
        </div>
    );
}

EditContent.propTypes = {
    /** 開啟編輯模式的callback */
    setIsEdited: PropTypes.func,
    /** 相關連結分類: ["合作夥伴", "相關資源"] */
    type: PropTypes.oneOf(["合作夥伴", "相關資源"])
};

EditContent.defaultProps = {
    /** 開啟編輯模式的callback */
    setIsEdited: () => null,
    /** 相關連結分類: ["合作夥伴", "相關資源"] */
    type: "合作夥伴"
};

export default EditContent;

import React from "react";

// ui
import { Container, Divider, Segment, Grid, Icon } from "semantic-ui-react";

// custom
import SelectDataset from "./SelectDataset";

const DownloadAllSheets = () => (
    <Container style={{ width: "95%" }}>
        <Segment basic compact>
            <h2>
                下載所有表單
                <Icon color="green" name="check circle outline" />
                <Icon color="red" name="exclamation circle" />
            </h2>
        </Segment>

        <Divider />

        <Grid celled>
            <Grid.Row>
                <Grid.Column width={16}>
                    <SelectDataset />
                </Grid.Column>
            </Grid.Row>
        </Grid>
    </Container>
);

export default DownloadAllSheets;

import React, { useContext } from "react";

// component
import FolderControlPanel from "./FolderControlPanel";
import Folder from "../../../../../common/imageCommon/folder";
import FilePickerModal from "../filePicker/FilePickerModal";
// store
import { StoreContext } from "../../../../../../store/StoreProvider";

const CurrentFolder = ({ type }) => {
    const [state] = useContext(StoreContext);
    const { common } = state;
    const { pickConfig } = common;

    // FilePickerModal可用於嵌入在 Gallery 中的 firstChild
    return (
        <Folder
            type={type}
            pickConfig={pickConfig.uploadPage}
            FolderControlPanel={FolderControlPanel}
            galleryFirstChild={<FilePickerModal type={type} />}
        />
    );
};

export default CurrentFolder;

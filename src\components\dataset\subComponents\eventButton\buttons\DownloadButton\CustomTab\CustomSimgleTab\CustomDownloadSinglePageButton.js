import React, { useContext, useState, useCallback } from 'react';

// ui
import { Button } from 'semantic-ui-react';

import { sortedByStroke } from 'twchar';
import CustomExcelFile from '../../../../../commonComp/CustomExcelFile';

// store
import { StoreContext } from '../../../../../../../../store/StoreProvider';

// common
import { isEmpty } from '../../../../../../../../commons';

// nmtl api
import { getUnintegratedData, readNmtlData } from '../../../../../../../../api/nmtl';
import Api from '../../../../../../../../api/nmtl/Api';
import { createHistoryEvent } from '../../../../../../../downloadData/components/history/common/common';
import { exportExcel } from '../../../../../../../common/sheetCrud/utils';
import arrayMerge from '../../../../../../../../commons/arrayMerge';

const DownloadSinglePageButton = () => {
    // eslint-disable-next-line no-unused-vars
    const [state] = useContext(StoreContext);
    // get dataset(mainSubject) and sheet
    const { mainSubject, sheet, pagination, search, content } = state.data;
    const { headerActiveName } = state.common;
    const { displayName } = state.user;
    const { header } = sheet;
    const { checked, rows } = content;
    // extract parameter for sidebar selected item
    const { dataset, value: datasetName } = mainSubject.selected;
    const { key: sheetName, contentReadPath, contentSearchPath, contentClassList } = sheet.selected;
    const { activePage } = pagination;
    const { keyword, searchDataset } = search;

    // 記錄在歷史訊息的欄位資訊
    const columns = [headerActiveName, mainSubject.selected.value, sheet.selected.value];

    // refresh CustomExcelFile component for download excel
    const [refreshKey, setRefreshKey] = useState(0);
    // isLoading
    const [isLoading, setIsLoading] = useState(false);
    // excel header
    const [excelHeader, setExcelHeader] = useState(undefined);
    // excel data
    const [excelData, setExcelData] = useState(undefined);

    const sortedByNumber = (results, key) =>
        results.sort((a, b) => {
            const numA = parseInt(a[key].replace(/\D/g, ''), 10);
            const numB = parseInt(b[key].replace(/\D/g, ''), 10);
            return numA - numB;
        });

    const getSortedIds = useCallback(async () => {
        if (dataset && contentClassList) {
            const personListApi = Api.getPersonDsAllIdList.replace(
                '{ds}',
                searchDataset || dataset,
            );
            const contentClassApi = contentClassList.replace('{ds}', searchDataset || dataset);

            // combine url and parameter
            const qryStr =
                dataset === 'authority' && sheetName === 'BasicInfo' && searchDataset
                    ? personListApi
                    : contentClassApi;

            const classList = await readNmtlData(qryStr);

            return dataset === 'authority' && sheetName === 'BasicInfo'
                ? // 權威檔要按id排序
                  sortedByNumber(classList.data, 'id')
                : sortedByStroke(classList.data, 'label');
        }

        return undefined;
    }, [dataset, contentClassList, searchDataset]);

    const searchNmtlData = useCallback(async () => {
        if (dataset && contentSearchPath) {
            // combine url and parameter
            const searchApiUrl = contentSearchPath
                .replace('{ds}', searchDataset || dataset)
                .replace('{keyword}', keyword);

            if (!searchApiUrl) {
                setIsLoading(false);
                return { data: [], total: null, error: 'Not ready' };
            }

            return readNmtlData(searchApiUrl);
        }

        return { data: [] };
    }, [dataset, contentSearchPath, keyword, searchDataset]);

    const getUnintegratedIds = async (uniValue) => {
        const chunkSize = 600;
        const idChunks = [];

        for (let i = 0; i < uniValue.length; i += chunkSize) {
            idChunks.push(uniValue.slice(i, i + chunkSize).toString());
        }

        const unintegratedDataPromises = idChunks.map((chunk) => getUnintegratedData(chunk));

        const allUnintegratedData = await Promise.all(unintegratedDataPromises);

        const tmpData = [
            ...new Set(
                allUnintegratedData
                    .filter((el) => el.length > 0)
                    .flatMap((el) => arrayMerge.unintegratedMergeSheet(el))
                    .map((el) => el.srcId),
            ),
        ];

        return tmpData || [];
    };

    // get sheet header
    const handleDownload = async () => {
        let searchData = [];
        let searchIds = '';
        let searchTotal = -1;

        // 有 checked 的情況優先
        if (checked.length > 0 && rows) {
            // rows: {{ label_Person: { 0: "丁寶溪@zh" }, hasBirthDate: { 0: "1886-00-00" }, srcId: "PER175" }, ... }
            // checked: {{ rowId: 1 }, { rowId: 2 }, ...}
            searchIds = checked.map((el) => rows[el.rowId].srcId).join(',');
        } else {
            // Search
            if (keyword && contentSearchPath) {
                // fetch data
                const { data } = await searchNmtlData();
                searchData = data;
                searchTotal = searchData.length;

                // 沒有結果
                if (searchTotal === 0) {
                    return;
                }
            } else {
                // id, label 全抓，Read sorted ids
                searchData = await getSortedIds();
            }

            // searchData 為全部搜尋到的結果
            if (searchData) {
                let allIds = searchData.map((el) => el.id);

                if (
                    content.showOnlyUnintegratedData &&
                    sheetName === 'BasicInfo' &&
                    dataset === 'authority'
                ) {
                    const unintegratedIds = await getUnintegratedIds(allIds);

                    if (unintegratedIds.length > 0) {
                        allIds = unintegratedIds;
                        searchTotal = unintegratedIds.length;
                    }
                }

                // 根據 active page 決定要取的 searchIds
                const startIndex = (activePage - 1) * 10;
                searchIds = allIds.slice(startIndex, startIndex + 10).join(',');
            }
        }

        if (datasetName && sheetName) {
            // set loading status
            setIsLoading(true);

            const { data: contentData, exportHeader } = await exportExcel(
                dataset,
                searchIds,
                contentReadPath,
                header,
            );

            setExcelHeader(exportHeader);

            // get content
            if (dataset && sheetName && contentReadPath) {
                if (!isEmpty(contentData) && !isEmpty(datasetName) && !isEmpty(exportHeader)) {
                    // 歷史紀錄，下載成功
                    createHistoryEvent(displayName, '下載', columns.join('/'));
                    setExcelData(contentData);
                    setRefreshKey(refreshKey + 1);
                } else {
                    // 歷史紀錄，下載失敗
                    createHistoryEvent(displayName, '下載失敗', columns.join('/'));
                }
            }
        }
        // set loading status
        setIsLoading(false);
    };

    return (
        <React.Fragment>
            <Button loading={isLoading} onClick={handleDownload} color="blue">
                下載 Excel (目前頁面)
            </Button>
            {!isEmpty(datasetName) &&
                !isEmpty(excelHeader) &&
                !isEmpty(excelData) &&
                refreshKey !== 0 && (
                    <CustomExcelFile
                        key={refreshKey}
                        name={datasetName}
                        data={excelData}
                        header={excelHeader}
                    />
                )}
        </React.Fragment>
    );
};

export default DownloadSinglePageButton;

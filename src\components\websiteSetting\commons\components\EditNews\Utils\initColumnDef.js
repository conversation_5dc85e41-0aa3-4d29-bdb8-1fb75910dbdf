/** 欄位定義說明(欄位定義名稱與fuseki名稱相同):
 * (補充 1: key後面帶"ZH"、"EN"的，是語系tag)
 * (補充 2: 左邊是fuseki真實欄位名稱（不包括語系tag), 右邊是api帶出來的欄位名稱)
 * 1. newsType: 消息分類，目前(20220919)有四種分類: 活動、公告、報導、書訊
 * 2. status: ["發佈", "不發佈"]
 * 3. newsSource: 最新消息出處
 * 4. newsCreator: 上傳者名稱
 * 5. hasStartDate: 開始發佈時間
 * 6. hasEndDate: 結束發佈時間
 * 7. top: 置頂
 * 8. titleZH: 中文標題
 * 9. titleEN: 英文標題
 * 10. externalLinkZH: 中文外部連結
 * 11. externalLinkEN: 英文外部連結
 * 12. webcacheZH: 中文頁庫存連結
 * 13. webcacheEN: 英文頁庫存連結
 * 14. fileAvailableAt: 附件連結
 * 15. hasURL: 輪播圖圖片
 * 16. contentZH: 中文內文
 * 17. contentEN: 英文內文
 * 18. newsIdStr: News event Id
 * */

const initColumnDef = {
    newsType: "newsType",
    status: "status",
    newsSource: "newsSource",
    newsCreator: "newsCreator",
    hasStartDate: "hasStartDate",
    hasEndDate: "hasEndDate",
    top: "top",
    titleZH: "titleZH",
    titleEN: "titleEN",
    externalLinksZH: "externalLinkZH",
    externalLinksEN: "externalLinkEN",
    webcacheZH: "webcacheZH",
    webcacheEN: "webcacheEN",
    fileAvailableAt: "allfileAvailableAt",
    hasURL: "allurlStr",
    contentZH: "contentZH",
    contentEN: "contentEN",
    newsIdStr: "newsIdStr"
};

export default initColumnDef;

/** imgURL object column define
 * {
 *  urlId: URLEvent id
 *  imgText: image desc
 *  order: image order
 *  imgUrl: full image url, split into "imagePath" and "imageName" before save data
 * }
 */

export const imageColDef = {
    urlId: "urlId",
    imgText: "imgText",
    order: "order",
    imgUrl: "imgUrl"
};

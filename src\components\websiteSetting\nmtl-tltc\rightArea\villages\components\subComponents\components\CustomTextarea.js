import React, { useEffect, useState } from "react";
import { Form, Grid } from "semantic-ui-react";
import "../../EditVillagesDetail.scss";

const CustomTextarea = ({
    type,
    rowName,
    value,
    updatedData,
    subRowName,
    debouncedUpdateFct
}) => {
    const [localValue, setLocalValue] = useState(value);

    useEffect(() => {
        setLocalValue(value);
    }, [value]);

    const onChangeFct = e => {
        const newValue = e.target.value;
        setLocalValue(newValue);
        debouncedUpdateFct(updatedData, type, newValue);
    };

    return (
        <Grid.Row>
            <Grid.Column
                width={3}
                style={{
                    backgroundColor: "#e0e1e2"
                }}
            >
                <div className="topArea__left">
                    <span>{rowName}</span>
                    {subRowName && (
                        <div className="topArea__left--subRowName">
                            {subRowName.includes("、") ? (
                                <>
                                    <span>{subRowName.split("、")[0]}</span>
                                    <span>{subRowName.split("、")[1]}</span>
                                </>
                            ) : (
                                <span>{subRowName}</span>
                            )}
                        </div>
                    )}
                </div>
            </Grid.Column>
            <Grid.Column width={13}>
                <div className="topArea__right">
                    <div className="topArea__right--box">
                        <Form style={{ height: "100%", width: "100%" }}>
                            <textarea
                                onChange={e => onChangeFct(e)}
                                value={localValue}
                                style={{ resize: "none", height: "100%" }}
                            />
                        </Form>
                    </div>
                </div>
            </Grid.Column>
        </Grid.Row>
    );
};

export default CustomTextarea;

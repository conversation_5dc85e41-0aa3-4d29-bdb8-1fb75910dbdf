import React from "react";
import PropTypes from "prop-types";

// semantic ui
import { Form, TextArea } from "semantic-ui-react";

function UpdateText({ desc, onChange, ...props }) {
  return (
    <Form style={{ height: "100%", width: "100%" }}>
      <TextArea
        style={{ resize: "none", height: "100%" }}
        // disabled={isEditedDisable}
        value={desc}
        onChange={onChange}
        {...props}
      />
    </Form>
  );
}

UpdateText.propTypes = {
  /** 更改文字 */
  desc: PropTypes.string,
  /** onChange function */
  onChange: PropTypes.func,
};

UpdateText.defaultProps = {
  /** 更改文字 */
  desc: "",
  /** onChange function */
  onChange: () => null,
};

export default UpdateText;

import React, { useContext, useEffect, useState } from "react";

// semantic-ui-react
import { Table } from "semantic-ui-react";

// components
import LanguageSelect from "../../components/LanguageSelect";
import UpdateText from "../../components/UpdateText";
import SaveButton from "../../components/SaveButton";

// general
import { sortByPriority } from "../../commons";
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import { isEmpty } from "../../../../commons";

function LiteralData() {
    const [language, setLanguage] = useState("zh");
    const [state, dispatch] = useContext(StoreContext);
    const { originData, menuActiveItem } = state.websiteSetting;
    const [allData, setAllData] = useState([]);

    useEffect(() => {
        // 沒有下拉選單
        dispatch({
            type: Act.SET_SELECTOPTION,
            payload: null
        });
    }, []);

    useEffect(() => {
        if (isEmpty(originData)) return;
        const tmpObj = originData.find(
            element => element.id === menuActiveItem.key
        )[language];
        const tmpData = [];
        Object.values(tmpObj).forEach(element => {
            tmpData.push(element);
        });
        setAllData(sortByPriority(tmpData));
    }, [language, originData]);

    return (
        <div className="LiteralData">
            <div className="topArea">
                <LanguageSelect language={language} setLanguage={setLanguage} />
            </div>
            <div className="listArea">
                <Table>
                    <Table.Header>
                        <Table.Row>
                            <Table.Cell>
                                <h2>標題</h2>
                            </Table.Cell>
                            <Table.Cell>
                                <h2>使用說明</h2>
                            </Table.Cell>
                        </Table.Row>
                    </Table.Header>
                    <Table.Body>
                        {allData.length > 0 &&
                            allData.map((data, index) => (
                                <Table.Row
                                    className={`row_${index}`}
                                    key={index}
                                >
                                    <Table.Cell style={{ width: "20%" }}>
                                        <UpdateText
                                            language={language}
                                            option={{
                                                priority: data.priority,
                                                message: data.title,
                                                column: "title"
                                            }}
                                        />
                                    </Table.Cell>
                                    <Table.Cell style={{ width: "20%" }}>
                                        <UpdateText
                                            language={language}
                                            option={{
                                                priority: data.priority,
                                                message: data.description,
                                                column: "description"
                                            }}
                                        />
                                    </Table.Cell>
                                </Table.Row>
                            ))}
                    </Table.Body>
                </Table>
            </div>
            <div className="btnArea">
                <SaveButton language={language} />
            </div>
        </div>
    );
}

export default LiteralData;

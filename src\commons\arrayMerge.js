import { graphOptions, literaryOptions } from '../components/common/sheetCrud/config';
import {
  tPersonID,
  hasRelationship,
  hasFoundationType,
  hasFoundationRelated,
} from '../components/common/sheetCrud/sheetCrudHelper';
import { isEmpty } from './index';

const arrayMerge = {};

// 根據 id 合併其它所有鍵值為 array
const arrayMergeSheet = (input) => {
  // srcId, p, o
  // RelationShip: srcId, p, o, relationType
  // hasFoundationRelated: srcId, p, o, hasFoundationRelated
  const resMap = {};
  input.forEach((item) => {
    const { srcId, p, o, relationType, foundationType } = item;
    if (isEmpty(o)) return;
    const newO = o.startsWith('DAE') ? o.substring(3) : o;

    if (Object.hasOwn(resMap, srcId)) {
      // 存在
      if (relationType) {
        // RelationShip
        if (Object.hasOwn(resMap[srcId], hasRelationship)) {
          resMap[srcId][hasRelationship].push(p);
          resMap[srcId][tPersonID].push(newO);
        } else {
          resMap[srcId][hasRelationship] = [p];
          resMap[srcId][tPersonID] = [newO];
        }
        return;
      }

      if (foundationType) {
        // Foundation
        if (Object.hasOwn(resMap[srcId], hasFoundationType)) {
          resMap[srcId][hasFoundationType].push(p);
          resMap[srcId][hasFoundationRelated].push(newO);
        } else {
          resMap[srcId][hasFoundationType] = [p];
          resMap[srcId][hasFoundationRelated] = [newO];
        }
        return;
      }

      if (Object.hasOwn(resMap[srcId], p)) {
        resMap[srcId][p].push(newO);
      } else {
        resMap[srcId][p] = [newO];
      }
    } else {
      // 不存在
      if (relationType) {
        // RelationShip
        resMap[srcId] = { [hasRelationship]: [p], [tPersonID]: [newO] };
        return;
      }
      if (foundationType) {
        // Foundation
        resMap[srcId] = {
          [hasFoundationType]: [p],
          [hasFoundationRelated]: [newO],
        };
        return;
      }
      resMap[srcId] = { [p]: [newO] };
    }
  });

  // 把 array 轉為 object，在 createState 才能分別控制!!
  return Object.keys(resMap).map((perId) =>
    Object.assign(
      {},
      ...Object.keys(resMap[perId]).map((cellId) => ({
        [cellId]: Object.assign({}, resMap[perId][cellId]),
      })),
      { srcId: perId },
    ),
  );
};

arrayMerge.sheet = arrayMergeSheet;

// 根據 id 合併其它所有鍵值為 array
// input: 'graph', 'startDate', 'authorName', 'type'
// [{graph: "全臺詩", startDate: "PER1", authorName: "作者名", type: '詩文作品', source: '作品名1', typeId: 'ART111'},
// {graph: "全臺詩", startDate: "PER1", authorName: "作者名", type: '詩文作品', source: '作品名2', typeId: 'ART222'},]
// return:
// [{graph: "全臺詩", startDate: "PER1", authorName: "作者名", type: '詩文作品', source: '作品名1', typeId: 'ART111',
// mergedList: {source: ['作品名1', '作品名2'], typeId: ['ART111', 'ART222']}}]
const arrayMergeTimeline = (input, keys, mergeKeys, mergedName = 'mergedList') =>
  input.reduce((re, obj) => {
    const item = re.find((o) => {
      let match = true;

      // 不存在此 property
      keys.forEach((key) => {
        // not found
        if (Object.keys(obj).indexOf(key) < 0) {
          match = false;
        }
        if (Object.keys(o).indexOf(key) < 0) {
          match = false;
        }

        // not the same
        if (o[key] !== obj[key]) {
          match = false;
        }
      });
      return match ? o : null;
    });

    const mergedObj = (inObj, inKeys) => {
      const mObj = {};
      inKeys.forEach((k) => {
        mObj[k] = inObj[k];
      });
      return mObj;
    };

    if (item) {
      item[mergedName] = item[mergedName].concat(mergedObj(obj, mergeKeys));
    } else {
      // eslint-disable-next-line no-param-reassign
      obj[mergedName] = [mergedObj(obj, mergeKeys)];
      re.push(obj);
    }
    return re;
  }, []);

arrayMerge.timeline = arrayMergeTimeline;

const unintegratedMergeSheet = (input) => {
  const resArr = [];
  input
    .filter(({ p, o }) => p !== 'lastModified' && !isEmpty(o))
    .forEach((item) => {
      const { srcId, p, o, g } = item;

      const newO = o.startsWith('DAE') ? o.substring(3) : o;

      const foundLiteraryItem = literaryOptions.find((el) => el.id === g);
      const foundGraphItem = graphOptions.find((el) => el.id === g);

      const existingItem = resArr.find(
        (el) =>
          (el.srcId === srcId && el.literary && el.literary['0'] === g) ||
          (foundGraphItem && el.srcId === srcId && el.graph && el.graph['0'] === g),
      );

      if (existingItem) {
        existingItem[p] = { '0': newO };
      } else if (foundLiteraryItem) {
        resArr.push({
          srcId,
          [p]: { '0': newO },
          literary: { '0': g },
          unintegrated: true,
        });
      } else if (foundGraphItem) {
        resArr.push({
          srcId,
          [p]: { '0': newO },
          graph: { '0': g },
          unintegrated: true,
        });
      }
    });

  return resArr;
};

arrayMerge.unintegratedMergeSheet = unintegratedMergeSheet;

export default arrayMerge;

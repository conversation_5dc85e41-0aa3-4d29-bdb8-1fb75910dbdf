import React from "react";

// ui
import { Segment, Divider } from "semantic-ui-react";

// custom
import AlertMessage from "../../../dataset/subComponents/alertMessage";
import CustomTab from "./CustomTab";
import textConfig from "../Utils/textConifg";

// scss
import "./UserInfo.scss";

const UserInfo = () => {
    console.log("Hi, I am accountManagement");

    return (
        <div className="UserInfo">
            <Segment basic compact>
                <h2>{textConfig.USERINFO_TITLE}</h2>
            </Segment>
            <Divider />
            <AlertMessage />
            <CustomTab />
            <br />
        </div>
    );
};

export default UserInfo;

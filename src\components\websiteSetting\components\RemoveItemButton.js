import React, { useContext, useEffect } from "react";
import { <PERSON><PERSON>, Icon } from "semantic-ui-react";
import { StoreContext } from "../../../store/StoreProvider";
import { sortByPriority, returnEmptyKeyName } from "../commons";
import Act from "../../../store/actions";
import { isEmpty } from "../../../commons";

// 把刪除後剩下的element依照priority從小到大重新編號
function reorderPriority(tmpArr) {
    const sortTmpArr = sortByPriority(tmpArr);
    for (let i = 1; i <= sortTmpArr.length; i++) {
        sortTmpArr[i - 1].priority = i;
    }
    return sortTmpArr;
}

function updateMainCarouselAllData(tmpAllData, menuActiveItem, setAllData) {
    const tmpObj = tmpAllData.find(element => element.id === menuActiveItem.key);
    if (tmpObj) {
        const keys = Object.keys(tmpObj).filter(element => element !== "id");
        const tmpData = [];
        keys.forEach(element => tmpData.push(tmpObj[element]));
        setAllData(sortByPriority(tmpData));
    }
}

function updateLinkingAllData(tmpAllData, menuActiveItem, selectOption, setAllData) {
    // console.log("tmpAllData ", tmpAllData);
    const dropDown = tmpAllData.find(element => element.id === menuActiveItem.key);
    if (isEmpty(dropDown) || selectOption === "") return;
    const tmpDropDown = [];
    const keys = Object.keys(dropDown[selectOption]).filter(element => element !== "name");
    keys.forEach(key => tmpDropDown.push(dropDown[selectOption][key]));
    // console.log("tmpDropDown ", tmpDropDown);
    setAllData(sortByPriority(tmpDropDown));
}

function RemoveItemButton({ data, setAllData }) {
    const [state, dispatch] = useContext(StoreContext);
    const { updatedData, menuActiveItem, selectOption } = state.websiteSetting;

    const removeItem = () => {
        const tmpAllData = JSON.parse(JSON.stringify(updatedData));
        let tmpObj = tmpAllData.find(element => element.id === menuActiveItem.key);
        let tmpObjList = [];
        if(selectOption) {
            // 處理LinkingPage
            // tmpObjList = Object.values(tmpObj[selectOption][language]);
            const keys = Object.keys(tmpObj[selectOption]).filter(element => element !== "name");
            keys.forEach(element => tmpObjList.push(tmpObj[selectOption][element]));
            tmpObjList = sortByPriority(tmpObjList);
        } else {
            // 處理MainCarousel
            const keys = Object.keys(tmpObj).filter(element => element !== "id");
            const tmpObjValue = [];
            keys.forEach(element => tmpObjValue.push(tmpObj[element]));
            tmpObjList = sortByPriority(tmpObjValue);
        }
        const remainList = tmpObjList.filter(element => element.priority !== data.priority);
        let keyName = "";
        if(selectOption) {
            // 處理LinkingPage
            keyName = returnEmptyKeyName(selectOption);
        } else {
            // 處理MainCarousel
            keyName = returnEmptyKeyName("carousel");
        }
        const remainListObj = {};
        reorderPriority(remainList).forEach(element => {remainListObj[keyName + element.priority] = element;});
        if(selectOption) {
            remainListObj.name = tmpObj[selectOption].name;
            tmpObj[selectOption] = remainListObj;
        } else {
            remainListObj.id = tmpObj.id;
            tmpObj = remainListObj;
        }
        const tmpObjIndex = tmpAllData.findIndex(element => element.id === menuActiveItem.key);
        tmpAllData[tmpObjIndex] = tmpObj;
        // console.log("tmpAllData ", tmpAllData);
        dispatch({
            type: Act.SET_UPDATEDDATA,
            payload: tmpAllData
        });

        if (setAllData) {
            // MainCarousel才會傳setAllData
            switch (menuActiveItem.key) {
                case "MainCarousel":
                    updateMainCarouselAllData(tmpAllData, menuActiveItem, setAllData);
                    break;
                case "LinkingPage":
                    updateLinkingAllData(tmpAllData, menuActiveItem, selectOption, setAllData);
                    break;
                default:
                    break;
            }
        }
    };

    return (
        <Button
            icon
            circular
            style={{ background: "inherit", fontSize: "large" }}
            onClick={removeItem}
        >
            <Icon name="cancel" style={{ color: "red" }} />
        </Button>
    );
}

export default RemoveItemButton;

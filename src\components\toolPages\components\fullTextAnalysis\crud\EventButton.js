import React from "react";
import ColumnModal from "./ColModal";

// 其他功能選單
const EventButton = ({ exclude }) => {
    const events = [{ name: "columns", component: ColumnModal }];
    const filterEvt = events.filter(evt => !(exclude || []).includes(evt.name));

    return (
        <div
            style={{
                display: "flex",
                justifyContent: "flex-end",
                columnGap: "10px"
            }}
        >
            {filterEvt.map((evt, idx) => (
                // eslint-disable-next-line react/no-array-index-key
                <evt.component key={idx.toString()} />
            ))}
        </div>
    );
};

export default EventButton;

import React, { useContext, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";

// components
import { Button, Segment } from "semantic-ui-react";
import QuillArea from "./subComponents/area/QuillArea";
import FormArea from "./subComponents/area/FormArea";
import TidbitsArea from "./subComponents/area/TidbitsArea";
import CustomButton from "./subComponents/components/CustomButton";
import EventArea from "./subComponents/area/EventArea";
import CustomRequiredModal from "./subComponents/components/CustomRequiredModal";

// store & api
import Api from "../../../../../../api/nmtl/Api";
import VillagesAct from "../VillagesAction";
import { StoreContext } from "../../../../../../store/StoreProvider";

// utils
import { isEmpty } from "../../../../../../commons";
import { areObjectsEqual } from "../utils/utils";
import { createHistoryEvent } from "../../../../../downloadData/components/history/common/common";
import useGetSubjectOPs from "../../../../../common/hooks/useGetSubjectOPs";

const EditVillages = () => {
    const dispatch = useDispatch();
    // eslint-disable-next-line no-unused-vars
    const [globalState, _] = useContext(StoreContext);
    const [stateContext] = useContext(StoreContext);
    const { user } = stateContext;
    const { displayName } = user;
    const { websiteSubject, menuActiveItem } = globalState.websiteSetting;
    const { headerActiveName } = stateContext.common;
    const { groupInfo } = stateContext.data;
    const dropOptions = useGetSubjectOPs(groupInfo);

    const {
        editingVillageId,
        notifyUpdateActivityInfo,
        isEditedActivityInfoList,
        translatorContentZhForQuill,
        translatorContentEnForQuill,
        villageList
    } = useSelector(state => state);

    const [villageData, setVillageData] = useState(null);
    const [villageUpdateData, setVillageUpdateData] = useState(null);
    const [activityInfoList, setActivityInfoList] = useState([]);
    const [requiredModalOpen, setRequiredModalOpen] = useState(false);

    const sheetSelectedValue = dropOptions.find(it => it.key === websiteSubject)
        ?.text;
    const columns = [headerActiveName, sheetSelectedValue, menuActiveItem.name];

    const handleCancel = () => {
        dispatch({
            type: VillagesAct.SET_TRANSLATOR_CONTENT_ZH_FOR_QUILL,
            payload: ""
        });

        dispatch({
            type: VillagesAct.SET_TRANSLATOR_CONTENT_EN_FOR_QUILL,
            payload: ""
        });
        dispatch({
            type: VillagesAct.SET_IS_EDITED_VILLAGES,
            payload: false
        });
    };

    const handleUpdate = async updatedData => {
        function combineZhEnKeys(data) {
            const result = {};
            // eslint-disable-next-line no-restricted-syntax
            for (const key in data) {
                if (key.endsWith("Zh") || key.endsWith("En")) {
                    const baseKey = key.slice(0, -2);
                    if (!result[baseKey]) {
                        result[baseKey] = [];
                    }
                    if (key.endsWith("Zh")) {
                        result[baseKey].unshift(
                            data[key] ? `${data[key]}@zh` : ""
                        );
                    } else {
                        result[baseKey].push(
                            data[key] ? `${data[key]}@en` : ""
                        );
                    }
                } else {
                    result[key] = data[key];
                }
            }
            return result;
        }
        const tmpUpdatedData = updatedData;

        const combinedSrcData = combineZhEnKeys(villageData);
        const combinedDstData = combineZhEnKeys(tmpUpdatedData);

        const entrySrc = {
            graph: "settings",
            classType: "VillageEvent",
            value: combinedSrcData,
            srcId: editingVillageId
        };

        const entryDst = {
            graph: "settings",
            classType: "VillageEvent",
            value: combinedDstData,
            srcId: editingVillageId
        };

        const updateHistoryMsg = `${JSON.stringify(
            entrySrc
        )}\n變動後：\n${JSON.stringify(entryDst)}`;

        const createHistoryMsg = `${JSON.stringify(entryDst)}`;

        // 建立歷史紀錄
        createHistoryEvent(
            displayName,
            isEmpty(combinedSrcData) ? "創建" : "更新",
            `${columns.join("/")}：${
                isEmpty(combinedSrcData) ? createHistoryMsg : updateHistoryMsg
            }`
        );

        await axios.put(Api.getGeneric, { entrySrc, entryDst });
    };

    const handleConnectCardtltc = async () => {
        const ogVilId = villageList.map(i => i.id);
        const entrySrc = {
            classType: "FrontEdit",
            graph: "settings",
            srcId: "CARDtltc",
            value: {
                hasVillageEvent: [...ogVilId]
            }
        };
        const entryDst = {
            classType: "FrontEdit",
            graph: "settings",
            srcId: "CARDtltc",
            value: {
                hasVillageEvent: [...ogVilId, editingVillageId]
            }
        };
        await axios.put(Api.getGeneric, { entrySrc, entryDst });
    };

    const handleSave = async () => {
        // 檢查必填欄位是否已填寫完成
        if (!villageUpdateData?.labelZh || !villageUpdateData?.labelEn) {
            setRequiredModalOpen(true);
            return;
        }

        // 更新譯者名人堂
        const quillDataSwitch = {
            translatorContentZh: translatorContentZhForQuill,
            translatorContentEn: translatorContentEnForQuill
        };

        const updatedData = Object.assign(
            {},
            villageUpdateData,
            quillDataSwitch
        );

        // imageName、hasVillageTidbits在各自的area更新
        ["imageName", "hasVillageTidbits"].forEach(prop => {
            // eslint-disable-next-line no-unused-expressions
            villageData[prop] && delete villageData[prop];
            // eslint-disable-next-line no-unused-expressions
            updatedData[prop] && delete updatedData[prop];
        });

        if (isEmpty(villageData) && !isEmpty(updatedData)) {
            await handleConnectCardtltc();
            await handleUpdate(updatedData);
        } else if (!areObjectsEqual(villageData, updatedData)) {
            await handleUpdate(updatedData);
        } else if (areObjectsEqual(villageData, updatedData)) {
            dispatch({
                type: VillagesAct.SET_IS_EDITED_VILLAGES,
                payload: false
            });
            return;
        }

        dispatch({
            type: VillagesAct.SET_TRANSLATOR_CONTENT_ZH_FOR_QUILL,
            payload: ""
        });

        dispatch({
            type: VillagesAct.SET_TRANSLATOR_CONTENT_EN_FOR_QUILL,
            payload: ""
        });

        dispatch({
            type: VillagesAct.SET_IS_EDITED_VILLAGES,
            payload: false
        });
    };

    const villageUpdateDataHandler = (d, key, val) => {
        const updateObjectValue = (obj, tmpKey, tmpValue) => {
            if (!key) return [];
            const tmpObj = JSON.parse(JSON.stringify(obj));
            tmpObj[tmpKey] = tmpValue;
            return tmpObj;
        };
        const updatedData = updateObjectValue(d, key, val);
        setVillageUpdateData(isEmpty(updatedData) ? d : updatedData);
    };

    useEffect(() => {
        if (!editingVillageId) return;

        const getVillagesData = async () => {
            const api = Api.getVillagesInfo.replace("{id}", editingVillageId);
            await axios.get(api).then(res => {
                const result = res?.data?.data[0];
                setVillageData(isEmpty(result) ? {} : result);
            });
        };
        getVillagesData();
    }, [editingVillageId]);

    useEffect(() => {
        if (!editingVillageId) return;

        const getActivityInfoList = async () => {
            const api = Api.getVillagesActivityInfoList.replace(
                "{id}",
                editingVillageId
            );
            await axios
                .get(api)
                .then(res => setActivityInfoList(res?.data?.data));
        };
        getActivityInfoList();
    }, [editingVillageId, notifyUpdateActivityInfo, isEditedActivityInfoList]);

    useEffect(() => {
        setVillageUpdateData(villageData);
    }, [villageData]);

    return (
        <>
            <div
                style={{
                    display: "flex",
                    width: "100%",
                    alignItems: "center",
                    // overflow: "scroll",
                    padding: "20px",
                    height: "100%",
                    flexDirection: "column"
                }}
            >
                <Segment className="EditCate">
                    <div
                        style={{
                            display: "flex",
                            justifyContent: "space-between"
                        }}
                    >
                        <div className="topArea">
                            <h1>新增內容</h1>
                        </div>
                        <div className="topArea">
                            <Button
                                color="grey"
                                content="取消"
                                onClick={handleCancel}
                            />
                            <CustomButton
                                onClick={handleSave}
                                message="儲存成功"
                                content="儲存"
                                color="green"
                            />
                        </div>
                    </div>
                    {villageData && (
                        <>
                            <div
                                style={{
                                    border: "1px solid rgba(34,36,38,.1",
                                    padding: "1rem",
                                    marginBottom: "1rem"
                                }}
                            >
                                <div className="topArea">
                                    <h3>上傳封面圖片</h3>
                                </div>
                                <FormArea
                                    updateFct={villageUpdateDataHandler}
                                    data={villageData}
                                    updatedData={villageUpdateData}
                                />
                            </div>
                            <div
                                style={{
                                    border: "1px solid rgba(34,36,38,.1",
                                    padding: "1rem",
                                    marginBottom: "1rem",
                                    display: "flex",
                                    flexDirection: "column"
                                }}
                            >
                                <EventArea
                                    list={activityInfoList}
                                    user={user}
                                    websiteSubject={websiteSubject}
                                />
                            </div>
                            <div
                                style={{
                                    border: "1px solid rgba(34,36,38,.1",
                                    padding: "1rem"
                                }}
                            >
                                <QuillArea updatedData={villageUpdateData} />
                            </div>
                            <div
                                style={{
                                    border: "1px solid rgba(34,36,38,.1",
                                    padding: "1rem"
                                }}
                            >
                                <TidbitsArea
                                    updateFct={villageUpdateDataHandler}
                                    data={villageData}
                                    updatedData={villageUpdateData}
                                />
                            </div>
                        </>
                    )}
                </Segment>
                <div
                    style={{
                        display: "flex",
                        width: "100%",
                        justifyContent: "flex-end"
                    }}
                />
                <CustomRequiredModal
                    onClick={setRequiredModalOpen}
                    open={requiredModalOpen}
                    fieldName="中、英文標題"
                />
            </div>
        </>
    );
};

export default EditVillages;

import React, { useState, useContext, useEffect } from 'react';
import { <PERSON><PERSON>, Modal } from 'semantic-ui-react';
import axios from 'axios';
import { StoreContext } from '../../../store/StoreProvider';
import { imagePath, sortByPriority, imageTypeTrans } from '../commons';

// configs
import UploadConfig from '../../toolPages/components/upload/uploadConfig';
import { fileServerAPI, fileServerMethod } from '../../../api/fileServer';
import Api from '../../../api/nmtl/Api';
import NMTL_WEB_CONFIGS from '../nmtl-web/config';

// components
import DropFileRegion from './DropFileRegion';
import Act from '../../../store/actions';
import SaveResultModal from '../commons/components/SaveResultModal';

// functions
import { updateNmtlData } from '../../../api/nmtl';

const { VRMUSEUM, IMAGE, WATERMARK } = NMTL_WEB_CONFIGS.MENU_ACTIVE_ITEM;

// 當updatedData有更改後，畫面需要立即變化，設定在allData變數，不能設定在updatedData，會有一直重複render的問題
function updateAllData(finalUpdatedData, menuActiveItem, selectOption, imageType, setAllData) {
    const tmpAllData = finalUpdatedData.find((element) => element.id === menuActiveItem.key);
    switch (menuActiveItem.key) {
        case 'MainIntroduction':
            setAllData(tmpAllData);
            break;

        case 'MainCarousel':
            {
                const keys = Object.keys(tmpAllData).filter((element) => element !== 'id');
                const tmpData = [];
                keys.forEach((element) => tmpData.push(tmpAllData[element]));
                setAllData(sortByPriority(tmpData));
            }
            break;

        case 'MainCardImage':
        case 'CardImageUpdater':
            setAllData(tmpAllData[selectOption].url);
            break;

        case VRMUSEUM:
            setAllData(tmpAllData[selectOption].url[imageType]);
            break;

        default:
            break;
    }
}

const uploadLogoHandler = ({ folder, logoFiles }) => {
    const formData = new FormData();

    logoFiles.forEach((file) => {
        const timestamp = Date.now();
        const fileNames = file.name.split('.');

        formData.append(
            UploadConfig.ImageFormName,
            file,
            `${fileNames[0]}-${timestamp}.${fileNames[1]}`,
        );
    });

    return axios({
        method: fileServerMethod.uploadFile,
        url: `${fileServerAPI.uploadLogo}/${folder}`,
        headers: {
            'Access-Control-Allow-Origin': '*',
            'Content-Type': 'multipart/form-data',
        },
        data: formData,
    });
};

function UploadImageModal({ setOpenModal, imageType, setAllData, imageWebOrMobile }) {
    const [state, dispatch] = useContext(StoreContext);
    const {
        updatedData,
        menuActiveItem,
        selectOption,
        listData,
        fusekiData: {
            vrMuseum: {
                logo: { oriData, tempData },
            },
        },
    } = state.websiteSetting;
    const [path, setPath] = useState(`${imagePath.root}/${imagePath[menuActiveItem.key]}`);
    const [imageDownloadPath, setImageDownloadPath] = useState('');
    const [finalUpdatedData, setFinalUpdatedData] = useState([]);
    const [uploading, setUploading] = useState(false);
    const [logoFiles, setLogoFiles] = useState([]);
    const [resultModal, setResultModal] = useState({
        isOpen: false,
        message: '',
    });

    const isVrMuseum = menuActiveItem?.key === VRMUSEUM;

    useEffect(() => {
        // VrMuseum才會傳imageType
        // 這裡的path只有路徑，不包含檔案名稱
        if (imageType) {
            setPath(
                `${imagePath.root}/${imagePath[menuActiveItem.key][menuActiveItem.key]}/${
                    imagePath[menuActiveItem.key][imageType]
                }`,
            );
        }
        setImageDownloadPath('');
    }, []);

    useEffect(() => {
        if (imageDownloadPath === '') return;
        const tmpAllData = JSON.parse(JSON.stringify(updatedData));
        const tmpObj = tmpAllData.find((element) => element.id === menuActiveItem.key);
        if (menuActiveItem.key === 'MainCardImage' || menuActiveItem.key === 'CardImageUpdater') {
            tmpObj[selectOption].url = imageDownloadPath;
        } else if (menuActiveItem.key === VRMUSEUM) {
            const fileName = path.split('/')[2];
            tmpObj[selectOption].url[fileName] = imageDownloadPath;
        } else {
            const keys = Object.keys(tmpObj);
            const findKey = keys.find((key) => listData.priority === tmpObj[key].priority);
            if (listData.priority) {
                // 處理MainCarousel的資料寫入
                tmpObj[findKey][imageTypeTrans[imageWebOrMobile]] = imageDownloadPath;
            } else {
                // 處理MainIntroduction的資料寫入
                tmpObj[imageTypeTrans[imageWebOrMobile]] = imageDownloadPath;
            }
        }
        const tmpObjIndex = tmpAllData.findIndex((element) => element.id === menuActiveItem.key);
        tmpAllData[tmpObjIndex] = tmpObj;
        setFinalUpdatedData(tmpAllData);
    }, [imageDownloadPath]);

    // for update logo data to Apache Jena Fuseki
    const updateNmtlLogoData = async ({ imgFileName, user, api, dataset, sheetName }) => {
        const { graph, srcId, label, type, imageName, classType } = oriData;

        const entrySrc = {
            graph,
            srcId,
            classType,
            value: { label, imageName, type },
        };
        const entryDst = {
            ...entrySrc,
            value: {
                label,
                imageName: `${selectOption}/${imgFileName}`,
                type: IMAGE,
            },
        };

        await updateNmtlData(user, api, dataset, sheetName, entrySrc, entryDst)
            .then(() => {
                dispatch({
                    type: Act.SET_VRMUSEUM_LOGO,
                    payload: {
                        oriData: {
                            ...oriData,
                            label,
                            imageName: `${selectOption}/${imgFileName}`,
                            type: IMAGE,
                        },
                        tempData: {
                            ...tempData,
                            imageUrl: `${fileServerAPI.readLogoImage}/${selectOption}/${imgFileName}`,
                        },
                    },
                });
                setResultModal({
                    isOpen: true,
                    message: '上傳圖片成功!',
                });
            })
            .catch((error) => console.log(`Update Nmtl Data Fialed: ${error}`));
    };

    return (
        <Modal size="large" open onClose={() => setOpenModal(false)}>
            <Modal.Header>圖片上傳</Modal.Header>
            <Modal.Content>
                <DropFileRegion
                    path={path}
                    setImageDownloadPath={setImageDownloadPath}
                    setUploading={setUploading}
                    {...(isVrMuseum && {
                        logoFiles,
                        setLogoFiles,
                        validType: ['jpg', 'jpeg', 'png'],
                    })}
                    isVrMuseum={isVrMuseum && imageType === WATERMARK}
                />
                <SaveResultModal
                    openModal={resultModal.isOpen}
                    onClose={() => {
                        setOpenModal(false);
                    }}
                    onClick={() => {
                        setOpenModal(false);
                    }}
                    modalMessage={resultModal.message}
                />
            </Modal.Content>
            <Modal.Actions>
                <Button negative onClick={() => setOpenModal(false)}>
                    取消
                </Button>
                {uploading ? (
                    <Button
                        disabled={uploading === 'disabled'}
                        loading={uploading === true}
                        primary
                    >
                        確認
                    </Button>
                ) : (
                    <Button
                        primary
                        onClick={() => {
                            setUploading(true);
                            // upload logo image to file server
                            if (isVrMuseum && imageType === WATERMARK && oriData) {
                                uploadLogoHandler({ folder: selectOption, logoFiles })
                                    .then(async ({ data, status }) => {
                                        setUploading('disabled');

                                        // update logo data
                                        status === 200 &&
                                            data?.status === 'success' &&
                                            updateNmtlLogoData({
                                                imgFileName: data?.images?.at(0)?.imgFileName,
                                                user: state.user,
                                                api: Api.getGeneric,
                                                dataset: selectOption,
                                                sheetName: 'frontEdit Data',
                                            });
                                    })
                                    .catch((error) => {
                                        setResultModal({
                                            isOpen: true,
                                            message: `上傳圖片失敗: ${error.message}`,
                                        });
                                        console.log('Upload logo to file-server failed!');
                                    });
                            } else {
                                if (finalUpdatedData.length !== 0) {
                                    updateAllData(
                                        finalUpdatedData,
                                        menuActiveItem,
                                        selectOption,
                                        imageType,
                                        setAllData,
                                    );
                                    dispatch({
                                        type: Act.SET_UPDATEDDATA,
                                        payload: finalUpdatedData,
                                    });
                                }
                                setOpenModal(false);
                            }
                        }}
                    >
                        確認
                    </Button>
                )}
            </Modal.Actions>
        </Modal>
    );
}

export default UploadImageModal;

import React from "react";
import { But<PERSON>, Modal } from "semantic-ui-react";
import "../../EditVillagesDetail.scss";

const CustomRequiredModal = ({ onClick, open, fieldName }) => {
    const handleClick = () => {
        onClick(!open);
    };
    return (
        <Modal onClose={onClick} onOpen={onClick} open={open}>
            <Modal.Header>警告</Modal.Header>
            <Modal.Content>
                <Modal.Description>
                    請確認{fieldName}是否已填寫完整
                </Modal.Description>
            </Modal.Content>
            <Modal.Actions>
                <Button onClick={handleClick} positive>
                    確認
                </Button>
            </Modal.Actions>
        </Modal>
    );
};
export default CustomRequiredModal;

import React, { useEffect, useMemo, useState } from "react";
import { Form, Label, TextArea } from "semantic-ui-react";
import useDebounce from "../../../../common/hooks/useDebounce";

const CustomDoubleTextArea = ({
    cellId,
    createState,
    disabled = false,
    setCallback,
    defaultValue,
    subTitle,
    menuName
}) => {
    // const [state] = useContext(StoreContext);
    const [firstInput, setFirstInput] = useState(null);
    const [secondInput, setSecondInput] = useState(null);
    const debFirstInput = useDebounce(firstInput, 800); // debounce value
    const debSecondInput = useDebounce(secondInput, 800); // debounce value

    useEffect(() => {
        if (defaultValue) {
            let newDefaultValue = defaultValue;
            // FIXME:
            if (!Array.isArray(defaultValue)) {
                newDefaultValue = defaultValue.split("\n");
            }

            // 權威中文
            const zhContent =
                newDefaultValue.find(label => label?.endsWith("@zh")) || null;
            // 權威外文
            const otherContent = newDefaultValue
                .filter(el => el !== zhContent)
                .join("");

            if (zhContent) setFirstInput(zhContent);
            if (otherContent) setSecondInput(otherContent);
        }
    }, [menuName]);
    //
    // const styleForForm = {
    //     field: {
    //         display: "flex",
    //         justifyContent: "flex-start",
    //         alignItems: "center"
    //     },
    //     div: {
    //         display: "flex",
    //         flexDirection: "column",
    //         overflow: "visible",
    //         textWrap: "nowrap"
    //     }
    //     // label: { overflow: "unset", whiteSpace: "nowrap", margin: "1rem" }
    // };

    // const handleEditorInputChange = value => {
    //     const mkValue = convert2HtmlEntities(value);
    //     console.log("mkValue", mkValue);
    //     // setFirstInput(mkValue);
    // };

    const handleZhChange = value => {
        if (disabled) {
            return;
        }

        if (!value.endsWith("@zh")) {
            setFirstInput(value);
            return;
        }

        setFirstInput(value);
    };
    //
    const handleLangChange = value => {
        if (disabled) {
            return;
        }

        setSecondInput(value);
    };
    //
    useEffect(() => {
        if (debFirstInput === null) return;

        // debFirstInput === "" 存 null
        if (!debFirstInput) {
            setCallback(cellId, [null, debSecondInput], menuName);
            return;
        }

        setCallback(cellId, [debFirstInput, debSecondInput], menuName);
    }, [debFirstInput]);
    //

    useEffect(() => {
        if (debSecondInput === null) return;

        // debSecondInput === "" 存 null
        if (!debSecondInput) {
            setCallback(cellId, [debFirstInput, null], menuName);
            return;
        }

        setCallback(cellId, [debFirstInput, debSecondInput], menuName);
    }, [debSecondInput]);

    // useEffect(() => {
    //     setFirstInput(defaultValue ? defaultValue[0] || "" : "");
    //     setSecondInput(defaultValue ? defaultValue[1] || "" : "");
    // }, [menuName]);

    // const handleChange = (value, setInput) => {
    //     if (disabled) {
    //         return;
    //     }
    //
    //     setInput(value);
    // };
    //
    // useEffect(() => {
    //     if (!debFirstInput) return;
    //
    //     setCallback(cellId, [debFirstInput, debSecondInput]);
    // }, [debFirstInput]);
    // //
    //
    // useEffect(() => {
    //     if (!debSecondInput) return;
    //     setCallback(cellId, [debFirstInput, debSecondInput]);
    // }, [debSecondInput]);

    return useMemo(
        () => (
            <Form>
                <Form.Field>
                    <Label basic>
                        {`中文版網站${subTitle || "中文"}
                        ，請於內容最後填上語系@zh`}
                    </Label>
                    <TextArea
                        rows={5}
                        value={firstInput || ""}
                        onChange={(e, data) => handleZhChange(data.value)}
                    />
                </Form.Field>
                <Form.Field>
                    <Label basic>{`外文版網站${subTitle || "外文"}`}</Label>
                    <TextArea
                        rows={5}
                        value={secondInput || ""}
                        onChange={(e, data) => handleLangChange(data.value)}
                    />
                </Form.Field>
            </Form>
        ),
        [cellId, firstInput, secondInput, createState, menuName]
    );
};

export default CustomDoubleTextArea;

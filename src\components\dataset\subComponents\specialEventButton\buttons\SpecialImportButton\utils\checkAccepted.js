import * as Excel from "exceljs";

// config
import { checkRes, splitTag } from "../config/config";
import impSheetHeader from "../../../../../../../api/firebase/cloudFirestore/impSheetHeader";

// check utils
import checkHeaderStyle from "./checkHeaderStyle";
import checkHeaderOrder from "./checkHeaderOrder";
import checkHeaderCell from "./checkHeaderCell";
import checkSrcId from "./checkSrcId";
import checkLabel from "./checkLabel";
import checkLangCol from "./checkLangCol";
import checkLangTagCol from "./checkLangTagCol";
import checkURLStr from "./checkURLStr";
import checkCopyRightList from "./checkCopyRightList";
import checkTrueFalse from "./checkTrueFalse";
import checkYYYYMMDD from "./checkYYYYMMDD";
import checkLitGenre from "./checkLitGenre";
import checkZHTag from "./checkZHTag";
import checkInstLabel from "./checkInstLabel";
import checkIsNumber from "./checkIsNumber";
import checkMustHas from "./checkMustHas";

const { Person, Publication } = impSheetHeader;

/** @description 定義檢查項目:
 * [{
 *     [id] : {
 *        [checkRes.failed]: 檢查結果錯誤的顯示訊息,
 *        [checkRes.success]: 檢查結果正確的顯示訊息,
 *        checkMethod: 執行檢查的function
 *     }
 * }, ...]
 * */
const formatCheck = {
    header_style: {
        [checkRes.failed]: "標頭樣式有誤，請取消資料第一列的所有樣式設定。",
        [checkRes.success]: "標頭樣式正確",
        checkMethod: checkHeaderStyle
    },
    header_order: {
        [checkRes.failed]: "標頭順序有誤，請依照匯出資料標頭順序填寫。",
        [checkRes.success]: "標頭順序正確",
        checkMethod: checkHeaderOrder
    },
    header_cell: {
        [checkRes.failed]: "標頭內容有誤。",
        [checkRes.success]: "標頭內容正確",
        checkMethod: checkHeaderCell
    },
    /** [firebase /setting/dataset/sheet/[sheetName]/Headers的id]: {checkMethod: {check callback}} */
    // Person
    [Person.srcId]: { checkMethod: checkSrcId },
    [Person.label_Person]: { checkMethod: checkLabel },
    [Person.hasTranslationLanguage]: { checkMethod: checkLangCol },
    [Person.birthName]: { checkMethod: checkLangTagCol },
    [Person.penName]: { checkMethod: checkLangTagCol },
    [Person.otherName]: { checkMethod: checkLangTagCol },
    [Person.introduction]: { checkMethod: checkLangTagCol },
    [Person.externalLinks]: { checkMethod: checkURLStr },
    [Person.imageURL_hasURL]: { checkMethod: () => "" },
    [Person.hasCopyrightStatus_hasURL]: {
        checkMethod: checkCopyRightList
    },
    [Person.comment]: { checkMethod: () => "" },
    [Person.pictureDisplay]: { checkMethod: checkTrueFalse },
    // Publication
    [Publication.isTranslationBookOf]: { checkMethod: checkSrcId },
    [Publication.label_Publication]: { checkMethod: checkMustHas },
    [Publication.hasAuthor]: { checkMethod: checkInstLabel },
    [Publication.srcId]: { checkMethod: checkSrcId },
    [Publication.translationLanguage]: { checkMethod: checkLangCol },
    [Publication.authorName]: { checkMethod: checkLangTagCol },
    [Publication.hasTranslator]: { checkMethod: checkInstLabel },
    [Publication.translatorName]: { checkMethod: checkLangTagCol },
    [Publication.hasEditor]: { checkMethod: checkInstLabel },
    [Publication.hasPublisher]: { checkMethod: checkInstLabel },
    [Publication.hasInceptionDate]: { checkMethod: checkYYYYMMDD },
    [Publication.LiteraryGenre]: { checkMethod: checkLitGenre },
    [Publication.hasLanguageOfWorkOrName]: { checkMethod: checkLangCol },
    [Publication.translationBookName]: { checkMethod: checkMustHas },
    [Publication.totalPage]: { checkMethod: () => "" },
    [Publication.ISBN]: { checkMethod: () => "" },
    [Publication.references]: { checkMethod: checkZHTag },
    [Publication.introduction]: { checkMethod: checkZHTag },
    [Publication.tableOfContents]: { checkMethod: () => "" },
    [Publication.fileAvailableAt]: { checkMethod: () => "" },
    [Publication.hasFullWorkCopyright]: { checkMethod: checkCopyRightList },
    [Publication.imageURL_hasURL]: { checkMethod: () => "" },
    [Publication.hasCopyrightStatus_hasURL]: {
        checkMethod: checkCopyRightList
    },
    [Publication.comment]: { checkMethod: () => "" },
    [Publication.lift]: { checkMethod: checkTrueFalse },
    [Publication.peak]: { checkMethod: checkTrueFalse },
    [Publication.srcId_hasPlaceOfPublication]: {
        checkMethod: checkMustHas
    },
    [Publication.geoLatitude_hasPlaceOfPublication]: {
        checkMethod: checkIsNumber
    },
    [Publication.geoLongitude_hasPlaceOfPublication]: {
        checkMethod: checkIsNumber
    }
};

/**
 * acceptedFiles: 符合規定副檔名、數量的檔案
 * checkData: 用在檢查資料的有限內容，e.g. 表頭欄位、語系...
 */
const checkAccepted = (acceptedFiles, checkData) => {
    const { fbHeader, langList, copyRightList, literGenList } = checkData;
    const { perOrgList } = checkData;
    // 檢查資料欄位內容
    const promise = acceptedFiles.map(async file => {
        const workbook = new Excel.Workbook();
        await workbook.xlsx.load(file);
        let pass = checkRes.success;
        let fileRes = `${file.name} - `;
        workbook.eachSheet(worksheet => {
            let resStr = `表單: ${worksheet.name} 檢查結果:\n`;
            // check header
            const headerRow = worksheet.getRow(1);
            let tmpRes = formatCheck.header_style.checkMethod(headerRow.values);
            pass = pass === checkRes.success ? tmpRes : checkRes.failed;
            resStr += `${formatCheck.header_style[tmpRes]}\n`;
            // order
            tmpRes = formatCheck.header_order.checkMethod(
                headerRow.values,
                fbHeader
            );
            pass = pass === checkRes.success ? tmpRes : checkRes.failed;
            resStr += `${formatCheck.header_order[tmpRes]}\n`;
            // cell value
            tmpRes = formatCheck.header_cell.checkMethod(
                headerRow.values,
                fbHeader
            );
            pass = pass === checkRes.success ? tmpRes : checkRes.failed;
            resStr += `${formatCheck.header_cell[tmpRes]}\n`;

            // check data body
            worksheet.eachRow((row, rowNumber) => {
                if (rowNumber > 1) {
                    row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
                        const colName = worksheet.getRow(1).getCell(colNumber)
                            .value;

                        const [propLabel, propID] = colName.split(splitTag);
                        if (Object.hasOwn(formatCheck, propID)) {
                            let tmpStr = "";
                            if (
                                [
                                    Person.hasTranslationLanguage,
                                    Publication.hasLanguageOfWorkOrName
                                ].includes(propID)
                            ) {
                                tmpStr = formatCheck[propID].checkMethod(
                                    cell,
                                    propLabel,
                                    langList
                                );
                            } else if (
                                [
                                    Person.hasCopyrightStatus_hasURL,
                                    Publication.hasFullWorkCopyright,
                                    Publication.hasCopyrightStatus_hasURL
                                ].includes(propID)
                            ) {
                                tmpStr = formatCheck[propID].checkMethod(
                                    cell,
                                    propLabel,
                                    copyRightList
                                );
                            } else if (propID === Publication.LiteraryGenre) {
                                tmpStr = formatCheck[propID].checkMethod(
                                    cell,
                                    propLabel,
                                    literGenList
                                );
                            } else if (
                                [
                                    Publication.hasAuthor,
                                    Publication.hasTranslator,
                                    Publication.hasEditor,
                                    Publication.hasPublisher
                                ].includes(propID)
                            ) {
                                tmpStr = formatCheck[propID].checkMethod(
                                    cell,
                                    propLabel,
                                    perOrgList
                                );
                            } else if (
                                propID ===
                                Publication.label_Location_hasPlaceOfPublication
                            ) {
                                const latVal = worksheet.getColumn("AB")
                                    .values[2];
                                const longVal = worksheet.getColumn("AC")
                                    .values[2];
                                if (latVal || longVal) {
                                    tmpStr = formatCheck[propID]
                                        .checkMethod(cell, propLabel)
                                        .trim();
                                    if (tmpStr) {
                                        tmpStr = tmpStr.replace("。", "");
                                        tmpStr += "(經緯度欄位有填寫)。\n";
                                    }
                                }
                            } else {
                                tmpStr = formatCheck[propID].checkMethod(
                                    cell,
                                    propLabel
                                );
                            }

                            pass =
                                pass === checkRes.success && !tmpStr
                                    ? checkRes.success
                                    : checkRes.failed;
                            resStr += tmpStr;
                        }
                    });
                }
            });
            fileRes += resStr;
        });

        return { res: fileRes, pass };
    });

    return Promise.allSettled(promise).then(data => data);
};

export default checkAccepted;

import React, { useEffect, useRef, useState } from "react";
import { InView } from "react-intersection-observer";
import { Checkbox, Icon, Popup } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import { uuidv4 } from "../../../../../../../../commons/utility";
import CustomPagination from "../../../../../../../common/CustomPagination/CustomPagination";
import {
    formatDateTimeZoneTW,
    getImgFilenameFromUrl
} from "../../../../../../../common/imageCommon/gallery/galleryHelper";
import { handleGalleryChange, uploadFile } from "../../../utils/utils";

import { isEmpty } from "../../../../../../../../commons";
import { getFileName } from "../../../../../../commons/components/EditNews/Utils/saveDataUtils";
import Api from "../../../../../../../../api/nmtl/Api";
import { readNmtlData } from "../../../../../../../../api/nmtl";
import VillagesAct from "../../../VillagesAction";

const style = {
    modalTrigger: {
        width: "100px",
        height: "100px",
        margin: "0 8px 8px 0",
        padding: "4px",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        border: "2px dashed #737373",
        textAlign: "center",
        cursor: "pointer"
    }
};

const Gallery = ({ images, updateFct, updatedData }) => {
    const dispatch = useDispatch();
    const fileInputRef = useRef(null);

    const {
        curFolderFiles,
        notifyUploadTidbits,
        editingVillageId
    } = useSelector(state => state);
    const [urlSelect, setUrlSelect] = useState([]); // for single select
    const [imagesList, setImagesList] = useState(images); // 增加其他參數

    const pageOption = [50, 100, 200];
    const [curPage, setCurPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [perPageNum, setPerPageNum] = useState(pageOption[0]);

    const popupHeader = (imgFileName, url) => (
        <>
            {/* {url && <img src={url} alt="" style={{ width: "150px" }} />} */}
            <h3>{imgFileName || getImgFilenameFromUrl(url)}</h3>
        </>
    );

    const popupContent = contentStr => {
        if (!(typeof contentStr === "string")) return "";
        const content = contentStr.split("\n");
        const dtReg = /(?<prefix>.+)(?<datetime>\d{4}-\d{2}-\d{2}[A-Z\s]\d{2}:\d{2}:\d{2}\+\d{2}:\d{2})/;
        return content.map(ct => {
            // 若符合時間格式, 則轉換成特定時區格式
            const dtMatch = ct.match(dtReg);
            if (dtMatch) {
                const { prefix, datetime } = dtMatch.groups;
                return (
                    <div key={uuidv4()}>
                        {`${prefix}${formatDateTimeZoneTW(datetime)}`}
                    </div>
                );
            }
            return <div key={uuidv4()}>{ct}</div>;
        });
    };

    const handleClick = (e, tmpData) => {
        if (urlSelect.includes(tmpData.value)) {
            const newUrlSelect = urlSelect.filter(url => url !== tmpData.value);
            setUrlSelect(newUrlSelect);
        } else {
            setUrlSelect(prev => [...(prev || []), tmpData.value]);
        }
    };

    const handleShow = _images => _images && _images.length > 0;

    const handleImgClick = () => {};

    const handlePage = (evt, { activePage }) => {
        setCurPage(activePage);
    };

    const handleDDPage = (evt, tmpData) => {
        setCurPage(tmpData.value);
    };

    const handlePerPageNum = (evt, tmpData) => {
        setCurPage(1);
        setPerPageNum(tmpData.value);
    };

    const onChange = (e, tmpData) => {
        handleGalleryChange({
            file: tmpData,
            curFolderFiles,
            dispatch,
            selectMode: "multiple"
        });
    };

    useEffect(() => {
        const imgUrls = images.map(img => img.url);
        const newUrlSelect = [];
        imgUrls.forEach(url => {
            if (urlSelect.includes(url)) {
                newUrlSelect.push(url);
            }
        });
        setUrlSelect(newUrlSelect);

        const apiStr = Api.getVillageTidbitsList.replace("{ds}", "settings");
        readNmtlData(apiStr).then(() => {
            // const imageNames = res?.data.map(({ imageName }) => imageName);
            const tmpImages = images.map(img => ({
                ...img,
                // hasUsed: imageNames.includes(img.imgFileName)
                hasUsed: false
            }));
            setImagesList(tmpImages);
        });
    }, [images]);

    useEffect(() => {
        setTotalPages(Math.ceil(imagesList.length / perPageNum));
    }, [imagesList, perPageNum]);

    const handleChange = async e => {
        const tmpUpdatedData = JSON.parse(JSON.stringify(updatedData));
        if (isEmpty(tmpUpdatedData.hasVillageTidbits)) {
            tmpUpdatedData.hasVillageTidbits = [];
        }
        const folderName = `villages/${process.env.REACT_APP_VILLAGES_TIDBITS_FILE_SERVER_URL}/${editingVillageId}`;
        const fileURL = await uploadFile(
            e.currentTarget,
            "settings",
            tmpUpdatedData.hasVillageTidbits,
            folderName,
            "tidbits"
        );
        if (isEmpty(fileURL)) return;

        const postURLEvent = async i => {
            const imageName = getFileName(i);
            const entry = {
                graph: "settings",
                classType: "URLEvent",
                value: {
                    imageName,
                    imagePath: `settings/${folderName}`
                },
                srcId: ""
            };
            await axios.post(Api.getGeneric, { entry });
            return imageName;
        };

        const getURLEvent = async name => {
            const api = Api.getImageId
                .replace("{name}", name)
                .replace("{ds}", "settings");
            const result = await axios.get(api);
            return result?.data?.data[0].id;
        };

        const imageName = await Promise.all(fileURL.map(i => postURLEvent(i)));

        const res = await Promise.all(imageName.map(i => getURLEvent(i)));

        const saveHasVillageTidbits = async () => {
            const ogHasVillageTidbits = updatedData?.hasVillageTidbits?.split(
                "、"
            );
            const entrySrc = {
                graph: "settings",
                classType: "VillageEvent",
                srcId: editingVillageId,
                value: {
                    hasVillageTidbits: isEmpty(ogHasVillageTidbits)
                        ? []
                        : [...ogHasVillageTidbits]
                }
            };
            const entryDst = {
                graph: "settings",
                classType: "VillageEvent",
                srcId: editingVillageId,
                value: {
                    hasVillageTidbits: isEmpty(ogHasVillageTidbits)
                        ? res
                        : [...ogHasVillageTidbits, ...res]
                }
            };

            await axios.put(Api.getGeneric, { entrySrc, entryDst }).then(() => {
                updateFct(
                    updatedData,
                    "hasVillageTidbits",
                    isEmpty(ogHasVillageTidbits)
                        ? res.join("、")
                        : [...ogHasVillageTidbits, ...res].join("、")
                );
            });

            dispatch({
                type: VillagesAct.SET_NOTIFY_UPLOAD_TIDBITS,
                payload: !notifyUploadTidbits
            });
        };
        await saveHasVillageTidbits();
    };

    return (
        <>
            <div
                style={style.modalTrigger}
                onClick={() => {
                    // eslint-disable-next-line no-unused-expressions
                    fileInputRef.current && fileInputRef.current.click();
                }}
            >
                <Icon name="plus" />
                <input
                    id="tidbits"
                    ref={fileInputRef}
                    onChange={handleChange}
                    style={{
                        display: "none"
                    }}
                    type="file"
                    accept="image/*"
                    multiple
                />
            </div>
            {handleShow(imagesList) &&
                imagesList
                    .sort((a, b) => {
                        const dateA = new Date(
                            a.imgInfo.match(/建立日期：(.+)/)[1]
                        );
                        const dateB = new Date(
                            b.imgInfo.match(/建立日期：(.+)/)[1]
                        );
                        return dateB - dateA;
                    })
                    .slice((curPage - 1) * perPageNum, curPage * perPageNum)
                    .map((img, index) => {
                        const {
                            url,
                            imgInfo,
                            imgFileName,
                            originalUrl,
                            type,
                            hasUsed
                        } = img;
                        return (
                            <InView
                                key={uuidv4()}
                                triggerOnce
                                rootMargin="500px"
                            >
                                {/* eslint-disable-next-line no-unused-vars */}
                                {({ inView, ref, entry }) => (
                                    <div>
                                        <div
                                            ref={ref}
                                            className="thumb"
                                            data-cur-thumb-id={index}
                                            onClick={() => handleImgClick(img)}
                                            style={{
                                                border:
                                                    hasUsed && "5px red solid"
                                            }}
                                        >
                                            <Popup
                                                header={popupHeader(
                                                    imgFileName,
                                                    url
                                                )}
                                                content={popupContent(imgInfo)}
                                                trigger={
                                                    <div className="thumbInner">
                                                        <img
                                                            src={
                                                                inView
                                                                    ? url
                                                                    : ""
                                                            }
                                                            alt=""
                                                        />
                                                    </div>
                                                }
                                                size="small"
                                                hoverable
                                                popperModifiers={{
                                                    preventOverflow: {
                                                        boundariesElement:
                                                            "window"
                                                    }
                                                }}
                                            />

                                            <Checkbox
                                                className="img-checkbox"
                                                name=""
                                                value={url}
                                                checked={(
                                                    urlSelect || []
                                                ).includes(url)}
                                                data-img-url={url}
                                                data-img-original-url={
                                                    originalUrl
                                                }
                                                data-img-info={imgInfo}
                                                data-img-file-name={imgFileName}
                                                onClick={(e, data) =>
                                                    handleClick(e, data)
                                                }
                                                // 判斷是「圖片」image或是「檔案」docs
                                                actiontype={type}
                                                onChange={(e, data) => {
                                                    onChange(e, data);
                                                }}
                                            />
                                        </div>
                                    </div>
                                )}
                            </InView>
                        );
                    })}
            <div style={{ width: "100%" }}>
                <CustomPagination
                    currentPage={curPage}
                    totalPages={totalPages}
                    handlePage={handlePage}
                    handlePerPageNum={handlePerPageNum}
                    handleDDPage={handleDDPage}
                    pageOption={pageOption}
                />
            </div>
        </>
    );
};
export default Gallery;

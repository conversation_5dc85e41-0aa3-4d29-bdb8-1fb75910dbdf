import React from "react";
//
import { Icon } from "semantic-ui-react";
//
import ActionCell from "../cell/ActionCell";
import useCell from "./useCell";
import { getPropDomainRange } from "../crudHelper";
import { columnDefault, HTTP_METHOD } from "../config";
import FltAnaAct from "../../../../../reduxStore/fltAnalysis/FltAnaAct";
import role from "../../../../../App-role";

// 是否為開發模式,利用此變數來變更 Table Footer 的顯示與否
const isLocalDevelop = process.env.NODE_ENV === "development";

const useColumnsDef = props => {
    const {
        curClassType,
        state,
        // keyword,
        curDispatch
        // mainDispatch
    } = props;
    const { user } = state;
    // 部分欄位使用使用者的腳色來判斷欄位是否要顯示
    const safeRole = user?.role || role.anonymous;

    // hook
    // 客製化 ReadCell,...
    const { getReadCell } = useCell();

    // 利用 classType 找到定義的 columnDefault(預設欄位)
    const { classColumns } =
        columnDefault.find(
            def => def.classType.toLowerCase() === curClassType.toLowerCase()
        ) || {};

    // 共通的 Cell props
    const commonCellProps = React.useCallback(
        info => {
            const { prop, domain, range } =
                getPropDomainRange(info?.column?.id) || {};
            return {
                colId: info.column.id,
                rowIdx: info.row.index,
                cellValue: info.value,
                column: info.column,
                rowData: info.row.original,
                domain,
                range,
                classType: curClassType
            };
        },
        [curClassType]
    );

    const readCellProps = info => ({
        ...commonCellProps(info)
    });

    // 僅供讀取的 columns
    const readColumns = React.useMemo(
        () =>
            classColumns
                .filter(col => {
                    if (typeof col.r === "boolean") {
                        return col.r;
                    }
                    if (typeof col.r === "function") {
                        return col.r(safeRole);
                    }
                    return false;
                })
                .reduce((acc, cur) => {
                    const {
                        accessor,
                        label,
                        group,
                        groupLabel,
                        Filter,
                        filter
                    } = cur || {};
                    const basic = {
                        accessor,
                        Header: () => <span>{label}</span>,
                        Cell: info => getReadCell(info, readCellProps),
                        Filter: Filter || "",
                        filter: filter || "",
                        Footer: () =>
                            isLocalDevelop ? <span>{accessor}</span> : null
                    };
                    if (!group) {
                        acc.push(basic);
                        return acc;
                    }
                    const fIdx = acc.findIndex(
                        // eslint-disable-next-line no-underscore-dangle
                        o => o.groupId === group
                    );
                    if (fIdx > -1) {
                        acc[fIdx].columns.push(basic);
                    } else {
                        acc.push({
                            // 若要將 Column group 時, Header及Footer為required
                            Header: groupLabel,
                            Footer: groupLabel,
                            groupId: group,
                            columns: [basic]
                        });
                    }
                    return acc;
                }, [])
                .concat([
                    {
                        // 若要將 Column group 時, Header及Footer為required
                        Header: "執行",
                        Footer: "執行",
                        columns: [
                            {
                                id: "actions",
                                Header: (
                                    <div>
                                        動作 <Icon name="play" />
                                    </div>
                                ),
                                width: 150,
                                Cell: info => {
                                    const {
                                        onClickStartAnalysis,
                                        onClickReAnalysis
                                        // eslint-disable-next-line react/destructuring-assignment
                                    } = info.meta || {};
                                    return (
                                        <ActionCell
                                            {...readCellProps(info)}
                                            onClickStartAnalysis={
                                                onClickStartAnalysis
                                            }
                                            onClickReAnalysis={
                                                onClickReAnalysis
                                            }
                                        />
                                    );
                                }
                            }
                        ]
                    }
                ]),
        []
    );

    // 定義 table meta:放進 React-table 的 useTable() 中來建立 table instance
    const readTableMetaDef = React.useMemo(
        () => ({
            // 開始分析
            onClickStartAnalysis: async payload => {
                const { data: rowData } = payload;
                // 彈跳視窗, 篩選要分析的步驟
                curDispatch({
                    type: FltAnaAct.setBypassModalOpen,
                    payload: true
                });
                curDispatch({
                    type: FltAnaAct.setBypassModalContext,
                    payload: {
                        rowData,
                        httpMethod: HTTP_METHOD.POST
                    }
                });
            },
            // 重新分析
            onClickReAnalysis: async payload => {
                const { data: rowData } = payload;
                // 彈跳視窗, 篩選要分析的步驟
                curDispatch({
                    type: FltAnaAct.setBypassModalOpen,
                    payload: true
                });
                curDispatch({
                    type: FltAnaAct.setBypassModalContext,
                    payload: {
                        rowData,
                        httpMethod: HTTP_METHOD.PUT
                    }
                });
            }
        }),
        []
    );

    // 定義 readColumn 顯示的欄位
    const readColumnVisibilityDef = classColumns
        .filter(col => !col.r)
        .reduce((acc, col) => {
            acc[col.accessor] = false;
            return acc;
        }, {});

    return {
        readColumns,
        readColumnVisibilityDef,
        readTableMetaDef
    };
};

export default useColumnsDef;

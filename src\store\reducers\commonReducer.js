import Act from "../actions";

const initState = {
    // FIXME: to be removed.
    pickConfig: {
        uploadPage: {
            selectMode: "multiple", // ENUM:["single", "multiple"]
            withCheckbox: true, // use checkbox for user to check
            maxFiles: 0, // 0 means there is no limit for uploading files count
            openSwalAlert: true
        },
        datasetPage: {
            selectMode: "single", // ENUM:["single", "multiple"]
            withCheckbox: true, // use checkbox for user to check
            maxFiles: 1,
            openSwalAlert: false
        }
    },
    headerActiveName: "", // 紀錄當下畫面所在的header name
    systemDataActiveItem: "", // 紀錄"系統相關"頁面選擇的項目名稱
    toolPagesActiveName: "", // 紀錄"工具相關"頁面選擇的項目名稱
    downloadDataActiveName: "" // 紀錄"數據資料"頁面選擇的項目名稱
};

const commonReducer = (state = initState, action) => {
    switch (action.type) {
        case Act.SET_HEADERACTIVENAME:
            return { ...state, headerActiveName: action.payload };
        case Act.SET_SYSTEMDATAACTIVEITEM:
            return { ...state, systemDataActiveItem: action.payload };
        case Act.SET_TOOLPAGESACTIVENAME:
            return { ...state, toolPagesActiveName: action.payload };
        case Act.SET_DOWNLOADDATAACTIVENAME:
            return { ...state, downloadDataActiveName: action.payload };
        default:
            return state;
    }
};

export default commonReducer;

import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";

// semantic ui
import { Header, Icon, Modal, Table, Container } from "semantic-ui-react";

// utils
import { isEmpty } from "../../../../../../commons";
import WarningConfig from "./utils/WarningConfig";

function PubContent({ warnKey, warningData }) {
    const [tbHeader, setTBHeader] = useState([]);

    const findHeadMap = WarningConfig[warnKey].head;

    useEffect(() => {
        setTBHeader(
            warningData.head.filter(header =>
                Object.keys(findHeadMap).includes(header)
            )
        );
    }, []);

    return (
        <Modal.Description>
            <Header
                size="medium"
                style={{ display: "flex", alignItems: "center" }}
            >
                <Icon
                    name="warning circle"
                    color="red"
                    style={{ fontSize: "larger" }}
                />
                {`警告訊息: ${WarningConfig[warnKey].waringMsg}`}
            </Header>
            <Container style={{ maxHeight: "45dvh", overflowY: "scroll" }}>
                <Table celled compact fixed>
                    <Table.Header style={{ position: "sticky", top: "0" }}>
                        <Table.Row>
                            {!isEmpty(tbHeader) &&
                                tbHeader.map(header => (
                                    <Table.HeaderCell key={header}>
                                        {findHeadMap[header]}
                                    </Table.HeaderCell>
                                ))}
                        </Table.Row>
                    </Table.Header>
                    <Table.Body>
                        {!isEmpty(warningData.data) &&
                            warningData.data.map(dataEl => {
                                const keyStr = `${dataEl.pubId}-${dataEl.pubCheckPropLabel}`;
                                return (
                                    <Table.Row key={keyStr}>
                                        {tbHeader.map(headerKey => (
                                            <Table.Cell
                                                key={`${keyStr}-${headerKey}`}
                                            >
                                                {dataEl[headerKey]}
                                            </Table.Cell>
                                        ))}
                                    </Table.Row>
                                );
                            })}
                    </Table.Body>
                </Table>
            </Container>
        </Modal.Description>
    );
}

PubContent.propTypes = {
    warnKey: PropTypes.string,
    warningData: PropTypes.objectOf(
        PropTypes.arrayOf(
            PropTypes.oneOfType([
                PropTypes.string, // head
                PropTypes.objectOf(PropTypes.string) // data
            ])
        )
    )
};

PubContent.defaultProps = {
    warnKey: "",
    warningData: {}
};

export default PubContent;

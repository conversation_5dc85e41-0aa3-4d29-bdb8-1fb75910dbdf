import React from "react";
import { useSelector, useDispatch } from "react-redux";
import { Pagination } from "semantic-ui-react";
import ataiMngAct from "../../../../../reduxStore/ataiManage/ataiManageAction";

const AtaiPagination = () => {
    const state = useSelector(tmpState => tmpState.ataiMng);
    const { currentPage, totalPage } = state;
    const dispatch = useDispatch();

    const handlePageChange = (e, { activePage }) => {
        dispatch({
            type: ataiMngAct.SET_CURRENT_PAGE,
            payload: activePage
        });
    };

    return (
        <div style={{ display: "flex", justifyContent: "center" }}>
            <Pagination
                activePage={currentPage}
                totalPages={totalPage}
                onPageChange={handlePageChange}
            />
        </div>
    );
};

export default AtaiPagination;

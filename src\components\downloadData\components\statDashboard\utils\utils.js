import XLSX from 'xlsx';
import axios from 'axios';
import { statsDataOrigin } from '../constants/statsDashboardConfig';
import Api from '../../../../../api/nmtl/Api';

export const filterDuplicates = (data) => {
    const seenIds = new Set();

    const uniqueData = data.filter((item) => {
        if (seenIds.has(item.id)) {
            return false;
        }
        seenIds.add(item.id);
        return true;
    });

    return uniqueData;
};

export const processData = async (itemId, itemUrl, mainSubjectData) => {
    let resData = [];
    let detailStatsArr = [];
    let stat = '';

    if (['mainSubject', 'vrliterary', 'article', 'publication'].includes(itemId)) {
        const response = await axios.get(itemUrl);
        resData = response.data.data;
    }

    const processDetailData = async (unit = '', label = '') => {
        if (mainSubjectData.length === 0) {
            return {
                stat: '未取得數據',
                detailStatsArr: [],
            };
        }

        try {
            const urls = mainSubjectData.map((subject) => itemUrl.replace('{ds}', subject.id));

            const allResponses = await Promise.all(urls.map((url) => axios.get(url)));

            const seenIds = new Set();
            const filteredResponses = allResponses.map((response) => {
                const uniqueData = response.data.data.filter((item) => {
                    if (seenIds.has(item.id)) {
                        return false;
                    }
                    seenIds.add(item.id);
                    return true;
                });
                return {
                    ...response,
                    data: {
                        ...response.data,
                        data: uniqueData,
                    },
                };
            });

            const totalCount = filteredResponses.reduce((sum, response) => {
                const count = response.data.data.length;
                return sum + count;
            }, 0);

            detailStatsArr = allResponses.map((response, i) => {
                const uniqueData = filterDuplicates(response.data.data);

                const count = uniqueData.length;
                const formattedCount = count.toLocaleString();

                return {
                    id: mainSubjectData[i].id,
                    title: mainSubjectData[i].label,
                    stat: label
                        ? `${formattedCount} ${unit} (${0}${label})`
                        : `${formattedCount} ${unit}`,
                };
            });

            const formattedTotal = totalCount.toLocaleString();
            return {
                stat: label
                    ? `${formattedTotal} ${unit} (${0}${label})`
                    : `${formattedTotal} ${unit}`,
                detailStatsArr,
            };
        } catch (error) {
            console.error('Error fetching data:', error);
            return {
                stat: '未取得數據',
                detailStatsArr: [],
            };
        }
    };

    const transformDetailData = (data, idKey, titleKey) =>
        data.map((el) => ({
            id: el[idKey] || '',
            title: el[titleKey] || '',
            stat: '',
        }));

    const getHasFltData = async (id, data, unit = '', label = '') => {
        const totalStatsRes = await axios.get(
            `${Api.getBaseUrl}/backend/stats/${id}FltTotal/1.0?&limit=-1&offset=0`,
        );

        const totalstats = totalStatsRes.data.data[0];

        const detailStatsArr = data
            .map((el) => {
                const matchingSubject = mainSubjectData.find((subject) => subject.id === el.g);

                if (matchingSubject) {
                    return {
                        id: el.g,
                        title: matchingSubject.label,
                        stat: `${Number(el.Count).toLocaleString()} ${unit} (${Number(
                            el.totalFLTCount,
                        ).toLocaleString()}${label})`,
                    };
                }
                return null;
            })
            .filter((el) => el !== null);

        return {
            stat: `${Number(totalstats.Count).toLocaleString()} ${unit} (${Number(
                totalstats.totalFLTCount,
            ).toLocaleString()}${label})`,
            detailStatsArr,
        };
    };

    switch (itemId) {
        case 'mainSubject':
            detailStatsArr = transformDetailData(resData, 'dataset', 'name');
            stat = resData.length.toLocaleString();
            break;

        case 'vrliterary':
            detailStatsArr = transformDetailData(resData, 'type', 'title');
            stat = resData.length.toLocaleString();
            break;

        case 'person':
            ({ stat, detailStatsArr } = await processDetailData('人'));
            break;

        case 'publication':
            ({ stat, detailStatsArr } = await getHasFltData(itemId, resData, '本', '本全文'));
            break;

        case 'article':
            ({ stat, detailStatsArr } = await getHasFltData(itemId, resData, '篇', '篇全文'));
            break;

        case 'organizationinfo':
        case 'location':
            ({ stat, detailStatsArr } = await processDetailData('個'));
            break;
        default:
            stat = '未取得數據';
            detailStatsArr = [];
            break;
    }

    return { stat, detailStatsArr };
};

export const getStatsCount = async (copystatsDataOrg, topicIdx) => {
    try {
        const statsCountData = await Promise.all(
            copystatsDataOrg[topicIdx].select.options.map(async (option) => {
                const selectedStats = await Promise.all(
                    copystatsDataOrg[topicIdx].stats.map(async (statItem) => {
                        if (statItem.url) {
                            try {
                                const url = statItem.url.replace('{ds}', option.key);
                                const res = await axios.get(url);

                                const uniqueData = filterDuplicates(res.data.data);

                                const statValue = uniqueData.length;

                                return {
                                    ...statItem,
                                    stat: statValue.toLocaleString(),
                                    id: `${statItem.id}_${option.key}`,
                                };
                            } catch (error) {
                                return {
                                    ...statItem,
                                    stat: '未取得數據',
                                    id: `${statItem.id}_${option.key}`,
                                };
                            }
                        }
                        return {
                            ...statItem,
                            stat: '未取得數據',
                            id: `${statItem.id}_${option.key}`,
                        };
                    }),
                );

                const totalStatValue = selectedStats.reduce((acc, item) => {
                    const statNumber = Number(item.stat.replace(/,/g, ''));
                    if (!isNaN(statNumber)) {
                        return acc + statNumber;
                    }
                    return acc;
                }, 0);

                const updatedOptionStats = selectedStats.map((item) => {
                    if (item.id.includes('statsTotal')) {
                        return {
                            ...item,
                            stat: totalStatValue.toLocaleString(),
                        };
                    }
                    return item;
                });

                return updatedOptionStats;
            }),
        );

        const flattenedStats = statsCountData.flat();

        return flattenedStats;
    } catch (e) {
        console.log(e);
    }
    return null;
};

export const getCurrentTime = () => {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    return `${year}/${month}/${day} ${hours}:${minutes}:${seconds}`;
};

export const downloadStatsAsExcel = (statsData) => {
    // 檢查數據
    for (const el of statsData) {
        if (el.stats.length === 0) {
            alert('請等待資料獲取完畢');
            return;
        }

        for (const statsEl of el.stats) {
            if (!statsEl.stat) {
                alert('請等待資料獲取完畢');
                return;
            }
        }
    }

    const workbook = XLSX.utils.book_new();

    // 全站統計1
    const allWebStats = statsData.find((data) => data.id === 'allWebStats');
    if (allWebStats) {
        const allWebFirstThreeStats = allWebStats.stats.slice(0, 3);
        const headers = allWebFirstThreeStats.map((item) => item.title);
        const maxRows = Math.max(
            ...allWebFirstThreeStats.map((item) => item.detailStatsArr.length),
        );
        const worksheetData = [];

        for (let i = 0; i < maxRows; i++) {
            const row = allWebFirstThreeStats.map((item) =>
                item.detailStatsArr[i] ? item.detailStatsArr[i].title : '',
            );
            worksheetData.push(row);
        }

        worksheetData.unshift(headers);

        const worksheet1 = XLSX.utils.aoa_to_sheet(worksheetData);
        XLSX.utils.book_append_sheet(workbook, worksheet1, '全站統計1');

        // 全站統計2
        const allWebOtherStats = allWebStats.stats.slice(3);
        const headers2 = allWebOtherStats.map((stat) => stat.title);
        const stats = allWebOtherStats.map((stat) => stat.stat);
        const maxDetailRows = Math.max(
            ...allWebOtherStats.map((stat) => stat.detailStatsArr.length),
        );
        const worksheetData2 = [];

        worksheetData2.push(['', ...headers2]);

        for (let i = 0; i < maxDetailRows; i++) {
            const row = [allWebOtherStats[0].detailStatsArr[i]?.title || ''];
            worksheetData2.push(row);
        }

        worksheetData2.forEach((row, index) => {
            if (index > 0) {
                const leftTitle = row[0];
                allWebOtherStats.forEach((stat) => {
                    const detailStat = stat.detailStatsArr.find(
                        (detail) => detail.title === leftTitle,
                    );
                    row.push(detailStat ? detailStat.stat : '');
                });
            }
        });

        worksheetData2.push(['總計', ...stats]);

        const worksheet2 = XLSX.utils.aoa_to_sheet(worksheetData2);
        XLSX.utils.book_append_sheet(workbook, worksheet2, '全站統計2');
    }

    // 瀏覽人次
    const pageViewStats = statsData.find((data) => data.id === 'pageView');
    if (pageViewStats) {
        const { datePicker, stats } = pageViewStats;
        const dateRange = `日期區間：${datePicker.startDate}～${datePicker.endDate}`;
        const pageViewData = [[dateRange], ...stats.map((stat) => [stat.title, stat.stat])];

        const worksheet3 = XLSX.utils.aoa_to_sheet(pageViewData);
        XLSX.utils.book_append_sheet(workbook, worksheet3, '瀏覽人次');
    }

    // 筆數統計
    const statsCountData = statsData.find((data) => data.id === 'datasetStats');

    if (statsCountData) {
        const headers = statsDataOrigin
            .find((data) => data.id === 'datasetStats')
            .stats.map((stat) => stat.title);

        const worksheetData = [];

        worksheetData.push(['', ...headers]);

        statsCountData.select.options.forEach((option) => {
            const statValues = [];

            // 遍歷每個 header
            headers.forEach((header) => {
                // 查找與當前 header 對應的 stat
                const matchedStat = statsCountData.stats.find(
                    (stat) => stat.id.endsWith(`_${option.key}`) && stat.title === header,
                );

                // 如果找到對應的 stat，則加入 statValue，否則放入空值
                if (matchedStat) {
                    statValues.push(matchedStat.stat);
                } else {
                    statValues.push('');
                }
            });

            worksheetData.push([option.text, ...statValues]);
        });

        const worksheet4 = XLSX.utils.aoa_to_sheet(worksheetData);
        XLSX.utils.book_append_sheet(workbook, worksheet4, '筆數統計');
    }

    // 將工作簿寫入文件
    XLSX.writeFile(workbook, '數據統計.xlsx');
};

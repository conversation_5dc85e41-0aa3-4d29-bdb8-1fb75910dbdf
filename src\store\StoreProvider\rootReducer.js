// combine
import combineReducer from "./combineReducer";
// reducer
import userReducer from "../reducers/userReducer";
import dataReducer from "../reducers/dataReducer";
import historyReducer from "../reducers/historyReducer";
import uploadReducer from "../reducers/uploadReducer";
import commonReducer from "../reducers/commonReducer";
import testReducer from "../reducers/testReducer";
import accountReducer from "../reducers/accountReducer";
import databaseReducer from "../reducers/databaseReducer";
import exampleReducer from "../reducers/exampleReducer";
import websiteSettingReducer from "../reducers/websiteSettingReducer";

const reducers = combineReducer({
    user: userReducer,
    account: accountReducer,
    data: dataReducer,
    history: historyReducer,
    upload: uploadReducer,
    common: commonReducer,
    test: testReducer,
    database: databaseReducer,
    example: exampleReducer,
    websiteSetting: websiteSettingReducer
});

export default reducers;

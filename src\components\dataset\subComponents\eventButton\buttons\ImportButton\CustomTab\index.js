import React, { useContext } from "react";

// ui
import { Tab } from "semantic-ui-react";

// custom
import CustomImportTab from "./CustomImportTab";
import CustomResultTab from "./CustomResultTab";

// store
import { StoreContext } from "../../../../../../../store/StoreProvider";

// custom

const panes = [
    {
        menuItem: "預覽",
        render: () => (
            <Tab.Pane attached={false}>
                <CustomImportTab />
            </Tab.Pane>
        )
    },
    {
        menuItem: "結果",
        render: () => (
            <Tab.Pane attached={false}>
                <CustomResultTab />
            </Tab.Pane>
        )
    }
];

const CustomTab = () => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { uploaded } = state.data;
    const { record } = uploaded;
    return <Tab menu={{ pointing: true }} panes={panes} activeIndex={record?.changeTabSignal} />;
};

export default CustomTab;

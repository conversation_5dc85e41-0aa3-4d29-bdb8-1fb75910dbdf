import React, { useContext, useEffect, useState } from "react";

// plugins
import { useDispatch, useSelector } from "react-redux";
import { <PERSON><PERSON>, Icon } from "semantic-ui-react";
import { StoreContext } from "../../../../../../../store/StoreProvider";

// config
import clsName from "../../Utils/clsName";
import textConfig from "../../Utils/textConfig";
import NewsAct from "../../EditNewsAction";
import {
    confirmColumn,
    delNews,
    saveData,
    setNewsInfo,
    setUpdateNewsInfo
} from "../../Utils/utils";
import { isEmpty } from "../../../../../../../commons";
import initColumnDef, { imageColDef } from "../../Utils/initColumnDef";

// image
import dragImage from "../../../../../../../images/dragImage.svg";
import { getReservedNewId } from "../../../../../../common/sheetCrud/utils";
import openModalControl from "../../Utils/openModalControl";
import useGetSubjectOPs from "../../../../../../common/hooks/useGetSubjectOPs";
import { createHistoryEvent } from "../../../../../../downloadData/components/history/common/common";

function CustomButton({ className }) {
    const newsDispatch = useDispatch();
    const {
        newsFullInfo,
        modalSelect,
        modalCaller,
        updateNewsInfo
    } = useSelector(state => state);

    const [text, setText] = useState("");
    const [iconStr, setIconStr] = useState("");

    const [oldState] = useContext(StoreContext);
    const { displayName } = oldState.user;
    const { websiteSubject, menuActiveItem } = oldState.websiteSetting;
    const { headerActiveName } = oldState.common;

    const { groupInfo } = oldState.data;
    const dropOptions = useGetSubjectOPs(groupInfo);

    const sheetSelectedValue = dropOptions.find(it => it.key === websiteSubject)
        ?.text;
    const columns = [headerActiveName, sheetSelectedValue, menuActiveItem.name];

    useEffect(() => {
        switch (className) {
            case clsName.SendBtn:
                setText(textConfig.CustomButton_Send);
                break;
            case clsName.AddBtn:
                setText(textConfig.CustomButton_Add);
                setIconStr("plus");
                break;
            case clsName.DeleteBtn:
                setText(textConfig.CustomButton_Delete);
                setIconStr("minus");
                break;
            case clsName.AddPic:
                setText(textConfig.CustomButton_AddPic);
                break;
            case clsName.CancelBtn:
                setText(textConfig.CustomButton_Cancel);
                break;
            default:
                break;
        }
    }, []);

    // 點選DeleteBtn跳出Modal後，點選"確認"的後續動作
    useEffect(() => {
        if (modalSelect === null || isEmpty(updateNewsInfo)) return;
        if (
            className === clsName.DeleteBtn &&
            modalCaller === clsName.DeleteBtn
        ) {
            if (modalSelect) {
                delNews(
                    newsDispatch,
                    newsFullInfo,
                    updateNewsInfo,
                    oldState.user,
                    websiteSubject
                );

                const historyMsg = `${JSON.stringify(updateNewsInfo)}`;

                // 建立歷史紀錄
                createHistoryEvent(
                    displayName,
                    "刪除",
                    `${columns.join("/")}：${historyMsg}`
                );
            }
        }
    }, [modalCaller, modalSelect, newsFullInfo, updateNewsInfo]);

    const handleClick = async () => {
        switch (className) {
            case clsName.AddBtn: {
                // create empty newsEvents
                const initNewsEvent = Object.keys(initColumnDef).reduce(
                    (cur, next) => {
                        // "hasURL", "fileAvailableAt"這兩個欄位的初始值給空陣列
                        const isArr = ["hasURL", "fileAvailableAt"].includes(
                            next
                        );
                        return {
                            ...cur,
                            [initColumnDef[next]]: isArr ? [] : ""
                        };
                    },
                    {}
                );

                // 下面欄位名稱，跟API getNewsInfo() 取值相同
                // id取空字串，產生ID交給nmtl-api
                initNewsEvent[initColumnDef.newsIdStr] = await getReservedNewId(
                    "News"
                );
                const curDate = new Date(Date.now());
                [
                    initNewsEvent[initColumnDef.hasStartDate]
                ] = curDate.toISOString().split("T");

                initNewsEvent[initColumnDef.newsCreator] = displayName;
                // 進EditMode
                newsDispatch({
                    type: NewsAct.SET_ISEDITED
                });
                // save origin newsEvents
                setUpdateNewsInfo(newsDispatch, initNewsEvent);
                break;
            }
            case clsName.AddPic: {
                const tmpNewsEvents = JSON.parse(
                    JSON.stringify(updateNewsInfo)
                );
                // 新增URLEvent不帶ID，使用getReservedNewId()會帶到資料庫中已經存在的ID
                const emptyRow = [
                    {
                        [imageColDef.urlId]: "",
                        [imageColDef.imgText]: "",
                        [imageColDef.order]: "",
                        [imageColDef.imgUrl]: dragImage
                    }
                ];

                if (isEmpty(tmpNewsEvents[initColumnDef.hasURL])) {
                    tmpNewsEvents[initColumnDef.hasURL] = emptyRow;
                } else {
                    tmpNewsEvents[initColumnDef.hasURL] = [
                        ...tmpNewsEvents[initColumnDef.hasURL],
                        ...emptyRow
                    ];
                }
                tmpNewsEvents[initColumnDef.hasURL].at(-1)[
                    imageColDef.order
                ] = tmpNewsEvents[initColumnDef.hasURL].length.toString();

                setUpdateNewsInfo(newsDispatch, tmpNewsEvents);
                break;
            }
            case clsName.SendBtn:
                {
                    const checkResult = confirmColumn(updateNewsInfo);

                    if (!checkResult) {
                        // update to fuseki
                        saveData(
                            newsDispatch,
                            newsFullInfo,
                            updateNewsInfo,
                            oldState.user,
                            websiteSubject
                        );
                        const historyMsg = isEmpty(newsFullInfo)
                            ? `${JSON.stringify(updateNewsInfo)}`
                            : `${JSON.stringify(
                                newsFullInfo
                            )}\n變動後：\n${JSON.stringify(updateNewsInfo)}`;

                        // 建立歷史紀錄
                        createHistoryEvent(
                            displayName,
                            isEmpty(newsFullInfo) ? "新增" : "更新",
                            `${columns.join("/")}：${historyMsg}`
                        );

                        // 恢復原本資料狀態
                        setNewsInfo(newsDispatch, updateNewsInfo);
                    } else {
                        // 顯示check message
                        newsDispatch({
                            type: NewsAct.SET_MODALMSG,
                            payload: checkResult
                        });
                        openModalControl(newsDispatch, className);
                    }
                }
                break;
            case clsName.CancelBtn:
                // 離開EditMode
                newsDispatch({
                    type: NewsAct.SET_NOTEDITED
                });

                // 恢復原本資料狀態
                setUpdateNewsInfo(newsDispatch, newsFullInfo);
                break;
            case clsName.DeleteBtn:
                openModalControl(newsDispatch, className);
                break;
            default:
                break;
        }
    };

    return (
        <React.Fragment>
            {iconStr !== "" ? (
                <Button
                    icon
                    labelPosition="left"
                    primary
                    positive={className === clsName.CancelBtn}
                    negative={className === clsName.DeleteBtn}
                    onClick={handleClick}
                >
                    {iconStr && <Icon name={iconStr} />}
                    {text}
                </Button>
            ) : (
                <Button
                    primary
                    positive={className === clsName.CancelBtn}
                    negative={className === clsName.DeleteBtn}
                    onClick={handleClick}
                >
                    {text}
                </Button>
            )}
        </React.Fragment>
    );
}

export default CustomButton;

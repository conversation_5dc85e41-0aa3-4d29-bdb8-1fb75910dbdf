import React, { useContext, useEffect, useRef, useState } from "react";

// components
import Selector from "../../components/Selector";
import SaveButton from "../../components/SaveButton";
import UpdateText from "../../components/UpdateText";
import LanguageSelect from "../../components/LanguageSelect";

// general
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import { setDropDownFromMsListData } from "../../commons";
import CustomQuill from "../components/CustomQuill";

function SubjectLiterature() {
    const [state, dispatch] = useContext(StoreContext);
    const editorRef = useRef(null);
    const {
        originData,
        updatedData,
        menuActiveItem,
        msListData
    } = state.websiteSetting;
    const [language, setLanguage] = useState("zh");
    const [dropDown, setDropDown] = useState({});

    useEffect(() => {
        // 下拉選單選項清空
        dispatch({
            type: Act.SET_SELECTOPTION,
            payload: ""
        });
    }, []);

    useEffect(() => {
        if (originData.length !== 0 && msListData.length !== 0) {
            const originObj = originData.find(
                element => element.id === menuActiveItem.key
            );
            const originArray = Object.keys(originObj);
            const msListArray = msListData.filter(
                element => element.type === "ms"
            );
            const newDropDown = Object.assign(
                {},
                originObj,
                setDropDownFromMsListData(
                    originArray,
                    msListArray,
                    "SubjectLiterature"
                )
            );
            setDropDown(newDropDown);
            // 更新 updatedData
            const tmpAllData = JSON.parse(JSON.stringify(updatedData));
            const tmpObjIndex = tmpAllData.findIndex(
                element => element.id === menuActiveItem.key
            );
            tmpAllData[tmpObjIndex] = newDropDown;
            dispatch({
                type: Act.SET_UPDATEDDATA,
                payload: tmpAllData
            });
        }
    }, [originData, msListData]);

    return (
        <div className="SubjectLiterature">
            <div className="Selector">
                <Selector dropDown={dropDown} />
            </div>
            <div className="updateArea">
                <div className="updateAreaTop">
                    <h1>概述</h1>
                    <LanguageSelect
                        language={language}
                        setLanguage={setLanguage}
                    />
                </div>
                <div className="updateAreaButtom" style={{ height: "80%" }}>
                    <UpdateText
                        language={language}
                        dropDown={dropDown}
                        option={{
                            column: "overview"
                        }}
                    />
                </div>
                <div className="updateAreaTop">
                    <h1>總述</h1>
                </div>
                <div className="updateAreaButtom">
                    {/* <UpdateText */}
                    {/*    language={language} */}
                    {/*    dropDown={dropDown} */}
                    {/*    option={{ */}
                    {/*        column: "introduction", */}
                    {/*        priority: "No priority" */}
                    {/*    }} */}
                    {/* /> */}
                    <CustomQuill
                        quillId="CustomEditorSubLt"
                        tmpRef={editorRef}
                        language={language}
                        dropDown={dropDown}
                        option={{
                            column: "introduction"
                        }}
                    />
                </div>
                <div className="btnArea">
                    <SaveButton language={language} />
                </div>
            </div>
        </div>
    );
}

export default SubjectLiterature;

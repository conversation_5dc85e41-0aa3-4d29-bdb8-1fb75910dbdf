{
	"parser": "babel-eslint",
	"env": {
		"browser": true,
		"es6": true,
		"commonjs": true
	},
	"settings": {
		"react": {
			"version": require("./package.json").dependencies.react
		}
	},
	"extends": ["airbnb", "prettier", "prettier/react"],
	"parserOptions": {
		"ecmaVersion": 6,
		"ecmaFeatures": {
			"experimentalObjectRestSpread": true,
			"jsx": true
		},
		"sourceType": "module"
	},
	"plugins": ["prettier", "react"],
	"globals": {
		"process": true, //for DefinePlugin provide env
		"tinymce": true,
		"jest": true,
		"describe": true,
		"it": true,
		"expect": true
	},
	"rules": {
		"prettier/prettier" : [
			"error",
			{"tabWidth": 4}
		],
		"indent": ["error", 4, {"SwitchCase": 1}],
		"linebreak-style": "off",
		"import/no-named-as-default": "off",
		"jsx-a11y/click-events-have-key-events": "off",
		"jsx-a11y/no-static-element-interactions": "off",
		"jsx-a11y/no-access-key": 0,
		"jsx-a11y/anchor-is-valid": "off",
		"react/jsx-filename-extension": [1, { "extensions": [".js", ".jsx"] }],
		"react/prop-types": 0,
		"react/no-array-index-key": "off"
	}
}

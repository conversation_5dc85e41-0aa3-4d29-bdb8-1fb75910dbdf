import React, {useContext, useEffect} from "react";

// ui
import { Table, Icon } from "semantic-ui-react";

// common
import { isEmpty } from "../../../../../../commons";

// custom
import CustomTableRowCell from "./CustomTableRowCell";
import {StoreContext} from "../../../../../../store/StoreProvider";

// const BottomContent = ({ type, dataset, sheetName, entrySrc, entryDst }) => {
const BottomContent = () => {
    // const mergedEntry = entrySrc && entryDst && Object.assign({}, entrySrc, entryDst);
    const [state, dispatch] = useContext(StoreContext);
    const { log } = state.history;
    useEffect(() => {
        console.log(log);
        // console.log(entrySrc, entryDst);
    }, []);

    return (
        /*
        <pre>
            {
                JSON.stringify(entry, null, 2)
            }
        </pre>
         */
        // !isEmpty(mergedEntry)
        !isEmpty(log) ? (
            <Table celled selectable compact>
                {/* <Table.Header>*/}
                {/*    <Table.Row>*/}
                {/*        <Table.HeaderCell>標籤 (Label)</Table.HeaderCell>*/}
                {/*        <Table.HeaderCell colSpan="2" textAlign="center">*/}
                {/*            原始資料 (RawData) &nbsp;&nbsp;*/}
                {/*            <Icon color="orange" name="exchange" size="large" />*/}
                {/*            &nbsp;&nbsp; 新增資料 (NewData)*/}
                {/*        </Table.HeaderCell>*/}
                {/*    </Table.Row>*/}
                {/*</Table.Header>*/}
                <Table.Body>
                    {/* { */}
                    {/*    Object.keys(mergedEntry).map((label, idx)=> { */}
                    {/*        const rawData = entrySrc[label] || ""; */}
                    {/*        const newData = entryDst[label] || ""; */}
                    {/*        return ( */}
                    {/*           <CustomTableRowCell */}
                    {/*               key={`src-${idx}-${label}`} */}
                    {/*               type={type} */}
                    {/*               label={label} */}
                    {/*               rawData={rawData} */}
                    {/*               newData={newData} */}
                    {/*           /> */}
                    {/*        ) */}
                    {/*    }) */}
                    {/* } */}
                    {Object.keys(log).map((keyName, idx) => {
                        // const rawData = entrySrc[label] || "";
                        // const newData = entryDst[label] || "";
                        return (
                            // <CustomTableRowCell
                            //     key={`src-${idx}-${label}`}
                            //     type={type}
                            //     label={label}
                            //     rawData={rawData}
                            //     newData={newData}
                            // />
                            <Table.Row key={`src-${idx}-${keyName}`}>
                                <Table.Cell>{log[keyName].name}</Table.Cell>
                                <Table.Cell positive>{log[keyName].loginTime}</Table.Cell>
                                <Table.Cell>{log[keyName].logoutTime}</Table.Cell>
                                <Table.Cell>{log[keyName].operation}</Table.Cell>
                                <Table.Cell>{log[keyName].eventTime}</Table.Cell>
                            </Table.Row>
                        );
                    })}
                </Table.Body>
            </Table>
        ) : (
            <Table>
                <Table.Body>
                    <Table.Row>
                        <Table.Cell textAlign="center">No Content</Table.Cell>
                    </Table.Row>
                </Table.Body>
            </Table>
        )
    );
};

export default BottomContent;

import React from "react";

// plugins
import { useDispatch } from "react-redux";
import { Table } from "semantic-ui-react";
import PropTypes from "prop-types";

// components
import EditBtn from "./EditBtn";

// utils
import { isEmpty } from "../../../../../../../commons";
import columnDefineLV1 from "./columnDefine";
import clsName from "../../Utils/clsName";
import NewsAct from "../../EditNewsAction";
import openModalControl from "../../Utils/openModalControl";
import getFullNewsInfo from "../../Utils/getFullNewsInfo";

function CustomTable({ tbBdData, currentPage, perPageNum }) {
    const newsDispatch = useDispatch();

    const editNews = el => {
        newsDispatch({
            type: NewsAct.SET_NEWSBRIEFINFO,
            payload: el
        });

        getFullNewsInfo(el, newsDispatch);

        // 進EditMode
        newsDispatch({
            type: NewsAct.SET_ISEDITED
        });
    };

    const tbIcon = [
        { label: clsName.EditBtn, action: editNews },
        { label: clsName.RemoveBtn, action: openModalControl }
    ];

    return (
        <Table celled structured size="small" selectable>
            <Table.Header>
                <Table.Row>
                    {columnDefineLV1.map(el => {
                        const { label, lv2Col } = el;
                        if (!lv2Col) {
                            return (
                                <Table.HeaderCell rowSpan="3" key={label}>
                                    {label}
                                </Table.HeaderCell>
                            );
                        }
                        return (
                            <Table.HeaderCell colSpan={1} key={label}>
                                {label}
                            </Table.HeaderCell>
                        );
                    })}
                </Table.Row>
            </Table.Header>

            <Table.Body>
                {!isEmpty(tbBdData) &&
                    tbBdData
                        .slice(
                            (currentPage - 1) * perPageNum,
                            currentPage * perPageNum
                        )
                        .map(el => (
                            <Table.Row
                                key={el.newsIdStr}
                                style={{ cursor: "pointer" }}
                                onClick={() => editNews(el)}
                            >
                                {columnDefineLV1.map(lv1Obj => {
                                    if (lv1Obj.value === "CustomLabel") {
                                        return (
                                            <Table.Cell
                                                width={3}
                                                key={lv1Obj.label}
                                                textAlign="center"
                                            >
                                                {tbIcon.map(obj => (
                                                    <EditBtn
                                                        key={obj.label}
                                                        selectItem={el}
                                                        className={obj.label}
                                                        action={obj.action}
                                                        id={el.newsIdStr}
                                                    />
                                                ))}
                                            </Table.Cell>
                                        );
                                    }
                                    if (lv1Obj.value === "top") {
                                        return (
                                            <Table.Cell
                                                key={lv1Obj.label}
                                                width={2}
                                            >
                                                {el[lv1Obj.value] === "true"
                                                    ? "是"
                                                    : "否"}
                                            </Table.Cell>
                                        );
                                    }
                                    return (
                                        <Table.Cell
                                            key={lv1Obj.label}
                                            width={2}
                                        >
                                            {el[lv1Obj.value]}
                                        </Table.Cell>
                                    );
                                })}
                            </Table.Row>
                        ))}
            </Table.Body>
        </Table>
    );
}

CustomTable.propTypes = {
    /** table data */
    tbBdData: PropTypes.arrayOf(
        PropTypes.shape({
            newsIdStr: PropTypes.string,
            newsType: PropTypes.string,
            hasStartDate: PropTypes.string,
            title: PropTypes.string,
            newsCreator: PropTypes.string,
            status: PropTypes.string
        })
    ),
    /** 目前頁碼 */
    currentPage: PropTypes.number,
    /** 每頁筆數 */
    perPageNum: PropTypes.number
};

CustomTable.defaultProps = {
    /** table data */
    tbBdData: [],
    /** 目前頁碼 */
    currentPage: 1,
    /** 每頁筆數 */
    perPageNum: 1
};

export default CustomTable;

import React, { useContext, useState, useEffect, useCallback } from "react";

// ui
import { Button } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";
import Act from "../../../../../../store/actions";

// common
import { isEmpty } from "../../../../../../commons";

// components
import DelModal from "./DelModal";

const DeleteButton = () => {
    // button
    const [open, setOpen] = useState(false);

    const [state, dispatch] = useContext(StoreContext);
    const { checked } = state.data.content;

    // clear checked
    const handleInitChecked = useCallback(
        () =>
            !isEmpty(checked) &&
            dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN }),
        []
    );

    useEffect(() => {
        handleInitChecked();
    }, [handleInitChecked]);

    return (
        <>
            <Button
                color="red"
                floated="right"
                disabled={checked.length === 0}
                onClick={() => setOpen(true)}
            >
                刪除
            </Button>
            {open && <DelModal open={open} setOpen={setOpen} />}
        </>
    );
};

export default DeleteButton;

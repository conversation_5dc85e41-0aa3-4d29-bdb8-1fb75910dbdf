import Api from '../../../../../api/nmtl/Api';

export const statsDataOrigin = [
    {
        id: 'allWebStats',
        statsCount: false,
        topicTitle: '全站統計',
        lastUpdatedTime: { show: true, time: '' },
        select: { show: false, options: [] },
        datePicker: { show: false, startDate: '', endDate: '' },
        detailStats: true,
        stats: [
            {
                id: 'mainSubject',
                title: '文學主題',
                stat: '',
                detailStatsArr: [],
                url: Api.getMainSubjectList,
            },
            {
                id: 'subTopicPlatform',
                title: '子主題平台',
                stat: '10',
                detailStatsArr: [
                    { id: 'hor', title: '鬼怪奇談', stat: '' },
                    { id: 'ol', title: '海洋文學', stat: '' },
                    { id: 'gl', title: '性別文學', stat: '' },
                    { id: 'ra', title: '農村文學', stat: '' },
                    { id: 'lg', title: '文學聚落與社群', stat: '' },
                    { id: 'abo', title: '原住民族文學', stat: '' },
                    { id: 'hr', title: '人權文學', stat: '' },
                    { id: 'ada', title: '文學改編', stat: '' },
                    { id: 'tahl', title: '台/客語文學', stat: '' },
                    { id: 'tltc', title: '外譯房', stat: '' },
                ],
                url: '',
            },
            {
                id: 'vrliterary',
                title: '文學館家族',
                stat: '',
                detailStatsArr: [],
                url: Api.getVrliteraryList,
            },
            {
                id: 'person',
                title: '人物',
                stat: '',
                detailStatsArr: [],
                url: Api.getPersonDsList,
            },
            {
                id: 'publication',
                title: '著作',
                stat: '',
                detailStatsArr: [],
                url: Api.getPublicationFltStat,
            },
            {
                id: 'article',
                title: '單篇作品',
                stat: '',
                detailStatsArr: [],
                url: Api.getArticleFltStat,
            },
            {
                id: 'organizationinfo',
                title: '組織',
                stat: '',
                detailStatsArr: [],
                url: Api.getOrganizationinfoDsList,
            },
            {
                id: 'location',
                title: '地點',
                stat: '',
                detailStatsArr: [],
                url: Api.getLocationDsList,
            },
        ],
    },
    {
        id: 'pageView',
        statsCount: false,
        topicTitle: '瀏覽人次',
        lastUpdatedTime: { show: false, time: '' },
        select: { show: false, options: [] },
        datePicker: {
            show: true,
            startDate: '',
            endDate: '',
        },
        detailStats: false,
        stats: [
            {
                id: 'nmtl',
                title: '好臺誌',
                stat: '',
                detailStatsArr: [],
                url: '',
            },
            {
                id: 'tltc',
                title: '外譯房',
                stat: '',
                detailStatsArr: [],
                url: '',
            },
            {
                id: 'hor',
                title: '鬼怪奇談',
                stat: '',
                detailStatsArr: [],
                url: '',
            },
            {
                id: 'ol',
                title: '海洋文學',
                stat: '',
                detailStatsArr: [],
                url: '',
            },
            {
                id: 'gl',
                title: '性別文學',
                stat: '',
                detailStatsArr: [],
                url: '',
            },
            {
                id: 'ra',
                title: '農村文學',
                stat: '',
                detailStatsArr: [],
                url: '',
            },
            {
                id: 'lg',
                title: '文學聚落與社群',
                stat: '',
                detailStatsArr: [],
                url: '',
            },
            {
                id: 'abo',
                title: '原住民族文學',
                stat: '',
                detailStatsArr: [],
                url: '',
            },
            {
                id: 'hr',
                title: '人權文學',
                stat: '',
                detailStatsArr: [],
                url: '',
            },
            {
                id: 'ada',
                title: '文學改編',
                stat: '',
                detailStatsArr: [],
                url: '',
            },
            {
                id: 'tahl',
                title: '台/客語文學',
                stat: '',
                detailStatsArr: [],
                url: '',
            },
        ],
    },
    {
        id: 'datasetStats',
        statsCount: true,
        topicTitle: '數據統計',
        lastUpdatedTime: { show: true, time: '' },
        select: {
            show: true,
            options: [
                {
                    key: 'tww',
                    value: 'tww',
                    text: '2017作家作品目錄',
                },
                {
                    key: 'ctlj',
                    value: 'ctlj',
                    text: '文學期刊目錄',
                },
                {
                    key: 'mw',
                    value: 'mw',
                    text: '現當代作家研究資料庫',
                },
                {
                    key: 'twp',
                    value: 'twp',
                    text: '數位全臺詩',
                },
                {
                    key: 'ra',
                    value: 'ra',
                    text: '農村文學',
                },
                {
                    key: 'of',
                    value: 'of',
                    text: '口傳文學',
                },
                {
                    key: 'cla',
                    value: 'cla',
                    text: '古典文學',
                },
                {
                    key: 'cho',
                    value: 'cho',
                    text: '地誌書寫',
                },
                {
                    key: 'tahl',
                    value: 'tahl',
                    text: '台／客語文學',
                },
                {
                    key: 'gen',
                    value: 'gen',
                    text: '家族書寫',
                },
                {
                    key: 'lg',
                    value: 'lg',
                    text: '文學聚落與社群',
                },
                {
                    key: 'ada',
                    value: 'ada',
                    text: '文學改編',
                },
                {
                    key: 'cl',
                    value: 'cl',
                    text: '兒童文學',
                },
                {
                    key: 'hr',
                    value: 'hr',
                    text: '人權文學',
                },
                {
                    key: 'sa',
                    value: 'sa',
                    text: '大河小說',
                },
                {
                    key: 'wfmdv',
                    value: 'wfmdv',
                    text: '眷村文學',
                },
                {
                    key: 'wcr',
                    value: 'wcr',
                    text: '武俠小說',
                },
                {
                    key: 'rn',
                    value: 'rn',
                    text: '言情小說',
                },
                {
                    key: 'jou',
                    value: 'jou',
                    text: '報導文學',
                },
                {
                    key: 'lab',
                    value: 'lab',
                    text: '勞工文學',
                },
                {
                    key: 'hor',
                    value: 'hor',
                    text: '鬼怪奇談',
                },
                {
                    key: 'imm',
                    value: 'imm',
                    text: '移民／工文學',
                },
                {
                    key: 'ndt',
                    value: 'ndt',
                    text: '災難書寫',
                },
                {
                    key: 'tra',
                    value: 'tra',
                    text: '臺灣行旅書寫',
                },
                {
                    key: 'abo',
                    value: 'abo',
                    text: '原住民族文學',
                },
                {
                    key: 'gou',
                    value: 'gou',
                    text: '飲食文學',
                },
                {
                    key: 'sfam',
                    value: 'sfam',
                    text: '科幻與推理文學',
                },
                {
                    key: 'ol',
                    value: 'ol',
                    text: '海洋文學',
                },
                {
                    key: 'gl',
                    value: 'gl',
                    text: '性別文學',
                },
                {
                    key: 'tltc',
                    value: 'tltc',
                    text: '外譯房',
                },
                {
                    key: 'tlvm',
                    value: 'tlvm',
                    text: '臺灣文學虛擬博物館',
                },
                {
                    key: 'newspaper',
                    value: 'newspaper',
                    text: '報紙',
                },
                {
                    key: 'lh',
                    value: 'lh',
                    text: '賴和紀念館',
                },
                {
                    key: 'clh',
                    value: 'clh',
                    text: '鍾理和紀念館',
                },
                {
                    key: 'yk',
                    value: 'yk',
                    text: '楊逵紀念館',
                },
                {
                    key: 'oit',
                    value: 'oit',
                    text: '王育德紀念館',
                },
                {
                    key: 'ccw',
                    value: 'ccw',
                    text: '陳千武文庫',
                },
                {
                    key: 'lrc',
                    value: 'lrc',
                    text: '李榮春文學館',
                },
                {
                    key: 'lyz',
                    value: 'lyz',
                    text: '龍瑛宗文學館',
                },
                {
                    key: 'kqh',
                    value: 'kqh',
                    text: '柯旗化故居',
                },
                {
                    key: 'la',
                    value: 'la',
                    text: '李昂文藏館',
                },
                {
                    key: 'sm',
                    value: 'sm',
                    text: '三毛夢屋',
                },
                {
                    key: 'klm',
                    value: 'klm',
                    text: '高雄文學館',
                },
                {
                    key: 'lsq',
                    value: 'lsq',
                    text: '梁實秋故居',
                },
                {
                    key: 'lyt',
                    value: 'lyt',
                    text: '林語堂故居',
                },
                {
                    key: 'tlm',
                    value: 'tlm',
                    text: '臺中文學館',
                },
                {
                    key: 'wikipedia',
                    value: 'wikipedia',
                    text: '維基百科',
                },
                {
                    key: 'yst',
                    value: 'yst',
                    text: '葉石濤文學紀念館',
                },
            ],
            selected: '',
        },
        datePicker: { show: false, startDate: '', endDate: '' },
        detailStats: false,
        stats: [
            {
                id: 'basicInfo',
                title: '基本資料',
                stat: '',
                detailStatsArr: [],
                url: Api.getPersonDsList,
            },
            {
                id: 'education',
                title: '教育',
                stat: '',
                detailStatsArr: [],
                url: Api.getEducationDsList,
            },
            {
                id: 'organization',
                title: '組織與職業',
                stat: '',
                detailStatsArr: [],
                url: Api.getOrganizationDsList,
            },
            {
                id: 'specialty',
                title: '專長',
                stat: '',
                detailStatsArr: [],
                url: Api.getSpecialtyDsList,
            },
            {
                id: 'relationship',
                title: '人際關係',
                stat: '',
                detailStatsArr: [],
                url: Api.getRelationshipDsList,
            },
            {
                id: 'event',
                title: '事件',
                stat: '',
                detailStatsArr: [],
                url: Api.getEventDsList,
            },
            {
                id: 'foundation',
                title: '相關建築',
                stat: '',
                detailStatsArr: [],
                url: Api.getFoundationDsList,
            },
            {
                id: 'derivatework',
                title: '衍生作品',
                stat: '',
                detailStatsArr: [],
                url: Api.getDerivateworkDsList,
            },
            {
                id: 'publication',
                title: '出版品或刊物',
                stat: '',
                detailStatsArr: [],
                url: Api.getPublicationDsList,
            },
            {
                id: 'article',
                title: '單篇作品',
                stat: '',
                detailStatsArr: [],
                url: Api.getArticleDsList,
            },
            {
                id: 'award',
                title: '獲獎',
                stat: '',
                detailStatsArr: [],
                url: Api.getAwardDsList,
            },
            {
                id: 'project',
                title: '計畫',
                stat: '',
                detailStatsArr: [],
                url: Api.getProjectDsList,
            },
            {
                id: 'location',
                title: '地點',
                stat: '',
                detailStatsArr: [],
                url: Api.getLocationDsList,
            },
            {
                id: 'newEducation',
                title: '新舊組織',
                stat: '',
                detailStatsArr: [],
                url: Api.getReplacedorgDsList,
            },
            {
                id: 'organizationinfo',
                title: '組織基本資訊',
                stat: '',
                detailStatsArr: [],
                url: Api.getOrganizationinfoDsList,
            },
            {
                id: 'collectible',
                title: '典藏',
                stat: '',
                detailStatsArr: [],
                url: Api.getCollectibleDsList,
            },
            {
                id: 'tlvmperiod',
                title: '台灣文學史',
                stat: '',
                detailStatsArr: [],
                url: Api.getTlvmperiodDsList,
            },
            {
                id: 'nanzi',
                title: '難字表',
                stat: '',
                detailStatsArr: [],
                url: Api.getNanziDsList,
            },
            {
                id: 'statsTotal',
                title: '總筆數',
                stat: '',
                detailStatsArr: [],
                url: '',
            },
        ],
    },
];

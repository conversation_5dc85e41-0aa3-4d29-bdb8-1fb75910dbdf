import React from "react";
import PropTypes from "prop-types";

// semantic ui
import { Button, Modal } from "semantic-ui-react";

// fixme: 用src/components/common/CustomModal/ResultModal.js取代
function SaveResultModal({ openModal, onClose, onClick, modalMessage }) {
    return (
        <Modal size="mini" open={openModal} onClose={onClose}>
            <Modal.Content>
                <p>{modalMessage}</p>
            </Modal.Content>
            <Modal.Actions>
                <Button positive onClick={onClick}>
                    關閉視窗
                </Button>
            </Modal.Actions>
        </Modal>
    );
}

SaveResultModal.propTypes = {
    /** 顯示儲存資料後結果的視窗 */
    openModal: PropTypes.bool,
    /** 關閉視窗動作 */
    onClose: PropTypes.func,
    /** 點選視窗按鈕 */
    onClick: PropTypes.func,
    /** 顯示訊息 */
    modalMessage: PropTypes.string
};

SaveResultModal.defaultProps = {
    /** 顯示儲存資料後結果的視窗 */
    openModal: false,
    /** 關閉視窗動作 */
    onClose: () => null,
    /** 點選視窗按鈕 */
    onClick: () => null,
    /** 顯示訊息 */
    modalMessage: ""
};

export default SaveResultModal;

import React, { useRef, useMemo } from "react";
import { Select } from "semantic-ui-react";
import StatCard from "./StatCard";
import CustomDatePicker from "../../../../datePicker/CustomDatePicker";
import "./topicStats.scss";
import refreshIcon from "../../../../../images/refreshIcon.svg";

const TopicStats = ({
    topicId,
    topicTitle,
    statsCount,
    lastUpdatedTime,
    select,
    datePicker,
    detailStats,
    stats,
    handleUpdateData,
    setStatsData,
    earliestDate,
    latestDate
}) => {
    const topicStatsRef = useRef(null);

    const handleChange = value => {
        setStatsData(prevStatsData => {
            const topicIdx = prevStatsData.findIndex(el => el.id === topicId);
            if (
                topicIdx !== -1 &&
                prevStatsData[topicIdx].select.selected !== value
            ) {
                const newStatsData = [...prevStatsData];
                newStatsData[topicIdx].select.selected = value;
                return newStatsData;
            }
            return prevStatsData;
        });
    };

    const selectedOptionText = useMemo(() => {
        return (
            select.options.find(el => el.value === select.selected)?.text || ""
        );
    }, [select.options, select.selected]);

    const handleChangeDate = (type, date) => {
        const formattedDate = date.toISOString().slice(0, 10);
        setStatsData(prevStatsData => {
            const topicIdx = prevStatsData.findIndex(el => el.id === topicId);
            if (topicIdx !== -1) {
                const newStatsData = [...prevStatsData];
                newStatsData[topicIdx].datePicker[type] = formattedDate;
                return newStatsData;
            }
            return prevStatsData;
        });
    };

    return (
        <div className="topicStats" ref={topicStatsRef}>
            {statsCount && (
                <div className="topicTitle">
                    <h2>筆數統計</h2>
                </div>
            )}
            {select.show && (
                <Select
                    placeholder="Select"
                    options={select.options}
                    className="statsSelect"
                    value={select.selected}
                    onChange={(e, { value }) => handleChange(value)}
                />
            )}
            <div
                className={
                    statsCount
                        ? "topicStatsContainerWithCount"
                        : "topicStatsContainer"
                }
            >
                <div className="topicTitle">
                    <h2>
                        {statsCount
                            ? `${selectedOptionText}${topicTitle}`
                            : topicTitle}
                    </h2>

                    {lastUpdatedTime.show && (
                        <div className="lastUpdatedTime">
                            <p>資料截止時間：{lastUpdatedTime.time}</p>
                            <button onClick={() => handleUpdateData()}>
                                <img src={refreshIcon} alt="refreshIcon" />
                            </button>
                        </div>
                    )}
                </div>
                {datePicker.show && datePicker.startDate && datePicker.endDate && (
                    <div className="dateContainer">
                        <CustomDatePicker
                            startDate={datePicker.startDate}
                            endDate={datePicker.endDate}
                            minDate={earliestDate}
                            maxDate={latestDate}
                            onChange={handleChangeDate}
                        />
                    </div>
                )}
                <div
                    className={statsCount ? "statCardsWithCount" : "statCards"}
                >
                    {stats
                        .filter(el =>
                            select.selected && select.options.length > 0
                                ? el.id.endsWith(`_${select.selected}`)
                                : true
                        )
                        .map(({ id, title, stat, detailStatsArr }) => (
                            <StatCard
                                key={id}
                                statsCount={statsCount}
                                title={title}
                                stat={stat}
                                detailStatsArr={detailStatsArr}
                                topicStatsRef={topicStatsRef}
                                detailStats={detailStats}
                            />
                        ))}
                </div>
            </div>
        </div>
    );
};

export default TopicStats;

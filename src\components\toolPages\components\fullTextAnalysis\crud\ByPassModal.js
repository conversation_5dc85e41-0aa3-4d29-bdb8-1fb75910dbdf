/* eslint-disable camelcase,no-shadow */
import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Checkbox, Segment } from "semantic-ui-react";
import { useSelector, useDispatch } from "react-redux";
import { FLT_PROCESS, HTTP_METHOD } from "../config";
import FltAnaAct from "../../../../../reduxStore/fltAnalysis/FltAnaAct";
import { ALERT_MSG_TYPE } from "../../../../common/AlertMsg";
// eslint-disable-next-line import/named
import { Pending, MsgTitle, ErrTitle } from "../message/customMsg";
import { getErrorString } from "../../../../../commons";
import {
    postFltAnalysis,
    putFltAnalysis
} from "../../../../common/words/crudHelper";

// 勾選全部
const TOGGLE_ALL_KEY = "all";
const NO_LANG = "無語系";

// 控制要處理那些流程
const ByPassModal = () => {
    // store
    const dispatch = useDispatch();
    const { byPassModal } = useSelector(state => state.fltAna);
    const { context, open } = byPassModal;
    const { rowData, httpMethod } = context;
    // eslint-disable-next-line camelcase
    const { id, type, label, fullWorkAvailableAts__MultiLang } = rowData || {};
    // state
    // 初始設定
    const [initPsOnOff, setInitPsOnOff] = useState({});
    // 即時狀態
    const [psOnOff, setPsOnOff] = useState({});
    // 要分析的語言
    const [langs, setLangs] = useState([]); // [{label,checked}]
    // 儲存 query 狀態,若清空則代表完成query
    const [query, setQuery] = useState([]);

    useEffect(() => {
        if (Array.isArray(fullWorkAvailableAts__MultiLang)) {
            // 一個語系只保留一個,
            // e.g.zh,zh,en,"" => zh,en,無語系
            const tmpLangs = Array.from(
                new Set(
                    fullWorkAvailableAts__MultiLang.map(
                        o => o.fullWorkLang || NO_LANG
                    )
                )
            );
            if (tmpLangs.length > 0) {
                setLangs(
                    tmpLangs.map(o => ({
                        name: o,
                        label: o,
                        checked: false
                    }))
                );
            }
        }
    }, [fullWorkAvailableAts__MultiLang]);

    const closeModal = React.useCallback(
        ({ refreshData, refreshFirestore, refreshTable }) => {
            dispatch({
                type: FltAnaAct.setBypassModalOpen,
                payload: false
            });
            dispatch({
                type: FltAnaAct.setBypassModalContextInit
            });
            if (refreshFirestore && refreshData) {
                // 先取得 firestore 資料,在取 nmtl-api 的資要
                dispatch({
                    type: FltAnaAct.fetchSignalDbAndFr
                });
            } else if (refreshFirestore) {
                // 重新取得 firestore 資料
                dispatch({
                    type: FltAnaAct.fetchSignalFrstore
                });
            } else if (refreshData) {
                // 重新取得 database 資料
                dispatch({
                    type: FltAnaAct.fetchSignalBasic
                });
            }
            if (refreshTable) {
                dispatch({
                    type: FltAnaAct.setRefreshTableSignal
                });
            }
        },
        []
    );

    //
    const handleSave = async () => {
        try {
            if (![HTTP_METHOD.POST, HTTP_METHOD.PUT].includes(httpMethod))
                return;

            const byPass = Object.entries(psOnOff)
                .filter(([key, obj]) => obj.checked === false)
                .map(([key]) => key);

            // 顯示訊息
            dispatch({
                type: FltAnaAct.setMessage,
                payload: {
                    type: ALERT_MSG_TYPE.info,
                    title: <Pending />
                }
            });
            //
            const onFinish = () => {
                // 顯示訊息
                dispatch({
                    type: FltAnaAct.setMessage,
                    payload: {
                        type: ALERT_MSG_TYPE.success,
                        title: <MsgTitle />,
                        text: (
                            <div>{`詳細資訊=> (名稱：${label}，id：${id}，類型:${type})`}</div>
                        )
                    }
                });
                closeModal({
                    refreshData: true,
                    refreshFirestore: true,
                    refreshTable: false
                });
            };
            const onError = err => {
                // 顯示訊息
                dispatch({
                    type: FltAnaAct.setMessage,
                    payload: {
                        type: ALERT_MSG_TYPE.error,
                        title: <ErrTitle />,
                        text: getErrorString(err)
                    }
                });
                closeModal({
                    refreshData: true,
                    refreshFirestore: true,
                    refreshTable: false
                });
            };

            // rowData資料格式:
            // {
            // id,label,type,graph,_type,
            // fileAvailableAt,fltId,fullWorkAvailableAt,
            // fullWorkLang, // 全文語系
            // docId, // firestore 的 doc id
            // label // 著作/文章名稱
            // fileAvailableAts__MultiLang":[{fileLang,fileAvailableAt}],
            // fullWorkAvailableAts__MultiLang:[{fullWorkLang,fullWorkAvailableAt}],
            // fltIds__MultiLang":[{fltIdLang,fltId}]
            // }

            const {
                id,
                label,
                type,
                graph,
                _type,
                docIds,
                fileAvailableAts__MultiLang,
                fullWorkAvailableAts__MultiLang,
                fltIds,
                fltIds__MultiLang
            } = rowData;

            const defFullWorkLangList = langs
                .map(o => (o.label === NO_LANG ? "" : o.label))
                .filter(o => !!o);

            const postData = langs
                .filter(o => o.checked)
                .map(o => (o.label === NO_LANG ? "" : o.label))
                .map(lang => {
                    // 依照篩選的語系找出對應的 fileAvailableAt,fullWorkAvailableAt,fltId

                    const fileAvailableAt = fileAvailableAts__MultiLang.find(
                        o => o.fileLang === lang
                    )?.fileAvailableAt;
                    const fullWorkAvailableAt = fullWorkAvailableAts__MultiLang.find(
                        o => o.fullWorkLang === lang
                    )?.fullWorkAvailableAt;

                    // 如果fltId 沒有定義 lang, 則使用第一個 fltId
                    const fltId =
                        fltIds.find(
                            o =>
                                Array.isArray(o.fltIdLang) &&
                                o.fltIdLang.includes(lang)
                        )?.fltId || fltIds?.[0]?.fltId;

                    let docId = docIds.find(o => o.fltId === fltId)?.docId;

                    // 把當前要處理的語系放在陣列的第一個
                    const fullWorkLangList = Array.from(
                        new Set(
                            [lang].concat(
                                fltIds.find(
                                    o =>
                                        Array.isArray(o.fltIdLang) &&
                                        o.fltIdLang.includes(lang)
                                )?.fltIdLang || []
                            )
                        )
                    );
                    if (httpMethod === HTTP_METHOD.PUT) {
                        // 如果找不到對應的 docId, 則以 docIds 的第一個為主
                        docId = docId || docIds?.[0]?.docId;
                    }
                    // fileAvailableAt,fullWorkAvailableAt,fltId 可能為 undefined
                    // 讓 postFltAnalysis & putFltAnalysis檢查
                    return {
                        // 僅需要使用 rowData 部分的資訊
                        id,
                        label,
                        type,
                        graph,
                        _type,
                        docId,
                        fileAvailableAt,
                        fullWorkAvailableAt,
                        fltId,
                        fullWorkLang: fullWorkLangList // e.g.["zh","en"],""
                    };
                });

            let fltAnaAct;
            if (httpMethod === HTTP_METHOD.POST) {
                fltAnaAct = postFltAnalysis;
            } else if (httpMethod === HTTP_METHOD.PUT) {
                fltAnaAct = putFltAnalysis;
            }
            if (fltAnaAct) {
                // handle finish
                const onSingleFltFinish = res => {
                    /**
                     * res.data
                     *  {
                     *   status: "OK",
                     *   message: "FullText analysis is in progress",
                     *   firestoreColDocId: firestoreColDocId,
                     *  }
                     */
                    // console.log("onSingleFltFinish res", res);
                    const { firestoreColDocId } = (res || {}).data || {};
                    // 把 docId 儲存至 redux 中,用來記憶使用者處理的 docId
                    dispatch({
                        type: FltAnaAct.putMemoFirestoreColDocIds,
                        payload: [firestoreColDocId].filter(t => !!t)
                    });
                };

                const allQuery = postData.map(dt =>
                    fltAnaAct({ data: dt, byPass }, onSingleFltFinish)
                );
                setQuery(postData);
                Promise.all(allQuery)
                    .then(res => {
                        const allErr = res
                            .filter(r => r.error)
                            .map(r => r.error);
                        console.log("allErr", allErr);
                        if (allErr.length > 0) {
                            onError(allErr);
                        } else {
                            onFinish();
                        }
                    })
                    .catch(err => {
                        onError(err);
                    })
                    .finally(() => {
                        setQuery([]);
                    });
            }
        } catch (err) {
            // 顯示訊息
            dispatch({
                type: FltAnaAct.setMessage,
                payload: {
                    type: ALERT_MSG_TYPE.error,
                    title: <ErrTitle />,
                    text: getErrorString(err)
                }
            });
            closeModal({
                refreshData: true,
                refreshFirestore: false,
                refreshTable: false
            });
        }
    };

    const handleCancel = () => {
        // 回復原本設定
        setPsOnOff(initPsOnOff);
        closeModal({
            refreshData: true,
            refreshFirestore: false,
            refreshTable: false
        });
        // 清空訊息
        dispatch({
            type: FltAnaAct.setMessage,
            payload: null
        });
    };

    React.useEffect(() => {
        if (!context) return;
        if (!(rowData && httpMethod)) return;
        const tmpInitPsOnOff = Object.entries(FLT_PROCESS).reduce(
            (acc, [key, obj]) => {
                acc[key] = {
                    ...obj,
                    checked: true,
                    // disabled: httpMethod === HTTP_METHOD.POST,
                    disabled: false
                };
                return acc;
            },
            {}
        );
        // 若為開始分析, 則所有 checked 皆為 true
        if (httpMethod === HTTP_METHOD.POST) {
            // do nothing
        }
        let toggleAllChecked = true;
        // 若為重新分析, 則依據前次狀態來調整 checked
        if (httpMethod === HTTP_METHOD.PUT) {
            Object.keys(rowData).forEach(key => {
                if (key in FLT_PROCESS) {
                    const { finishTime, errorTime } = rowData[key] || {};
                    if (finishTime && !errorTime) {
                        tmpInitPsOnOff[key].checked = false;
                        // 只要有一個 checked:off, 則 all 為 false
                        toggleAllChecked = false;
                    }
                }
            });
        }
        // 只要有一個 checked:off, 則 all 為 false
        tmpInitPsOnOff[TOGGLE_ALL_KEY] = {
            name: TOGGLE_ALL_KEY,
            label: "勾選全部",
            checked: toggleAllChecked,
            // disabled: httpMethod === HTTP_METHOD.POST,
            disabled: false
        };
        setInitPsOnOff(tmpInitPsOnOff);
        setPsOnOff(tmpInitPsOnOff);
    }, [context]);

    const onLangChange = lang => (e, { checked }) => {
        const tmpLangs = [...langs];
        const findIndex = tmpLangs.findIndex(o => o?.name === lang?.name);
        if (findIndex > -1) {
            tmpLangs[findIndex].checked = checked;
            setLangs(tmpLangs);
        }
    };

    const onStepChange = psId => (e, { checked }) => {
        if (psId === TOGGLE_ALL_KEY) {
            const tmpVisibility = Object.keys(psOnOff).reduce((obj, key) => {
                // eslint-disable-next-line no-param-reassign
                obj[key] = {
                    ...psOnOff[key],
                    checked
                };
                return obj;
            }, {});
            setPsOnOff(tmpVisibility);
        } else {
            const tmpVisibility = { ...psOnOff };
            if (!checked) {
                tmpVisibility[TOGGLE_ALL_KEY].checked = false;
            }
            tmpVisibility[psId].checked = checked;
            setPsOnOff(tmpVisibility);
        }
    };

    if (!context) return null;

    const Info = text => (
        <Header as="h4" style={{ marginTop: "10px", marginBottom: "5px" }}>
            {text}
        </Header>
    );

    // 是否要顯示 〈要分析的語系〉
    const showLangFilter = () =>
        Array.isArray(fullWorkAvailableAts__MultiLang) &&
        fullWorkAvailableAts__MultiLang.length > 0;

    const allowSave = showLangFilter() ? !!langs.find(o => o.checked) : true;

    return (
        <Modal
            onClose={() => {
                closeModal({
                    refreshData: true,
                    refreshFirestore: false,
                    refreshTable: false
                });
            }}
            open={open}
            size="large"
        >
            <Header>全文字詞分析流程</Header>
            <Modal.Content>
                {Info(`名稱：${label}`)}
                {Info(`id：${id}`)}
                {Info(`類型：${type}`)}
                {showLangFilter() && (
                    <Header as="h4">若有全文，請挑選要分析的語系：</Header>
                )}
                {showLangFilter() && (
                    <div>
                        {langs.map((lang, idx) => (
                            <Checkbox
                                key={idx.toString()}
                                label={lang.label}
                                checked={lang.checked}
                                onChange={onLangChange(lang)}
                                disabled={lang?.disabled}
                                style={{
                                    marginRight: "20px",
                                    marginBottom: "12px"
                                }}
                            />
                        ))}
                    </div>
                )}
                <Header as="h4">請挑選要分析的步驟：</Header>
                {(context && (
                    <Segment>
                        <div>
                            <Checkbox
                                label={psOnOff[TOGGLE_ALL_KEY]?.label}
                                checked={psOnOff[TOGGLE_ALL_KEY]?.checked}
                                onChange={onStepChange(TOGGLE_ALL_KEY)}
                                disabled={psOnOff[TOGGLE_ALL_KEY]?.disabled}
                                style={{
                                    marginRight: "10px",
                                    marginBottom: "20px"
                                }}
                            />
                        </div>
                        {Object.keys(psOnOff).map((key, idx) => {
                            if (key === TOGGLE_ALL_KEY) {
                                // eslint-disable-next-line react/no-array-index-key
                                return <React.Fragment key={idx.toString()} />;
                            }
                            return (
                                <Checkbox
                                    key={idx.toString()}
                                    label={psOnOff[key]?.label}
                                    checked={psOnOff[key]?.checked}
                                    onChange={onStepChange(key)}
                                    disabled={psOnOff[key]?.disabled}
                                    style={{
                                        marginRight: "20px",
                                        marginBottom: "12px"
                                    }}
                                />
                            );
                        })}
                    </Segment>
                )) || <div />}
            </Modal.Content>
            <Modal.Actions>
                <div>
                    <Button
                        onClick={() => {
                            handleSave();
                        }}
                        loading={query.length > 0}
                        color="blue"
                        disabled={!allowSave}
                    >
                        開始分析
                    </Button>
                    <Button onClick={() => handleCancel()} color="yellow">
                        取消
                    </Button>
                </div>
            </Modal.Actions>
        </Modal>
    );
};

export default ByPassModal;

import React, { useEffect, useState } from "react";
import { Icon, Label, Popup, Table } from "semantic-ui-react";

const StatusCell = ({
    // 所有 cell 共用的 props
    colId,
    rowIdx,
    cellValue,
    column,
    rowData,
    domain,
    range,
    graph,
    classType
    // 特定 cell 才有的 props

    // 除了 table 之外的使用,可以自定義其他的 property 擴充功能
}) => {
    //
    const info = _value => {
        const { finishTime, requestTime, errorTime, errorMessage } =
            _value || {};
        const status = () => {
            if (!requestTime) return null;
            if (finishTime) {
                return (
                    <div>
                        <Icon name="check" color="green" />
                    </div>
                );
            }
            if (errorTime) {
                return (
                    <div>
                        <Icon name="warning sign" color="red" />
                        {JSON.stringify(errorMessage)}
                    </div>
                );
            }
            return (
                <div>
                    <Icon name="ellipsis horizontal" color="grey" />
                </div>
            );
        };
        const localeDate = isoStr => new Date(isoStr).toLocaleString();

        return (
            <div>
                {requestTime && <div>請求時間：{localeDate(requestTime)}</div>}
                {status()}
            </div>
        );
    };

    const getLabel = valueObj => {
        const { value, label } = valueObj;
        const { finishTime, errorTime, requestTime } = value || {};

        const status = () => {
            if (!requestTime) return null;
            if (finishTime) {
                return <Icon name="check" color="green" />;
            }
            if (errorTime) {
                return <Icon name="warning sign" color="red" />;
            }
            return <Icon name="ellipsis horizontal" color="grey" />;
        };

        return (
            <div>
                <Label style={{ marginBottom: "8px", cursor: "pointer" }}>
                    {label || "未定義"}
                    {status()}
                </Label>
            </div>
        );
    };

    const valueList = (Array.isArray(cellValue)
        ? cellValue
        : [cellValue]
    ).filter(o => !!o && o.value);

    // const show = value => value?.label && value?.value?.requestTime;
    const show = value => value?.label;

    return (
        <div>
            {valueList.map(
                (o, idx) =>
                    show(o) && (
                        <Popup
                            key={idx.toString()}
                            // on={["hover"]}
                            // hoverable
                            on="click"
                            wide
                            position="right center"
                            trigger={getLabel(o)}
                        >
                            <Popup.Content>
                                <div
                                    style={{
                                        maxHeight: "300px",
                                        overflow: "auto"
                                    }}
                                >
                                    <Label style={{ marginBottom: "8px" }}>
                                        {o?.label || "未定義"}
                                    </Label>
                                    {info(o.value)}
                                </div>
                            </Popup.Content>
                        </Popup>
                    )
            )}
        </div>
    );
};

export default StatusCell;

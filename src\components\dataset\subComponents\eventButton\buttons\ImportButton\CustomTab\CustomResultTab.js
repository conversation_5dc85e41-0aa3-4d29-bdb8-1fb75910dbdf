import React, { useContext } from "react";

import { Divider, Message } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../../../../store/StoreProvider";

const CustomResultTab = () => {
    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    // get dataset(mainSubject) and sheet
    const { uploaded } = state.data;
    const { record } = uploaded;

    return (
        <Message>
            <Message.Header>更新結果</Message.Header>
            <Divider />
            <p>更新成功:{record?.success}</p>
            <p>更新失敗:{record?.error}</p>
        </Message>
    );
};

export default CustomResultTab;

// import checkStr from "./checkStr";

const checkPD = (cell, propLabel) => {
    let tmpResStr = "";
    const limitList = ["true", "false"];

    if (cell.value) {
        if (typeof cell.value === "string") {
            if (!limitList.includes(cell.value)) {
                const reason = `內容錯誤，僅能填寫下列選項${limitList}`;
                tmpResStr += `${cell.address}, [${cell.value}], 欄位:${propLabel}，${reason}`;
            }
        }
        // else {
        //     tmpResStr += checkStr(cell, propLabel);
        // }
    }

    return tmpResStr;
};

export default checkPD;

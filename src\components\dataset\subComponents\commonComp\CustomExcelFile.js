import React, { useMemo } from "react";

// excel
import ReactExport from "react-data-export";

const CustomExcelFile = ({ name, filename, header, data }) => {
    // console.log("I am CustomExcelFile");
    const { ExcelFile } = ReactExport;
    const { ExcelSheet } = ReactExport.ExcelFile;
    const { ExcelColumn } = ReactExport.ExcelFile;
    const MemoziedExcelFile = useMemo(
        () => (
            <ExcelFile hideElement filename={filename}>
                <ExcelSheet data={data} name={name}>
                    {header &&
                        header.map(item => (
                            <ExcelColumn
                                key={item.id}
                                label={item.label}
                                value={item.id}
                            />
                        ))}
                </ExcelSheet>
            </ExcelFile>
        ),
        [name, header, data]
    );
    return MemoziedExcelFile;
};

export default CustomExcelFile;

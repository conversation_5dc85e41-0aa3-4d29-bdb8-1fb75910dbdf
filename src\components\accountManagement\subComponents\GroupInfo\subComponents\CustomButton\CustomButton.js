import React, { useEffect, useState, useContext } from "react";

// plugin
import { But<PERSON> } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";
import PropTypes from "prop-types";

//
import { TypeName } from "../../../Utils/compoConfig";
import accMngAct from "../../../../../../reduxStore/accManage/accManageAction";
import { saveGroupData } from "../../../Utils/utils";
import { StoreContext } from "../../../../../../store/StoreProvider";

function CustomButton({ compoInfo, onClick }) {
    const [stateContext, dispatchContext] = useContext(StoreContext);
    const { user } = stateContext;
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { groupData, groupFsID } = state;
    const { typeName, btnName } = compoInfo;
    const [primBool, setPrimBool] = useState(false);
    const [posBool, setPosBool] = useState(false);

    useEffect(() => {
        switch (typeName) {
            case TypeName.SaveData:
            case TypeName.GroupMemberAddAPerson:
                setPrimBool(true);
                break;
            case TypeName.GroupDetailCancel:
                setPosBool(true);
                break;
            default:
                break;
        }
    }, []);

    // eslint-disable-next-line no-shadow
    const handleClick = typeName => {
        switch (typeName) {
            case TypeName.GroupDetailCancel:
                dispatch({
                    type: accMngAct.SET_EDITGROUPMODE,
                    payload: false
                });
                break;
            case TypeName.SaveData:
                // console.log("groupFsID ", groupFsID);
                // console.log("groupData ", groupData);
                // save data to firebase
                saveGroupData(
                    groupFsID,
                    groupData,
                    dispatch,
                    user,
                    dispatchContext
                );
                break;
            case TypeName.GroupMemberAddAPerson:
                onClick();
                break;
            default:
                break;
        }
    };

    return (
        <Button
            className={typeName}
            onClick={() => handleClick(typeName)}
            primary={primBool}
            positive={posBool}
        >
            {btnName}
        </Button>
    );
}

CustomButton.defaultProps = {
    compoInfo: { typeName: "", btnName: "", segment: "" },
    onClick: () => null
};

CustomButton.propTypes = {
    // eslint-disable-next-line react/forbid-prop-types
    compoInfo: PropTypes.object,
    onClick: PropTypes.func
};

export default CustomButton;

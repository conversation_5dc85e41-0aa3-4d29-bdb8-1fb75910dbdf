import axios from "axios";
import Api from "./Api";
import { readNmtlData } from "./index";
import { isEmpty } from "../../commons";
import { SHOW_ID } from "../../components/common/sheetCrud/sheetCrudHelper";

const ApiField = {
    // a
    getAcademicDegreelist: ["hasAcademicDegree"],
    getAcademicDisciplinelist: ["hasAcademicDiscipline"],
    getAreaOfTaiwan: ["isAreaOf", "hasLiteraryAreaIn"],
    getArticleList: ["hasCollection"],
    getAwardList: ["hasAwardReceived"],
    // c
    getCity: ["hasCity", "hasCurrentCity"],
    // getCollection: ["hasCollection"],
    getCollectible: ["Collectible", "CollectibleID"],
    getCollectType: ["CollectibleType"],
    getCopyrightStatus: [
        "hasCopyrightStatus",
        "hasImageCopyright",
        "hasCopyrightStatus__Event",
        "hasCopyrightStatus__Publication",
        "hasCopyrightStatus__Article",
        "hasCopyrightStatus__Collectible",
        "hasFullWorkCopyright",
        "hasCopyrightStatus_hasURL"
    ],
    getCountry: ["hasCountry", "hasCurrentCountry"],
    // d
    // getDatasetArticle: ["hasCollection", "hasDescribedTarget"],
    // getDatasetPublication: ["hasCollection", "hasDescribedTarget"],
    getDatasetArticle: ["hasDescribedTarget"],
    getDatasetPublication: ["hasDescribedTarget"],
    getDynasty: ["hasDynasty"],
    getDerivateWork: ["hasDerivateWork"],
    // e
    getEduOrg: ["hasEducatedAt"],
    getEthnicGroup: ["hasEthnicGroup"],
    getExaminationlist: ["hasExamination"],
    // "Event" for create
    getEvent: ["hasOwnedBy", "Event"],
    // f
    getFoundationList: ["hasFoundation"],
    getFoundationType: ["hasFoundationType"],
    getFictionalCharacter: ["hasCharacters"],
    // g
    getGender: ["hasGender"],
    // l
    getLanguage: [
        "hasLanguageOfWorkOrName",
        "hasNanziLanguage",
        "hasTranslationLanguage"
    ],
    getLiteraryGenre: ["LiteraryGenre"],
    getLocationList: [
        "srcId_hasPlaceOfPublication",
        "hasPlaceOfPublication",
        "hasWritingLocation",
        "hasNarrativeLocation",
        "hasResidence",
        "hasEventLocation",
        "hasFamilyOrigin",
        "hasCountryOfOrigin",
        "hasAncestralHome",
        "hasPlaceOfBirth",
        "hasLocation",
        "hasPlaceOfDeath",
        "hasLocationOfFormation",
        "hasWorkLocation",
        "hasFamilyOrigin",
        "hasAncestralHome",
        "LocationID",
        "Location"
    ],
    // m
    getMSList: ["MainSubject"],
    // o
    getOccupationlist: ["hasOccupation"],
    getOrganizationList: [
        "hasAffiliatedIn",
        "hasAuthor",
        "hasAppraiser",
        "hasContributor",
        "hasCreator",
        "hasCollector",
        "hasDescribedPoetClub",
        "hasDataWriter",
        "hasEditor",
        "hasFounded",
        "hasFoundationRelated",
        "hasInteractive",
        "hasIllustrator",
        "hasInterviewer",
        "hasInscriptioner",
        "hasModifier",
        "hasOraler",
        "hasOwnedBy",
        "hasPublisher",
        "hasParticipant",
        "hasPositionIn",
        "hasPrefaceAuthor",
        "hasPrincipalInvestigator",
        "hasReplaced",
        "hasReviewer",
        "hasSponsor",
        "hasTranslator",
        "hasRequester",
        "hasUser",
        "hasWriter",
        "isCollectedBy",
        "isEventOf",
        "isMemberOf",
        "isReplacedBy",
        // for add
        "OrganizationID",
        // for create
        "Organization"
    ],
    // p
    getPersonlist: [
        "hasAppraiser",
        "hasAuthor",
        "hasCollator",
        "hasCollector",
        "hasCreator",
        "hasContributor",
        "hasDescribedTarget",
        "hasDescribedPoet",
        "hasDataWriter",
        "hasEditor",
        "hasFoundationRelated",
        "hasInterviewer",
        "hasIllustrator",
        "hasInscriptioner",
        "hasIntroductionWriter",
        "hasModifier",
        "hasOraler",
        "hasOwnedBy",
        "hasPublisher",
        "hasPrefaceAuthor",
        "hasPrincipalInvestigator",
        "hasReviewer",
        "hasRequester",
        "hasRelatedPerson",
        "hasSponsor",
        "hasTranslator",
        "hasUser",
        "hasWriter",
        "isAwardReceivedOf",
        "isEducationOf",
        "isEventOf",
        "isOrganizationOf",
        "isRelationOf",
        "isSpecialtyOf",
        "tPersonID",
        // for add
        "PersonID",
        // for create
        "Person"
    ],
    getPrintBook: ["PrintBook"],
    getProvince: ["hasProvince", "hasCurrentProvince"],
    getPublishing: ["Publishing"],
    getPublicationList: [
        "hasPublishedIn",
        "hasTranslationBook",
        "hasCollection"
    ],
    getPositionList: ["hasPosition"],
    // r
    getRelationOp: ["hasRelationship", "relationType"],
    getMSWorks: [
        "hasRelatedWorks",
        "hasPublishedIn",
        "referencesIds",
        "hasAwardedForWork"
    ],
    // t
    getTaiwanPeriod: ["hasSetInPeriod"],
    getTownship: ["hasTownship", "hasCurrentTownship"],
    // s
    getSpecialty: ["hasSpecialty"],
    getSocialProcedureList: ["hasSocialProcedure"],
    //
    // 為了新增
    getEducationList20: ["EducationEvent"],
    getOrganizationEvent20: ["OrganizationEvent"],
    getSpecialty20: ["SpecialtyEvent"],
    getRelationEventList20: ["RelationEvent"],
    // 暫時停用新舊組織的新增
    getReplaceOrg: ["replaceOrgLabel"],
    getFoundation20: ["FoundationEvent"],
    getPublicationList20: [
        "PublicationID",
        "hasPublishedIn",
        "PublicationInfo"
    ],
    getArticleList20: ["ArticleID", "hasPublishedIn"],
    getAward20: ["AwardEvent"],
    getProject: ["ProjectID"],
    getDerivateWork20: ["DerivateWorkID"],
    getTlvmPeriod: ["TlvmPeriodID"],
    getNanzi: ["NanziID"]
};

const ADD_SUFFIX = {
    getOrganizationList: "@ORG",
    getPersonlist: "@PER",
    getEvent: "@EVT",
    // getLocationList: "@LOC",
    getEducationList20: "@EDUEVT",
    getRelationEventList20: "@RETEVT",
    getTlvmPeriod: "@TLP"
};

const COPY_SUFFIX = {
    getOrganizationList: "@ORG",
    getPersonlist: "@PER",
    getEvent: "@EVT",
    // getLocationList: "@LOC",
    getEducationList20: "@EDUEVT",
    getRelationEventList20: "@RETEVT",
    getTlvmPeriod: "@TLP",
    getLocationList: "@LOC",
    getArticleList: "@ART",
    getPublicationList: "@PUB"
};

const convertSuffixToClass = {
    "@ORG": "Organization",
    "@PER": "Person",
    "@EVT": "Event",
    // "@LOC": "Location",
    "@EDUEVT": "EducationEvent",
    "@RETEVT": "RelationEvent",
    "@TLP": "TlvmPeriod",
    "@ART": "Article",
    "@PUB": "Publication",
    "@LOC": "Location"
};

const AllowedLang = [
    "en",
    "zh",
    "zh-tw",
    "jp",
    "ja",
    "fr",
    "gu",
    "cs",
    "de",
    "es",
    "it",
    "ta",
    "tr",
    "sv",
    "ko",
    "vi",
    "hu",
    "th",
    "am",
    "bn",
    "nl",
    "ca",
    "te",
    "sl",
    "lb",
    "no",
    "rw",
    "ku",
    "mn",
    "ro",
    "pl",
    "la",
    "ru",
    "ms",
    "su",
    "hr",
    "sq",
    "sr",
    "ar",
    "uz"
];

const keepWithId = ["srcId", "hasPublishedIn", "references", "hasRelatedWorks"];

const exportHeaderRemove = ["imageURL", "seqid"];

const getApiByField = field =>
    Object.keys(ApiField).find(a => ApiField[a].indexOf(field) > -1);

const getApiByAllField = field =>
    Object.keys(ApiField).filter(a => ApiField[a].indexOf(field) > -1);

const getFieldsByApi = field => ApiField[field];
const getFieldKeepWithId = () => keepWithId;
const getExportHeaderRemove = header =>
    header.filter(h => exportHeaderRemove.indexOf(h.id) < 0);

const addSuffix = apiName =>
    Object.keys(ADD_SUFFIX).indexOf(apiName) > -1 ? ADD_SUFFIX[apiName] : "";

const copySuffix = apiName =>
    Object.keys(COPY_SUFFIX).indexOf(apiName) > -1 ? COPY_SUFFIX[apiName] : "";
const isCorrectSuffix = (actHeader, val) => {
    // const isCorrectSuffix = (cellId, val) => {
    //     let actHeader = cellId;
    //
    //     // 新舊組織特殊情況
    //     if (actHeader === "replaceOrgLabel") {
    //         actHeader = TO_CREATE_REPLACE_ORG;
    //     }
    const apiNames = getApiByAllField(actHeader);
    // console.log(actHeader, apiNames, val);
    if (apiNames.length === 0) {
        return {};
    }

    // 傳入的 value 已有後綴，使用這個後綴
    const foundPrefix = Object.keys(convertSuffixToClass).filter(prefix =>
        // prefix: @PER, @ORG...
        val.endsWith(prefix)
    );
    // console.log(foundPrefix, foundPrefix.length);
    // 如果找到 prefix，就用這個 class，因為有些 property 可以有多個 class
    if (foundPrefix && foundPrefix.length > 0) {
        const newPrefix = foundPrefix[0];
        const newClass = convertSuffixToClass[newPrefix];

        // console.log(getApiByField(newClass));
        return {
            isSuffix: true,
            newValue: val.replace(newPrefix, ""),
            newClass,
            newApi: getApiByField(newClass)
        };
    }

    // 判斷後綴是否正確，API 可能有多個 type
    let res = false;
    const newValue = val;
    let newClass = null;
    let newApi = null;

    // 欄位有多個 type ，但是卻沒有加後綴，無法判斷要新增至哪個 type，直接 return
    if (apiNames.length > 1) {
        return { isSuffix: res, newValue, newClass, newApi };
    }

    apiNames.forEach(api => {
        const suffix = addSuffix(api);

        // 選擇的 class
        newClass = convertSuffixToClass[suffix];
        newApi = api;
        res = true;

        // if (val.endsWith(suffix)) {
        //     res = true;
        //
        //     // 移除 suffix
        //     newValue = val.slice(0, val.length - suffix.length);
        // }
    });

    return { isSuffix: res, newValue, newClass, newApi };
};

const checkLangTag = val => {
    if (typeof val !== "string") {
        return false;
    }

    // 長度比 language tag 短，直接塞
    if (val.length < 3) {
        return false;
    }

    // 已有 language tag，不判斷 language 是否合法
    if (val.slice(-3, -2) === "@") {
        const langTag = val.slice(-2);

        if (AllowedLang.indexOf(langTag) > -1) {
            return true;
        }
        return false;
    }
    return false;
    // return `${val}@zh`;
};

// language tag 為字串最後 @xy 格式
// 此函式判斷是否存在 language tag，如果有則 pass
// 如果沒有則塞入 default 的 language tag，"zh"
const addLangTag = val => {
    if (typeof val !== "string") {
        return val;
    }

    // 長度比 language tag 短，直接塞
    if (val.length < 3) {
        return `${val}@zh`;
    }

    // 已有 language tag，不判斷 language 是否合法
    if (val.slice(-3, -2) === "@") {
        return val;
    }

    return `${val}@zh`;
};

const removeLangTag = val => {
    if (typeof val !== "string") {
        return val;
    }

    // 長度比 language tag 短
    if (val.length < 3) {
        return val;
    }

    // 已有 language tag
    if (val.slice(-3, -2) === "@") {
        return val.slice(0, -3);
    }

    return val;
};

const returnClasstype = (cellId, val) => {
    const apiNames = getApiByAllField(cellId);

    const foundPrefix = Object.keys(convertSuffixToClass).filter(prefix =>
        (val || "").endsWith(prefix)
    );

    if (foundPrefix && foundPrefix.length > 0) {
        const newPrefix = foundPrefix[0];
        return [convertSuffixToClass[newPrefix]];
    }

    return apiNames.map(api => {
        const suffix = copySuffix(api);
        return convertSuffixToClass[suffix];
    });
};

const existedDataInOtherGraph = async (classtype, kw, cellId) => {
    // 移除後綴
    function removeAfterFirstAt(s) {
        const atPosition = s.indexOf("@");
        if (atPosition === -1) {
            return s;
        }
        return s.slice(0, atPosition);
    }
    const tmpKw = removeAfterFirstAt(kw);
    const tmpCls = classtype[0];
    const apistr = SHOW_ID.includes(cellId)
        ? Api.getSameListWithGraphByIdForAll
            .replace("{id}", kw)
            .replace("{type}", tmpCls)
        : Api.getSameListWithGraphForAll
            .replace("{keyword}", tmpKw)
            .replace("{type}", tmpCls);
    const { data } = await readNmtlData(apistr);

    return data;
};

const checkDataInGraph = async (id, ds) => {
    const apiStr = Api.isDataInGraph.replace("{id}", id).replace("{ds}", ds);
    const data = await axios.get(apiStr).then(res => res?.data?.data);
    return !isEmpty(data);
};

export {
    ApiField,
    getApiByField,
    getApiByAllField,
    getFieldsByApi,
    getFieldKeepWithId,
    getExportHeaderRemove,
    addSuffix,
    returnClasstype,
    isCorrectSuffix,
    checkLangTag,
    addLangTag,
    removeLangTag,
    AllowedLang,
    existedDataInOtherGraph,
    checkDataInGraph,
    convertSuffixToClass
};

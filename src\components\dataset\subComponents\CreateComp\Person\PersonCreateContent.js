import React, { useContext, useMemo } from "react";
import { StoreContext } from "../../../../../store/StoreProvider";
import PersonConfig from "./PersonConfig";
import "./PersonCreateContent.scss";
import CustomTableView from "../CustomTableView";
import { isEmpty } from "../../../../../commons";
import { createConfig } from "../createConfig";

const PersonCreateContent = ({
    setCallback,
    setCreateState,
    createState,
    header,
    action
}) => {
    const [state] = useContext(StoreContext);
    const { sheet } = state.data;
    const { key: sheetName } = sheet.selected;

    return useMemo(
        () => (
            <React.Fragment>
                <CustomTableView
                    sheetName={sheetName}
                    header={header}
                    content={PersonConfig.topArea}
                    setCallback={setCallback}
                    setCreateState={setCreateState}
                    createState={
                        isEmpty(createState.default)
                            ? null
                            : createState.default
                    }
                    menuName="default"
                    isDisableSecondInput={
                        action === createConfig.createButton ||
                        action === createConfig.editButton
                    }
                />
                {(action === createConfig.createButton ||
                    action === createConfig.editButton) && (
                    <CustomTableView
                        sheetName={sheetName}
                        header={header}
                        content={PersonConfig.bottomArea}
                        setCallback={setCallback}
                        setCreateState={setCreateState}
                        createState={
                            isEmpty(createState.default)
                                ? null
                                : createState.default
                        }
                        menuName="default"
                    />
                )}
            </React.Fragment>
        ),
        [sheetName, header, createState.default, action]
    );
};

export default PersonCreateContent;

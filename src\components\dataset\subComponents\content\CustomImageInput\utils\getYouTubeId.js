const getYouTubeId = (url) => {
    if (!url) return null;
    try {
        const u = new URL(url);
        const host = u.hostname.replace(/^www\./, '');

        // youtu.be/<id>
        if (host === 'youtu.be') {
            return u.pathname.split('/').filter(Boolean)[0] || null;
        }

        // youtube.com、m.youtube.com、其他 *.youtube.com
        if (host === 'youtube.com' || host === 'm.youtube.com' || host.endsWith('.youtube.com')) {
            // 1) watch?v=<id>
            const v = u.searchParams.get('v');
            if (v) return v;

            // 2) /embed/<id>、/shorts/<id>、/live/<id>、/v/<id>
            const parts = u.pathname.split('/').filter(Boolean);
            if (parts.length >= 2) {
                const [kind, id] = parts;
                if (['embed', 'shorts', 'live', 'v'].includes(kind)) {
                    return id || null;
                }
            }
        }
    } catch (e) {
        console.log('getYouTubeId error:', e);
    }
    return null;
};

export default getYouTubeId;

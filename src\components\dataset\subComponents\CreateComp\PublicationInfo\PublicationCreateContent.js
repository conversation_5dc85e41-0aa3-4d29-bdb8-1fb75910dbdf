import React, { useMemo } from "react";
import { Header } from "semantic-ui-react";
import { createConfig } from "../createConfig";
import "./PublicationCreateContent.scss";
import CustomTabContent from "./CustomTabContent/CustomTabContent";
import CustomTableView from "../CustomTableView";
import PublicationInfoConfig from "./PublicationInfoConfig";

const PublicationCreateContent = ({
    sheetName,
    setCallback,
    setCreateState,
    createState,
    header,
    action
}) =>
    useMemo(
        () => (
            <React.Fragment>
                <Header>原文書</Header>
                <CustomTableView
                    sheetName={sheetName}
                    header={header}
                    content={PublicationInfoConfig.oriBookArea}
                    setCallback={setCallback}
                    setCreateState={setCreateState}
                    createState={
                        Object.hasOwn(createState, "default")
                            ? createState.default
                            : createState
                    }
                    menuName="default"
                    isDisableSecondInput={
                        action === createConfig.createButton ||
                        action === createConfig.editButton
                    }
                />
                {(action === createConfig.createButton ||
                    action === createConfig.editButton) && (
                    <CustomTabContent
                        sheetName={sheetName}
                        content={PublicationInfoConfig.transBookArea}
                        header={header}
                        setCallback={setCallback}
                        setCreateState={setCreateState}
                        createState={createState}
                        isDisableSecondInput={
                            action === createConfig.createButton ||
                            action === createConfig.editButton
                        }
                    />
                )}
            </React.Fragment>
        ),
        [sheetName, header, createState, action]
    );
export default PublicationCreateContent;

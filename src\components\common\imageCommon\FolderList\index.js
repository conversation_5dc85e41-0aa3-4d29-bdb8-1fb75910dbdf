import React, { useContext, useEffect, useRef } from "react";
import { useSelector, useDispatch } from "react-redux";
import { StoreContext } from "../../../../store/StoreProvider";
import titleConfig from "./titleConfig";

// component
import DisplayFolder from "./DisplayFolder";

// store
// eslint-disable-next-line import/named
import { handleFolderClick, handleToggle } from "./folderListHelper";

import FileAct from "../../../../reduxStore/file/fileAction";

const FolderList = () => {
    const dispatch = useDispatch();
    const {
        files: { folderPattern, initFileSettings }
    } = useSelector(state => state);

    const [state] = useContext(StoreContext);
    const { selected: sheetSelected } = state.data.sheet;
    const { headerActiveName } = state.common;
    const { key: mainSubjectSelected } = state.data.mainSubject.selected;

    useEffect(() => {
        if (!folderPattern || initFileSettings) return;

        if (sheetSelected && headerActiveName === "資料管理") {
            // 書封照的圖片分類
            let parentImgFolder = "";

            // 找出圖片分類
            titleConfig.forEach(titleConfigItem => {
                titleConfigItem.titleName.forEach(titleNameItem => {
                    if (
                        sheetSelected.key === titleNameItem &&
                        folderPattern[0].collapse
                    ) {
                        parentImgFolder = titleConfigItem.folderName;
                    }
                });
            });

            // 找出目前上傳模式
            folderPattern[0].folders.forEach(folderPatternItem => {
                // image picker
                if (
                    parentImgFolder &&
                    folderPatternItem.folderName === parentImgFolder
                ) {
                    folderPatternItem.folders.forEach(detailFolderItem => {
                        if (
                            detailFolderItem.folderName === mainSubjectSelected
                        ) {
                            handleFolderClick(
                                null,
                                detailFolderItem,
                                folderPattern,
                                dispatch
                            );
                        }
                    });
                }
                // file picker
                else if (
                    !parentImgFolder &&
                    folderPatternItem.folderName === mainSubjectSelected
                ) {
                    handleFolderClick(
                        null,
                        folderPatternItem,
                        folderPattern,
                        dispatch
                    );
                }
            });

            dispatch({
                type: FileAct.INIT_FILE_SETTINGS,
                payload: true
            });
        } else if (headerActiveName === "網頁設定選項") {
            handleFolderClick(
                null,
                folderPattern[0].folders[4].folders[0],
                folderPattern,
                dispatch
            );

            dispatch({
                type: FileAct.INIT_FILE_SETTINGS,
                payload: true
            });
        }
    }, [
        folderPattern,
        initFileSettings,
        state.data.sheet.selected,
        state.common.headerActiveName
    ]);

    return (
        <DisplayFolder
            folderPattern={folderPattern}
            onClickFolder={(e, data) =>
                handleFolderClick(e, data, folderPattern, dispatch)
            }
            onToggle={(e, data) =>
                handleToggle(e, data, folderPattern, dispatch)
            }
        />
    );
};

export default FolderList;

import React, { useEffect, useState } from "react";

// plugins
import { List } from "semantic-ui-react";
import { useDispatch, useSelector } from "react-redux";

//
import { isEmpty } from "../../../../../../../commons";
import { BtnName, TypeName } from "../../../../Utils/compoConfig";
import CustomButton from "../../CustomButton/CustomButton";
import accMngAct from "../../../../../../../reduxStore/accManage/accManageAction";
import textConfig from "../../../../Utils/textConifg";
import { getUsers } from "../../../../../../../api/firebase/realtimeDatabase";
import role from "../../../../../../../App-role";

function MemberAddCnt() {
    const dispatch = useDispatch();
    const state = useSelector(tmpState => tmpState.accMng);
    const { groupData } = state;
    const [userList, setUserList] = useState([]);
    const tmpGDUserID = groupData.members
        .filter(el => el.uid)
        .map(el => el.uid);
    const showUsers = userList.filter(
        ({ uid }) => tmpGDUserID.indexOf(uid) === -1
    );

    const handleGetUser = async () => {
        const usersData = await getUsers();
        const filteredUser = usersData.filter(
            user =>
                user.role !== role.anonymous &&
                Object.keys(role).includes(user.role)
        );
        setUserList(filteredUser);
    };

    useEffect(() => {
        handleGetUser();
    }, []);

    return (
        <List divided verticalAlign="middle">
            {/* eslint-disable-next-line react/destructuring-assignment */}
            {!isEmpty(showUsers) && !isEmpty(userList) ? (
                showUsers.map(({ displayName, email, uid }) => (
                    <List.Item key={`${displayName}(${email})`}>
                        <List.Content floated="left">
                            <List.Icon name="users" />
                            {`${displayName}(${email})`}
                        </List.Content>
                        <List.Content floated="right">
                            {BtnName.filter(
                                ({ typeName }) =>
                                    typeName === TypeName.GroupMemberAddAPerson
                            ).map(btnInfo => (
                                <CustomButton
                                    compoInfo={btnInfo}
                                    key={btnInfo.typeName}
                                    onClick={() => {
                                        const personInfo = {
                                            displayName,
                                            email,
                                            uid
                                        };
                                        const tmpGData = JSON.parse(
                                            JSON.stringify(groupData)
                                        );
                                        tmpGData.members.push(personInfo);
                                        dispatch({
                                            type: accMngAct.SET_GROUPDATA,
                                            payload: tmpGData
                                        });
                                    }}
                                />
                            ))}
                        </List.Content>
                    </List.Item>
                ))
            ) : (
                <List.Item>
                    <List.Content>{textConfig.NO_MOREMEMBER}</List.Content>
                </List.Item>
            )}
        </List>
    );
}

export default MemberAddCnt;

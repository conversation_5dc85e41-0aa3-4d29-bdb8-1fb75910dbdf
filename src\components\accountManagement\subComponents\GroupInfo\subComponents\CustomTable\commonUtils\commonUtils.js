import accMngAct from "../../../../../../../reduxStore/accManage/accManageAction";

// click table header checkbox
const clickTitleChB = (detailInfo, dispatch) => {
    const { tableName, tableNames, data, tableSelectPool } = detailInfo;
    let resultArr = [];
    if (tableName === tableNames.groups) {
        if (tableSelectPool.groups.length !== data.length) {
            // push all Id
            resultArr = data.map(el => el.id);
        }
        dispatch({
            type: accMngAct.SET_TABLESELECTPOOL,
            payload: { ...tableSelectPool, groups: resultArr }
        });
    } else if (tableName === tableNames.users) {
        if (tableSelectPool.users.length !== data.length) {
            // push all Id
            resultArr = data.map(el => el.uid);
        }
        dispatch({
            type: accMngAct.SET_TABLESELECTPOOL,
            payload: { ...tableSelectPool, users: resultArr }
        });
    }
};

// click table child checkbox
const clickChildChB = (infoObj, dispatch) => {
    const { rowId, tableSelectPool, poolName } = infoObj;
    let tmpArr = JSON.parse(JSON.stringify(tableSelectPool[poolName]));
    if (tmpArr.indexOf(rowId) === -1) {
        tmpArr.push(rowId);
    } else {
        tmpArr = tmpArr.filter(idStr => idStr !== rowId);
    }
    dispatch({
        type: accMngAct.SET_TABLESELECTPOOL,
        payload: { ...tableSelectPool, [poolName]: tmpArr }
    });
};

export { clickTitleChB, clickChildChB };

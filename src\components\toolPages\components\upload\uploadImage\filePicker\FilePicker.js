import React, { useContext, useEffect } from "react";

// component

import { useSelector } from "react-redux";
import DropAlertMsg from "../../../../../common/imageCommon/drop/DropAlertMsg";
import Drop from "../../../../../common/imageCommon/drop/Drop";

// store
import { StoreContext } from "../../../../../../store/StoreProvider";

import helpers from "./filePickerHelper";
import Act from "../../../../../../store/actions";

const FilePicker = ({ type }) => {
    const [state, dispatch] = useContext(StoreContext);
    const { common } = state;
    const { pickConfig } = common;
    const {
        files: { folderPattern }
    } = useSelector(state);

    useEffect(
        // 離開此頁面時, 清除 cache
        () => () => {
            dispatch({
                type: Act.UPLOAD_IMAGE,
                payload: []
            });
            dispatch({
                type: Act.CLEAN_PICKER_DROP_MESSAGE,
                payload: []
            });
        },
        []
    );

    return (
        <Drop
            type={type}
            config={pickConfig.uploadPage}
            DropAlertMsg={DropAlertMsg}
            // useSmallDrop
            handleDropCallback={helpers.handleDropCallback(
                state,
                dispatch,
                folderPattern
            )}
        />
    );
};
export default FilePicker;

server {
    listen       80;
    listen  [::]:80;
    server_name  localhost;

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

	location /en {
		# First attempt to serve request as file, then
		# as directory, then fall back to displaying a 404.
		root /usr/share/nginx/html;
		try_files $uri $uri/ /index.html =500;
	}

	location /zh-tw {
		# First attempt to serve request as file, then
		# as directory, then fall back to displaying a 404.
		root /usr/share/nginx/html;
		try_files $uri $uri/ /index.html =500;
	}
    
	location / {
		root /usr/share/nginx/html;
		try_files $uri $uri/ /index.html =500;
	}
}
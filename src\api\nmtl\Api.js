import axios from 'axios';

const baseUrl = process.env.REACT_APP_NMTL_API_NODE;
const langPath = process.env.REACT_APP_NMTL_API_LANG;
const googleApiKey = process.env.REACT_APP_GOOGLE_API_KEY;
const ataiUrl = process.env.REACT_APP_ATAI_URL;
const ataiPdfUrl = process.env.REACT_APP_ATAI_PDF_URL;

if (process.env.NODE_ENV === 'production') {
    // Disable the console log in production.
    // eslint-disable-next-line no-console
    if (!(process.env.REACT_APP_DEBUG === 'true' || process.env.REACT_APP_DEBUG === true))
        console.log = () => { };
}

const url = `${baseUrl}/${langPath}`;
const POST_PREFIX = 'post';

const Api = {
    setAxiosAuth(token) {
        // 不加 prefix "Bearer "
        axios.defaults.headers.Authorization = `${token}`;
        axios.defaults.headers.common.Authorization = `${token}`;
    },
    // baseUrl
    getBaseUrl: `${url}`,
    // health
    getHealth: `${url}/misc/versionInfo/3.0?limit=-1&offset=0`,
    // a
    getAcademicDegreelist: `${url}/misc/academicDegree/list/3.0?limit=-1&offset=0`,
    getAcademicDisciplinelist: `${url}/misc/academicDiscipline/list/3.0?limit=-1&offset=0`,
    getAreaOfTaiwan: `${url}/misc/areaoftaiwan/list/3.0?limit=-1&offset=0`,
    getArticleList: `${url}/misc/article/list/3.0?limit=-1&offset=0`,
    getAuthOrTransInSinglePub: `${url}/backend/singlePub/hasAuthorORhasTranslator/3.0?limit=-1&offset=0&ds={ds}&pubId={pubId}&aORt={aORt}&keyword={keyword}`,
    // dummy
    getArticleList20: `${url}/misc/article/list/3.1?limit=-1&offset=0&ds={ds}`,
    getAwardList: `${url}/misc/award/list/3.0?limit=-1&offset=0`,
    getAward20: '',
    // b
    getSubValue: `${url}/${POST_PREFIX}/backend/subvalue/2.2?limit=-1&offset=0&ds={ds}&type={type}&ids={ids}`,
    // 與2.0差別: 回傳值帶語系
    getSubValue2: `${url}/${POST_PREFIX}/backend/subvalue/2.1?limit=-1&offset=0&ds={ds}&type={type}&ids={ids}`,
    // c
    getCity: `${url}/misc/city/list/3.0?limit=-1&offset=0`,
    getCollection: `${url}/misc/collection/3.0?limit=-1&offset=0`,
    getCollectible: `${url}/misc/collectible/list/3.0?limit=-1&offset=0`,
    getCollectType: `${url}/misc/collecttype/list/3.0?limit=-1&offset=0`,
    getCopyrightStatus: `${url}/misc/copyrightStatus/3.0?limit=-1&offset=0`,
    getCountry: `${url}/misc/country/list/3.0?limit=-1&offset=0`,
    // d
    getDatabase: `${url}/database/download?filename={filename}`,
    backupDatabase: `${url}/database/backup`,
    getBackupStatus: `${url}/database/backup/status`,
    getBackupTaskId: `${url}/database/backup/taskId`,
    getDBBackupList: `${url}/database/backupList`,
    uploadDatabase: `${url}/database/upload?db=[dbFilename]`,
    getDatabaseStatus: `${url}/database/status`,
    putDatabase: `${url}/database/upload`,
    getDynasty: `${url}/misc/dynasty/list/3.0?limit=-1&offset=0`,
    getDerivateWork: `${url}/misc/derivateWork/list/3.0?limit=-1&offset=0`,
    // dummy
    getDerivateWork20: `${url}/misc/derivateWork/list/3.1?limit=-1&offset=0&ds={ds}`,
    getDatasetArticle: `${url}/misc/dataset/article/list/3.0?limit=-1&offset=0&ds={ds}`,
    getDatasetPublication: `${url}/misc/dataset/publication/list/3.0?limit=-1&offset=0&ds={ds}`,
    // e
    getEduOrg: `${url}/misc/eduOrg/list/3.0?limit=-1&offset=0`,
    getEthnicGroup: `${url}/misc/ethnicGroup/list/3.0?limit=-1&offset=0`,
    getExaminationlist: `${url}/misc/examination/list/3.0?limit=-1&offset=0`,
    getEvent: `${url}/misc/event/list/3.0?limit=-1&offset=0&ds={ds}`,
    getEducationList20: '',
    // f
    getFoundationList: `${url}/misc/foundation/list/3.0?limit=-1&offset=0`,
    getFoundationType: `${url}/misc/foundationtype/3.0?limit=-1&offset=0`,
    getFoundation20: '',
    getFictionalCharacter: `${url}/backend/fictionalcharacter/list/all/2.0?limit=-1&offset=0`,
    getFullTxtAnalysis: `${url}/backend/fullTextAnalysis/2.0?limit={limit}&offset=0&keyword={keyword}&ds={dataset}`,
    getFullTxtAnalysisDetail: `${url}/backend/fullTextAnalysis/detail/2.1?limit=-1&offset=0&ids={ids}&ds={dataset}`,
    // g
    getGender: `${url}/misc/gender/3.0?limit=-1&offset=0`,
    getGeneric: `${url}/generic/2.0`,
    // l
    getLanguage: `${url}/backend/language/2.0?limit=-1&offset=0`,
    getLiteraryGenre: `${url}/backend/literaryGenre/list/2.0?limit=-1&offset=0`,
    getLogoImage: `${url}/backend/vrliterature/watermark/2.0?limit=-1&offset=0`,
    getImageList: `${url}/backend/image/list/1.0?limit=-1&offset=0&ds={ds}`,
    getVillageTidbitsList: `${url}/backend/villageTidbits/list/1.0?limit=-1&offset=0&ds={ds}`,
    // getLocationList:           `${url}/location/list/2.0?limit=-1&offset=0`,
    getLocationList: `${url}/misc/location/list/3.2?limit=-1&offset=0`,
    // m
    getMaxId: `${url}/backend/class/maxid/2.0?limit=1&offset=0&classname={classname}&prefix={prefix}`,
    getMSList: `${url}/misc/mainSubject/3.0?limit=-1&offset=0`,
    getMSWorks: `${url}/misc/msworks/list/3.0?limit=-1&offset=0&ds={ds}`,
    getMerge: `${url}/mergeInstances/2.0`,
    // n
    getNanzi: `${url}/backend/nanzi/list/2.0?limit=-1&offset=0&ds={ds}`,
    // o
    getOccupationlist: `${url}/misc/occupation/list/3.0?limit=-1&offset=0`,
    getOrganizationList: `${url}/misc/organization/list/3.1?limit=-1&offset=0`,
    getOrganizationEvent20: '',
    getOtherNameList: `${url}/misc/otherName/list/3.0?limit=-1&offset=0&ds={ds}&ids={ids}`,
    // p
    getPersonlist: `${url}/misc/personlist/3.0?limit=-1&offset=0`,
    getDsPerOrgInfolist: `${url}/backend/perOrgInfo/list/2.0?limit=-1&offset=0&ds={ds}`,
    getPrintBook: `${url}/misc/printBook/3.0?limit=-1&offset=0`,
    getProvince: `${url}/misc/province/list/3.0?limit=-1&offset=0`,
    getPublishing: `${url}/misc/publishing/3.0?limit=-1&offset=0`,
    getPublicationList: `${url}/misc/publication/list/3.0?limit=-1&offset=0`,
    getSameListWithGraph: `${url}/backend/findlabel/list/1.0?limit=-1&offset=0&keyword={keyword}&type={type}`,
    // getDsPublicationList: `${url}/backend/publicationInfo/list/2.0?limit=-1&offset=0&ds={ds}`,
    // dummy
    getPublicationList20: `${url}/misc/publication/list/3.1?limit=-1&offset=0&ds={ds}`,
    getPositionList: `${url}/misc/position/list/3.0?limit=-1&offset=0`,
    getPubByPerName: (ds, ids) =>
        `${url}/backend/dataset/delete/person/update/pubProp/1.0?limit=-1&offset=0&ds=${ds}&ids=${ids}`,
    getProject: `${url}/misc/project/list/3.0?limit=-1&offset=0&ds={ds}`,
    // r
    getRelationOp: `${url}/misc/relationshipop/3.0?limit=-1&offset=0`,
    getRelationEventList20: '',
    getReplaceOrg: '',
    getReservedBnodeId: `${url}/backend/bnodeid/2.2?limit=-1&offset=0&random={random}`,
    getReservedNewId: `${url}/newInstance/2.0`,
    getReservedCurId: `${url}/backend/curid/2.2?limit=-1&offset=0&prefix={prefix}`,
    // t
    getTaiwanPeriod: `${url}/misc/taiwanperiod/3.0?limit=-1&offset=0`,
    getTownship: `${url}/misc/township/list/3.0?limit=-1&offset=0`,
    getTransBookByOri: (ds, ids) =>
        `${url}/backend/dataset/delete/translationBook/1.0?limit=-1&offset=0&ds=${ds}&ids=${ids}`,
    getTlvmPeriodList: `${url}/misc/tlvmPeriod/list/3.0?limit=-1&offset=0`,
    // dummy
    getTlvmPeriod: `${url}/misc/tlvmperiod/addlist/3.0?limit=-1&offset=0`,
    // s
    getSpecialty: `${url}/misc/specialty/3.0?limit=-1&offset=0`,
    getSpecialty20: '',
    getSocialProcedureList: `${url}/misc/socialProcedure/list/3.0?limit=-1&offset=0`,
    getSystemInfo: `${url}/systemInfo`,
    // single
    SingleAcademicDegree: `${url}/generic/2.0`,
    SingleAcademicDiscipline: `${url}/generic/2.0`,
    SingleArticle: `${url}/generic/2.0`,
    SingleCollectible: `${url}/generic/2.0`,
    // SingleAward: `${url}/singleAward/1.0`,
    SingleEducation: `${url}/generic/2.0`,
    SingleEvent: `${url}/generic/2.0`,
    SingleExamination: `${url}/generic/2.0`,
    SingleFoundation: `${url}/generic/2.0`,
    SingleLocation: `${url}/generic/2.0`,
    SingleNanzi: `${url}/generic/2.0`,
    SingleOccupation: `${url}/generic/2.0`,
    SinglePerson: `${url}/generic/2.0`,
    SinglePosition: `${url}/generic/2.0`,
    SingleProject: `${url}/generic/2.0`,
    SinglePublication: `${url}/generic/2.0`,
    SingleRelationEvent: `${url}/generic/2.0`,
    SingleOrganization: `${url}/generic/2.0`,
    SingleSocialProcedure: `${url}/generic/2.0`,
    SingleSpecialty: `${url}/generic/2.0`,
    SingleTlvmPeriod: `${url}/generic/2.0`,
    // update option
    getNewOption: `${url}/backend/find/id/1.0?limit=-1&offset=0&label={label}&class={class}`,
    // frontend-setting
    getCityLiteratureList: `${url}/misc/literature/location/3.0?limit=-1&offset=0&region=AOT7`,
    getTltcliftPageData: `${url}/backend/frontEdit/tltc/liftbooks/1.0?limit=-1&offset=0`,
    getTltcpeakPageData: `${url}/backend/frontEdit/tltc/peakMonos/1.0?limit=-1&offset=0`,
    getTltcvillagesPageData: `${url}/backend/frontEdit/tltc/villages/1.0?limit=-1&offset=0`,
    getTltcRellinksType: `${url}/backend/frontEdit/tltc/rellinks/type/1.0?limit=-1&offset=0`,
    getTltcRellinksList: `${url}/backend/frontEdit/tltc/rellinks/list/1.0?limit=-1&offset=0&type={type}`,
    getTltcRellinksListOrder: `${url}/backend/frontEdit/tltc/rellinks/listMaxOrder/1.0?limit=-1&offset=0&type={type}`,
    getNewsList: `${url}/backend/frontEdit/news/1.0?limit=-1&offset=0&subject={subject}`,
    getNewsInfo: `${url}/backend/frontEdit/news/info/1.0?limit=-1&offset=0&newsId={newsId}`,
    getPeakMonosList: `${url}/backend/frontEdit/tltc/peakMonos/list/1.0?limit=-1&offset=0`,
    getVillagesList: `${url}/backend/frontEdit/tltc/villages/list/1.0?limit=-1&offset=0`,
    getVillagesListInfo: `${url}/backend/frontEdit/tltc/villages/listInfo/1.0?limit=-1&offset=0`,
    getPeakMonosInfo: `${url}/backend/frontEdit/tltc/peakMonos/info/1.1?limit=-1&offset=0&id={peakMonoId}`,
    getVillagesInfo: `${url}/backend/frontEdit/tltc/villages/info/1.0?limit=-1&offset=0&id={id}`,
    getPeakMonoChapterList: `${url}/backend/peakChapter/list/1.1?limit=-1&offset=0&pekId={peakMonoId}`,
    getVillagesActivityInfoList: `${url}/backend/villages/activityInfo/list/1.0?limit=-1&offset=0&id={id}`,
    getPeakMonoChapterContent: `${url}/backend/peakChapter/content/1.0?limit=-1&offset=0&pchId={pchId}`,
    getPeakMonoChapterNewContent: `${url}/backend/peakChapter/newContent/1.0?limit=-1&offset=0&pchId={pchId}`,
    getWriterList: `${url}/backend/peakChapter/writerList/1.1?limit=-1&offset=0`,
    getPublisherList: `${url}/backend/peakChapter/publisherList/1.0?limit=-1&offset=0`,
    getTLACategoryList: `${url}/backend/peakChapter/TLACategory/1.1?limit=-1&offset=0`,
    getTLAAwardsList: `${url}/backend/peakChapter/TLAAwards/1.1?limit=-1&offset=0`,
    getImageName: `${url}/backend/peakChapter/getImageName/1.0?limit=-1&offset=0&id={id}`,
    getImageId: `${url}/backend/peakChapter/getImageId/1.0?limit=-1&offset=0&name={name}&ds={ds}`,

    // words(全文字詞分析)
    getWords: `${baseUrl}/words/generic/2.0?type={type}&reserved={reserved}`,
    postWords: `${baseUrl}/words/generic/2.0`,
    putWords: `${baseUrl}/words/generic/2.0`,

    // mail
    mail: `${baseUrl}/mail`,

    // google API: get target location
    // getTargetLocation: `https://maps.googleapis.com/maps/api/geocode/json?latlng={latlng}&key=${googleApiKey}&language=zh-TW`,
    // import
    importData: `${baseUrl}/import/file/{graph}?type=nmtlImport&sheetName={sheetName}`,

    // Only for specialContentView
    getScvPersonList: `${url}/misc/personlist/3.1?limit=-1&offset=0`,
    getScvLocationList: `${url}/misc/location/list/3.1?limit=-1&offset=0&ds=tltc`,
    getScvOrganizationList: `${url}/misc/organization/list/3.0?limit=-1&offset=0&ds=tltc`,

    getSameListWithGraphForPerOrg: `${url}/backend/findlabel/list/1.2?limit=-1&offset=0&keyword={keyword}&type={type}`,
    getSameListWithGraphForLoc: `${url}/backend/findlocationlabel/list/1.0?limit=-1&offset=0&keyword={keyword}&type={type}`,
    getSameListWithGraphForAll: `${url}/backend/find/existedlabel/list/1.0?limit=-1&offset=0&keyword={keyword}&type={type}`,
    getSameListWithGraphByIdForAll: `${url}/backend/find/existedid/list/1.0?limit=-1&offset=0&id={id}&type={type}`,
    getCopy: `${url}/copyInstances/2.0`,
    getCopyOnlyId: `${url}/copyInstancesOnlyId/2.0`,
    isDataInGraph: `${url}/backend/existed/in/graph/1.0?limit=-1&offset=0&ds={ds}&id={id}`,

    // ataiManagement
    getAtaiData: `${ataiUrl}/fileshow`,
    uploadAtaiData: `${ataiUrl}/upload`,
    deleteAtaiData: `${ataiUrl}/delete`,
    updateAtaiData: `${ataiUrl}/update`,
    ataiUpdateLock: `${ataiUrl}/check_lock`,
    openAtaiDataPdf: ataiPdfUrl,
    // openAtaiDataPdf: "https://fs2.daoyidh.com/static/file/upload/atai-backend"

    // 取得權威檔未整合的資料
    getUnintegratedData: `${url}/backend/dl_basicinfo/unintegrated/3.2?limit=-1&offset=0&ids={ids}`,

    // ================================================
    // backend APIs
    // ================================================
    getPersonDsAllIdList: `${url}/backend/ds/person/list/all_id/3.0?ds={ds}&limit=-1&offset=0`,

    // 數據資料/數據統計/全站統計
    getMainSubjectList: `${url}/backend/mainSubject/list/3.0?limit=-1&offset=0`,
    getVrliteraryList: `${url}/backend/vrliterary/region/3.0?limit=-1&offset=0&region=AOT7`,
    getPublicationFltStat: `${url}/backend/stats/publicationFlt/3.0?limit=-1&offset=0`,
    getArticleFltStat: `${url}/backend/stats/articleFlt/3.0?limit=-1&offset=0`,

    // 數據資料/數據統計/筆數統計
    getPersonDsList: `${url}/backend/ds/person/list/3.0?ds={ds}&limit=-1&offset=0`,
    getEducationDsList: `${url}/backend/ds/education/list/3.0?ds={ds}&limit=-1&offset=0`,
    getOrganizationDsList: `${url}/backend/ds/organization/list/3.0?ds={ds}&limit=-1&offset=0`,
    getSpecialtyDsList: `${url}/backend/ds/specialty/list/3.0?ds={ds}&limit=-1&offset=0`,
    getRelationshipDsList: `${url}/backend/ds/relationship/list/3.0?ds={ds}&limit=-1&offset=0`,
    getEventDsList: `${url}/backend/ds/event/list/3.0?ds={ds}&limit=-1&offset=0`,
    getFoundationDsList: `${url}/backend/ds/foundation/list/3.0?ds={ds}&limit=-1&offset=0`,
    getDerivateworkDsList: `${url}/backend/ds/derivatework/list/3.0?ds={ds}&limit=-1&offset=0`,
    getPublicationDsList: `${url}/backend/ds/publication/list/3.0?ds={ds}&limit=-1&offset=0`,
    getArticleDsList: `${url}/backend/ds/article/list/3.0?ds={ds}&limit=-1&offset=0`,
    getAwardDsList: `${url}/backend/ds/award/list/3.0?ds={ds}&limit=-1&offset=0`,
    getProjectDsList: `${url}/backend/ds/project/list/3.0?ds={ds}&limit=-1&offset=0`,
    getLocationDsList: `${url}/backend/ds/location/list/3.0?ds={ds}&limit=-1&offset=0`,
    getReplacedorgDsList: `${url}/backend/ds/replacedorg/list/3.0?ds={ds}&limit=-1&offset=0`,
    getOrganizationinfoDsList: `${url}/backend/ds/organizationinfo/list/3.0?ds={ds}&limit=-1&offset=0`,
    getCollectibleDsList: `${url}/backend/ds/collectible/list/3.0?ds={ds}&limit=-1&offset=0`,
    getNanziDsList: `${url}/backend/ds/nanzi/list/3.0?ds={ds}&limit=-1&offset=0`,
    getTlvmperiodDsList: `${url}/backend/ds/tlvmperiod/list/3.0?ds={ds}&limit=-1&offset=0`,

    // Search API
    getBasicInfoSearchApi: `${url}/backend/dl_basicinfo/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getEducationSearchApi: `${url}/backend/dl_education/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getOrganizationSearchApi: `${url}/backend/dl_organization/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getSpecialtySearchApi: `${url}/backend/dl_specialty/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getRelationshipSearchApi: `${url}/backend/dl_relationship/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getEventSearchApi: `${url}/backend/dl_event/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getFoundationSearchApi: `${url}/backend/dl_foundation/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getDerivateWorkSearchApi: `${url}/backend/dl_derivatework/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getPublicationSearchApi: `${url}/backend/dl_publication/search/3.1?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getArticleSearchApi: `${url}/backend/dl_article/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getAwardSearchApi: `${url}/backend/dl_award/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getProjectSearchApi: `${url}/backend/dl_project/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getLocationSearchApi: `${url}/backend/dl_location/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getReplaceOrgSearchApi: `${url}/backend/dl_replacedorg/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getOrganizationInfoSearchApi: `${url}/backend/dl_organizationInfo/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getCollectibleSearchApi: `${url}/backend/dl_collectible/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getNanziSearchApi: `${url}/backend/dl_nanzi/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getTlvmPeriodSearchApi: `${url}/backend/dl_tlvmperiod/search/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,

    // Authority
    getAuthorityContentReadPath: `${url}/backend/authority/dl_basicinfo/3.2?limit=-1&offset=0&ids={ids}`,
    getAuthorityNeedProcessPersonIds: `${url}/backend/authority/need_process/person/list/3.0?ds={ds}&limit=-1&offset=0`,
    getAuthoritySearchPersonIds: `${url}/backend/authority/search/person/list/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,
    getAuthorityNeedProcessSearchPersonIds: `${url}/backend/authority/need_process/search/person/list/3.0?limit=-1&offset=0&ds={ds}&keyword={keyword}`,

    // Tool Pages
    getEventImageLinkList: (ds) =>
        `${url}/backend/ds/event/image-link/list/3.0?ds=${ds}&limit=-1&offset=0`,
};

export default Api;

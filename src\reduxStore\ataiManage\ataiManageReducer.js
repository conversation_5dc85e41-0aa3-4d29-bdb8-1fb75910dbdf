import menuItem from "../../components/ataiDataManagement/components/MenuBar/menuItem";
import ataiMngAct from "./ataiManageAction";

const INITIAL_STATE = {
    ataiData: [],
    activeItemAtai: menuItem[0],
    totalPage: 1,
    currentPage: 1,
    itemPerPage: 10,
    isUpdating: false,
    updateTime: "尚未"
};

const ataiManageReducer = (state = INITIAL_STATE, action) => {
    switch (action.type) {
        case ataiMngAct.SET_ATAI_DATA:
            return {
                ...state,
                ataiData: action.payload
            };
        case ataiMngAct.SET_ACTIVE_ITEM_ATAI:
            return {
                ...state,
                activeItemAtai: action.payload
            };
        case ataiMngAct.SET_TOTAL_PAGE:
            return {
                ...state,
                totalPage: action.payload
            };
        case ataiMngAct.SET_CURRENT_PAGE:
            return {
                ...state,
                currentPage: action.payload
            };
        case ataiMngAct.SET_ITEM_PER_PAGE:
            return {
                ...state,
                itemPerPage: action.payload
            };
        case ataiMngAct.SET_IS_UPDATING:
            return {
                ...state,
                isUpdating: action.payload
            };
        case ataiMngAct.SET_UPDATE_TIME:
            return {
                ...state,
                updateTime: action.payload
            };
        default:
            return state;
    }
};

export default ataiManageReducer;

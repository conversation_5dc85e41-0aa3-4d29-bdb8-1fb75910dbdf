import { checkRes } from "../config/config";
import { isEmpty } from "../../../../../../../commons";

const checkHeaderOrder = (headerArr, fbHeader) => {
    const tmpHeaderArr = headerArr
        .map(el => el.split("\n")[1])
        .filter(el => el);

    if (isEmpty(tmpHeaderArr)) return checkRes.failed;

    const tmpFbHeader = fbHeader.filter(el => el.id !== "lastModified");
    const check = tmpHeaderArr.every(
        (elStr, idx) => elStr === tmpFbHeader[idx].id
    );

    return check ? checkRes.success : checkRes.failed;
};

export default checkHeaderOrder;

// firebase
import { writeNMTLDocRootPath } from "../../../api/firebase/cloudFirestore/nmtlDoc";
import { getSingleLayerCollectionWhere } from "../../../api/firebase/cloudFirestore";

// utils
import { isEmpty } from "../../../commons";

// config
import fsConfig from "../../../config/config-firestore";
import RPAct from "../reportIssueAction";
import fbConfig from "./fbConfig";
import { statusConfig } from "./statusConfig";

const { FIRESTORE_REPORT_ISSUE } = fsConfig;

export const setNewAllData = (dispatch, value) => {
    dispatch({
        type: RPAct.SET_RPNEWAllDATA,
        payload: value
    });
};

const checkDataDiff = (srcObj, dstObj) => {
    const compareCol = [fbConfig.status, fbConfig.response];

    return compareCol.some(colKey => {
        if (Object.hasOwn(srcObj, colKey) && Object.hasOwn(dstObj, colKey)) {
            return srcObj[colKey] !== dstObj[colKey];
        }
        return Boolean(srcObj[colKey]) !== Boolean(dstObj[colKey]);
    });
};

// check update button disable or not
export const checkDataChange = (allData, newAllData) => {
    // eslint-disable-next-line no-restricted-syntax
    for (const oriData of allData) {
        const { id } = oriData;
        const findNewObj = newAllData.find(newData => newData.id === id);
        if (findNewObj) {
            const check = checkDataDiff(oriData, findNewObj);
            if (check) {
                return !check;
            }
        }
    }
    return true;
};

const getStatusText = statusKey =>
    statusConfig.find(el => el.status === statusKey).text;

export const createHistoryDesc = (srcData, dstData, displayName) => {
    let hisStr = "";
    Object.keys(fbConfig).forEach(key => {
        if (key === fbConfig.status) {
            if (!isEmpty(srcData) && srcData[key] !== dstData[key]) {
                hisStr += `處理狀態: ${getStatusText(
                    srcData[key]
                )}改成${getStatusText(dstData[key])}\n`;
            }
        } else if (key === fbConfig.response) {
            if (!isEmpty(srcData) && srcData[key] !== dstData[key]) {
                hisStr += `處理摘要: 從${srcData[key] || "尚未回應"}改成${
                    dstData[key]
                }\n`;
            }
        } else if (key === fbConfig.staffEmail) {
            // 回報Email時，srcData是empty object
            if (srcData[key] !== dstData[key]) {
                hisStr += `信件已發送到以下信箱:\n`;
                dstData[key].forEach((emailStr, idx) => {
                    hisStr += `${idx + 1}. ${emailStr}\n`;
                });
            }
        }
    });

    if (hisStr) {
        hisStr += `處理人員: ${displayName}。\n`;
    }
    return hisStr;
};

// 儲存單一條問題回報資料
export const saveSingleRPIssue = (docName, dataObj) =>
    new Promise((resolve, reject) => {
        if (!docName || isEmpty(dataObj)) resolve({ status: "Fail" });
        const tmpDataObj = JSON.parse(JSON.stringify(dataObj));
        delete tmpDataObj.id;

        writeNMTLDocRootPath(
            FIRESTORE_REPORT_ISSUE,
            docName,
            tmpDataObj,
            (emptyParam, res) => {
                res.then(() => resolve({ status: "OK" })).catch(err =>
                    reject(err)
                );
            }
        );
    });

// 儲存所有資料內容
export const saveAllData = (allData, newAllData, displayName) => {
    const fbDocID = []; // 紀錄資料有變的issue ID
    const tmpAllData = JSON.parse(JSON.stringify(newAllData));
    allData.forEach(oriData => {
        const { id } = oriData;
        const findNewData = tmpAllData.find(newData => newData.id === id);
        if (checkDataDiff(oriData, findNewData)) {
            fbDocID.push(findNewData.id);
            const tmpHisObj = {
                [fbConfig.historyProp.time]: Date.now(),
                [fbConfig.historyProp.status]: createHistoryDesc(
                    oriData,
                    findNewData,
                    displayName
                )
            };
            findNewData[fbConfig.history].push(tmpHisObj);
            findNewData[fbConfig.staff] = displayName;
        }
    });

    return new Promise((resolve, reject) => {
        fbDocID.forEach(docName => {
            const dataObj = tmpAllData.find(el => el.id === docName);
            saveSingleRPIssue(docName, dataObj)
                .then(res => resolve(res))
                .catch(err => reject(err));
        });
    });
};

export const getAllData = (rpSubject, dispatch) => {
    getSingleLayerCollectionWhere(FIRESTORE_REPORT_ISSUE, [
        fbConfig.subject,
        "==",
        rpSubject
    ])
        .then(res => {
            // 檢查有沒有該主題網站的問題回報
            const checkErr = !Array.isArray(res) && Object.hasOwn(res, "error");

            const data = !checkErr ? res : [];
            dispatch({
                type: RPAct.SET_RPAllDATA,
                payload: data
            });
            setNewAllData(dispatch, data);
        })
        .catch(err => {
            console.log("err ", err);
        });
};

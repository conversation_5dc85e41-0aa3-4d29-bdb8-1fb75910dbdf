// import sheet header fixme: 之後要改成從firebase取的欄位id
const impSheetHeader = {
    /** [sheetName]: { [header id]: [header id value] } */
    Person: {
        srcId: "srcId",
        label_Person: "label_Person",
        hasTranslationLanguage: "hasTranslationLanguage",
        authorOrTranslator: "authorOrTranslator",
        birthName: "birthName",
        penName: "penName",
        otherName: "otherName",
        introduction: "introduction",
        externalLinks: "externalLinks",
        imageURL_hasURL: "imageURL_hasURL",
        hasCopyrightStatus_hasURL: "hasCopyrightStatus_hasURL",
        comment: "comment",
        pictureDisplay: "pictureDisplay"
    },
    Publication: {
        isTranslationBookOf: "isTranslationBookOf",
        label_Publication: "label_Publication",
        hasAuthor: "hasAuthor",
        srcId: "srcId",
        hasLanguageOfWorkOrName: "hasLanguageOfWorkOrName",
        translationBookName: "translationBookName",
        authorName: "authorName",
        hasTranslator: "hasTranslator",
        translatorName: "translatorName",
        hasEditor: "hasEditor",
        hasPublisher: "hasPublisher",
        hasInceptionDate: "hasInceptionDate",
        srcId_hasPlaceOfPublication: "srcId_hasPlaceOfPublication",
        LiteraryGenre: "LiteraryGenre",
        totalPage: "totalPage",
        ISBN: "ISBN",
        references: "references",
        introduction: "introduction",
        tableOfContents: "tableOfContents",
        fileAvailableAt: "fileAvailableAt",
        hasFullWorkCopyright: "hasFullWorkCopyright",
        imageURL_hasURL: "imageURL_hasURL",
        hasCopyrightStatus_hasURL: "hasCopyrightStatus_hasURL",
        comment: "comment",
        lift: "lift",
        peak: "peak",
        friendlyLink: "friendlyLink",
        geoLatitude_hasPlaceOfPublication: "geoLatitude_hasPlaceOfPublication",
        geoLongitude_hasPlaceOfPublication: "geoLongitude_hasPlaceOfPublication"
    }
};

export default impSheetHeader;

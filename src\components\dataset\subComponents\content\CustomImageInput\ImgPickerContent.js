import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

// semantic ui
import { Button, Container, Grid, Segment } from "semantic-ui-react";

// components
import CurrentImage from "./CurrentImage";
import FolderMenu from "./uploadImage/FolderMenu";
import CurrentFolder from "./uploadImage/curFolder";
import FilePicker from "./uploadImage/FilePicker";

// utils
import { uuidv4 } from "../../../../../commons/utility";
import { getImgFolderPattern } from "../../../../common/imageCommon/FolderList/folderListHelper";

// config
import uploadConfig from "../../../../toolPages/components/upload/uploadConfig";
import FileAct from "../../../../../reduxStore/file/fileAction";

/**
 * fixme: split code from ImagePicker，之後再整合到ImagePicker裡面，目前先用在SpecialImportButton裡面(Bennis 20231018)
 * */
function ImgPickerContent() {
    const {
        files: { defaultValue, selectFile }
    } = useSelector(state => state);
    const dispatchRedux = useDispatch();

    const [pickMethod, setPickMethod] = useState("store"); // ENUM("store", "upload")

    useEffect(() => {
        dispatchRedux({
            type: FileAct.SET_DEFAULT_VALUE,
            payload: ""
        });
        dispatchRedux({
            type: FileAct.SELECT_FILE,
            payload: ""
        });
        getImgFolderPattern(dispatchRedux, uploadConfig.ApiGetImages);
        return () => {
            dispatchRedux({ type: FileAct.INIT_FOLDERPATTERN });
            dispatchRedux({
                type: FileAct.FOLDER_FILES_URL,
                payload: []
            });
            dispatchRedux({ type: FileAct.INIT_SELECT_FOLDER });
        };
    }, []);

    const selectMode = {
        store: {
            name: "store",
            label: "從圖片庫中挑選",
            onClick: () => setPickMethod("store"),
            leftComp: <FolderMenu />,
            rightComp: <CurrentFolder type={uploadConfig.image} />
        },
        upload: {
            name: "upload",
            label: "上傳圖片",
            onClick: () => setPickMethod("upload"),
            leftComp: <FolderMenu />,
            rightComp: <FilePicker type={uploadConfig.image} />
        }
    };

    // 刪除連結
    const handleClearLink = () => {
        dispatchRedux({
            type: FileAct.SET_DEFAULT_VALUE,
            payload: ""
        });
    };

    return (
        <Container>
            {/* display current image */}
            <CurrentImage
                defaultValue={defaultValue}
                currentValue={selectFile}
            />
            {/* switcher: 使用圖片庫 or 上傳圖片 */}
            <Button.Group>
                {Object.values(selectMode).map(btn => (
                    <Button
                        key={uuidv4()}
                        positive={pickMethod === btn.name}
                        onClick={() => btn.onClick()}
                    >
                        {btn.label}
                    </Button>
                ))}
            </Button.Group>
            <Button style={{ marginLeft: "20px" }} onClick={handleClearLink}>
                刪除圖片連結
            </Button>
            <Grid textAlign="center" celled stackable>
                <Grid.Row>
                    <Grid.Column width={3}>
                        <Segment basic compact>
                            {/* left side */}
                            {pickMethod in selectMode &&
                                selectMode[pickMethod].leftComp}
                        </Segment>
                    </Grid.Column>
                    <Grid.Column width={13}>
                        {/* right side */}
                        {pickMethod in selectMode &&
                            selectMode[pickMethod].rightComp}
                    </Grid.Column>
                </Grid.Row>
            </Grid>
        </Container>
    );
}

export default ImgPickerContent;

import { sortedByStroke } from 'twchar';
import { addSuffix, getApi<PERSON>y<PERSON>llField, getExportHeaderRemove } from '../../../api/nmtl/ApiField';
import {
    // multiProperties,
    splitMultiValues,
    NOT_SHOWN_IN_TABLE,
    NOT_SHOWN_IN_ADD,
    NOT_SHOWN_IN_EDIT,
    LABEL_PREFIX,
    DATE_EVENTS,
    // LABEL_EVENT,
    SubValues,
    IMAGE_URL,
    IMAGE_KEY,
    bindSP,
    CREATE_ID,
    SET_TIMEOUT,
    REMOVE_FROM_HEADER,
    PEICES_INFO,
    LOCATION_KEY,
    LOCATION_LABEL,
    TMP_LOCATION_KEY,
    LOCATION_TYPE,
} from './sheetCrudHelper';
import Api from '../../../api/nmtl/Api';
import { readNmtlData } from '../../../api/nmtl';
import { classPrefix } from '../../../api/nmtl/classPrefix';
import { uuidv4 } from '../../../commons/utility';
import arrayMerge from '../../../commons/arrayMerge';
import { convertToExport, datasetCoverToExport } from '../../../commons/convertToExport';
import { isEmpty } from '../../../commons';
import { sortedMethod, sortStroke } from '../../dataset/subComponents/content/ContentConfig';
import { graphOptions, literaryOptions, SHEET_TYPES } from './config';

// 圖片 CRUD 指到 fs-root ("https://fs-root.daoyidh.com")
const fsRootUrl = process.env.REACT_APP_FILE_SERVER_URL;

export const convertToEditState = (header, headerFields, rowData) => {
    const allCreateState = {};

    header.forEach((h) => {
        const { id: headerId } = h;
        const apiName = getApiByAllField(headerId);

        // Those values should be string
        if (!apiName || apiName.length === 0) {
            return;
        }

        let options = [];
        if (apiName && apiName.length > 0) {
            apiName.forEach((api) => {
                if (headerFields[api] && headerFields[api].length > 0) {
                    options = options.concat(headerFields[api]);
                }
            });
        }

        let value = null;
        const contentVal = rowData[headerId];
        if (contentVal && options) {
            const ids = splitMultiValues(headerId, contentVal);
            value = options.filter((op) => ids.indexOf(op.id) > -1);
        }

        allCreateState[headerId] = {
            isLoading: false,
            options: apiName ? options : null,
            // dropdown 為 value，input 為 contentVal
            value: value || contentVal || undefined,
        };
    });

    return allCreateState;
};

export const convertToCreateState = (header, headerFields) => {
    const allCreateState = {};

    header.forEach((h) => {
        const { id: headerId } = h;

        // label_XXX 皆為字串
        if (headerId.startsWith(LABEL_PREFIX)) {
            allCreateState[headerId] = { [CREATE_ID]: null };
            return;
        }

        const apiName = getApiByAllField(headerId);

        let options = [];
        if (apiName && apiName.length > 0) {
            apiName.forEach((api) => {
                if (headerFields[api] && headerFields[api].length > 0) {
                    options = options.concat(headerFields[api]);
                }
            });
        }

        allCreateState[headerId] = {
            isLoading: false,
            options: apiName ? options : null,
            value: undefined,
        };
    });

    return allCreateState;
};

export const multiConvertToCreateState = (content, header, headerFields) => {
    const allMultiCreateState = [];
    const count = content.length;

    for (let i = 0; i < count; i += 1) {
        const allCreateState = {};

        header.forEach((h) => {
            const { id: headerId } = h;
            // srcId 為特殊的 id，不用設定多個
            if (headerId === 'srcId') {
                allCreateState[headerId] = content[i][headerId];
                return;
            }

            const apiName = getApiByAllField(headerId);

            if (
                (!apiName || apiName.length === 0) &&
                headerId !== 'graph' &&
                headerId !== 'literary' &&
                headerId !== 'Dataset'
            ) {
                return;
            }

            let options = [];
            if (apiName && apiName.length > 0) {
                apiName.forEach((api) => {
                    if (headerFields[api] && headerFields[api].length > 0) {
                        options = options.concat(headerFields[api]);
                    }
                });
            }

            // graph、literary、Dataset 有指定的options所以特殊處理
            if (headerId === 'graph' || headerId === 'literary' || headerId === 'Dataset') {
                allCreateState[headerId] = {
                    isLoading: false,
                    options: headerId === 'literary' ? literaryOptions : graphOptions,
                    value: content[i][headerId],
                    isOption: true,
                };
            } else {
                // 這個 header 不需要轉換
                allCreateState[headerId] = {
                    isLoading: false,
                    options: apiName && apiName.length > 0 ? options : null,
                    value: content[i][headerId],
                    isOption: true,
                };
            }
        });

        allMultiCreateState.push(allCreateState);
    }
    return allMultiCreateState;
};

export const allConvertToCreateState = (content, header, headerFields) => {
    const allCreateState = [];
    const count = content.length;

    for (let i = 0; i < count; i += 1) {
        const createState = {};

        header.forEach((h) => {
            const { id: headerId } = h;
            const apiName = getApiByAllField(headerId);

            if (
                (!apiName || apiName.length === 0) &&
                headerId !== 'graph' &&
                headerId !== 'literary' &&
                headerId !== 'Dataset'
            ) {
                if (isEmpty(content[i][headerId])) return;

                createState[headerId] = content[i][headerId];
                return;
            }

            let options = [];
            if (apiName && apiName.length > 0) {
                apiName.forEach((api) => {
                    if (headerFields[api] && headerFields[api].length > 0) {
                        options = options.concat(headerFields[api]);
                    }
                });
            }

            // graph、literary、Dataset 有指定的options所以特殊處理
            if (headerId === 'graph' || headerId === 'literary' || headerId === 'Dataset') {
                createState[headerId] = {
                    isLoading: false,
                    options: headerId === 'literary' ? literaryOptions : graphOptions,
                    value: content[i][headerId],
                    isOption: true,
                };
            } else {
                // 這個 header 不需要轉換
                createState[headerId] = {
                    isLoading: false,
                    options: apiName && apiName.length > 0 ? options : null,
                    value: content[i][headerId],
                    isOption: true,
                };
            }
        });

        allCreateState.push(createState);
    }
    return allCreateState;
};

export const specialConvertToOption = (header, headerFields) => {
    const apiName = getApiByAllField(header);

    if (!apiName || apiName.length === 0) {
        return [];
    }

    let options = [];
    if (apiName && apiName.length > 0) {
        apiName.forEach((api) => {
            if (headerFields[api] && headerFields[api].length > 0) {
                options = options.concat(headerFields[api]);
            }
        });
    }

    return options;
};

// changed:
// Object { 0:{ label_Person:{ "0": "丁允恭@zh", "1": "11111"}...}, 1: {}, 2: {},
// 3: {}, 4: {}, 5: {}, 6: {}, 7: {}, 8: {}, 9: {} }
// Output: newRows, updatedIds
// updatedIds: [0, 2, 4]
// newRows: { 0:{ label_Person:{ "0": "丁允恭@zh", "1": "11111"}...}, 1: {...} }
export const organizeChanged = (rows, changed) => {
    const updatedIds = changed
        ? Object.keys(changed).filter((k) => Object.keys(changed[k]).length > 0)
        : [];
    const newRows = {};

    updatedIds.forEach((rowId) => {
        // 先把原本的 row 複製起來，必須全部複製！
        // 因為在某些狀況，需要其它值配合，即使其它值沒有改變。ex: imagePath, imageName
        newRows[rowId] = JSON.parse(JSON.stringify(rows[rowId]));

        // 把改變的值填進去
        Object.keys(changed[rowId]).forEach((header) => {
            // 只有改變的 header 會出現在這裡
            // header: label_Person, hasBirthDate, courtesyName ...
            newRows[rowId][header] = JSON.parse(JSON.stringify(changed[rowId][header]));
        });
    });
    return { newRows, updatedIds };
};

// convertArrayToObject(
//     [
//         { id: 111, name: 'John', age: 29 },
//         { id: 112, name: 'Sarah', age: 25 },
//     ],
//     'id',
//     'name',
// ),
// return:
// {
//     111:'John',
//     112:'Sarah',
// }
export const convertArrayToObject = (array, key, val) => {
    const initialValue = {};
    return array.reduce(
        (obj, item) => ({
            ...obj,
            [item[key]]: item[val],
        }),
        initialValue,
    );
};

const isDate = (vals) => {
    let res = true;

    // remove date, it is valid.
    if (!vals || vals === '' || vals[0] === '') {
        return true;
    }

    // 可能有 period: 1911-01-02_1922-01-08
    const dates = Object.values(vals)
        .filter((v) => v !== null)
        .map((dt) => dt.split('_'))
        .flat();

    // 如果有多個時間區段
    dates.forEach((val) => {
        if (!val) {
            // null: pass
            return;
        }
        const date = val.split('-');

        // only have year => pass
        if (date.length === 1 && Number(date[0])) {
            return;
        }

        if (date.length !== 3) {
            // should have year, month and day
            res = false;
            return;
        }

        // check is digital
        date.forEach((d) => {
            // this is allowed and it is special case.
            // will fail in Number() check.
            if (d === '00' || d === '0000') {
                return;
            }
            if (!Number(d)) {
                res = false;
            }
        });
        if (res === false) {
            // val not number
            res = false;
            return;
        }

        // check month
        const month = parseInt(date[1], 10);
        if (month < 0 || month > 12) {
            res = false;
            return;
        }

        // check day
        const day = parseInt(date[2], 10);
        if (day < 0 || day > 31) {
            res = false;
        }
    });
    return res;
};

// check data is filled okay or not.
// For instance, date should be 1921-01-02 type.
export const isDataCorrect = (rows, ids) => {
    let res = true;
    let errMsg = '';
    ids.forEach((id) => {
        const row = rows[id];
        Object.keys(row).forEach((key) => {
            const val = row[key];
            // check Date
            if (DATE_EVENTS.indexOf(key) > -1) {
                // val 為 Object: { 0: "1000-01-02_1002-02-03", 1: "1911-02-03" }
                if (!isDate(val)) {
                    res = false;
                    errMsg += `${key} 「${val}」 is incorrect.\n`;
                }
            }
        });
    });

    return [res, errMsg];
};

// The header ids which should not be shown in table, but shown in create/edit.
export const isNotShownInTable = (actHeader) => NOT_SHOWN_IN_TABLE.indexOf(actHeader) > -1;

// The header ids which should be shown in table, but not shown in create.
export const isNotShownInAdd = (actHeader) =>
    NOT_SHOWN_IN_ADD.indexOf(actHeader) > -1 || actHeader.startsWith(LABEL_PREFIX);

// The header ids which should be shown in table, but not shown in edit.
export const isNotShownInEdit = (actHeader) => NOT_SHOWN_IN_EDIT.indexOf(actHeader) > -1;

// // FIXME:: 程式碼整併
// export const tmpEnvSetting = (type, column, api) => {
//     if (process.env.REACT_APP_WEB_MODE === "production") {
//         return column;
//     }
//
//     // 測試站
//     if (type === "人物" && api === "hasTranslationLanguage") {
//         return column.map(el => (el === "translationLanguage" ? api : el));
//     }
//     if (type === "人物") {
//         return api;
//     }
//     return column;
//
//     // if (type === "人物" && api === "hasTranslationLanguage") {
//     //     return column.map(el =>
//     //         el === "translationLanguage" ? api : column
//     //     );
//     // }else{
//     //     return column;
//     // }
// };

// 取語系
export const getLangTag = (content) => {
    if (!content) {
        return '';
    }
    if (content.length > 3 && content.slice(-3, -2) === '@') {
        return content.slice(-2);
    }
    return '';
};

// firestore 的資料結構做 mapping
export const sheetMapping = (sheetsData) =>
    Object.values(sheetsData)
        .filter((sh) => sh.enable === '1')
        .map((item) => {
            const originMapping = {
                // react key
                key: item.id,
                // onChange event return value
                value: item.label,
                // ui show text
                text: item.label,
                // for create, update and delete
                contentWritePath: item.api,
                // for search
                contentReadPath:
                    item.id === SHEET_TYPES.BASIC_INFO ? item.getTable3 : item.getTable2,
                // content_count_path: item.getTableCount,
                contentClassList: item.getClassList,
                // search: object || string
                // contentSearchPath: item.searchApi,
                contentSearchPath: item.searchApi,
                contentSearchPath2: item.searchApi2, // 測試站
                // For specialTable
                // contentInfoPath: tmpEnvSetting(
                //     item.label,
                //     item.getInformationTable,
                //     "backend/person/information/2.2"
                // ),
                contentInfoPath: item.getInformationTable,
                // displayHeader: tmpEnvSetting(
                //     item.label,
                //     item.displayHeader,
                //     "hasTranslationLanguage"
                // ),
                displayHeader: item.displayHeader,
                contentTabPath: item.tabApi,
                hasTab: item.hasTab,
                useTab: item.useTab,
                // order
                order: parseInt(item.order, 10),
            };

            // authority only
            let authorityMapping = {};
            if (item.authority === '1') {
                authorityMapping = {
                    authority: item.authority,
                    ...(item.id === SHEET_TYPES.BASIC_INFO && {
                        authorityContentReadPath: Api.getAuthorityContentReadPath,
                    }),
                };
            }

            return { ...originMapping, ...authorityMapping };
        })
        .sort((itemA, itemB) => itemA.order - itemB.order);

const spliceIntoChunks = (arr, chunkSize) => {
    const res = [];
    while (arr.length > 0) {
        const chunk = arr.splice(0, chunkSize);
        res.push(chunk);
    }
    return res;
};

// 取完 SubValues 要做 assign，如: hasURL, hasSource
export const assignSubValues = async (data, dataset) => {
    // hasSource, hasURL, hasFormat, hasReplaced...
    // dlObj:
    // {
    //     "hasSource": [
    //     [
    //         "SOU_c7a2a4e3_6d4c_4079_bf53_8989af378ec6_141",
    //         "SOU_c7a2a4e3_6d4c_4079_bf53_8989af378ec6_146",
    //         "SOU_c7a2a4e3_6d4c_4079_bf53_8989af378ec6_648",
    //     ],
    //     [
    //         "SOU_c7a2a4e3_6d4c_4079_bf53_8989af378ec6_150",
    //         "SOU_c7a2a4e3_6d4c_4079_bf53_8989af378ec6_151"
    //     ],
    // ],
    //     "hasURL": [
    //     [
    //         "URLEVT_c7a2a4e3_6d4c_4079_bf53_8989af378ec6_667",
    //         "URLEVT_c7a2a4e3_6d4c_4079_bf53_8989af378ec6_668",
    //         "URLEVT_c7a2a4e3_6d4c_4079_bf53_8989af378ec6_669",
    //         "URLEVT_c7a2a4e3_6d4c_4079_bf53_8989af378ec6_670"
    //     ]
    // ]
    // }

    const dlObj = {};
    data.forEach((row) => {
        Object.keys(SubValues).forEach((keyId) => {
            if (row[keyId]) {
                if (Object.keys(dlObj).indexOf(keyId) < 0) {
                    dlObj[keyId] = [];
                }
                dlObj[keyId].push(...Object.values(row[keyId]));
            }
        });
    });

    const promises = [];
    Object.keys(dlObj).forEach((keyId) => {
        const removeSameId = [...new Set(dlObj[keyId])];
        // 如果 ids 太多，無法一次用 get 的方式抓，需要分開
        const chunks = spliceIntoChunks(removeSameId, 100);
        // const chunks = spliceIntoChunks(dlObj[keyId], 100);
        chunks.forEach((idsArr) => {
            const ids = idsArr.join(',');
            const classType = SubValues[keyId].type;
            const apiUrl = Api.getSubValue
                .replace('{ds}', dataset)
                .replace('{ids}', '')
                .replace('{type}', classType);

            const entry = { ids };

            const options = {
                method: 'POST',
                headers: {
                    'content-type': 'application/json',
                    'Accept-Encoding': 'gzip',
                },
                body: JSON.stringify({ entry }),
            };
            promises.push(readNmtlData(apiUrl, 60000, options));
        });

        // 不批次
        // const ids = dlObj[keyId].join(",");
        // const classType = SubValues[keyId].type;
        // const apiUrl = Api.getSubValue
        //     .replace("{ds}", dataset)
        //     .replace("{ids}", "")
        //     .replace("{type}", classType);
        //
        // const entry = { ids };
        // const options = {
        //     method: "POST",
        //     headers: {
        //         "content-type": "application/json",
        //         "Accept-Encoding": "gzip"
        //     },
        //     body: JSON.stringify({ entry })
        // };
        // promises.push(readNmtlData(apiUrl, 60000, options));
    });

    // get results from promises
    const subValues = await Promise.allSettled(promises).then((res) => res);

    // 將回傳值填入 data
    const dataArr2 = subValues.reduce((acc, { status, value }) => {
        if (status === 'fulfilled' && value) {
            const { data: dataArr } = value;
            return {
                ...acc,
                ...dataArr.reduce((acc2, data) => {
                    const { srcId, p, o } = data;
                    if (!acc2[srcId]?.[p]) {
                        acc2[srcId] = { srcId, ...acc2[srcId], ...{ [p]: o } };
                    }
                    return acc2;
                }, {}),
            };
        }
        return acc;
    }, {});

    return data.map((row) => {
        Object.entries(SubValues).forEach(([SubValueKey, SubValue]) => {
            if (row[LOCATION_KEY] || LOCATION_KEY in row) {
                row = {
                    ...row,
                    [LOCATION_LABEL]: row[LOCATION_KEY],
                };
            } else if (row[SubValueKey] || SubValueKey in row) {
                Object.entries(row[SubValueKey]).forEach(([objkey, value]) => {
                    const { type, property } = SubValue;

                    // 有值
                    if (dataArr2[value]) {
                        property.forEach((sp) => {
                            const newP = bindSP(sp, sp === 'label' ? type : SubValueKey);

                            if (!row[newP]) {
                                // eslint-disable-next-line no-param-reassign
                                row[newP] = { 0: '' };
                            }

                            // 針對地點的Label
                            if (
                                type === LOCATION_TYPE &&
                                sp === TMP_LOCATION_KEY &&
                                dataArr2[value]?.label
                            ) {
                                // eslint-disable-next-line no-param-reassign
                                row[newP][objkey] = `${dataArr2[value]?.label}@zh`;
                            }

                            if (sp in dataArr2[value]) {
                                // 所有的 sub property 都要被建立出來
                                // 例：hasURL: hasCopyRightStatus, imageName, imagePath, imageFrom

                                // eslint-disable-next-line no-param-reassign
                                row[newP][objkey] =
                                    dataArr2[value]?.[sp] || row[newP][objkey] || null;
                            }
                        });
                    } else {
                        // 沒有值, 刪除
                        // eslint-disable-next-line no-param-reassign
                        delete row[SubValueKey][objkey];
                    }
                });
            }

            const isLink = (url) => {
                if (!url || typeof url !== 'string') return false;

                return url.includes('https://') || url.includes('http://');
            };

            if (row[IMAGE_KEY] || IMAGE_KEY in row) {
                const {
                    imagePath_hasURL: imagePathHasUrl,
                    imageName_hasURL: imageNameHasUrl,
                } = row;

                row = {
                    ...row,
                    ...(imagePathHasUrl &&
                        imageNameHasUrl && {
                            [IMAGE_URL]: Object.entries(imagePathHasUrl).reduce(
                                (acc, [imgIdx, value]) => {
                                    acc[imgIdx] = isLink(imageNameHasUrl[imgIdx])
                                        ? imageNameHasUrl[imgIdx]
                                        : `${fsRootUrl}/static/${value}/600x600_${imageNameHasUrl[imgIdx]}`;
                                    return acc;
                                },
                                {},
                            ),
                        }),
                };
            }
        });

        return row;
    });
};

// 取得新的 class ID
export const getReservedNewId = async (classType) => {
    if (!classType) {
        return null;
    }
    // 先確認這個 classType 是否有 prefix
    const found = classPrefix.filter((cl) => cl.eventType === classType);

    if (!found || found.length === 0) {
        return null;
    }

    // 取回目前最大值
    const curMaxData = await readNmtlData(
        Api.getReservedCurId.replace('{prefix}', found[0].prefix),
    );

    const maxValueId = curMaxData?.data[0]?.maxValue;
    if (!maxValueId || maxValueId === '0') {
        // FIXME: Vincent, 希望可以動態計算最大值並填入 max_xxx
        // 沒有這個 property 存在，試著取最大值
        const oldMaxData = await readNmtlData(
            Api.getMaxId.replace('{classname}', classType).replace('{prefix}', found[0].prefix),
        );

        const curMaxId = oldMaxData?.data[0]?.maxNum;
        if (curMaxId && curMaxId !== '') {
            // 寫入目前最大值
            // 避免 ID 被 cache，所以帶入 random 值
            const optionsOld = {
                method: 'POST',
                headers: {
                    'content-type': 'application/json',
                    'Accept-Encoding': 'gzip',
                },
                body: JSON.stringify({
                    entry: { prefix: found[0].prefix, number: curMaxId },
                }),
            };
            await readNmtlData(Api.getReservedNewId, 60000, optionsOld);
        }
    }

    // 避免 ID 被 cache，所以帶入 random 值
    const options = {
        method: 'POST',
        headers: {
            'content-type': 'application/json',
            'Accept-Encoding': 'gzip',
        },
        body: JSON.stringify({
            entry: { prefix: found[0].prefix, number: '1' },
        }),
    };
    await readNmtlData(Api.getReservedNewId, 60000, options);

    // 取回目前最大值
    const resData = await readNmtlData(Api.getReservedCurId.replace('{prefix}', found[0].prefix));

    let newValueId = resData?.data[0]?.maxValue;
    if (!newValueId || newValueId === '0') {
        // 無法更新 max_xxx, 出現問題, 使用 bnodeId
        const bnodeData = await readNmtlData(Api.getReservedBnodeId.replace('{random}', uuidv4()));
        newValueId = `_${bnodeData?.data[0]?.bnodeId}`;
        if (!newValueId) {
            // 如果還是 create 失敗
            newValueId = `_${uuidv4().replace(':', '')}`;
        }
    }

    // found.eventType
    return `${found[0].prefix}${newValueId}`;
};

export const getLookupTable = (hf) => {
    if (!hf) {
        return console.log('ERROR Value::', hf);
    }
    const contentLookupTable = {};
    // callback result to apiList by apiResults
    Object.keys(hf).forEach((api) => {
        const resArr = hf[api];
        resArr.forEach((item) => {
            const { id, label } = item;
            if (isEmpty(contentLookupTable[id])) {
                contentLookupTable[id] = [label];
            } else {
                contentLookupTable[id].push(label);
            }
        });
    });
    return { contentLookupTable };
};

export const getFieldTable = async (Ids, ds) => {
    //
    const apiList = Ids.map((field) => ({
        field,
        apiName: getApiByAllField(field),
    })).filter(
        (item) =>
            // The getApiByField will return undefined when it not found from config(api:nmtl:ApiField.js)
            item.apiName.length > 0,
    );
    // unique apiName to avoids repeated queries
    const uniqueApiNameList = [];
    apiList.forEach((a) => {
        a.apiName.forEach((api) => {
            if (uniqueApiNameList.indexOf(api) < 0) {
                uniqueApiNameList.push(api);
            }
        });
    });

    // query all uniqueApiNameList
    const promises = uniqueApiNameList.map((apiName) =>
        readNmtlData(Api[apiName].replace('{ds}', ds)),
    );

    // get results from promises
    const results = await Promise.allSettled(promises).then((res) => res);
    // create cache result dictionary
    const apiResults = {};
    // To map the api and result in a dictionary as lookup table
    results.forEach((res, rIdx) => {
        const { status, value } = res;
        // if Promise.allSettled has succeeded or failed, it will return 'fulfilled' or 'rejected'
        if (status === 'fulfilled' && value && !isEmpty(value.data)) {
            const apiName = uniqueApiNameList[rIdx];
            apiResults[apiName] = value.data.map((d) => ({
                id: d.id,
                label: `${d.label}${addSuffix(apiName)}`,
                value: d.id,
            }));
        }
    });

    return apiResults;
};

const fetchSubValue = async (data, dataset) => assignSubValues(data, dataset);

// idList = "", 取全部資料
// idList = "PER236,PER859...", 取指定 id 資料
const getDataApi = (searchId, ds, apiPath, limit = -1, offset = 0) => {
    let searchStr = '';
    // 當 searchId 為空字串時, if 判斷視為 false
    if (searchId && ds) {
        searchStr = searchId;
    }

    // combine url and parameter
    const urlPath = `${Api.getBaseUrl}/${apiPath}`;
    const parameter = `ds=${ds}&limit=${limit}&offset=${offset}&ids=${searchStr}`;
    return `${urlPath}?${parameter}`;
};

const combineData = async (idList, dataset, contentReadPath) => {
    let tmpData = [];
    const splitedIdList = Array.isArray(idList) ? idList : idList.split(',');
    const chunkSize = 7000;
    const idChunks = [];

    for (let i = 0; i < splitedIdList.length; i += chunkSize) {
        idChunks.push(splitedIdList.slice(i, i + chunkSize).toString());
    }

    const options = {
        method: 'POST',
        headers: {
            'content-type': 'application/json',
            'Accept-Encoding': 'gzip',
        },
    };

    const apiUrl = `${Api.getBaseUrl}/POST/${contentReadPath}?ds=${dataset}&limit=-1&offset=0&ids=`;

    const requests = idChunks.map(async (chunk) => {
        const entry = {
            ids: chunk.toString(),
        };

        const { data } = await readNmtlData(apiUrl, SET_TIMEOUT, {
            ...options,
            body: JSON.stringify({ entry }),
        });
        return data || [];
    });

    const allData = await Promise.all(requests);

    allData.forEach((data) => {
        tmpData = tmpData.concat(data);
    });

    return tmpData;
};

// header 處理
const getExportHeader = (header) => {
    const tmpHeader = getExportHeaderRemove(header);

    // 過濾特定 key 值
    return tmpHeader.filter((h) => !(REMOVE_FROM_HEADER in h));
};

// sheetName: "BasicInfo"
// contentReadPath: "backend/dl_basicinfo/2.0"
// idList : "PER236,PER859..."
// dataset: "wikipedia"
// headerFields: {
//      getAreaOfTaiwan: [
//          { id: 'AOT1', label: '北部', value: 'AOT1' },
//          { id: 'AOT2', label: '中部', value: 'AOT2' },
//          { id: 'AOT3', label: '南部', value: 'AOT3' },
//          { id: 'AOT4', label: '東部及離島', value: 'AOT4' },
//          { id: 'AOT7', label: '不分區', value: 'AOT7' }
//      ]
//      getCopyrightStatus: [
//          { id: 'PublicDomain', label: '公用領域', value: 'PublicDomain' },
//          { id: 'Copyrighted', label: '版權所有', value: 'Copyrighted' },
//          { id: 'CopyrightUnknown', label: '未知授權狀況', value: 'CopyrightUnknown' },
//          { id: 'LibraryUseOnly', label: '僅限館內閱覽', value: 'LibraryUseOnly' }
//      ]
//      getEthnicGroup: [{ id: 'EthnicAims', label: '阿美族', value: 'EthnicAims' },{...}]
//      getGender: [
//          { id: 'Female', label: '女', value: 'Female' },
//          { id: 'Male', label: '男', value: 'Male' },
//          { id: 'OtherGender', label: '其它', value: 'OtherGender' },
//      ]
//      getLocationList: [{...},....]
//      getPersonlist: [{...},....]
// }
// headers: [
//      { id: 'srcId', seq: 0 , label: '人名ID'},
//      { id: 'label_Person', seq: 1 , label: '姓名(@zh @en @jp)'},
//      { id: 'hasBirthDate', seq: 2 , label: '出生時間(yyyy-mm-dd)'},
//      ...
// ]
export const exportExcel = async (dataset, idList, contentReadPath, headers) => {
    let allData = [];

    // get subValue

    // firebase seq = 1 才做排序
    const sortedByHeader = headers.find((header) => header.seq === 1).id;
    // filter header
    const exportHeader = getExportHeader(headers);
    const headerIds = exportHeader && exportHeader.map((item) => item.id);
    // field mapping table for easy to find
    const fields = await getFieldTable(headerIds, dataset);
    const { contentLookupTable } = getLookupTable(fields);

    const data = await combineData(idList, dataset, contentReadPath);

    // if data exist
    if (data.length > 0) {
        const mergeData = arrayMerge.sheet(data);
        // subValue
        allData = await fetchSubValue(mergeData, dataset);
    }

    // 使資料符合 excel 輸出格式
    const exportData = allData.map((elObj) => {
        const objData = {};
        Object.keys(elObj).forEach((elKey) => {
            if (typeof elObj[elKey] === 'string') {
                objData[elKey] = elObj[elKey];
            } else if (typeof elObj[elKey] === 'object') {
                objData[elKey] = Object.values(elObj[elKey]);
            } else {
                console.log('ERROR Value::', elObj[elKey]);
            }
        });
        return objData;
    });

    let transformedContent = [];
    transformedContent = datasetCoverToExport(exportData);
    // 根據下拉清單的 key 找到對應的 label
    transformedContent = convertToExport({
        contentData: transformedContent,
        contentLookupTable,
    });

    // 根據特定 header 做排序
    const sortedData = sortedByStroke(transformedContent, sortedByHeader);

    return { data: sortedData, exportHeader };
};

export const removeDuplicateId = (createState) => {
    //
    if (!createState.options) return createState;
    //
    // if (createState.value) {
    //     const tmpOptions = createState.options.map(item => ({
    //         id: item.id,
    //         label: item.id,
    //         value: item.value
    //     }));
    //     const tmpRes = [];
    //     Object.values(createState.value).forEach(value => {
    //         const findObj = tmpOptions.find(el => el.id === value);
    //         if (findObj) {
    //             tmpRes.push(findObj);
    //         }
    //     });
    //     return tmpRes;
    // }
    //
    const uniqueOption = {};
    createState.options.forEach((item) => {
        if (!item.id) return;
        uniqueOption[item.id] = {
            id: item.id,
            label: item.id,
            value: item.value,
        };
    });

    const resultOpt = Object.values(uniqueOption).map((opt) => opt);
    return resultOpt;
};

export const timeStampToDate = (timeObj) => {
    if (!timeObj) return null;

    const numTimeArr = Object.values(timeObj).map(Number);
    const lastTimestamp = Math.max(...numTimeArr);

    if (isNaN(lastTimestamp)) return null;

    return new Date(lastTimestamp).toISOString().slice(0, 10);
};

export const readContent = async ({
    tabClass,
    searchIds,
    readApiUrl,
    // readTabUrl
}) => {
    // 特殊情況: 件層級
    if (tabClass === PEICES_INFO) {
        // 根據翻譯書ID拿到欄位需要的資料
        const { data, error } = await readNmtlData(readApiUrl);
        const mergedData = arrayMerge.sheet(data);

        // sort ID, 注意後續處理不能改變陣列順序
        const sortedData = [];
        searchIds.forEach((curValue) => {
            sortedData.push(...mergedData.filter((dt) => dt?.srcId === curValue));
        });

        /** *
         * 目前資料有可能多本原文書對多本翻譯書
         * PUB1234,PUB5667:[{
         *     hasTranslationBook: "PUB6678",
         *     lastModified: { 0: 1234567890 },
         *     translationBookName: { 0: xxx@zh, 1:xxx@en },
         *     translationLanguage: { 0: 日文 }
         * },
         * {
         *     hasTranslationBook: "PUB6671",
         *     lastModified: { 0: 1234567890 },
         *     translationBookName: { 0: xxx@zh, 1:xxx@en },
         *     translationLanguage: { 0: 日文 }
         * },...]
         ** */

        // 將原文書對應翻譯書
        const translationList = sortedData.reduce((acc, cur) => {
            const {
                // 原文書ID
                isTranslationBookOf,
                // 翻譯書ID
                srcId,
                // 翻譯書最後修改時間
                lastModified,
                // 翻譯書名
                translationBookName,
                // 翻譯語言
                hasLanguageOfWorkOrName,
            } = cur;

            // 取出原文書ID
            const tmpSrcId = Object.values(isTranslationBookOf).join('、');

            if (!Object.hasOwn(acc, tmpSrcId)) {
                acc[tmpSrcId] = [];
            }

            acc[tmpSrcId].push({
                srcId,
                lastModified,
                translationBookName,
                hasLanguageOfWorkOrName,
            });

            return acc;
        }, {});

        // 找到原文書作者與原文書名
        const formatData = Object.keys(translationList).reduce((acc, cur) => {
            //
            const targetObj = sortedData.find((el) => {
                const { isTranslationBookOf } = el;
                const oriId = Object.values(isTranslationBookOf).join('、');
                return oriId === cur;
            });

            // eslint-disable-next-line camelcase
            const { hasAuthor, label_Publication } = targetObj;

            acc.push({
                isTranslationBookOf: cur,
                hasAuthor,
                label_Publication,
                transList: translationList[cur],
            });

            return acc;
        }, []);

        return { mergedData: formatData, error };
    }

    const { data, error } = await readNmtlData(readApiUrl);

    const finalData = arrayMerge.sheet(data);

    // 根據排序後的 id 放入資料
    const mergedData = searchIds.map((curValue) => finalData.find((dt) => dt.srcId === curValue));

    return { mergedData, error };
};

export const exportToFile = async (dataset, head, path, ids, sortIds) => {
    const urlPath = `${Api.getBaseUrl}/POST/${path}`;
    const parameter = `ds=${dataset}&limit=-1&offset=0&ids=`;
    const readPath = `${urlPath}?${parameter}`;

    const options = {
        method: 'POST',
        headers: {
            'content-type': 'application/json',
            'Accept-Encoding': 'gzip',
        },
        body: JSON.stringify({ entry: { ids, ds: dataset } }),
    };

    const { data } = await readNmtlData(readPath, 60000, options);
    if (!data) return [];

    const mergedData = arrayMerge.sheet(data);
    const subData = await assignSubValues(mergedData, dataset);

    const headId = head.map((el) => el.id);
    const fields = await getFieldTable(headId, dataset);
    const { contentLookupTable } = getLookupTable(fields);

    // 過濾出需要下載的欄位
    const getFilterData = subData.map((el) =>
        Object.keys(el).reduce((acc, key) => {
            const extractValues = (header, headerZhKey, headerEnKey) => {
                const headerValues = Object.values(el[header]);

                const headerZh = headerValues.filter((value) => value.includes('zh'));
                const headerEn = headerValues.filter((value) => value.includes('en'));

                if (headId.includes(headerZhKey) && headerZh.length > 0) {
                    acc[headerZhKey] = headerZh;
                }
                if (headId.includes(headerEnKey) && headerEn.length > 0) {
                    acc[headerEnKey] = headerEn;
                }
            };

            switch (key) {
                case 'label_Person':
                    extractValues('label_Person', 'label_Person_Zh', 'label_Person_En');
                    break;
                case 'introduction':
                    extractValues('introduction', 'introduction_Zh', 'introduction_En');
                    break;
                case 'hasAuthor':
                    extractValues('hasAuthor', 'hasAuthor_Zh', 'hasAuthor_En');
                    break;
                case 'authorName':
                    extractValues('authorName', 'authorName_Zh', 'authorName_En');
                    break;
                case 'hasTranslator':
                    extractValues('hasTranslator', 'hasTranslator_Zh', 'hasTranslator_En');
                    break;
                case 'translatorName':
                    extractValues('translatorName', 'translatorName_Zh', 'translatorName_En');
                    break;
                case 'hasEditor':
                    extractValues('hasEditor', 'hasEditor_Zh', 'hasEditor_En');
                    break;
                case 'hasPublisher':
                    extractValues('hasPublisher', 'hasPublisher_Zh', 'hasPublisher_En');
                    break;
                case 'references':
                    extractValues('references', 'references_Zh', 'references_En');
                    break;
                default:
                    if (headId.includes(key)) {
                        acc[key] = el[key];
                    }
                    break;
            }

            return acc;
        }, {}),
    );

    const exportData = getFilterData.map((elObj) => {
        const objData = {};
        Object.keys(elObj).forEach((elKey) => {
            if (typeof elObj[elKey] === 'string') {
                objData[elKey] = elObj[elKey];
            } else if (typeof elObj[elKey] === 'object') {
                objData[elKey] = Object.values(elObj[elKey]);
            } else {
                console.log('ERROR Value::', elObj[elKey]);
            }
        });
        return objData;
    });

    const transformedContent = convertToExport({
        contentData: exportData,
        contentLookupTable,
    });

    return { data: transformedContent, head };
};

export const getYMDNow = () => {
    const dt = new Date();
    return `${dt.getFullYear()}_${dt.getMonth() + 1}_${dt.getDate()}`;
};

export const handleSorting = (tabClass, sortCol, direction, searchData, uniValue) => {
    let sortedIds;

    const newRes = uniValue.map((tmpId) => searchData.find((el) => el.srcId === tmpId));

    sortedIds = newRes.sort((a, b) => {
        if (sortStroke.includes(sortCol)) {
            return parseInt(a[`sort_${sortCol}`], 10) - parseInt(b[`sort_${sortCol}`], 10);
        }

        return parseInt(a[sortCol], 10) - parseInt(b[sortCol], 10);
    });

    // 件層級每項排序都需要依據原文書將翻譯書彙整顯示
    if (tabClass === PEICES_INFO) {
        sortedIds = sortedIds.reduce((acc, curVal, curIdx, curArray) => {
            const matchVal = curArray.map((cv, idx) =>
                cv.isTranslationBookOf === curVal.isTranslationBookOf ? idx : -1,
            );

            matchVal.forEach((matchIdx) => {
                if (matchIdx >= 0) {
                    acc.push(curArray[matchIdx]);
                    // eslint-disable-next-line no-param-reassign
                    delete curArray[matchIdx];
                }
            });

            return acc;
        }, []);
    }

    if (direction === sortedMethod.DESC) {
        sortedIds = sortedIds.reverse();
    }

    return sortedIds.map((item) => item.srcId);
};

import React from "react";

// ui
import { Table} from 'semantic-ui-react'

const StatusTable = ({ networkInfo }) => {

    return (
        <Table celled padded>
            <Table.Header>
                <Table.Row textAlign='center'>
                    <Table.HeaderCell>傳送</Table.HeaderCell>
                    <Table.HeaderCell>封包丟失</Table.HeaderCell>
                    <Table.HeaderCell>封包錯誤</Table.HeaderCell>
                    <Table.HeaderCell>接收</Table.HeaderCell>
                    <Table.HeaderCell>封包丟失</Table.HeaderCell>
                    <Table.HeaderCell>封包錯誤</Table.HeaderCell>
                    <Table.HeaderCell>回應時間</Table.HeaderCell>
                </Table.Row>
            </Table.Header>
            <Table.Body>
                <Table.Row textAlign='center'>
                    <Table.Cell>{networkInfo?.rx_bytes}</Table.Cell>
                    <Table.Cell>{networkInfo?.rx_dropped}</Table.Cell>
                    <Table.Cell>{networkInfo?.rx_errors}</Table.Cell>
                    <Table.Cell>{networkInfo?.tx_bytes}</Table.Cell>
                    <Table.Cell>{networkInfo?.tx_dropped}</Table.Cell>
                    <Table.Cell>{networkInfo?.tx_errors}</Table.Cell>
                    <Table.Cell>{networkInfo?.responseTime} ms</Table.Cell>
                </Table.Row>
            </Table.Body>
        </Table>
    );
};

export default StatusTable;
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Icon, Header, Segment, SegmentInline, Dropdown } from 'semantic-ui-react';
import React, { useContext, useEffect, useState, useMemo } from 'react';
import axios from 'axios';
import { useQuery } from 'react-query';
import { StoreContext } from '../../../../../store/StoreProvider';
import { createHistoryEvent } from '../../../../downloadData/components/history/common/common';
import Api from '../../../../../api/nmtl/Api';
import { isEmpty } from '../../../../../commons/index';

function DBBackupList({ isReload }) {
    const [backupList, setBackupList] = useState([]);
    const [state] = useContext(StoreContext);
    const { headerActiveName } = state.common;
    const { displayName, systemDataActiveItem } = state.user;
    const columns = [headerActiveName, systemDataActiveItem, '資料庫下載'];
    const [isLoading, setIsLoading] = useState(true);
    const [selectedFileName, setSelectedFileName] = useState('');
    const [isDownloaded, setIsDownloaded] = useState(false);

    useEffect(() => {
        if (!isReload) return;

        (async () => {
            const apiStr = Api.getDBBackupList;
            const res = await axios(apiStr);
            const { backups } = res.data;
            if (backups) {
                const tmpBackups = backups.reverse().map((fileName, idx) => ({
                    key: idx,
                    text: fileName,
                    value: fileName,
                }));
                setBackupList(tmpBackups);
                setSelectedFileName(tmpBackups[0]?.value || '');
            }
            setIsLoading(false);
        })();
    }, [isReload]);

    const onClickDBDownload = async (fileName = '') => {
        try {
            setIsDownloaded(true);
            const apiStr = Api.getDatabase.replace('{filename}', fileName);

            const response = await fetch(apiStr);
            if (!response.ok) throw new Error('Network response was not ok');

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);

            const link = document.createElement('a');
            link.download = fileName;
            link.href = url;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Cleanup
            window.URL.revokeObjectURL(url);

            createHistoryEvent(displayName, '下載', columns.join('/'));
        } catch (error) {
            console.log('error ', error);
            createHistoryEvent(displayName, '下載失敗', columns.join('/'), error);
        }
        setIsDownloaded(false);
    };

    return (
        <SegmentInline style={{ display: 'flex', justifyContent: 'space-between' }}>
            {!isLoading && (
                <Dropdown
                    placeholder="Version"
                    search
                    selection
                    options={backupList}
                    value={selectedFileName}
                    onChange={(evt, data) => {
                        setSelectedFileName(data.value);
                    }}
                />
            )}
            <Button
                icon
                onClick={() => onClickDBDownload(selectedFileName)}
                loading={isDownloaded}
                style={{ margin: '0' }}
            >
                <Icon name="download" />
            </Button>
        </SegmentInline>
    );
}

// 與nmtl-api狀態需一致
const BACKUP_STATUS = {
    success: 'success',
    fail: 'fail',
    pending: 'pending',
};

const DownloadDbFile = () => {
    const [startBackUp, setStartBackUp] = useState(false);
    const [taskId, setTaskId] = useState('');

    const isLoading = useMemo(() => Boolean(startBackUp || taskId), [startBackUp, taskId]);

    // step 1: 先取得taskId
    useQuery(
        'getBackupTaskId',
        async () => {
            const response = await axios(Api.getBackupTaskId);
            return response.data;
        },
        {
            refetchInterval: (res) => {
                if (!startBackUp) return false;

                const intervalTime = 1000;
                if (isEmpty(res?.taskInfo)) return intervalTime;

                setTaskId(res.taskInfo.taskId);
                setStartBackUp(false);
                return false;
            },
            refetchIntervalInBackground: true,
            refetchOnMount: false,
            refetchOnReconnect: false,
            refetchOnWindowFocus: false,
            enabled: startBackUp,
        },
    );

    // 根據taskId取得backup status
    useQuery(
        'getBackupStatus',
        async () => {
            const response = await axios({
                method: 'post',
                url: Api.getBackupStatus,
                data: { taskId },
            });
            return response.data;
        },
        {
            refetchInterval: (res) => {
                if (!taskId) return false;

                const intervalTime = 1000;
                if (!res?.status) return intervalTime;

                const isPending = res.status === BACKUP_STATUS.pending;
                if (isPending) return intervalTime;

                setTaskId('');
                return false;
            },
            refetchIntervalInBackground: true,
            refetchOnMount: false,
            refetchOnReconnect: false,
            refetchOnWindowFocus: false,
            enabled: Boolean(taskId),
        },
    );

    const handleBackUp = async () => {
        await axios(Api.backupDatabase);
        setStartBackUp(true);
    };

    return (
        <div style={{ width: '100%' }}>
            <Header as="h3">資料庫下載</Header>
            <Divider />
            <Segment
                className="dbFileDownload"
                style={{
                    border: '2px dashed #737373',
                    padding: '10px 15px',
                }}
            >
                <Icon name="database" size="big" />
                <Button.Group>
                    <Button
                        basic
                        color="blue"
                        onClick={handleBackUp}
                        loading={isLoading}
                        disabled={isLoading}
                    >
                        備份最新資料
                    </Button>
                </Button.Group>
            </Segment>
            <Divider hidden />
            <DBBackupList isReload={!isLoading} />
        </div>
    );
};

export default DownloadDbFile;

import React, { useContext, useState } from 'react';
import axios from 'axios';
import { Button, Modal, Radio, Table } from 'semantic-ui-react';
import { StoreContext } from '../../../../store/StoreProvider';
import { convertSuffixToClass, isCorrectSuffix } from '../../../../api/nmtl/ApiField';
import getKeyBySingle from '../../../../api/nmtl/ApiKey';
import getSingleByApi from '../../../../api/nmtl/ApiSingle';
import Api from '../../../../api/nmtl/Api';
import { createNmtlData } from '../../../../api/nmtl';
import { isEmpty } from '../../../../commons';
import Act from '../../../../store/actions';
import { SHOW_ID, shouldCopyOnlyInstanceId } from '../../../common/sheetCrud/sheetCrudHelper';

const ExistedModalForOldForm = ({
  equalValue,
  open,
  setOpen,
  cellId,
  inputIds,
  rowId,
  idx,
  setCallback,
  handleClose,
}) => {
  const [state, dispatch] = useContext(StoreContext);
  const { user } = state;
  const { mainSubject, sheet } = state.data;
  const { dataset } = mainSubject.selected;
  const { key: sheetName } = sheet.selected;
  const [checkItem, setCheckItem] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const modalConfirm = async () => {
    setIsLoading(true);

    const isPersonOrOrg =
      checkItem.id.substring(0, 3) === 'PER' || checkItem.id.substring(0, 3) === 'ORG';
    const tmpZhVal = isPersonOrOrg
      ? checkItem.label.concat('@zh', '@', checkItem.id.substring(0, 3))
      : checkItem.label;

    const { newClass, newApi } = isCorrectSuffix(cellId, tmpZhVal);

    // label為id的，需要用其他方式抓classtype
    const cls = SHOW_ID.includes(cellId)
      ? convertSuffixToClass[`@${checkItem.id.substring(0, 3)}`]
      : newClass;

    const itemKey = getKeyBySingle(getSingleByApi(newApi));

    const apiUrl = Api.getGeneric;
    const isShouldCopyOnlyInstanceId = shouldCopyOnlyInstanceId(cls || itemKey, dataset);
    const copyApiUrl = isShouldCopyOnlyInstanceId ? Api.getCopyOnlyId : Api.getCopy;

    // 將資料複製到外譯房，並且api會將原本沒有lang tag的label移除，如果該資料並沒有zh lang tag label，
    // 會再自動放上zh lang tag label
    await axios.put(copyApiUrl, {
      entrySrc: {
        graph: checkItem.ds,
        classType: cls || itemKey,
        value: checkItem.ds,
        srcId: checkItem.id,
      },
      entryDst: {
        graph: checkItem.ds,
        classType: cls || itemKey,
        value: dataset, // 要移動到的資料集
        srcId: checkItem.id,
      },
    });
    // label加上en
    const enObj = {
      graph: dataset,
      classType: cls || itemKey,
      srcId: checkItem.id || '',
      value: {
        label: checkItem.label.concat('@en'),
      },
    };
    if (!isShouldCopyOnlyInstanceId) {
      // console.log("SHOULD create label for instance");
      //
      await createNmtlData(user, apiUrl, dataset, sheetName, enObj).then((res) => res === 'OK');
    } else {
      // console.log("PASS create label for instance");
    }

    setIsLoading(false);

    // dispatch
    dispatch({
      type: Act.DATA_CONTENT_ROW_CHANGED,
      payload: {
        rowId,
        cellId,
        idx,
        // 因為 multiple drop down 不需要管順序
        // cellData: {0: "value1", 1: "value2"}
        cellValue: Object.assign({}, inputIds),
      },
    });
    setCallback(cellId, rowId, idx, {
      isOption: true,
      value: Object.assign({}, inputIds),
    });

    setOpen(false);
    handleClose();
  };

  const modalCancel = () => {
    setCheckItem(null);
    setOpen(false);
    handleClose();
  };

  const modalTableBody = () =>
    equalValue.map((el, idx) => (
      <Table.Row key={`ModalTable-${idx}`}>
        <Table.Cell>
          <Radio
            value={el.id}
            checked={checkItem?.index === idx}
            onChange={(event, data) => {
              if (data.checked) {
                setCheckItem({
                  id: el.id,
                  ds: el.Graph,
                  label: el.label,
                  index: idx,
                });
              } else {
                setCheckItem(undefined);
              }
            }}
          />
        </Table.Cell>
        <Table.Cell>{el.id}</Table.Cell>
        <Table.Cell>{el.label}</Table.Cell>
        <Table.Cell>{el.Graph}</Table.Cell>
      </Table.Row>
    ));
  const modal = (
    <React.Fragment>
      <Modal.Header>名稱確認</Modal.Header>

      <Modal.Content image scrolling>
        <Modal.Description>
          <span>名稱已存在，請選擇ID匯入當前資料集，或是取消，重新修改名稱。</span>
          <Table celled striped>
            <Table.Header>
              <Table.Row>
                {!isEmpty(equalValue) && (
                  <React.Fragment>
                    <Table.HeaderCell collapsing />
                    <Table.HeaderCell>ID</Table.HeaderCell>
                    <Table.HeaderCell>名稱</Table.HeaderCell>
                    <Table.HeaderCell>資料集</Table.HeaderCell>
                  </React.Fragment>
                )}
              </Table.Row>
            </Table.Header>
            <Table.Body>{!isEmpty(equalValue) && modalTableBody()}</Table.Body>
          </Table>
        </Modal.Description>
      </Modal.Content>

      <Modal.Actions>
        <Button onClick={modalCancel} color="black">
          取消
        </Button>
        <Button disabled={isEmpty(checkItem)} onClick={modalConfirm} loading={isLoading} positive>
          確定
        </Button>
      </Modal.Actions>
    </React.Fragment>
  );

  return (
    <Modal onClose={() => setOpen(false)} onOpen={() => setOpen(true)} open={open}>
      {modal}
    </Modal>
  );
};
export default ExistedModalForOldForm;

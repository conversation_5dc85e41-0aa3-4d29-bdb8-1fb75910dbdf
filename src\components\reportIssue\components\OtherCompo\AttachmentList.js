import React, { useEffect, useState } from "react";
import { useSelector } from "react-redux";

// semantic ui
import { List, Container } from "semantic-ui-react";

// utils
import { isEmpty } from "../../../../commons";
import { getFileName } from "../../../../commons/utility";
import downloadFile from "../../../../commons/downloadFile";

function AttachmentList() {
    const { cntData } = useSelector(state => state.report);

    const [files, setFiles] = useState([]);

    useEffect(() => {
        if (!cntData?.file) return;
        setFiles(cntData.file);
    }, [cntData]);

    return (
        <Container fluid>
            <List bulleted>
                {!isEmpty(files) &&
                    files.map(item => (
                        <List.Item
                            key={item}
                            href="#"
                            onClick={() => {
                                downloadFile(item);
                            }}
                        >
                            {getFileName(item)}
                        </List.Item>
                    ))}
            </List>
        </Container>
    );
}

export default AttachmentList;

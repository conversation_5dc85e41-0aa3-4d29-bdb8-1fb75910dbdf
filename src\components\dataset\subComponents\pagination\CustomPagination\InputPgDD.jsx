import React, { useContext, useEffect, useState } from "react";
import { Input } from "semantic-ui-react";
import { StoreContext } from "../../../../../store/StoreProvider";
import Act from "../../../../../store/actions";

function InputPgDD() {
    const [state, dispatch] = useContext(StoreContext);
    const { pagination } = state.data;
    const { totalPage, activePage: currentPage, totalCount } = pagination;
    const [inputValue, setInputValue] = useState(currentPage);

    useEffect(() => {
        setInputValue(currentPage);
    }, [currentPage]);

    const handleChange = e => {
        const { value } = e.target;
        // 檢查輸入是否為數字且在範圍內
        if (
            /^\d*$/.test(value) &&
            (value === "" || (Number(value) >= 1 && Number(value) <= totalPage))
        ) {
            setInputValue(value);
        }
    };

    const handleBlurOrEnter = () => {
        if (inputValue !== "" && Number(inputValue) !== currentPage) {
            dispatch({
                type: Act.DATA_PAGINATION_ACTIVE_PAGE,
                payload: Number(inputValue)
            });
        } else {
            // 如果輸入無效或空白，重置為當前頁碼
            setInputValue(currentPage);
        }
    };

    const handleKeyDown = evt => {
        if (evt.key === "Enter") {
            handleBlurOrEnter();
        }
    };

    return (
        <React.Fragment>
            <div style={{ marginRight: "0.5rem" }}>
                <p>現在是第</p>
            </div>
            <Input
                value={inputValue}
                onChange={handleChange}
                onBlur={handleBlurOrEnter}
                onKeyDown={handleKeyDown}
                style={{ width: "5rem" }}
            />
            <div style={{ marginLeft: "0.5rem" }}>
                <p>
                    頁，共 {totalPage} 頁，共 {totalCount} 筆
                </p>
            </div>
        </React.Fragment>
    );
}

export default InputPgDD;

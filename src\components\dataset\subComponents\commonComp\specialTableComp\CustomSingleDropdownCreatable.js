import React, { useContext, useState, useMemo, useEffect } from "react";

// store
import { components, createFilter } from "react-select";
import CreatableSelect from "react-select/creatable";
import { WindowedMenuList } from "react-windowed-select";
import Act from "../../../../../store/actions";
import { StoreContext } from "../../../../../store/StoreProvider";

import { createNmtlData } from "../../../../../api/nmtl";
import getSingleByApi from "../../../../../api/nmtl/ApiSingle";
import {
    getReservedNewId,
    specialConvertToOption
} from "../../../../common/sheetCrud/utils";
import Api from "../../../../../api/nmtl/Api";
import getKeyBySingle from "../../../../../api/nmtl/ApiKey";
import { isEmpty } from "../../../../../commons";
import { isCorrectSuffix } from "../../../../../api/nmtl/ApiField";
import ExistedModal from "./ExistedModal";
import DropdownEditModal from "./DropdownEditModal";
import { findSameIdList } from "../../CreateComp/helper";

// 關閉偵測同一id的彈跳視窗
const showExistedModal = true;
const CustomSingleDropdownCreatable = ({
    cellId,
    // rowId,
    // default 0
    // idx = 0,
    defaultValue,
    createState,
    setCallback,
    // isCreateNew = false,
    menuName
}) => {
    const [state, dispatch] = useContext(StoreContext);
    const { user } = state;
    const { sheet, mainSubject } = state.data;
    const { dataset } = mainSubject.selected;
    const { key: sheetName } = sheet.selected;
    const { headerFields } = sheet;
    const [inputValue, setInputValue] = useState();
    const [option, setOption] = useState(null);
    const [equalValue, setEqualValue] = useState(null);
    const [open, setOpen] = useState(false);
    const [editId, setEditId] = useState(false);
    const [editModalOpen, setEditModalOpen] = useState(false);

    const MultiValueLabel = props => (
        <div
            onMouseDown={e => {
                e.stopPropagation();
                // 儲存目前編輯chip的id
                setEditId(props.data.id);
                setEditModalOpen(true);
            }}
        >
            <components.MultiValueLabel {...props}>
                {props.children}
            </components.MultiValueLabel>
        </div>
    );

    const handleChange = selectValue => {
        // const cellValue = selectValue ? [selectValue.id] : null;
        //
        // setCallback(cellId, cellValue, menuName);
        // selectValue: [{ id: "PER96766", label: "施仁思@PER", value: "PER96766" }...]
        // Select設置為isMulti，因此selectValue會有多個，只選擇最後一個(最新的選擇)項項
        const cellValueKeyId = isEmpty(selectValue)
            ? ""
            : selectValue.at(-1).id;
        const cellValue = isEmpty(selectValue)
            ? []
            : selectValue.filter(item => item.id === cellValueKeyId);

        const selectCombinedIds = cellValue.map(item => item.id).sort();

        if (cellValue.length === 0) {
            setCallback(cellId, null, menuName);
        } else {
            setCallback(cellId, selectCombinedIds, menuName);
        }
    };

    const handleInputChange = value => {
        if (!value) {
            return;
        }
        setInputValue(value);
    };
    const newOptions = useMemo(() => {
        if (!headerFields) {
            return [];
        }

        return specialConvertToOption(cellId, headerFields) || [];
    }, [cellId, headerFields]);

    const getCreateNmtlItemResult = async (item, newClass, newApi) => {
        if (isEmpty(item) || isEmpty(dataset) || isEmpty(sheetName)) {
            return { newSrcId: null };
        }

        const apiUrl = Api.getGeneric;
        const itemKey = getKeyBySingle(getSingleByApi(newApi));

        // 先 reserved id
        const newSrcId = await getReservedNewId(itemKey);
        if (!newSrcId) {
            dispatch({
                type: Act.DATA_INFO_MESSAGE,
                payload: {
                    title: `${cellId} 欄位請填入正確後綴，如：@PER, @ORG 或 @EVT`,
                    error: 1,
                    renderSignal: `update-${new Date().getTime()}`
                }
            });
            return { newSrcId: null };
        }

        if (apiUrl && itemKey) {
            const newItem = {
                graph: dataset,
                classType: newClass || itemKey,
                srcId: newSrcId || "",
                value: {
                    label: [item.concat("@zh"), item.concat("@en")]
                }
            };

            const createResult = await createNmtlData(
                user,
                apiUrl,
                dataset,
                sheetName,
                newItem
            ).then(res => res === "OK");

            return { newSrcId, createResult };
        }
        return { newSrcId: null };
    };

    const handleCreate = async value => {
        // 移除@zh、@PER、@en等
        function removeAfterFirstAt(s) {
            const atPosition = s.indexOf("@");
            if (atPosition === -1) {
                return s;
            }
            return s.slice(0, atPosition);
        }

        const sameList = await findSameIdList(
            removeAfterFirstAt(value),
            sheetName,
            Api.getSameListWithGraphForLoc
        );

        if (!isEmpty(sameList)) {
            setEqualValue(sameList);
            setOpen(true);
            return;
        }
        // suffix feature
        const { isSuffix, newValue, newClass, newApi } = isCorrectSuffix(
            cellId,
            value
        );
        if (!isSuffix) {
            dispatch({
                type: Act.DATA_INFO_MESSAGE,
                payload: {
                    title: `${cellId} 欄位請填入正確後綴，如：@PER, @ORG 或 @EVT`,
                    error: 1,
                    renderSignal: `update-${new Date().getTime()}`
                }
            });
            return;
        }

        const { newSrcId } = await getCreateNmtlItemResult(
            newValue,
            newClass,
            newApi
        );
        // setCallback(cellId, rowId, idx, { isOption: true, isLoading: true });
        if (newSrcId) {
            const newOpt = { id: newSrcId, label: value, value: newSrcId };

            // 如果此欄位為多重 type，增加到該 type
            // hasPublisher: ORG, PER
            if (Object.keys(headerFields).indexOf(newApi) > -1) {
                headerFields[newApi].push(newOpt);
            }

            setOption(newOpt);
            setCallback(cellId, [newOpt?.id], menuName);
        }
    };

    useEffect(() => {
        if (!newOptions || isEmpty(defaultValue)) {
            return;
        }

        // console.log(defaultValue);
        const tmp = newOptions.find(
            el => Object.values(defaultValue).indexOf(el.id) > -1
        );

        setInputValue(tmp?.label);
    }, [defaultValue]);

    const customStyles = {
        container: styles => ({
            ...styles,
            margin: "-9px",
            minWidth: "100%",
            width: "300px"
        }),
        control: (styles, { selectProps: { controlColor } }) => ({
            ...styles,
            borderStyle: "none",
            borderRadius: "unset",
            backgroundColor: controlColor
        })
    };

    useEffect(() => {
        if (!option) return;
        newOptions.push(option);
    }, [option]);

    return useMemo(
        () => (
            <>
                <CreatableSelect
                    isClearable
                    // 單選，顯示zh及en Label，需設置為isMulti
                    isMulti
                    styles={customStyles}
                    options={newOptions}
                    value={
                        createState && createState[cellId]
                            ? newOptions.filter(
                                o =>
                                    createState[cellId] &&
                                      Object.values(
                                          createState[cellId]
                                      ).includes(o.id)
                            )
                            : null
                    }
                    onChange={handleChange}
                    onInputChange={handleInputChange}
                    onCreateOption={handleCreate}
                    components={{
                        MenuList: WindowedMenuList,
                        MultiValueLabel
                    }}
                    // [fixed] select lag issue: https://github.com/JedWatson/react-select/issues/3128 by M1K3Yio commented on 19 Oct 2018
                    filterOption={createFilter({ ignoreAccents: false })}
                />
                {/* 關閉偵測同一id的彈跳視窗 */}
                {showExistedModal && (
                    <ExistedModal
                        equalValue={equalValue}
                        open={open}
                        setOpen={setOpen}
                        cellId={cellId}
                        newOptions={newOptions}
                        setCallback={setCallback}
                        menuName={menuName}
                        createState={createState}
                        headerFields={headerFields}
                    />
                )}
                <DropdownEditModal
                    id={editId}
                    onClick={() => {
                        setEditModalOpen(prev => !prev);
                    }}
                    open={editModalOpen}
                    // update newOptions
                    options={newOptions.filter(o => o)}
                    cellId={cellId}
                />
            </>
        ),
        [
            cellId,
            createState,
            inputValue,
            newOptions,
            menuName,
            open,
            editModalOpen
        ]
    );
};

export default CustomSingleDropdownCreatable;

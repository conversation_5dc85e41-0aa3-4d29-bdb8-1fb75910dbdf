import { checkRes } from "../config/config";

const checkHeaderCell = (headerArr, fbHeader) => {
    const tmpHeaderArr = headerArr
        .filter(el => el)
        .map(el => {
            const [label, id] = el.split("\n");
            return { label: label || "", id: id || "" };
        });

    if (tmpHeaderArr.some(({ label, id }) => !label || !id))
        return checkRes.failed;

    const check = tmpHeaderArr.every(obj => {
        const { label, id } = obj;
        const findEl = fbHeader.find(el => el.id === id);
        if (findEl) {
            return findEl.label === label;
        }
        return false;
    });

    return check ? checkRes.success : checkRes.failed;
};
export default checkHeaderCell;

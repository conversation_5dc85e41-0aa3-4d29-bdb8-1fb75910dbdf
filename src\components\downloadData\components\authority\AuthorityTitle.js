import React, { useCallback, useEffect, useState, useContext } from "react";

// ui
import { Table } from "semantic-ui-react";
import { StoreContext } from "../../../../store/StoreProvider";

import CustomDownloadAuthority from "./CustomDownloadAuthority";
import { getDownloadSheet } from "../../../../api/firebase/cloudFirestore";

const headerCells = [{ label: "Type", value: "種類" }];
const dataCells = [
    {
        label: "person",
        value: "人名權威檔",
        sheetName: "BasicInfo"
    },
    { label: "place", value: "地名權威檔", sheetName: "Location" }
];
// 目前權威檔的來源為 tww 和 twp
const datasetName = "twp,tww";

const Authority = () => {
    const [activeRow, setActiveRow] = useState(-1);
    const [getContentApi, setGetContentApi] = useState(null);

    const [state, dispatch] = useContext(StoreContext);
    const { headerActiveName, downloadDataActiveName } = state.common;
    const { displayName, role } = state.user;
    const [columns, setColumns] = useState([]);

    const fixFirstChild = {
        position: "sticky",
        top: "0",
        // zIndex: "1"
    };

    // get sheet header for table title
    const handleGetSheetApi = useCallback(async () => {
        if (activeRow < 0) {
            return;
        }
        const { value, sheetName } = dataCells[activeRow];
        if (sheetName) {
            // 取得 API
            const { getAuthority } = await getDownloadSheet(sheetName);
            const tmpColumns = [headerActiveName, downloadDataActiveName];
            tmpColumns.push(value);
            setColumns(tmpColumns);
            if (getAuthority) {
                setGetContentApi(getAuthority);
            }
        }
    }, [activeRow]);

    // 1. get sheet header
    useEffect(() => {
        handleGetSheetApi();
    }, [handleGetSheetApi, activeRow]);

    const handleClick = idx => {
        setActiveRow(idx);
    };

    return (
        <div
            style={{
                overflowX: "hidden",
                maxHeight: "260px"
            }}
        >
            <Table celled selectable compact>
                <Table.Header>
                    <Table.Row>
                        {headerCells.map(item => (
                            <Table.HeaderCell
                                key={`logs-header-${item.label}`}
                                style={fixFirstChild}
                            >
                                {item.value}
                            </Table.HeaderCell>
                        ))}
                    </Table.Row>
                </Table.Header>

                <Table.Body>
                    {dataCells.map((item, idx) => {
                        const { label, value } = item;
                        return (
                            <Table.Row
                                key={`log-${label}`}
                                onClick={() => {
                                    if (role !== "reader") {
                                        handleClick(idx);
                                    }
                                }}
                                active={activeRow === idx}
                                style={{ cursor: "pointer" }}
                            >
                                <Table.Cell>{`${value}（點我下載）`}</Table.Cell>
                            </Table.Row>
                        );
                    })}
                </Table.Body>
            </Table>
            {activeRow > -1 && (
                <CustomDownloadAuthority
                    limit={-1}
                    offset={0}
                    prefix={dataCells[activeRow].label}
                    datasetName={datasetName}
                    sheetName={dataCells[activeRow].sheetName}
                    getContentApi={getContentApi}
                    displayName={displayName}
                    columns={columns}
                />
            )}
        </div>
    );
};

export default Authority;

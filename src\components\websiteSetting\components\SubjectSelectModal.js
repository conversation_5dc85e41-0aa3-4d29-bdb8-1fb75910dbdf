import React from "react";
import { Button, Modal } from "semantic-ui-react";

function SubjectSelectModal({ openModal, setOpenModal }) {
    return (
        <Modal size="mini" open={openModal} onClose={() => setOpenModal(false)}>
            <Modal.Content>
                <p>此主題已被選取</p>
            </Modal.Content>
            <Modal.Actions>
                <Button
                    positive
                    onClick={() => {
                        setOpenModal(false);
                    }}
                >
                    關閉視窗
                </Button>
            </Modal.Actions>
        </Modal>
    );
}

export default SubjectSelectModal;

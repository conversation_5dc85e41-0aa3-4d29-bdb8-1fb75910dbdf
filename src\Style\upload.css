.dropzone {
  width: 100%;
  height: 100%;
  border: 2px dashed #737373;
  text-align: center;
}

.dropzone-small {
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 2px dashed #737373;
  text-align: center;
  margin: 0 8px 8px 0;
  padding: 4px;
}
.dropzone-small:hover {
  cursor: pointer;
}

.dropImageTitle {
  text-align: center;
}

.thumbsContainer {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16px;
  padding: 20px;
  height: 300px;
  overflow: scroll;
  overflow-x: hidden;
  border: 1px solid darkgrey;
  background-color: #fff;
}

.thumbsContainer .thumb {
  position: relative;
  display: inline-flex;
  border-radius: 2px;
  border: 1px solid #eaeaea;
  margin-bottom: 8px;
  margin-right: 8px;
  width: 100px;
  height: 100px;
  padding: 4px;
  box-sizing: border-box;
}

.thumbInner {
  display: flex;
  min-width: 0px;
  overflow: hidden;
  justify-content: center;
}

.thumb .thumbInner img {
  display: block;
  width: auto;
  /*max-height: 100px;*/
  max-width: 100px;
  height: auto;
  width: auto;
  align-self: center;
  justify-self: center;
}

.thumb .img-checkbox {
  position: absolute !important;
  right: 3px;
  top: 3px;
}

.thumbButton {
  position: absolute;
  right: 10px;
  top: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  border: 0px;
  border-radius: 0.325em;
  font-weight: 500;
  cursor: pointer;
}

.folder-list-item-1st, .folder-list-item-2nd {
  color: #b8b8b8;
  cursor: pointer;
}

.folder-list-item-1st:hover, .folder-list-item-2nd:hover {
  color: black;
}

.folder-list-item-1st:active, .folder-list-item-2nd:active {
  color: black !important;
}

.control-panel-container, .control-panel {
  display: flex;
  justify-content: space-around;
}

/*# sourceMappingURL=upload.css.map */

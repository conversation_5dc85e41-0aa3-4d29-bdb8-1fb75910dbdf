import React, { useContext, useState, useEffect } from "react";

// ui
import { Input, Button, Icon, Dropdown } from "semantic-ui-react";

// store
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";

import { createHistoryEvent } from "../../../downloadData/components/history/common/common";
import settingSearchKeyword from "../../../../commons/settingSearchKeyword";
import { getTextAndLabel } from "../../../../commons/transferKeyword";
import { graphOptions, literaryOptions } from "../../../common/sheetCrud/config";
import { getMainSubject } from "../../../../api/firebase/cloudFirestore";

const AuthoritySearchBar = () => {
    // console.log('I am CustomSearchBar');

    // keep input value
    const [inputValue, setInputValue] = useState("");
    const [options, setOptions] = useState([]);
    const [selected, setSelected] = useState("");

    // eslint-disable-next-line no-unused-vars
    const [state, dispatch] = useContext(StoreContext);
    const { mainSubject, sheet, search } = state.data;
    const { headerActiveName } = state.common;
    const { contentSearchPath } = sheet.selected;
    const { displayName } = state.user;
    const { keyword } = search;
    const { header } = sheet;

    const handleInputChange = (event, { value }) => {
        // console.log("I am handleInputChange function:", value);

        setInputValue(value);
    };

    // refresh all parameter
    const handleRefreshAll = () => {
        // refresh pagination when sheet changed
        dispatch({ type: Act.DATA_PAGINATION_ACTIVE_PAGE_INIT });
        // refresh changed when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CHANGED_CLEAN });
        // refresh checked when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CHECKED_CLEAN });
        // refresh created when sheet changed
        dispatch({ type: Act.DATA_CONTENT_ROW_CREATED_CLEAN });
    };

    const handleInputClick = () => {
        const columns = [headerActiveName, mainSubject.selected.value, sheet.selected.value];
        createHistoryEvent(displayName, "查詢", columns.join("/"));
        // dispatch search keyword
        dispatch({
            type: Act.DATA_SEARCH_KEYWORD,
            payload: inputValue
        });

        dispatch({
            type: Act.DATA_SEARCH_DATASET,
            payload: selected
        });

        handleRefreshAll();
    };

    const handleInputKeyPress = event => {
        if (event.key === "Enter") {
            // dispatch search keyword
            dispatch({
                type: Act.DATA_SEARCH_KEYWORD,
                payload: inputValue
            });

            dispatch({
                type: Act.DATA_SEARCH_DATASET,
                payload: selected
            });

            handleRefreshAll();
        }
    };

    const handleDropdownChange = (e, { value }) => {
        setSelected(value);
    };

    useEffect(() => {
        setInputValue(keyword);
    }, [keyword]);

    useEffect(() => {
        const fetchOptions = async () => {
            let allOptions = [];
            const mainSubjectData = await getMainSubject();

            for (const mainSubjectItem of mainSubjectData) {
                const foundOption = [...literaryOptions, ...graphOptions].some(
                    option => option.id === mainSubjectItem.id
                );

                if (foundOption) {
                    const newItem = {
                        key: mainSubjectItem.id,
                        text: mainSubjectItem.label,
                        value: mainSubjectItem.id
                    };

                    allOptions.push(newItem);
                }
            }
            setOptions(allOptions);
        };

        fetchOptions();
    }, []);

    // [
    //     { key: "123", text: "123", value: "123" },
    //     { key: "3", text: "3", value: "3" },
    //     { key: "1", text: "1", value: "1" }
    // ]

    return (
        <div style={{ display: "flex", gap: "1rem" }}>
            {/* <Dropdown */}
            {/*    placeholder="選項" */}
            {/*    search */}
            {/*    selection */}
            {/*    options={header.map((item, index) => ({ */}
            {/*        key: index, */}
            {/*        text: item.label, */}
            {/*        value: item.label */}
            {/*    }))} */}
            {/*    clearable */}
            {/*    onChange={() => { */}
            {/*        console.log("hihi"); */}
            {/*    }} */}
            {/* /> */}
            <Input
                action
                type="text"
                placeholder="搜尋..."
                value={inputValue}
                disabled={!contentSearchPath}
                onChange={handleInputChange}
                onKeyPress={handleInputKeyPress}
                style={{ width: "100%" }}
            >
                <input />
                {/* <Button onClick={handleInputClick}>搜尋</Button> */}
                <Dropdown
                    style={{
                        zIndex: 100
                    }}
                    options={options}
                    clearable
                    selection
                    placeholder="選擇資料集"
                    disabled={!contentSearchPath}
                    onChange={handleDropdownChange}
                    value={selected}
                />
                <Button animated="fade" disabled={!contentSearchPath} onClick={handleInputClick}>
                    <Button.Content hidden>搜尋</Button.Content>
                    <Button.Content visible>
                        <Icon name="search" />
                    </Button.Content>
                </Button>
            </Input>
        </div>
    );
};

export default AuthoritySearchBar;

import React, { useContext, useState, useCallback, useEffect } from 'react';

// ui
import { Button } from 'semantic-ui-react';

// store
import { StoreContext } from '../../../../../../../../store/StoreProvider';

// common
import { isEmpty } from '../../../../../../../../commons';

// nmtl api
import { getUnintegratedData, readNmtlData } from '../../../../../../../../api/nmtl';

// utils
import { createHistoryEvent } from '../../../../../../../downloadData/components/history/common/common';
import { exportExcel, getYMDNow } from '../../../../../../../common/sheetCrud/utils';
import arrayMerge from '../../../../../../../../commons/arrayMerge';
import CustomMultiSheetsExcelJS from '../../../../../commonComp/CustomMultiSheetsExcelJS';

const batchSize = 10000;
const CustomDownloadAllPageButton = ({ range }) => {
    const [state] = useContext(StoreContext);
    // get dataset(mainSubject) and sheet
    const { mainSubject, sheet, search, content } = state.data;
    const { headerActiveName } = state.common;
    const { displayName } = state.user;
    // extract parameter for sidebar selected item
    const { dataset: datasetLabel, value: datasetName } = mainSubject.selected;
    const { header } = sheet;
    const { key: sheetName, contentReadPath, contentSearchPath, contentClassList } = sheet.selected;
    const { keyword, searchDataset } = search;

    // 記錄在歷史訊息的欄位資訊
    const columns = [headerActiveName, mainSubject.selected.value, sheet.selected.value];

    // isLoading
    const [isLoading, setIsLoading] = useState(false);
    // download excel searchTotal
    const [searchTotal, setSearchTotal] = useState(-1);
    const [searchIds, setSearchIds] = useState('');

    const filename = `${datasetLabel}_${sheetName}_${getYMDNow()}`;

    const searchNmtlData = useCallback(async () => {
        if (datasetLabel && contentSearchPath && contentClassList) {
            // combine url and parameter
            const searchApiUrl = keyword
                ? contentSearchPath
                    .replace('{ds}', searchDataset || datasetLabel)
                    .replace('{keyword}', keyword)
                : contentClassList.replace('{ds}', searchDataset || datasetLabel);
            if (!searchApiUrl) {
                setIsLoading(false);
                return { data: [], total: null, error: 'Not ready' };
            }
            return readNmtlData(searchApiUrl, 100000);
        }
        return { data: [] };
    }, [datasetLabel, contentSearchPath, keyword]);

    const getUnintegratedIds = async (uniValue) => {
        const chunkSize = 600;
        const idChunks = [];

        for (let i = 0; i < uniValue.length; i += chunkSize) {
            idChunks.push(uniValue.slice(i, i + chunkSize).toString());
        }

        const unintegratedDataPromises = idChunks.map((chunk) => getUnintegratedData(chunk));

        const allUnintegratedData = await Promise.all(unintegratedDataPromises);

        const tmpData = [
            ...new Set(
                allUnintegratedData
                    .filter((el) => el.length > 0)
                    .flatMap((el) => arrayMerge.unintegratedMergeSheet(el))
                    .map((el) => el.srcId),
            ),
        ];

        return tmpData || [];
    };

    useEffect(() => {
        let isMounted = true;

        const fetchData = async () => {
            const { data } = await searchNmtlData();

            if (isMounted) {
                // remove duplicated ids
                let allIds = [...new Set(data.map(({ id }) => id))];
                let dataTotal = allIds.length;

                if (
                    content.showOnlyUnintegratedData &&
                    sheetName === 'BasicInfo' &&
                    datasetLabel === 'authority'
                ) {
                    const unintegratedIds = await getUnintegratedIds(allIds);

                    if (unintegratedIds.length > 0) {
                        allIds = unintegratedIds;
                        dataTotal = unintegratedIds.length;
                    }
                }
                // 只有當組件仍然掛載時才更新狀態
                setSearchTotal(dataTotal);
                setSearchIds(allIds.join(','));
            }
        };

        fetchData();

        return () => {
            isMounted = false;
        };
    }, [keyword, contentSearchPath]);

    // get sheet header
    const handleDownload = async () => {
        if (searchTotal === 0) {
            return;
        }

        if (datasetName && sheetName) {
            // set loading status
            setIsLoading(true);

            // get content
            if (datasetLabel && sheetName && contentReadPath) {
                // fetch data
                const { data: excelData, exportHeader } = await exportExcel(
                    datasetLabel,
                    searchIds,
                    contentReadPath,
                    header,
                );

                if (!isEmpty(excelData)) {
                    CustomMultiSheetsExcelJS({
                        allData: { [sheetName]: { headers: exportHeader, data: excelData } },
                        filename,
                        batchSize,
                    });

                    setIsLoading(false);
                    createHistoryEvent(displayName, '下載', columns.join('/'));
                } else {
                    createHistoryEvent(displayName, '下載失敗', columns.join('/'));
                    setIsLoading(false);
                }
            }
        }
    };

    const customStyle = {
        marginBottom: '1em',
        marginRight: '1em',
        minWidth: '200px',
    };

    return (
        <React.Fragment>
            <Button
                loading={isLoading || searchTotal < 0}
                color="orange"
                onClick={handleDownload}
                style={customStyle}
                disabled={isLoading}
            >
                下載 Excel (1-{searchTotal})
            </Button>
        </React.Fragment>
    );
};

export default CustomDownloadAllPageButton;

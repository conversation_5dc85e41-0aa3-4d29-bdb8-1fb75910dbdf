import React, { useContext, useEffect, useState } from "react";

// semantic-ui-react
import { Image } from "semantic-ui-react";

// components
import SaveButton from "../../components/SaveButton";
import UpdateText from "../../components/UpdateText";
import UploadImageModal from "../../components/UploadImageModal";
import LanguageSelect from "../../components/LanguageSelect";
import ImageTypeSelect from "../../components/ImageTypeSelect";

// general
import dragImage from "../../../../images/dragImage.svg";
import { StoreContext } from "../../../../store/StoreProvider";
import Act from "../../../../store/actions";
import { imageTypeTrans } from "../../commons";

function MainIntroduction() {
    const [state, dispatch] = useContext(StoreContext);
    const { originData, updatedData, menuActiveItem } = state.websiteSetting;
    const [openModal, setOpenModal] = useState(false);
    const [language, setLanguage] = useState("zh");
    const [allData, setAllData] = useState({});
    const [imageWebOrMobile, setImageWebOrMobile] = useState("web");

    useEffect(() => {
        // 沒有下拉選單
        dispatch({
            type: Act.SET_SELECTOPTION,
            payload: null
        });
    }, [menuActiveItem]);

    useEffect(() => {
        if (originData.length !== 0) {
            const tmpAllData = originData.find(element => element.id === menuActiveItem.key);
            if (tmpAllData) {
                setAllData(tmpAllData);
            }
        }
    }, [originData]);

    return (
        <div className="MainIntroduction">
            <div className="topArea">
                <h1>圖片</h1>
                <ImageTypeSelect imageWebOrMobile={imageWebOrMobile} setImageWebOrMobile={setImageWebOrMobile}/>
            </div>
            <div className="updateArea">
                <Image
                    style={{cursor: "pointer", height: "100%"}}
                    src={allData[imageTypeTrans[imageWebOrMobile]] !== "" && allData[imageTypeTrans[imageWebOrMobile]] ? allData[imageTypeTrans[imageWebOrMobile]] : dragImage}
                    onClick={() => {
                        setOpenModal(true);
                    }}
                />
            </div>
            <div className="topArea">
                <h1>網站簡介</h1>
                <LanguageSelect language={language} setLanguage={setLanguage} />
            </div>
            <div className="textArea">
                <UpdateText
                    language={language}
                    option={{
                        message: allData[language] ? allData[language].description : "",
                        column: "description",
                        priority: "No priority"
                    }}
                />
            </div>
            <div className="btnArea">
                <SaveButton language={language} />
            </div>
            {openModal && <UploadImageModal setOpenModal={setOpenModal} setAllData={setAllData} imageWebOrMobile={imageWebOrMobile} />}
        </div>
    );
}

export default MainIntroduction;

import Swagger from './components/swagger';
import authority from '../../App-authority';
import Ontology from './components/ontolotgy/Ontology';
import ImageUpload from './components/upload/ImageUpload';
import FulltextUpload from './components/fulltextUpload/FulltextUpload';
// eslint-disable-next-line import/no-cycle
import FullTxtAnalysis from './components/fullTextAnalysis/FullTxtAnalysis';
import Example from './components/example/Example';
import Env from '../pages/Env';
import State from '../pages/Status';
import ImageLinkChecker from './components/ImageLinkChecker';

export const menuId = {
    api: 'api',
    ontology: 'ontology',
    imgUpl: 'imgUpl',
    fileUpl: 'fileUpl',
    fltAna: 'fltAna',
    exampl: 'exampl',
    env: 'env',
    state: 'state',
    ImageLinkChecker: 'ImageLinkChecker',
};

const toolPagesMenu = [
    {
        id: menuId.api,
        name: 'API',
        component: Swagger,
        authority: authority.Api,
    },
    {
        id: menuId.ontology,
        name: '語意架構',
        component: Ontology,
        authority: authority.Ontology,
    },
    {
        id: menuId.imgUpl,
        name: '圖片批次上傳',
        component: ImageUpload,
        authority: authority.Upload,
    },
    {
        id: menuId.fileUpl,
        name: '全文批次上傳',
        component: FulltextUpload,
        authority: authority.Upload,
    },
    {
        id: menuId.fltAna,
        name: '全文字詞分析',
        component: FullTxtAnalysis,
        authority: authority.FullTxtAna,
    },
    {
        id: menuId.exampl,
        name: '公版表單',
        component: Example,
        authority: authority.Example,
    },
    {
        id: menuId.env,
        name: '環境變數',
        component: Env,
        authority: authority.Env,
    },
    {
        id: menuId.state,
        name: 'State',
        component: State,
        authority: authority.State,
    },
    {
        id: menuId.ImageLinkChecker,
        name: '圖片連結檢查',
        component: ImageLinkChecker,
        authority: authority.ImageLinkChecker,
    },
];

export default toolPagesMenu;

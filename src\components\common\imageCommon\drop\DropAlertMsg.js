import React, { useEffect } from "react";

// component
import { useSelector, useDispatch } from "react-redux";
import AlertMsg from "../../AlertMsg";
import FileAct from "../../../../reduxStore/file/fileAction";

const timeoutMs = {
    success: 5000,
    error: 5000,
    info: 5000,
    warning: 5000,
    default: 5000
};

const DropAlertMsg = () => {
    const dispatch = useDispatch();
    const { imgPickerMessage } = useSelector(state => state.files);
    const message = imgPickerMessage.drop;

    useEffect(() => {
        const { type } = message || {};
        const delayMs =
            (type in timeoutMs && timeoutMs[type]) || timeoutMs.default;
        setTimeout(() => {
            dispatch({
                type: FileAct.CLEAN_PICKER_DROP_MESSAGE
            });
        }, delayMs);
    }, [imgPickerMessage.drop]);

    return message && message.title && <AlertMsg message={message} />;
};

export default DropAlertMsg;

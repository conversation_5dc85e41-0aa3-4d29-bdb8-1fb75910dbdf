import React, { useCallback, useEffect, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { debounce } from "lodash";
// import CustomQuill from "../components/CustomQuill";
import VillagesAct from "../../../VillagesAction";
import { isEmpty } from "../../../../../../../../commons";
// import htmlToMD from "../../../../../../../../commons/htmlToMD";
import CustomQuillWithImage from "../components/CustomQuillWithImage";

const useDebouncedDispatch = (dispatch, delay = 300) => {
    const debouncedFuncs = useRef({});

    return useCallback(
        type => {
            if (!debouncedFuncs.current[type]) {
                debouncedFuncs.current[type] = debounce(payload => {
                    dispatch({ type, payload });
                }, delay);
            }
            return debouncedFuncs.current[type];
        },
        [dispatch, delay]
    );
};

const QuillArea = ({ updatedData }) => {
    const dispatch = useDispatch();
    const getDebouncedDispatch = useDebouncedDispatch(dispatch);

    const refs = {
        translatorContentZh: useRef(null),
        translatorContentEn: useRef(null)
    };

    const [values, setValues] = useState({
        translatorContentZh: "",
        translatorContentEn: ""
    });

    const handleChange = (type, key) => e => {
        setValues(prevValues => ({
            ...prevValues,
            [key]: e
        }));

        getDebouncedDispatch(type)(e);
    };

    const quillConfig = [
        {
            id: "CustomEditorTranslatorContentZh",
            ref: refs.translatorContentZh,
            title: "譯者名人堂 (中文版)",
            value: values.translatorContentZh,
            type: VillagesAct.SET_TRANSLATOR_CONTENT_ZH_FOR_QUILL,
            key: "translatorContentZh"
        },
        {
            id: "CustomEditorTranslatorContentEn",
            ref: refs.translatorContentEn,
            title: "譯者名人堂 (外文版)",
            value: values.translatorContentEn,
            type: VillagesAct.SET_TRANSLATOR_CONTENT_EN_FOR_QUILL,
            key: "translatorContentEn"
        }
    ];

    useEffect(() => {
        if (isEmpty(updatedData)) return;
        const updateValues = {
            translatorContentZh: updatedData?.translatorContentZh,
            translatorContentEn: updatedData?.translatorContentEn
        };

        setValues(prevValues => ({
            ...prevValues,
            ...Object.fromEntries(
                Object.entries(updateValues).filter(
                    ([key, value]) => value && value !== prevValues[key]
                )
            )
        }));

        dispatch({
            type: VillagesAct.SET_TRANSLATOR_CONTENT_ZH_FOR_QUILL,
            payload: updatedData?.translatorContentZh
        });

        dispatch({
            type: VillagesAct.SET_TRANSLATOR_CONTENT_EN_FOR_QUILL,
            payload: updatedData?.translatorContentEn
        });
    }, [updatedData?.translatorContentZh, updatedData?.translatorContentEn]);

    return (
        <>
            {quillConfig.map(({ id, ref, title, value, type, key }) => (
                <div style={{ marginTop: "1rem" }} key={id}>
                    <div className="topArea">
                        <h1>{title}</h1>
                    </div>
                    <CustomQuillWithImage
                        key={id}
                        quillId={id}
                        tmpRef={ref}
                        tmpValue={value}
                        onChangeFct={handleChange(type, key)}
                    />
                </div>
            ))}
        </>
    );
};

export default QuillArea;

export const initToolbarOptions = {
    container: [
        [{ header: [1, 2, 3, 4, 5, 6, false] }],
        ["bold", "italic", "underline", "strike", "blockquote", "link"], // toggled buttons
        // [{ header: 1 }, { header: 2 }], // custom button values
        [{ list: "ordered" }, { list: "bullet" }],
        [{ script: "sub" }, { script: "super" }], // superscript/subscript

        // [{ size: ["small", false, "large", "huge"] }], // custom dropdown

        // [{ color: [] }, { background: [] }], // dropdown with defaults from theme
        // [{ font: [11, 12, 13] }],
        // [{ direction: "rtl" }, { align: [] }, { indent: "-1" }, { indent: "+1" }], // text direction/align/outdent/indent
        ["image", "video"],
        ["clean"] // remove formatting button
    ]
};

export const tablePeakHeaderConfig = [
    { header: "著作編號", row: 3, display: "left" },
    { header: "出版品/刊物", row: 8, display: "left" },
    { header: "編輯", row: 1, display: "center" }
];

export const tableEditPeakHeaderConfig = [
    { header: "", row: 1, display: "left", key: "drag" },
    { header: "", row: 1, display: "left", key: "order" },
    { header: "書名", row: 12, display: "left", key: "title" },
    { header: "編輯", row: 1, display: "center", key: "edit" },
    { header: "刪除", row: 1, display: "center", key: "delete" }
];

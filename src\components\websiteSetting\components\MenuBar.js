import React, { useContext, useEffect } from 'react';

//
import { Menu } from 'semantic-ui-react';
import { StoreContext } from '../../../store/StoreProvider';
import Act from '../../../store/actions';

// css
import '../websiteSetting.scss';

// 切換頁面後，更改component和需要回到預設值的動作
const handleItemClick = (item, dispatch) => {
    dispatch({
        type: Act.SET_MENUACTIVEITEM,
        payload: item,
    });
    // 下拉選單選項清空
    dispatch({
        type: Act.SET_SELECTOPTION,
        payload: '',
    });
};

function MenuBar() {
    const [state, dispatch] = useContext(StoreContext);
    const { menuActiveItem, originData, subMenu } = state.websiteSetting;

    useEffect(() => {
        if (subMenu.length === 0) return;
        dispatch({
            type: Act.SET_MENUACTIVEITEM,
            payload: subMenu[0],
        });
    }, [subMenu]);

    useEffect(() => {
        // updateData換成OriginData，不同頁的更改動作，如果沒有存檔，到其他頁會清空
        dispatch({
            type: Act.SET_UPDATEDDATA,
            payload: originData,
        });
        // 切換頁面，預設沒有開啟編輯模式
        dispatch({
            type: Act.SET_ISEDITEDDISABLE,
            payload: false,
        });
        // listData只在MainCarousel會用到
        dispatch({
            type: Act.SET_LISTDATA,
            payload: {},
        });
    }, [menuActiveItem]);

    return (
        <Menu pointing vertical style={{ width: '100%' }} className="menuBar">
            {/* 清單顯示 */}
            {subMenu.length > 0 &&
                subMenu.map((item, index) => (
                    <Menu.Item
                        key={index}
                        name={item.name}
                        active={menuActiveItem.name === item.name}
                        onClick={() => handleItemClick(item, dispatch, originData)}
                    />
                ))}
        </Menu>
    );
}

export default MenuBar;

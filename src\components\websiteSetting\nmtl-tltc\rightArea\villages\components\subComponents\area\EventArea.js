import React, { useEffect, useState } from "react";
import "../../EditVillagesDetail.scss";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import { <PERSON><PERSON>, Ref, Table } from "semantic-ui-react";
import { DragDropContext, Draggable, Droppable } from "react-beautiful-dnd";
import { isEmpty } from "../../../../../../../../commons";
import { getReservedNewId } from "../../../../../../../common/sheetCrud/utils";
import { deleteActivityInfo, saveActivityInfo } from "../../../utils/utils";
import Api from "../../../../../../../../api/nmtl/Api";
import iconDrag from "../../../../../../../../images/icon_drag.svg";
import { tableEditActivityHeaderConfig } from "../../../config";
import ACIModal from "../components/ACIModal";
import VillagesAct from "../../../VillagesAction";
import CustomButton from "../components/CustomButton";
import DeleteModal from "../components/DeleteModal";

const EventArea = ({ list, user, websiteSubject }) => {
    const dispatch = useDispatch();
    const {
        editingVillageId,
        deletingActivityInfo,
        aciIsEdited,
        isEditedActivityInfoList
    } = useSelector(state => state);
    const [characters, updateCharacters] = useState(list);
    const [combinedChapterList, setCombinedChapterList] = useState([]);
    const [isACIDraggingOver, setIsACIDraggingOver] = useState(true);
    const [isDragged, setIsDragged] = useState(false);
    // react beautiful dnd settings
    const grid = 8;
    const getItemStyle = (isDragging, draggableStyle) => ({
        userSelect: "none",
        padding: grid * 2,
        margin: `0 0 ${grid}px 0`,
        width: "100%",
        background: isDragging ? "lightgreen" : "initial",
        ...draggableStyle
    });

    const getListStyle = isDraggingOver => ({
        background: isDraggingOver ? "initial" : "initial",
        padding: grid
    });

    const cellPaddingStyle = { padding: "0.5rem" };
    const cellBorderBottomStyle = {
        borderBottom: "1px solid rgba(34,36,38,.1)"
    };
    const buttonStyle = {
        backgroundColor: "initial",
        padding: "0.3rem",
        fontSize: "1.3rem",
        width: "100%"
    };

    const ActivityCell = ({ contentZh, contentEn, colSpan, width }) => (
        <Table.Cell
            verticalAlign="middle"
            colSpan={colSpan}
            width={width}
            style={{ padding: "0" }}
        >
            <div style={{ ...cellBorderBottomStyle, ...cellPaddingStyle }}>
                {contentZh}
            </div>
            <div style={cellPaddingStyle}>{contentEn}</div>
        </Table.Cell>
    );

    const IconButton = ({
        icon,
        onClick,
        disabled,
        style,
        extraStyles = {}
    }) => (
        <Button
            style={{ ...style, ...extraStyles }}
            onClick={onClick}
            disabled={disabled}
            icon={icon}
        />
    );

    const onDragStart = () => {
        setIsACIDraggingOver(true);
    };

    const onDragEnd = ({ destination, source }) => {
        if (!destination) return;

        const items = Array.from(characters);
        const [reorderedItem] = items.splice(source.index, 1);
        items.splice(destination.index, 0, reorderedItem);
        const updatedItems = items.map((item, index) => ({
            ...item,
            order: (index + 1).toString()
        }));

        const sortedData = data => {
            const tmpData = data.sort((a, b) => {
                const numA = parseInt(a.id.slice(3), 10);
                const numB = parseInt(b.id.slice(3), 10);
                return numA - numB;
            });
            return tmpData;
        };

        const tmpNewItems = sortedData(
            JSON.parse(JSON.stringify(updatedItems))
        );
        const tmpPrevItems = sortedData(isEmpty(list) ? items : list);

        const combinedData = tmpPrevItems.map((prevData, index) => {
            const newData = tmpNewItems[index];
            return {
                prevData,
                newData
            };
        });

        setCombinedChapterList(combinedData);
        updateCharacters(updatedItems);

        setIsACIDraggingOver(false);
        setIsDragged(true);
    };
    // 新增活動
    const handleAddCate = async () => {
        const tmpAciId = await getReservedNewId("ActivityInfo");
        dispatch({ type: VillagesAct.SET_EDITING_ACI_ID, payload: tmpAciId });
        dispatch({ type: VillagesAct.SET_IS_EDITED_ACI, payload: true });
        dispatch({
            type: VillagesAct.SET_ACI_LIST_LENGTH,
            payload: list.length
        });
        dispatch({
            type: VillagesAct.SET_ACI_LIST,
            payload: list
        });
    };

    const handleEdit = i => {
        dispatch({ type: VillagesAct.SET_EDITING_ACI_ID, payload: i.id });
        dispatch({ type: VillagesAct.SET_IS_EDITED_ACI, payload: true });
        dispatch({ type: VillagesAct.SET_EDITING_ACI_DATA, payload: i });
    };

    const handleDelete = () => {
        // 移除該活動與VIL的關聯
        deleteActivityInfo(
            dispatch,
            editingVillageId,
            deletingActivityInfo.id,
            user,
            websiteSubject
        )
            .then(() => {
                const tmpArr = [];
                const getACIList = async () => {
                    const api = Api.getVillagesActivityInfoList.replace(
                        "{id}",
                        editingVillageId
                    );

                    await axios.get(api).then(res => {
                        tmpArr.push(...res?.data?.data);
                    });
                };

                // 重新抓取PEK目前的書目列表
                getACIList().then(() => {
                    const tmpPCH = JSON.parse(JSON.stringify(tmpArr)).sort(
                        (a, b) => a.order - b.order
                    );

                    const newPCH = tmpPCH
                        .map((item, index) => ({
                            ...item,
                            order: (index + 1).toString()
                        }))
                        .sort((a, b) => {
                            const numA = parseInt(a.id.slice(3), 10);
                            const numB = parseInt(b.id.slice(3), 10);
                            return numA - numB;
                        });

                    const generateUpdateData = (oldData, newData) =>
                        newData.map(newItem => {
                            const prevItem = oldData.find(
                                oldItem => oldItem.id === newItem.id
                            );
                            return {
                                newData: newItem,
                                prevData: prevItem
                            };
                        });

                    // 整理舊排序與新排序
                    const updateOrderData = generateUpdateData(tmpPCH, newPCH);

                    // 更新排序
                    const promises = updateOrderData
                        .filter(
                            item => item.prevData.order !== item.newData.order
                        )
                        .map(pch =>
                            saveActivityInfo(
                                dispatch,
                                pch.prevData,
                                pch.newData,
                                user,
                                websiteSubject,
                                "ActivityInfo",
                                editingVillageId
                            )
                        );

                    Promise.all(promises)
                        .then(() => {
                            dispatch({
                                type:
                                    VillagesAct.SET_IS_EDITED_ACTIVITY_INFO_LIST,
                                payload: !isEditedActivityInfoList
                            });
                            dispatch({
                                type: VillagesAct.SET_IS_DEL_MODAL_OPEN,
                                payload: false
                            });
                        })
                        .catch(error => {
                            console.error("Error:", error);
                        });
                });
            })
            .then(() => {
                dispatch({
                    type: VillagesAct.SET_IS_EDITED_ACI,
                    payload: false
                });
                setIsACIDraggingOver(true);
            });
    };

    const handleSaveOrder = () => {
        const promises = combinedChapterList
            .filter(item => item.prevData.order !== item.newData.order)
            .map(i =>
                saveActivityInfo(
                    dispatch,
                    i.prevData,
                    i.newData,
                    user,
                    websiteSubject,
                    "ActivityInfo",
                    editingVillageId
                )
            );

        Promise.all(promises)
            .then(() => {
                dispatch({
                    type: VillagesAct.SET_IS_EDITED_ACTIVITY_INFO_LIST,
                    payload: !isEditedActivityInfoList
                });
                dispatch({
                    type: VillagesAct.SET_IS_MODAL_OPEN,
                    payload: true
                });
                setIsACIDraggingOver(true);
                setIsDragged(false);
            })
            .catch(error => {
                console.error("Error:", error);
            });
    };

    useEffect(() => {
        updateCharacters(list);
        list.sort((a, b) => a.order - b.order);
    }, [list]);

    const handleACIModalOpen = () => {
        dispatch({
            type: VillagesAct.SET_IS_EDITED_ACI,
            payload: !aciIsEdited
        });
    };

    return (
        <>
            <div className="topArea">
                <div className="topArea__title">
                    <h1>活動列表</h1>
                </div>
                <div className="topArea__btn">
                    <Button
                        content="+ 新增活動"
                        style={{
                            background: "#e4f5e8",
                            color: "#21ba45",
                            fontSize: "12px",
                            cursor: "pointer",
                            textAlign: "center",
                            padding: "0.5rem 1rem",
                            borderRadius: "4px",
                            height: "100%"
                        }}
                        onClick={handleAddCate}
                        disabled={isDragged}
                    />
                </div>
            </div>
            <div className="bookArea">
                <DragDropContext
                    onDragEnd={onDragEnd}
                    onDragStart={onDragStart}
                >
                    <Table celled structured size="small" selectable fixed>
                        <Table.Header>
                            <Table.Row>
                                {tableEditActivityHeaderConfig.map(i => (
                                    <Table.HeaderCell
                                        key={i.key}
                                        textAlign={i.display}
                                        colSpan={i.row}
                                    >
                                        {i.header}
                                    </Table.HeaderCell>
                                ))}
                            </Table.Row>
                        </Table.Header>
                        <Droppable droppableId="tableBody">
                            {(tProvided, tSnapshot) => (
                                // semantic-ui-react 的 table 需此 ref 才可使用react beautiful dnd
                                <Ref innerRef={tProvided.innerRef}>
                                    <Table.Body
                                        {...tProvided.droppableProps}
                                        style={getListStyle(
                                            tSnapshot.isDraggingOver
                                        )}
                                    >
                                        {characters.map((i, idx) => (
                                            <Draggable
                                                key={i.id}
                                                draggableId={i.id}
                                                index={idx}
                                            >
                                                {(tmpProvided, snapshot) => (
                                                    // semantic-ui-react 的 table 需此ref才可使用react beautiful dnd
                                                    <Ref
                                                        innerRef={
                                                            tmpProvided.innerRef
                                                        }
                                                    >
                                                        <Table.Row
                                                            key={i.id}
                                                            id="tableData"
                                                            {...tmpProvided.draggableProps}
                                                            {...tmpProvided.dragHandleProps}
                                                            style={getItemStyle(
                                                                snapshot.isDragging,
                                                                tmpProvided
                                                                    .draggableProps
                                                                    .style
                                                            )}
                                                        >
                                                            <Table.Cell
                                                                colSpan={1}
                                                                width={1}
                                                                textAlign="center"
                                                            >
                                                                <img
                                                                    src={
                                                                        iconDrag
                                                                    }
                                                                    alt="drag"
                                                                />
                                                            </Table.Cell>

                                                            <ActivityCell
                                                                contentZh={
                                                                    i.activityDateZh
                                                                }
                                                                contentEn={
                                                                    i.activityDateEn
                                                                }
                                                                colSpan={3}
                                                                width={3}
                                                            />

                                                            <ActivityCell
                                                                contentZh={
                                                                    i.activityTitleZh
                                                                }
                                                                contentEn={
                                                                    i.activityTitleEn
                                                                }
                                                                colSpan={5}
                                                                width={5}
                                                            />

                                                            <Table.Cell
                                                                verticalAlign="middle"
                                                                colSpan={4}
                                                                width={4}
                                                                style={{
                                                                    padding: "0"
                                                                }}
                                                            >
                                                                <div
                                                                    style={{
                                                                        display:
                                                                            "flex",
                                                                        flexDirection:
                                                                            "column"
                                                                    }}
                                                                >
                                                                    <div
                                                                        style={{
                                                                            ...cellBorderBottomStyle,
                                                                            ...cellPaddingStyle
                                                                        }}
                                                                    >
                                                                        {
                                                                            i.activityPlaceZh
                                                                        }
                                                                    </div>
                                                                    <div
                                                                        style={
                                                                            cellPaddingStyle
                                                                        }
                                                                    >
                                                                        {
                                                                            i.activityPlaceEn
                                                                        }
                                                                    </div>
                                                                </div>
                                                            </Table.Cell>

                                                            <Table.Cell
                                                                colSpan={1}
                                                                textAlign="center"
                                                            >
                                                                <IconButton
                                                                    icon="edit"
                                                                    onClick={() =>
                                                                        handleEdit(
                                                                            i
                                                                        )
                                                                    }
                                                                    disabled={
                                                                        isDragged
                                                                    }
                                                                    style={{
                                                                        ...buttonStyle,
                                                                        margin:
                                                                            "0",
                                                                        color:
                                                                            "black"
                                                                    }}
                                                                />
                                                            </Table.Cell>

                                                            <Table.Cell
                                                                colSpan={1}
                                                                textAlign="center"
                                                            >
                                                                <IconButton
                                                                    icon="trash"
                                                                    onClick={() => {
                                                                        dispatch(
                                                                            {
                                                                                type:
                                                                                    VillagesAct.SET_DELETING_ACTIVITY_INFO,
                                                                                payload: i
                                                                            }
                                                                        );
                                                                        dispatch(
                                                                            {
                                                                                type:
                                                                                    VillagesAct.SET_IS_DEL_MODAL_OPEN,
                                                                                payload: true
                                                                            }
                                                                        );
                                                                    }}
                                                                    disabled={
                                                                        isDragged
                                                                    }
                                                                    style={{
                                                                        ...buttonStyle,
                                                                        margin:
                                                                            "0"
                                                                    }}
                                                                />
                                                                <DeleteModal
                                                                    onClick={() =>
                                                                        handleDelete(
                                                                            i
                                                                        )
                                                                    }
                                                                    message="請問是否確認刪除?"
                                                                />
                                                            </Table.Cell>
                                                        </Table.Row>
                                                    </Ref>
                                                )}
                                            </Draggable>
                                        ))}
                                        {tProvided.placeholder}
                                    </Table.Body>
                                </Ref>
                            )}
                        </Droppable>
                    </Table>
                </DragDropContext>
            </div>
            <div className="btnArea">
                <CustomButton
                    onClick={handleSaveOrder}
                    disableBool={isACIDraggingOver}
                    message="儲存成功"
                    content="儲存活動"
                    color="green"
                />
            </div>
            <ACIModal onClick={handleACIModalOpen} />
        </>
    );
};

export default EventArea;

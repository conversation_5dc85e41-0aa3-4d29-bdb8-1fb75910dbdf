import React, { useContext, useEffect, useState } from "react";

// semantic-ui-react
import { Image, List, Icon } from "semantic-ui-react";

// react-beautiful-dnd
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";

// components
import SaveButton from "../../components/SaveButton";
import AddItemButton from "../../components/AddItemButton";
import CancelButton from "../../components/CancelButton";
import EditListButton from "../../components/EditListButton";
import UploadImageModal from "../../components/UploadImageModal";
import RemoveItemButton from "../../components/RemoveItemButton";
import UpdateText from "../../components/UpdateText";

// general
import dragImage from "../../../../images/dragImage.svg";
import { sortByPriority, imageTypeTrans } from "../../commons";
import Act from "../../../../store/actions";
import { StoreContext } from "../../../../store/StoreProvider";
import ImageTypeSelect from "../../components/ImageTypeSelect";
import CarouselSubjectSelector from "../../components/CarouselSubjectSelector";

const DisableMS = "0";

function getSubjectList(msListData) {
    const tmpListData = {};
    msListData.forEach(element => {
        if (element.enable !== DisableMS) {
            const tmpObj = {};
            tmpObj.priority = parseInt(element.seq, 10);
            tmpObj.name = element.label;
            tmpListData[element.id] = tmpObj;
        }
    });
    return tmpListData;
}

function MainCarousel() {
    const [state, dispatch] = useContext(StoreContext);
    const {
        originData,
        updatedData,
        menuActiveItem,
        isEditedDisable,
        msListData
    } = state.websiteSetting;
    const [openModal, setOpenModal] = useState(false);
    const [allData, setAllData] = useState([]);
    const [imageWebOrMobile, setImageWebOrMobile] = useState("web");

    useEffect(() => {
        // 沒有下拉選單
        dispatch({
            type: Act.SET_SELECTOPTION,
            payload: null
        });
        dispatch({
            type: Act.SET_ISEDITEDDISABLE,
            payload: true
        });
    }, []);

    useEffect(() => {
        const tmpObj = originData.find(
            element => element.id === menuActiveItem.key
        );
        if (tmpObj) {
            const keys = Object.keys(tmpObj).filter(
                element => element !== "id"
            );
            const tmpData = [];
            keys.forEach(element => tmpData.push(tmpObj[element]));
            setAllData(sortByPriority(tmpData));
        }
    }, [originData]);

    const onDragEnd = result => {
        if (result.destination === null) return;
        const startIndex = result.source.index;
        const endIndex = result.destination.index;
        const tmpAllData = JSON.parse(JSON.stringify(allData));
        // swap value
        const tmpObj = tmpAllData[startIndex];
        tmpAllData[startIndex] = tmpAllData[endIndex];
        tmpAllData[endIndex] = tmpObj;

        // swap priority of value
        const tmpPriority = tmpAllData[startIndex].priority;
        tmpAllData[startIndex].priority = tmpAllData[endIndex].priority;
        tmpAllData[endIndex].priority = tmpPriority;
        setAllData(tmpAllData);

        // 將交換後的array寫回去updatedData
        const tmpUpdatedData = JSON.parse(JSON.stringify(updatedData));
        const tmpUpdatedDataObj = tmpUpdatedData.find(
            element => element.id === menuActiveItem.key
        );
        const keys = Object.keys(tmpUpdatedDataObj);
        const findObjKey1 = keys.find(
            key =>
                tmpUpdatedDataObj[key].priority ===
                tmpAllData[startIndex].priority
        );
        const findObjKey2 = keys.find(
            key =>
                tmpUpdatedDataObj[key].priority ===
                tmpAllData[endIndex].priority
        );
        tmpUpdatedDataObj[findObjKey1] = tmpAllData[startIndex];
        tmpUpdatedDataObj[findObjKey2] = tmpAllData[endIndex];
        // console.log("tmpUpdatedData ", tmpUpdatedData);
        dispatch({
            type: Act.SET_UPDATEDDATA,
            payload: tmpUpdatedData
        });
    };

    return (
        <div className="MainCarousel">
            <div className="topArea">
                <ImageTypeSelect
                    imageWebOrMobile={imageWebOrMobile}
                    setImageWebOrMobile={setImageWebOrMobile}
                />
            </div>
            <DragDropContext onDragEnd={onDragEnd}>
                <div className="listArea">
                    <Droppable droppableId="droppableId">
                        {(provided, snapshot) => (
                            <div
                                ref={provided.innerRef}
                                {...provided.droppableProps}
                            >
                                <List relaxed divided>
                                    {allData.length !== 0 &&
                                        allData.map((data, index) => (
                                            <Draggable
                                                draggableId={`draggable_${index}`}
                                                index={index}
                                                key={index}
                                                isDragDisabled={isEditedDisable}
                                            >
                                                {(provided, snapshot) => (
                                                    <div
                                                        ref={provided.innerRef}
                                                        {...provided.draggableProps}
                                                        {...provided.dragHandleProps}
                                                    >
                                                        <List.Item
                                                            style={{
                                                                display: "flex",
                                                                justifyContent:
                                                                    "space-evenly",
                                                                margin: "5px"
                                                            }}
                                                            className={`listItem_${index}`}
                                                        >
                                                            {!isEditedDisable && (
                                                                <Icon
                                                                    name="sidebar"
                                                                    style={{
                                                                        fontSize:
                                                                            "1rem",
                                                                        alignSelf:
                                                                            "center"
                                                                    }}
                                                                />
                                                            )}
                                                            <List.Content className="contentWithImageAndSelector">
                                                                <CarouselSubjectSelector
                                                                    dropDown={getSubjectList(
                                                                        msListData
                                                                    )}
                                                                    listData={
                                                                        data
                                                                    }
                                                                />
                                                                <Image
                                                                    style={{
                                                                        cursor: isEditedDisable
                                                                            ? "default"
                                                                            : "pointer"
                                                                    }}
                                                                    src={
                                                                        data[
                                                                            imageTypeTrans[
                                                                                imageWebOrMobile
                                                                            ]
                                                                        ] !== ""
                                                                            ? data[
                                                                                imageTypeTrans[
                                                                                    imageWebOrMobile
                                                                                ]
                                                                            ]
                                                                            : dragImage
                                                                    }
                                                                    onClick={() => {
                                                                        if (
                                                                            !isEditedDisable
                                                                        ) {
                                                                            setOpenModal(
                                                                                true
                                                                            );
                                                                            dispatch(
                                                                                {
                                                                                    type:
                                                                                        Act.SET_LISTDATA,
                                                                                    payload: data
                                                                                }
                                                                            );
                                                                        }
                                                                    }}
                                                                />
                                                            </List.Content>
                                                            <List.Content
                                                                style={{
                                                                    width:
                                                                        "30%",
                                                                    display:
                                                                        "flex",
                                                                    alignItems:
                                                                        "center"
                                                                }}
                                                            >
                                                                <UpdateText
                                                                    language=""
                                                                    option={{
                                                                        priority:
                                                                            data.priority,
                                                                        message:
                                                                            data.websiteUrl,
                                                                        column:
                                                                            "websiteUrl"
                                                                    }}
                                                                />
                                                            </List.Content>
                                                            {!isEditedDisable && (
                                                                <RemoveItemButton
                                                                    language=""
                                                                    data={data}
                                                                    setAllData={
                                                                        setAllData
                                                                    }
                                                                />
                                                            )}
                                                        </List.Item>
                                                        <hr />
                                                    </div>
                                                )}
                                            </Draggable>
                                        ))}
                                </List>
                                {provided.placeholder}
                            </div>
                        )}
                    </Droppable>
                </div>
            </DragDropContext>
            <div className="btnArea">
                {isEditedDisable && <EditListButton />}
                {!isEditedDisable && <AddItemButton setAllData={setAllData} />}
                {!isEditedDisable && <CancelButton />}
                {!isEditedDisable && <SaveButton language="" />}
            </div>
            {openModal && (
                <UploadImageModal
                    setOpenModal={setOpenModal}
                    setAllData={setAllData}
                    imageWebOrMobile={imageWebOrMobile}
                />
            )}
        </div>
    );
}

export default MainCarousel;
